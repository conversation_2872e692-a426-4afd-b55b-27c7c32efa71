// @ts-nocheck
/* entity */
import Field from '@model/entity/Field';
/* enum */
import Column from '@model/types/Column';
import {
  FieldTypeMappingEnum,
  LogisticsFieldNameMappingEnum,
  TaskFieldNameMappingEnum
} from '@model/enum/FieldMappingEnum';
/* utils */
import {isArray, isEmpty, isFunction, isObject, isUndefined} from '@src/util/type';
import {fieldArrayToObject, stringArrayToObject} from '@src/util/array';
import Log from '@src/util/log.ts';
import {_isHiddenField, initialize} from '@src/component/form/util';
/* model */
import {ShbFormUtilInitOptions} from '@model/biz/Form';

// 过滤导出字段的字段类型列表
const FilterExportFieldFormTypes: FieldTypeMappingEnum[] = [
  FieldTypeMappingEnum.Attachment,
  FieldTypeMappingEnum.Autograph,
  FieldTypeMappingEnum.Separator
];

/**
 * @description 过滤导出字段的字段类型
 */
export function filterExportFieldWithFormType(field: Field): boolean {
  try {
    return !FilterExportFieldFormTypes.includes(
      field.formType as FieldTypeMappingEnum
    );
  } catch (error) {
    Log.error(error, filterExportFieldWithFormType.name);
    return true;
  }
}

/**
 * @description: 获取字段名称
 * @param {Field} field 字段
 */
export function getFieldName(field: Field | Column): string {
  // @ts-ignore
  return field?.field || field?.fieldName || field?.name || '';
}

/**
 * @description: 是否是相同的字段
 * @param {Field} field1 字段1
 * @param {Field} field2 字段2
 * @return {Boolean} 是否相等
 */
export function isSameField(field1: Field, field2: Field): boolean {
  return (
    field1.field === field2.field ||
    field1.fieldName === field2.fieldName ||
    field1.field === field2.fieldName ||
    field1.fieldName === field2.field
  );
}

/**
 * @description: 当前字段是否支持搜索
 * @param {Field} field 字段
 */
export function isSearchField(field: Field): boolean {
  return Boolean(field?.isSearch && field.isSearch === 1);
}

/**
 * @description: 当前字段是为系统字段
 */
export function isSystemFiled(field: Field | Column): boolean {
  return Boolean(
    field?.isSystem && (field.isSystem === 1 || field.isSystem === true)
  );
}

/**
 * @description: 当前字段是为附件字段
 */
export function isAttachmentFiled(field: Field | Column): boolean {
  return Boolean(field?.formType === FieldTypeMappingEnum.Attachment);
}

/**
 * @description: 是否为工单编号字段
 * @param {Field} field 字段
 */
export function isTaskNoField(field: Field): boolean {
  return getFieldName(field) === TaskFieldNameMappingEnum.TaskNo;
}

/**
 * @description: 当前字段是为备注字段
 */
export function isRemarkFiled(field: Field | Column): boolean {
  return Boolean(getFieldName(field) === TaskFieldNameMappingEnum.Remark);
}

/**
 * @description: 是否为计划时间字段
 * @param {Field} field 字段
 */
export function isPlanTimeField(field: Field): boolean {
  return getFieldName(field) === TaskFieldNameMappingEnum.PlanTime;
}

/**
 * @description: 是否为工单描述字段
 * @param {Field} field 字段
 */
export function isDescriptionField(field: Field): boolean {
  return getFieldName(field) === TaskFieldNameMappingEnum.Description;
}

/**
 * @description: 是否为用户字段
 * @param {Field} field 字段
 */
export function isUserField(field: Field): boolean {
  return Boolean(field?.formType === FieldTypeMappingEnum.User);
}

/**
 * @description: 不支持显示的字段
 * @param {Field | Column} field 字段
 * @return {Boolean} 是否支持显示
 */
export function isNotSupportedDisplayField(field: Field | Column): boolean {
  /**
   * 不支持显示的字段类型列表
   * 附件，分割线，说明信息，签名
   */
  const notSupportedDisplayFieldsFormType: FieldTypeMappingEnum[] = [
    FieldTypeMappingEnum.Attachment,
    FieldTypeMappingEnum.Separator,
    FieldTypeMappingEnum.Info,
    FieldTypeMappingEnum.Autograph,
    FieldTypeMappingEnum.Location,
    FieldTypeMappingEnum.SubForm,
    FieldTypeMappingEnum.SubFormApi,
    FieldTypeMappingEnum.SparePart,
    FieldTypeMappingEnum.ServiceItem,
    FieldTypeMappingEnum.RelatedData,
    FieldTypeMappingEnum.QualityResult,
    FieldTypeMappingEnum.InStock,
    FieldTypeMappingEnum.OutStock,
    FieldTypeMappingEnum.LackStock,
    // FieldTypeMappingEnum.ProviderSettlement,
    FieldTypeMappingEnum.BackCheck,
    FieldTypeMappingEnum.DispatchFix,
    FieldTypeMappingEnum.MaterialApply,
    FieldTypeMappingEnum.OutOrder,
    FieldTypeMappingEnum.OutWarehouse,
    FieldTypeMappingEnum.InWarehouse,
    FieldTypeMappingEnum.SunmiOutWarehouse,
    FieldTypeMappingEnum.SunmiInWarehouse,
    FieldTypeMappingEnum.InSparepart,
    FieldTypeMappingEnum.OutSparepart,
    FieldTypeMappingEnum.Connector,
    FieldTypeMappingEnum.JsCodeBlock,
    FieldTypeMappingEnum.RichText,
    FieldTypeMappingEnum.MemberPay,
    FieldTypeMappingEnum.Contract,
  ];

  return notSupportedDisplayFieldsFormType.includes(
    field.formType as FieldTypeMappingEnum
  );
}

/**
 * @description: 字段排序
 * @param {Field[] | Column[]} fields 字段
 */
export function sortFields(fields: Field[], sortFieldNames: string[]): Field[] {
  // 没排序字段数组参数，则按照 orderId默认规则排序
  if (isEmpty(sortFieldNames)) return sortFieldsWithOrderId(fields);

  // 排序字段对象
  const FiledSortMap: Record<string, string> = stringArrayToObject(
    sortFieldNames
  );
  // 排序的字段列表
  const SortedFields: Field[] = [];
  // 未排序的字段列表
  const UnSortedFields: Field[] = [];

  // 字段名称
  let fieldName = '';
  // 字段索引
  let fieldIndex = '';

  fields.forEach((field: Field) => {
    // 字段名称
    fieldName = getFieldName(field);
    fieldIndex = FiledSortMap[fieldName];

    // 排序字段对象存在此字段名称则取其索引
    if (!isUndefined(fieldIndex)) {
      SortedFields[Number(fieldIndex)] = field;
    } else {
      UnSortedFields.push(field);
    }
  });

  const ResultFields: Field[] = [...SortedFields, ...UnSortedFields];

  return ResultFields.filter((field: Field) => Boolean(field));
}

/**
 * @description: 字段排序按照
 * @param {Field[]} fields 字段
 */
function sortFieldsWithOrderId(fields: Field[]): Field[] {
  return fields.sort((field1: Field, field2: Field) => {
    // @ts-ignore
    return field1?.orderId - field2?.orderId;
  });
}

/**
 * @description: 合并字段列表
 * @param {Field[]} fieldsLeft 左侧字段列表
 * @param {Field[]} fieldsRight 右侧字段列表
 * @param {string} mergeFieldNames 需要合并字段的字段名数组
 * @param {boolean} isLeftJoin
 * @return {Field[]} 合并完的字段列表
 */
export function mergeFields(
  fieldsLeft: Field[],
  fieldsRight: Field[],
  mergeFieldNames: string[],
  isLeftJoin = false
): Field[] {
  // 需要合并字段的字段名称对象
  const MergeFieldNameMap: Record<string, string> = stringArrayToObject(
    mergeFieldNames
  );
  // 左侧字段列表
  const FieldsLeftMap: Record<string, Field> = fieldArrayToObject(
    isLeftJoin ? fieldsRight : fieldsLeft
  );
  // 右侧字段列表
  const FieldsRightMap: Record<string, Field> = fieldArrayToObject(
    isLeftJoin ? fieldsLeft : fieldsRight
  );

  const MergedFieldsMap = isLeftJoin
    ? { ...FieldsRightMap, ...FieldsLeftMap }
    : { ...FieldsLeftMap, ...FieldsRightMap };
  // 合并后的结果字段列表
  let mergedResultFields: Field[] = [];

  // 遍历左侧字段列表
  for (let fieldName in MergedFieldsMap) {
    /**
     * 如果需要合并字段的字段名称对象里面 不存在此字段名
     * 或者 右侧字段名称对象里面 不存在此字段名
     */
    if (!MergeFieldNameMap[fieldName] || !FieldsRightMap[fieldName]) {
      mergedResultFields.push(MergedFieldsMap[fieldName]);
      continue;
    }

    mergedResultFields.push({
      ...MergedFieldsMap[fieldName],
      ...FieldsRightMap[fieldName]
    });
  }

  return mergedResultFields;
}

/**
 * @description:
 * -- 默认使用fieldName为键名
 * ! Note: 注意此方法会改变原对象
 * @param {Field} fieldsLeft
 * @param {Field} fieldsRight
 * @param {Function} customizer
 * @return {void}
 */
export function mergeFieldsWithProperty<Left, Right, Result>(
  fieldsLeft: Left[],
  fieldsRight: Right[],
  customizer: (leftFieldItem: Left, rightFieldItem: Right) => Result
): Result[] {
  // 效验自定义合并函数类型
  if (!isFunction(customizer)) {
    Log.error(
      'Caused: customizer must be a function',
      mergeFieldsWithProperty.name
    );
    return [];
  }

  // 左侧字段列表
  const FieldsLeftMap: Record<string, Left> = fieldArrayToObject<Left>(
    fieldsLeft
  );
  // 右侧字段列表
  const FieldsRightMap: Record<string, Right> = fieldArrayToObject<Right>(
    fieldsRight
  );

  let leftFieldItem: Left;
  let rightFieldItem: Right;
  let result: Result[] = [];

  for (let fieldName in FieldsLeftMap) {
    leftFieldItem = FieldsLeftMap[fieldName];
    rightFieldItem = FieldsRightMap[fieldName];

    result.push(customizer(leftFieldItem, rightFieldItem));
  }

  return result;
}

export function getFieldWithFieldName(
  fields: Field[],
  fieldName: string
): Field | null {
  try {
    return (
      fields.filter((field: Field) => getFieldName(field) === fieldName)[0] ||
      null
    );
  } catch (error) {
    Log.error(error, getFieldWithFieldName.name);
    return null;
  }
}

/**
 * @description 是否存在某个字段
 */
export function existsField(
  fieldName: TaskFieldNameMappingEnum,
  fields?: Field[],
  fieldsMap?: Record<string, Field>
): Field | null {
  if (!isUndefined(fields) && isArray(fields))
    return existsFieldWithFields(fieldName, fields as Field[]);
  if (!isUndefined(fieldsMap) && isObject(fieldsMap))
    return existsFieldWithFieldsMap(
      fieldName,
      fieldsMap as Record<string, Field>
    );

  return null;
}

/**
 * @description: 字段列表中是否存在某个字段
 * @param {TaskFieldNameMappingEnum} fieldName 字段名称
 * @param {Field} fields 字段列表
 * @return {Field | null}
 */
function existsFieldWithFields(
  fieldName: TaskFieldNameMappingEnum,
  fields: Field[]
): Field | null {
  let field: Field | null = null;

  try {
    for (let fieldValue of fields) {
      if (getFieldName(fieldValue) === fieldName) {
        field = fieldValue;
        break;
      }
    }
  } catch (error) {
    field = null;
    Log.error(error, existsFieldWithFields.name);
  }

  return field;
}

/**
 * @description: 字段列表对象中是否存在某个字段
 * @param {TaskFieldNameMappingEnum} fieldName 字段名称
 * @param {Record} fieldsMap 字段列表对象
 * @return {Field | null}
 */
function existsFieldWithFieldsMap(
  fieldName: TaskFieldNameMappingEnum,
  fieldsMap: Record<string, Field>
): Field | null {
  return fieldsMap[fieldName] ?? null;
}

export function isEnabled(fields: Field): boolean {
  return fields?.enabled === 1;
}

/**
 * 判断相关关联显示逻辑的字段是否存在
 * @param fieldNameArray
 * @param field
 */
const hiddenFieldExistDependenciesField = (fieldNameArray, field)=> {
  if(Reflect.has(field, 'setting') && field?.setting?.dependencies && isObject(field.setting.dependencies)) {
    const dependenciesKeys = Object.keys(field?.setting?.dependencies);
    return fieldNameArray.find(fieldName=> dependenciesKeys.includes(fieldName)) || false;
  }
  return false;
};

/**
 * @description: 过滤隐藏字段对应的值
 * @return {*}
 * @param fields
 * @param formData
 */
export function hiddenFieldValueFilterHandler(
  fields: Field[],
  formData: Record<string, any>
): Record<string, any> {
  const hiddenFields: Field[] = [];
  const fieldMap: Record<string, Field> = fieldArrayToObject(fields);
  const fieldNameArray = Object.keys(fieldMap);

  fields.forEach((field: Field) => {
    const isHiddenField = _isHiddenField(field, formData, fieldMap, true);
    // 判断是隐藏了 并且相关的关联字段存在（下拉菜单 显示逻辑）
    if (isHiddenField && hiddenFieldExistDependenciesField(fieldNameArray, field)) {
      hiddenFields.push(field);
    }
  });

  const emptyHiddenFieldValue: Record<string, any> = initialize(
    hiddenFields,
    {},
    undefined,
    new ShbFormUtilInitOptions({
      isUseFieldDefaultValue: false
    })
  );

  const result = {
    ...formData,
    ...emptyHiddenFieldValue
  };

  return result;
}

/**
 * @description: 获取字段类型
 * @param {Field} field 字段
 */
export function getFieldFormType(field: Field | Column): string {
  return field?.formType || '';
}

/**
 * @description: 是否为质保信息字段
 * @param {Field} field 字段
 */
export function isQualityInfoField(field: Field): boolean {
  return getFieldFormType(field) === FieldTypeMappingEnum.QualityInfo;
}

/**
 * @description: 是否为物流字段
 * @param {Field} field 字段
 */
export function isLogisticsField(field: Field): boolean {
  return getFieldFormType(field) === FieldTypeMappingEnum.Logistics;
}

/**
 * @description: 是否为物流单号字段
 * @param {Field} field 字段
 */
export function isLogisticsNoField(field: Field): boolean {
  return isLogisticsField(field) && getFieldName(field).indexOf(LogisticsFieldNameMappingEnum.LogisticsNo) > -1;
}

/**
 * @description: 是否为货币金额字段
 * @param {Field} field 字段
*/
export function isCurrencyField(field: Field): boolean {
  return Boolean(field?.formType === FieldTypeMappingEnum.Currency);
}

export default {
  isSameField,
  isSearchField,
  isSystemFiled,
  isNotSupportedDisplayField,
  getFieldName,
  getFieldWithFieldName,
  existsField,
  isQualityInfoField,
  isLogisticsField,
  isLogisticsNoField,
  isCurrencyField,
};
