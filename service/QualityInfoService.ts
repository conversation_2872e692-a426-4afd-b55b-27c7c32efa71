// @ts-nocheck
/* entity */
import Field from '@model/entity/Field';
import {
  getQualityStatusField,
  getQualityStartTimeField,
  getQualityEndTimeField
} from '@model/entity/QualityInfo';
/* service */
import { isQualityInfoField } from '@service/FieldService';
/* util */
import * as _ from 'lodash';

/**
 * @description 产品类型质保信息字段展开为质保开始时间、质保结束时间、质保状态
 * @return {Field[]} 展开的字段列表
 */
export function smoothQualityInfoField(
  fields: Field[],
  productFieldName
): Field[] {
  const originFields: Field[] = _.cloneDeep(fields);

  // 质保信息字段索引
  const qualityInfoFieldIndex: number = originFields.findIndex((field: Field) =>
    isQualityInfoField(field)
  );

  // 无质保信息字段则返回
  if (qualityInfoFieldIndex < 0) return originFields;

  // 质保信息字段
  const qualityInfoField: Field = fields[qualityInfoFieldIndex];

  // 质保信息字段列表
  const qualityInfoFields = [
    getQualityStartTimeField(productFieldName),
    getQualityEndTimeField(productFieldName),
    getQualityStatusField(productFieldName)
  ];

  // 质保信息字段是否可填写
  if(qualityInfoFieldIndex >= 0 ) {
    qualityInfoFields.forEach(f => {
      f.disabled = qualityInfoField.revisable === 0;
    });   
  }
  // 替换质保字段为质保字段列表(平铺)
  fields.splice(qualityInfoFieldIndex, 1, ...qualityInfoFields);

  return fields.filter(field => Boolean(field));
}

export default {
  smoothQualityInfoField
};
