/* util */
import { isPlainObject, isString } from 'lodash'
import { parse } from '@src/util/lang/object'
import Log from '@src/util/log'
/* model */
import CardInfo, { CardInfoConfig } from '@model/entity/CardInfo'

/** 
 * @description 获取附加组件的配置
*/
function getCardConfig<CardType extends CardInfo, ConfigType extends CardInfoConfig>(card: CardType): ConfigType {
  try {
    
    const config = card.config || {}
    
    if (isString(config)) {
      
      const configObj = parse(config as unknown as string) as unknown
      
      return configObj as ConfigType
      
    }
    
    if (isPlainObject(config)) {
      return config as ConfigType
    }
    
    return {} as ConfigType
    
  } catch (error) {
    
    Log.error(error, getCardConfig.name)
    
    return {} as ConfigType
    
  }
}

export {
  getCardConfig
}
