// @ts-nocheck
/* entity */
import Field from '@model/entity/Field';
import { getLogisticsCompanyField, getLogisticsNoField, getLogisticsExPhoneField } from '@model/entity/Logistics';
import { FieldTypeMappingEnum } from "@model/enum/FieldMappingEnum";
/* service */
import { isLogisticsField } from '@service/FieldService';
import Result from '@src/model/Result';
/* util */
import _ from 'lodash';
import { isSubForm } from "@src/component/form/util";


/**
 * @description 物流字段展开为物流公司、物流单号
 * @return {Field[]} 展开的字段列表
 */
export function smoothSplitField(fields: Field[], isForm = false): Field[] {
  const originFields: Field[] = _.cloneDeep(fields);

  // 物流字段索引
  // const logisticsFields: Field[] = originFields.filter((field: Field) => isLogisticsField(field));

  return originFields.reduce((result, field)=> {
    if(isLogisticsField(field) && !field.isHidden) {
      const logisticsAllFields = [
        getLogisticsCompanyField(field),
        getLogisticsNoField(field)
      ];
      if(isForm) logisticsAllFields.push(getLogisticsExPhoneField(field));
      result.push(...logisticsAllFields);
    } else {
      if (isSubForm(field) && !field.isHidden && !['productAndSparepart'].includes(field.formType)) {
        field.subFormFieldList = smoothSplitField(field.subFormFieldList, isForm);
      }
      result.push(field);
    }
    return result;
  }, []);
}

export function smoothMergeField(fields: Field[]): Field[] {

  fields.reduce((result, field)=> {

    if(field.formType === FieldTypeMappingEnum.Logistics) {
      const  fieldName =  field.fieldName.split('_')?.[0] || '';
      field.fieldName = fieldName;
      const existField = result.find(item=> item.fieldName.includes(fieldName));
      if(!existField) result.push(field);
    } else {
      result.push(field);
    }
    return result;
  });

}

export default {
  smoothSplitField
};
