// @ts-nocheck
/* entity */
import Field from '@model/entity/Field';
/* enum */
import { FieldTypeMappingEnum, MaterialWarehouseFieldNameMappingEnum, SparepartWarehouseFieldNameMappingEnum } from '@model/enum/FieldMappingEnum';
/* util */
import * as FormUtil from '@src/component/form/util';
import MathUtil from '@src/util/math';
import _ from 'lodash';
import i18n from '@src/locales';

// 物料清单支持的字段类型
const MATERIAL_SUPPORT_FORM_TYPE = [
  FieldTypeMappingEnum.Number,
  FieldTypeMappingEnum.Select,
];

// 物料清单不支持的字段类型
const MATERIAL_NOT_SUPPORT_FORM_TYPE = [
  FieldTypeMappingEnum.Attachment,
];

/**
 * @description 物料清单支持的自定义字段类型
 * 文本、数字、下拉菜单/多级下拉、人员、日期、电话、邮箱、地址、位置、计算公式
 */
const MATERIAL_ORDER_SUPPORT_FORM_TYPE = [
  FieldTypeMappingEnum.Text,
  FieldTypeMappingEnum.Textarea,
  FieldTypeMappingEnum.Number,
  FieldTypeMappingEnum.Select,
  FieldTypeMappingEnum.Cascader,
  FieldTypeMappingEnum.User,
  FieldTypeMappingEnum.Date,
  FieldTypeMappingEnum.Phone,
  FieldTypeMappingEnum.Email,
  FieldTypeMappingEnum.Address,
  FieldTypeMappingEnum.Location,
  FieldTypeMappingEnum.Formula,
];

// 物料清单必填字段
const MATERIAL_REQUIRED_FIELDS = [
  MaterialWarehouseFieldNameMappingEnum.Num,
];

// 出入库不可再发起提示枚举
enum StockAppliedErrorMsgEnum {
  outWarehouse = i18n.t('model.materialService.tip1'),
  inWarehouse = i18n.t('model.materialService.tip2'),
  sunmiOutWarehouse = i18n.t('model.materialService.tip1'),
  sunmiInWarehouse = i18n.t('model.materialService.tip2'),
  outSparepart = i18n.t('model.materialService.tip3'),
  inSparepart = i18n.t('model.materialService.tip4'),
}

/**
 * @description 将云仓服务端物料表单字段转成物料清单需要的数据
 * @return {Field[]} 展开的字段列表
 */
export function convertMaterialRemoteDataToMaterialOrderValue(
  fields: Field[],
  parentFieldName
): Field[] {
  // 只取物料表单的系统字段
  const originFields = fields.filter(field => !MATERIAL_NOT_SUPPORT_FORM_TYPE.includes(field.formType) && field.isSystem)
    .map(field => {
      const fieldSetting = field?.setting || {};
      if(Reflect.has(fieldSetting, 'dependencies')) {
        Reflect.deleteProperty(fieldSetting, 'dependencies');
      }
      return {
        ...field,
        formType: MATERIAL_SUPPORT_FORM_TYPE.includes(field.formType) ? field.formType : 'text',
        id: null, // 去掉原字段id，由后端生成
        isNull: MATERIAL_REQUIRED_FIELDS.includes(field.fieldName) ? 0 : 1,
        isSystem: 1, // 为了区别自定义显示字段
        fieldName: `${parentFieldName}_${field.fieldName}` // 拼接fieldName防止重复
      };
    });
  return FormUtil.toFormField(originFields);
}

/**
 * @description 将云仓服务端物料表单字段转成物料清单需要的数据 (支持物料自定义字段)
 * @return {Field[]} 展开的字段列表
 */
export function convertMaterialRemoteDataToMaterialOrderValueV2(
  fields: Field[],
  parentFieldName
): Field[] {
  const originFields = fields
    .filter(field => (field.isSystem && !MATERIAL_NOT_SUPPORT_FORM_TYPE.includes(field.formType)) || (!field.isSystem && MATERIAL_ORDER_SUPPORT_FORM_TYPE.includes(field.formType)))
    .map(field => {
      const fieldSetting = field?.setting || {};

      // 清除物料的显示逻辑关系
      if (Reflect.has(fieldSetting, 'dependencies')) {
        Reflect.deleteProperty(fieldSetting, 'dependencies');
      }

      // 字段类型
      const formType = field.isSystem
        ? MATERIAL_SUPPORT_FORM_TYPE.includes(field.formType)
          ? field.formType
          : FieldTypeMappingEnum.Text
        : field.formType

      return {
        ...field,
        formType,
        id: null, // 去掉原字段id，由后端生成
        isNull: MATERIAL_REQUIRED_FIELDS.includes(field.fieldName) ? 0 : 1,
        isSystem: 1, // 为了区别自定义显示字段
        fieldName: `${parentFieldName}_${field.fieldName}` // 拼接fieldName防止重复
      };
    });

  return FormUtil.toFormField(originFields);
}

/**
 * @description 根据关联字段值自动带入
 * @param {*} currentFieldName 当前字段fieldName
 * @param {*} subFormFields 当前子字段
 * @param {*} linkFieldName 关联字段fieldName
 * @param {*} formValue 表单值
 * @param {*} linkFieldNameNewValue 关联字段新值
 * @param {*} linkFieldNameOldValue 关联字段旧值
 * @param {*} uniqueKey 唯一key
 */
export function getFieldValueByLinkFieldName(
  currentFieldName: string,
  subFormFields: Field[],
  linkFieldName: string,
  formValue: object,
  linkFieldNameNewValue: Array,
  linkFieldNameOldValue: Array,
  uniqueKey: string,
) {
  const currentFieldNameValue =_.cloneDeep(formValue[currentFieldName]);

  // 根据新旧值筛选出不同项
  const differentArr = findDiffFromArr(linkFieldNameNewValue, linkFieldNameOldValue);

  const copiedLinkFormValues = _.cloneDeep(differentArr);
  const replaceCopiedLinkFormValues = replaceLinkFormValues(currentFieldName, linkFieldName, copiedLinkFormValues, subFormFields);

  let duplicateArr = [];

  // 判断是否是删除以及编辑
  if (linkFieldNameNewValue.length <= linkFieldNameOldValue.length) {
    // 判断是否是编辑
    if (linkFieldNameNewValue.length === linkFieldNameOldValue.length) {
      // 找出旧值的数组
      const oldValueArr = replaceLinkFormValues(currentFieldName, linkFieldName, findDiffFromArr(linkFieldNameOldValue, linkFieldNameNewValue), subFormFields);
      const needSpliceItems = [];

      currentFieldNameValue.forEach((field, index) => {
        oldValueArr.forEach(oldItem => {
          // 判断新旧值是否相等然后替换成新值
          if(_.isEqual(field, {...field, ...oldItem})) {
            // 这里是合并在出库中每项的值（防止在出库申请表单的旧值丢失）
            const contactOldValuesArr = replaceCopiedLinkFormValues.map(item => ({...field, ...item}));
            needSpliceItems.push({ key: index, value: contactOldValuesArr });
          }
        });
      });

      // 根据needSpliceIdx来替换
      for (let [k, { key, value }] of Object.entries(needSpliceItems)) {
        currentFieldNameValue.splice(key, value.length, ...value);
      }
    } else {
      deleteLinkFieldValue(currentFieldNameValue, replaceCopiedLinkFormValues, uniqueKey);
    }

    duplicateArr = [...currentFieldNameValue];
  } else {
    duplicateArr = [...currentFieldNameValue, ...replaceCopiedLinkFormValues].reduce((prev, current) => {
      const existIndex = prev.findIndex(item => item[uniqueKey] === current[uniqueKey]);
      existIndex === -1 ? prev.push(current) : prev.splice(existIndex, 1, {...prev[existIndex], ...current});
      return prev;
    }, []);
  }

  return duplicateArr;
}

/**
 * @description 替换旧的fieldName为新的fieldName（因为是关联的问题）
 */
export function replaceLinkFormValues(
  currentFieldName: string,
  linkFieldName: string,
  copiedLinkFormValues: Array,
  fields: Field[],
) {
  return copiedLinkFormValues.map(valItem => {
    const newValItem = {};

    // 只取子字段中对应的关联字段的值
    for(let [k, v] of Object.entries(valItem)) {
      const fieldName = k.replace(linkFieldName, currentFieldName);

      // 判断当前子字段中是否存在对应的关联字段
      let isExit = fields.find(field => field.fieldName === fieldName);
      if (isExit){
        newValItem[fieldName] = v;
      }else if(k.split('_').length > 1){
        newValItem[`${currentFieldName}_${k.split('_')[1]}`] = v;
        if (k.split('_')[1] == 'num') {
          newValItem[`${currentFieldName}_stockApplied`] = v;
        }
      }else if(k.indexOf(fieldName) > -1){ //兼容关联的自定义字段
        newValItem[`${currentFieldName}_${k}`] = v;
      }
    }

    return newValItem;
  });
}

/**
 * @description 删除管理的values中的item
 */
export function deleteLinkFieldValue(
  currentFieldNameValue: Array,
  replaceCopiedLinkFormValues: Array,
  uniqueKey: string,
) {
  currentFieldNameValue.forEach((item, index) => {
    const existItem = replaceCopiedLinkFormValues.find(dfItem => dfItem[uniqueKey] === item[uniqueKey]);
    if (existItem) currentFieldNameValue.splice(index, 1);
  });
}

/**
 * @description 删除object中指定的key
 */
export function delObjKey(obj, keys = []) {
  keys.forEach(key => {
    if(Reflect.has(obj, key)) {
      Reflect.deleteProperty(obj, key);
    }
  });
}

/**
 * @description 过滤出两个数组中不同元素项
 */
export function findDiffFromArr(arr1 = [], arr2 = []) {
  // 判断删除无用的key
  [...arr1, ...arr2].forEach(item => delObjKey(item, ['contentId', 'templateBizId', 'id']));

  if (arr1.length >= arr2.length) return arr1.filter(item => !arr2.find(nItem => _.isEqual(item, nItem)));

  return findDiffFromArr(arr2, arr1);
}

/**
 * @description 校验申请数量
 */
export function validateStockAppliedNumber(parentField, fields, value, stockAppliedValue) {
  // 需出入库数量字段
  const numField = fields.find(field => field.fieldName === `${parentField.fieldName}_${MaterialWarehouseFieldNameMappingEnum.Num}`) || {};
  // 已出入库数量字段
  const stockFinishedField = fields.find(field => field.fieldName === `${parentField.fieldName}_${MaterialWarehouseFieldNameMappingEnum.StockFinished}`) || {};
  // 出入库中的数量字段
  const stockUnderwayField = fields.find(field => field.fieldName === `${parentField.fieldName}_${MaterialWarehouseFieldNameMappingEnum.StockUnderway}`) || {};
  // 申请数量字段
  const stockAppliedField = fields.find(field => field.fieldName === `${parentField.fieldName}_${MaterialWarehouseFieldNameMappingEnum.StockApplied}`) || {};

  // 需出入库数量
  const num = value[numField.fieldName];
  // 已出入库数量
  const stockFinishedNum = value[stockFinishedField.fieldName];
  // 出入库中的数量
  const stockUnderwayNum = value[stockUnderwayField.fieldName];
  // 申请数量
  const stockAppliedNum = stockAppliedValue || value[stockAppliedField.fieldName];

  // 判断需出入库的数量为空
  const isEmptyNum = !num || !num.toString().length;
  // 判断申请数量是否为空
  const isEmptyValue = !stockAppliedNum || !stockAppliedNum.toString().length;

  let message = '';

  // 申请数量小于0
  if (!isEmptyValue && Number(stockAppliedNum) <= 0) return message = `${stockAppliedField.displayName}必须>0`;

  // 需出入库的数量不为空时 申请数量不能大于它
  if (!isEmptyNum && Number(num) < Number(stockAppliedNum)) return message = `${stockAppliedField.displayName}不能大于${numField.displayName}`;

  // 需出入库的数量为空时 已出入库数量/出入库中的数量有值时 不允许申请
  if (isEmptyNum && !isEmptyValue && (Number(stockFinishedNum) || Number(stockUnderwayNum))) return message = StockAppliedErrorMsgEnum[parentField.formType];

  return message;
}

/**
 * @description 校验备件数量、备件出入库申请数量
 */
export function validateSparepartStockAppliedNumber(parentField, fields, value, precision) {
  // 需入库数量
  const num = value[`${parentField.fieldName}_${SparepartWarehouseFieldNameMappingEnum.Num}`];
  // 申请数量
  const stockAppliedNum = value[`${parentField.fieldName}_${SparepartWarehouseFieldNameMappingEnum.StockApplied}`];
  // 需入库数量字段
  const numField = fields.find(field => field.fieldName === `${parentField.fieldName}_${SparepartWarehouseFieldNameMappingEnum.Num}`) || {};
  // 申请数量字段
  const stockAppliedField = fields.find(field => field.fieldName === `${parentField.fieldName}_${SparepartWarehouseFieldNameMappingEnum.StockApplied}`) || {};
  // 判断申请数量是否为空
  const isEmptyValue = !stockAppliedNum || !stockAppliedNum.toString().length;
  // 判断需出入库的数量为空
  const isEmptyNum = !num || !num.toString().length;
  let message = '';

  // 申请数量小于0
  if (!isEmptyValue && Number(stockAppliedNum) <= 0) {
    return message = i18n.t('model.materialService.tip5', {name: stockAppliedField.displayName});
  }
  // 需出入库的数量不为空时 申请数量不能大于它
  if (!isEmptyNum && Number(num) < Number(stockAppliedNum)) {
    return message = i18n.t('model.materialService.tip6', {data1: stockAppliedField.displayName, data2: numField.displayName});
  }

  // 备件入库 校验申请数量是否可以保持两位小数
  const digitsNum = MathUtil.decimalNumber(num);
  const digitsStockAppliedNum = MathUtil.decimalNumber(stockAppliedNum);

  if(digitsNum > precision){
    if(precision === 0){
      message = i18n.t('model.materialService.tip7', {name: numField.displayName});
    }else{
      message = i18n.t('model.materialService.tip8', {name: numField.displayName, count: precision});
    }
  }
  if(digitsStockAppliedNum > precision){
    if(precision === 0){
      message = i18n.t('model.materialService.tip7', {name: stockAppliedField.displayName});
    }else{
      message = i18n.t('model.materialService.tip8', {name: stockAppliedField.displayName, count: precision});
    }
  }
  return message;
}


/**
 * @description 校验批量出入库
 */
export function validateBatchStock(fields, value, stockFieldNames) {
  // 取表单中的需校验的出入库控件
  const batchStockFields = fields.filter(field => stockFieldNames.includes(field.fieldName));

  let message = [];

  batchStockFields.forEach(field => {
    const { fieldName, displayName, formType, subFormFieldList = [] } = field;

    // TODO: 先过滤掉出库控件, 后期出库支持批量时可去掉这行代码（物料出库添加了 备件还没有）
    if (formType == FieldTypeMappingEnum.OutSparepart) return;

    // 当前控件的值
    const fieldValue = value[fieldName] || [];

    // 需出入库数量字段
    const numField = subFormFieldList.find(field => field.fieldName === `${fieldName}_${MaterialWarehouseFieldNameMappingEnum.Num}`) || {};
    // 申请数量字段
    const stockAppliedField = subFormFieldList.find(field => field.fieldName === `${fieldName}_${MaterialWarehouseFieldNameMappingEnum.StockApplied}`) || {};

    // 查找 分批出入库的数据
    const batchStockValues = fieldValue.filter(item => {
      // 需出入库数量
      const num = item[numField.fieldName];
      // 申请数量
      const stockAppliedNum = item[stockAppliedField.fieldName];

      // 判断需出入库的数量为空
      const isEmptyNum = !num || !num.toString().length;
      // 判断申请数量是否为空
      const isEmptyValue = !stockAppliedNum || !stockAppliedNum.toString().length;

      return !isEmptyNum && !isEmptyValue && Number(num) > Number(stockAppliedNum);
    });

    const nameString = batchStockValues.map(item => item[`${fieldName}_name`]).join('、');
    if (batchStockValues.length) message.push(`${displayName}：{${nameString}}${stockAppliedField.displayName} ${i18n.t('common.base.lessThan')} ${numField.displayName}`);
  });

  return message;
}

/**
 * @description 把物料类数据转成出库申请需要的表单数据格式
 * @param {object} data 物料类数据
 * @param {Field[]} fields 出库字段
 * @param  {String} linkFieldName 关联控件的字段名
 * @param  {String} currentFieldName 本身控件名
 */
export function convertMaterialDataToOutWarehouseData(
  data,
  fields,
  linkFieldName,
  currentFieldName
) {
  const systemFields = fields.filter(field => field.isSystem);
  const objectTypeFieldName = `${linkFieldName}_objectType`; // 3：物料
  // 过滤掉非物料类型的数据
  data = data.filter(item => item[objectTypeFieldName] == 3);
  return data?.map(item => {
    item[`${linkFieldName}_sn`] = item[`${linkFieldName}_commodityCode`] // 用来做唯一key进行对比
    let expand = item?.expand;
    if (expand) {
      const form = {};
      const data = { ...expand, ...expand.attribute };
      // num取表单上的值
      data.num = item[`${linkFieldName}_num`];
      data.stockApplied = item[`${linkFieldName}_num`]; // 手动添加申请数量
      systemFields.forEach(field => {
        const key = field.fieldName.replace(`${currentFieldName}_`, '');
        form[`${linkFieldName}_${key}`] = data[key];
      });
      return form;
    }
    return item;
  });
}

/**
 * @description 初始化含有物料materialJson字段数据
 * @param {value} data 物料类数据
 * @param {Field[]} fields 出库字段
 * @param  {String} currentFieldName 本身控件名
 */
export function convertOutWarehouseInitData(data, fields, currentFieldName) {
  try {
    const systemFields = fields.filter(field => field.isSystem);
    const materialJsonFieldName = `expand`;
    const list =
      data?.map(item => {
        if (!item[materialJsonFieldName]) return item;

        const tempObj = {};
        systemFields.map(field => {
          const key = field.fieldName.replace(`${currentFieldName}_`, '');
          const materialData = item[materialJsonFieldName]
          const newMaterialJsonFieldName = {...materialData, ...materialData.attribute}
          tempObj[field.fieldName] = newMaterialJsonFieldName[key];
        });
        delete item[materialJsonFieldName];
        return {
          ...tempObj,
          ...item,
        };
      }) || [];
      return list
  } catch (error){
    console.log(error)
    return data;
  }
}

export default {
  convertMaterialRemoteDataToMaterialOrderValue,
  convertMaterialRemoteDataToMaterialOrderValueV2,
  getFieldValueByLinkFieldName,
  validateStockAppliedNumber,
  validateBatchStock,
  validateSparepartStockAppliedNumber,
  convertMaterialDataToOutWarehouseData,
  convertOutWarehouseInitData,
};
