const isDefault = process.env.VUE_APP_ENV === 'default';
const tragetUrl = isDefault ? 'http://*************' : process.env.VUE_APP_TARGET_URL;
const proxyList = [
  // ['/api/user', tragetUrl, 8003, ''],
  // ['/api/paas', tragetUrl, 10030, ''],
  // ['/api/local', tragetUrl, 8090, '']
  // ['/api/paas', 'http://*************', 10030, ''],
  ['/api/paas', 'http://************', 10030, ''],

];
const proxyConfig = {};

proxyList.forEach(([key, ip, port, rewrite]) => {
  proxyConfig[key] = {
    target: `${ip}${isDefault && port ? `:${port}` : ''}`,
    secure: false,
    ws: false,
    changeOrigin: true,
    pathRewrite: {
      [`^${key}`]: rewrite
    }
  };
});

module.exports = isDefault ? proxyConfig : {};
