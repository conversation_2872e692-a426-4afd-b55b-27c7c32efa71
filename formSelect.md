# 我的需求是需要改造一下 FormSelect 控件

这是目前的下拉控件字段数据结构：{"id":"39670e81-5817-11f0-82cd-0242ac110007","tenantId":"f3975fd73871ff9638709d87bc54968f","tableName":"task","isSystem":0,"fieldName":"field_NZgy8JSH4YyZLIY4","displayName":"下拉菜单","displayNameLanguage":{"zh":"下拉菜单"},"formType":"select","isNull":1,"isSearch":1,"isAdd":1,"placeHolder":"","placeHolderLanguage":null,"setting":{"isMulti":false,"customizedSelectOptionMax":3000,"dataSourceLanguage":{"zh":["1","2","3"]},"isCustomerVisibleShow":0,"dataSource":["1","2","3"]},"orderId":9,"templateId":"1","templateName":"默认工单","defaultValue":null,"defaultValueLanguage":null,"enabled":1,"guideProfessions":[],"isGuideData":false,"isAppShow":0,"modifyTime":1751580521000,"createTime":1751580521000,"isCommon":0,"isHidden":0,"isOnceCommon":0,"isUpgrade":0,"isVisible":true,"isDragCommon":null,"intelligentCheckField":false,"intelligentCheckNow":false,"subFormFieldList":null,"guideData":false,"isCellLast":false}

# 目前存在以下几个问题

1.  配置下拉选项只能配置文本数据，生成下拉项目存储的值就是文本值，没有唯一标识，比如下拉选项配置的是[1,2,3],就存储其中某个选中值
2.  我们下拉选项是支持多语言配置的，就会存在不同语言下同一个选项会有不同的值，导致判断该下拉选项的时候不能判断具体选的哪个值

# 想改成的样子
1. 我现在的想法是用两个字段来存，当前字段名还是按以前的存储，同时再追加一个fieldNma_id来存储设置的id值，比如当前字段field_NZgy8JSH4YyZLIY4选择1，对应要再设置一个field_NZgy8JSH4YyZLIY4_id来存储选中的id值，这样就可以保证选中值的唯一性了
2. 我需要支持指自定义输入 id，可以放在 name 输入框前面，默认生成的 id 就以当前选项的索引+1，不需要任何前缀, 新增一个dataSourceIds字段和dataSource对应
3. 看看是否可以在 src/component/form/components/FormSelect/FormSelect.vue 中监听value变化的时候，自动把选中的id值存到对应的id字段中

请用心帮我优化一下，并且兼容之前的数据，之前单选存的就是一个文本，多选传的是一个数组，现在不管是单选还是多选都存储为[{id:'x',name:''}]格式，如果有相关联的功能也需要修改一下。
