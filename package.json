{"name": "shb-pc-fe", "version": "1.0.0", "description": "兼容IE 11, Edge, Firefox, Chrome, Safari", "private": true, "repository": {"type": "git", "url": "*******************:publink/shb-pc-fe.git"}, "scripts": {"dev": "vite serve", "dev:vite": "vite serve", "dev:vite:f": "vite serve --force", "dev:vite:force": "vite serve --force", "build:vite": "node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build", "serve": "node --max-old-space-size=18196 script/development.js", "build:spa": "node --max_old_space_size=24496 script/build-spa.js --user=cdh --sameBranch=false --immediately", "build:spa:hbc": "node --max_old_space_size=24496 script/build-spa.js --user=huangbc --immediately", "build:spa:bell": "node --max_old_space_size=24496 script/build-spa.js --user=bell --sameBranch=false --immediately --analyzer", "build:spa:zhy": "node --max_old_space_size=24496 script/build-spa.js --user=zhouhy --sameBranch=false --immediately", "build:spa:wish": "node --max_old_space_size=24496 script/build-spa.js --user=wish --sameBranch=false --immediately", "build:spa:cloud": "node --max_old_space_size=24496 script/build-spa.js --user=cloud --cloud=true --immediately", "build:spa:vite:cloud": "node --max_old_space_size=24496 script/build-spa.js --user=cloud --cloud=true --vite=true --immediately", "dev:spa": "node --max_old_space_size=24496 script/development-spa.js", "build:cloud": "node --max-old-space-size=40960 script/build.js --user=cloud --cloud=true", "build:hbc": "git pull && node --max-old-space-size=18196 script/build.js --user=huangbc", "build:wangye": "git pull && pnpm install && node --max-old-space-size=12248 script/build.js --user=wangye", "build:ly": "git pull && npm install && node script/build.js --user=liyuan", "build:ykl": "git pull && yarn install && node --max-old-space-size=12248 script/build.js --user=yuankl", "build:wj": "git pull && cnpm install && node --max-old-space-size=12248 script/build.js --user=wj", "build:lemon": "git pull && cnpm install && node --max-old-space-size=12248 script/build.js --user=lemon", "build:cdh": "node --max-old-space-size=12248 script/build.js --user=cdh --sameBranch=false", "build:ws": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=ws", "build:wish": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=wish", "build:wsq": "node --max-old-space-size=12248 script/build.js --user=wsq", "build:lily": "git pull && yarn install && node --max-old-space-size=12248 script/build.js --user=lily", "build:df": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=df", "build:phc": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=phc", "build:tt": "git pull && yarn install && node --max-old-space-size=12248 script/build.js --user=taotao", "build:bodz": "node --max-old-space-size=14336 script/build.js --user=bodzPublic", "build:jiandan": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=jiandan", "build:wzy": "node --max_old_space_size=24496 script/build-spa.js --user=wzy --cloud=true --sameBranch=false --vite=true --immediately", "build:sy": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=sy", "build:huaying": "git pull && npm install && node --max-old-space-size=12248 script/build.js --user=huaying", "lint": "eslint --fix --ext .js,.vue src", "count": "cloc ./ --exclude-dir=node_modules,dist,package-lock.json", "comp": "node script/component.js", "tag": "./script/tag.sh ${1} ${2}", "br": "./script/br.sh ${1} ${2} ${3}", "commit-lint": "./script/commit-message.sh", "vite": "npm run dev:vite", "chrome:canary:debug": "/Applications/Google\\ Chrome\\ Canary.app/Contents/MacOS/Google\\ Chrome\\ Canary --remote-debugging-port=9222 --user-data-dir=/chrome-user-data", "remove:shb-lib": "node ./script/remove-shb-lib-dependencies.js"}, "author": "dongls", "license": "ISC", "devDependencies": {"@babel/core": "7.20.12", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.21.0", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-private-methods": "7.18.6", "@babel/plugin-proposal-private-property-in-object": "7.21.0", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-jsx": "7.21.4", "@babel/plugin-transform-runtime": "7.21.4", "@babel/preset-env": "7.21.4", "@babel/preset-typescript": "7.21.4", "@babel/traverse": "7.25.1", "@babel/types": "7.20.7", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@types/dompurify": "^3.0.5", "@types/js-md5": "^0.4.3", "@types/lodash": "^4.14.202", "@types/markdown-it": "^14.1.2", "@types/mathjs": "^6.0.12", "@types/node": "^18.19.3", "@types/resize-observer-browser": "^0.1.11", "@vitejs/plugin-vue-jsx": "^1.3.10", "@volar-plugins/vetur": "latest", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/composition-api": "^1.7.2", "autoprefixer": "9.8.8", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "babel-plugin-component": "^1.1.1", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "babel-plugin-transform-vue-jsx": "^4.0.1", "cache-loader": "^4.1.0", "clean-webpack-plugin": "^1.0.1", "colors": "1.3.2", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^3.4.1", "css-unicode-loader": "^1.0.3", "current-git-branch": "^1.1.0", "cz-customizable": "^6.9.2", "eslint": "^8.55.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.19.2", "express": "^4.18.2", "fe-lib-city": "^2.0.6", "file-loader": "^6.2.0", "filing-cabinet": "^3.3.1", "html-webpack-plugin": "^5.5.4", "http-proxy-middleware": "^2.0.6", "husky": "^4.3.8", "inquirer-tree-prompt": "^1.1.2", "is-relative-path": "^2.0.0", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-connect": "^2.1.0", "koa-router": "^7.4.0", "koa-static": "^5.0.0", "koa2-connect-history-api-fallback": "^0.1.3", "lodash-webpack-plugin": "^0.11.6", "madge": "^5.0.2", "mini-css-extract-plugin": "^2.7.6", "minimist": "^1.2.8", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "optimize-css-assets-webpack-plugin": "^6.0.1", "postcss-loader": "^6.2.1", "precinct": "^8.3.1", "progress-bar-webpack-plugin": "^2.1.0", "publink-inject-html-log-util": "^1.0.5", "publink-koa-webpack": "^6.0.0", "quill-image-extend-module": "^1.1.2", "sass": "1.33.0", "sass-loader": "^8.0.2", "sass-resources-loader": "^2.2.5", "shelljs": "^0.8.5", "ts-loader": "^8.4.0", "ts-node": "^10.9.1", "typescript": "4.1.5", "url-loader": "^4.1.1", "vite": "^3.2.7", "vite-plugin-commonjs": "0.5.2", "vite-plugin-require-transform": "^1.0.21", "vite-plugin-resolve-externals": "^0.2.2", "vite-plugin-vue-inspector": "^1.1.4", "vite-plugin-vue2": "^2.0.3", "vite-plugin-vue2-jsx": "^1.0.3", "vue-dom-hints": "^1.3.0", "vue-i18n": "^8.28.2", "vue-loader": "15.10.0", "vue-style-loader": "4.1.3", "vue-table-with-tree-grid": "^0.2.4", "vue-template-compiler": "2.7.13", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^4.10.0", "webpack-dev-middleware": "^5.3.3", "webpack-merge": "^5.10.0", "webpackbar": "^5.0.2"}, "dependencies": {"@antv/g6": "^4.8.23", "@babel/polyfill": "7.2.5", "@babel/runtime": "^7.23.5", "@fullcalendar/core": "^5.11.5", "@fullcalendar/daygrid": "^5.11.5", "@fullcalendar/interaction": "^5.11.5", "@fullcalendar/list": "^5.11.5", "@fullcalendar/resource-timegrid": "^5.11.5", "@fullcalendar/resource-timeline": "^5.11.5", "@fullcalendar/scrollgrid": "^5.11.5", "@fullcalendar/timegrid": "^5.11.5", "@fullcalendar/vue": "^5.11.5", "@highlightjs/cdn-assets": "^11.11.1", "@popperjs/core": "^2.11.8", "@riophae/vue-treeselect": "^0.4.0", "@shb-lib/core": "1.0.55", "@shb-lib/gray": "1.0.55", "@shb-lib/tenant": "1.0.55", "@shb-lib/version": "1.0.55", "@tinymce/tinymce-vue": "3.0.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vue-office/docx": "1.6.0", "@vue-office/excel": "1.7.6", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-eslint": "^5.0.8", "@vueuse/core": "^5.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "ali-oss": "^6.21.0", "axios": "^0.21.4", "babel-helper-vue-jsx-merge-props": "^2.0.3", "benz-amr-recorder": "^1.1.5", "browserslist": "^4.22.2", "chalk": "^4.1.2", "clipboard": "2.0.4", "codemirror": "^5.65.16", "core-js": "^3.34.0", "cti-js-sdk": "3.7.20", "dayjs": "^1.11.10", "dingtalk-design-libs": "0.2.0", "dingtalk-jsapi": "^2.15.4", "dompurify": "^3.0.6", "easemob-websdk": "^3.6.3", "echarts": "4.3.0", "echarts5": "npm:echarts@^5.4.3", "element-ui": "2.15.7", "entity-decode": "^2.0.2", "file-saver": "^2.0.5", "hard-source-webpack-plugin": "^0.13.1", "html2canvas": "^1.4.1", "inquirer": "8.2.0", "js-md5": "^0.7.3", "jszip": "^3.10.1", "koa2-origin-cors": "^1.0.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "markdown-it": "^8.4.1", "markdown-it-highlightjs": "^4.2.0", "marked": "^10.0.0", "mathjs": "^6.6.5", "number-precision": "^1.6.0", "pinyin-match": "^1.2.5", "popper.js": "^1.16.1", "postcss": "^8.4.32", "prettier": "^2.8.8", "pub-bbx-api": "^0.0.24", "pub-bbx-global": "git+ssh://*********************:5edefa8d405cdab50f4003e2/shb-fed/pub-bbx-global.git#feature/hsglaser/master", "pub-bbx-pc-vue2": "git+ssh://*********************:5edefa8d405cdab50f4003e2/shb-fed/pub-bbx-pc-vue2.git#master", "pub-bbx-utils": "git+ssh://*********************:5edefa8d405cdab50f4003e2/shb-fed/pub-bbx-utils.git#featture/hsjg", "publink-layx": "^0.0.6", "publink-paas": "^1.0.9", "qrcodejs2": "npm:publink-qrcodejs2@^0.0.7", "qs": "^6.11.2", "quill": "^1.3.7", "request": "^2.88.2", "search-text-highlight": "^1.0.51", "shb-ai-chat-md": "^1.0.7", "sortablejs": "^1.15.1", "textarea-caret": "^3.1.0", "throttle-debounce": "^3.0.1", "tiny-emitter": "^2.1.0", "tinymce": "5.1.0", "tippy.js": "^6.3.7", "url": "^0.11.3", "uuid": "^11.0.3", "v-calendar": "2.4.1", "vant": "^2.13.2", "vconsole": "^3.15.1", "viewerjs": "^1.11.6", "vue": "2.7.13", "vue-class-component": "^7.2.6", "vue-clipboard2": "^0.3.3", "vue-codemirror": "4.0.6", "vue-cropper": "^0.5.11", "vue-demi": "0.14.6", "vue-dom-hints": "^1.3.0", "vue-eslint-parser": "^9.3.2", "vue-frag": "^1.4.3", "vue-markdown": "^2.2.4", "vue-monoplasty-slide-verify": "^1.3.1", "vue-print-nb": "^1.7.5", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-tour": "^1.6.0", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "web-downloadfile": "^0.0.1"}, "browserslist": ["> 1% in CN", "ie >= 11", "chrome >= 49"], "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "vite": {"optimizeDeps": {"include": ["@fullcalendar/daygrid", "@fullcalendar/interaction", "@fullcalendar/list", "@fullcalendar/resource-timegrid", "@fullcalendar/resource-timeline", "@fullcalendar/timegrid", "@fullcalendar/vue", "@riophae/vue-treeselect", "@vue/babel-helper-vue-jsx-merge-props", "@vueuse/core", "axios", "clipboard", "cti-js-sdk", "dayjs", "dayjs/plugin/isBetween", "dayjs/plugin/isSameOrBefore", "dingtalk-design-libs", "dingtalk-jsapi/entry/union", "easemob-websdk", "element-ui", "entity-decode", "fe-lib-city", "js-md5", "localforage", "lodash", "mathjs", "moment", "popper.js", "publink-layx", "qs", "quill", "textarea-caret", "tippy.js", "url", "v-calendar/lib/components/date-picker.umd", "vant/lib/count-down", "viewerjs", "vue-class-component", "vue-frag", "vue-property-decorator", "vue-quill-editor", "vue-tour", "vuedraggable", "vuex", "web-downloadfile"], "exclude": ["<PERSON><PERSON>", "@shb-lib/tenant", "@shb-lib/version"]}}}