// 该mixin是用于表单业务中新建中 获取来自在线客服的标签的数据

import { defineComponent, onMounted } from 'vue'
/* hooks */
import { useSessionLocal } from '@src/business/intelligentTags/hooks/useLinkAge'
const { setSessionLocal, getSessionLocal, sessionLocalKey, clearSessionLocal } = useSessionLocal()

export default defineComponent({
    name: 'intelligentTagsCreateAndMixin',
    data() {
        return {
            currentInstance: null,
            // 当前标签类型
            IntelligentCurrentType: '',
            // 当前标签类型对应的标签数据
            IntelligentCurrentLabelFromIm: [],
            sessionLocalKey,
        }
    },
    methods: {
        setSessionLocal, getSessionLocal, clearSessionLocal,
        IntelligentInitFormLabelHandler(instance: any) {
            this.currentInstance = instance
            // @ts-ignore
            this.IntelligentCurrentType = this.$route.query.formId || ''
        },
        IntelligentGetSessionHandler() {
            try {
                this.IntelligentCurrentLabelFromIm = this.getSessionLocal(this.sessionLocalKey + this.IntelligentCurrentType) || []

                this.clearSessionLocal(this.sessionLocalKey + this.IntelligentCurrentType)
            } catch (error) {
                console.error(error)
            }
        },
        executeValueChange() {
            // @ts-ignore
            this.currentInstance && this.currentInstance?.changeLabelFromSession();
        },
        handlerLabelFromFormBuilderWindow(event: MessageEvent) {
            const { action, data } = event.data
            if (action == 'changeAllFormBuilderLabelValue') {
                // 如果不存在
                if (!sessionStorage.hasOwnProperty(this.sessionLocalKey + this.IntelligentCurrentType)) {
                    return
                }
                this.IntelligentGetSessionHandler();
                this.executeValueChange();
            }
        }
    },
    mounted() {
        window.addEventListener('message', this.handlerLabelFromFormBuilderWindow)
    },
    destroyed() {
        window.removeEventListener('message', this.handlerLabelFromFormBuilderWindow)
    }
})