import {defineComponent, onBeforeMount, ref} from 'vue';
/* enum */
import { ModulesEnum, StoreModuleEnum } from '@src/business/intelligentTags/model/enum';
/* const */
import { exportFields } from '@src/business/intelligentTags/model/const';
import { SET_INIT_TAGS_COMPONENT_PARAMS } from "@src/store/model/const/const";
/* store */
import store from '@src/store';
/* hooks */
import { useTags } from '@src/business/intelligentTags/hooks/useTags';
import { useRouter } from "@src/view/designer/workflow/viewV2/hooks/useVC";
// import { useIntelligentTagsGuide } from "@src/business/intelligentTags/hooks/useGuide";
/* utils */
import { generateUseTagsParams } from '@src/business/intelligentTags/utils/generate';
import {isPlainObject} from "lodash";

export default defineComponent({
  setup() {
    const useTagsHookValue = useTags();
    const { query } = useRouter();
    // const { showGuide, getGuide } = useIntelligentTagsGuide();
    const intelligentTagsExportFields = ref(exportFields);

    /**
     * @des 更改对应组件初始化参数的方法
     * @returns {any}
     * @param module
     * @param appId
     */
    const initIntelligentTagsParams = (module: ModulesEnum, appId: string)=> {
      const params = generateUseTagsParams(module, appId);
      store.commit(`${StoreModuleEnum.Component}/${SET_INIT_TAGS_COMPONENT_PARAMS}`, params);
    };

    const getLinkInitIntelligentTagsList = (initIntelligentTagsVal: any, key = 'labelList')=> {
      if(isPlainObject(initIntelligentTagsVal)) {
        if(Array.isArray(initIntelligentTagsVal[key])) return initIntelligentTagsVal[key];
        return [];
      }
      if(!Array.isArray(initIntelligentTagsVal)) return [];
      return initIntelligentTagsVal.reduce((acc, item)=> {
        if(item[key] && Array.isArray(item[key])) {
          acc.push(...item[key]);
        }
        return acc;
      }, []);
    };

    const getLinkInitIntelligentTagsListShowTextArray = (array: any, key: string)=> {
      return array.map((item: any)=> item[key]);
    };


    onBeforeMount(async ()=> {
      initIntelligentTagsParams(ModulesEnum.PAAS, query.formId as string);
      // @ts-ignore
      useTagsHookValue?.getSessionFn()
      // const isComplete = await getGuide();
      // !isComplete && showGuide();
    });

    return  {
      ...useTagsHookValue,
      intelligentTagsExportFields,
      initIntelligentTagsParams,
      getLinkInitIntelligentTagsList,
      getLinkInitIntelligentTagsListShowTextArray
    };
  }
});
