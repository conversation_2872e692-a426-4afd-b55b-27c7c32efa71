import { defineComponent, onBeforeMount } from 'vue';
/* component */
import IntelligentTagsTaggingView from '@src/business/intelligentTags/components/IntelligentTagsTaggingView';
/* hooks */
import { useTagsSingle } from '@src/business/intelligentTags/hooks/useTags';
import { useRouter } from "@src/view/designer/workflow/viewV2/hooks/useVC";
/* enum */
import { ModulesEnum, StoreModuleEnum } from "@src/business/intelligentTags/model/enum";
/* utils */
import { generateUseTagsParams} from "@src/business/intelligentTags/utils/generate";
/* const */
import { SET_INIT_TAGS_COMPONENT_PARAMS } from "@src/store/model/const/const";
/* store */
import store from "@src/store";

export default defineComponent({
  components: {
    IntelligentTagsTaggingView: IntelligentTagsTaggingView
  },
  setup() {
    const { query } = useRouter();


    const initIntelligentTagsParams = (module: ModulesEnum, appId: string)=> {
      const params = generateUseTagsParams(module, appId);
      store.commit(`${StoreModuleEnum.Component}/${SET_INIT_TAGS_COMPONENT_PARAMS}`, params);
    };

    onBeforeMount(()=> {
      //@ts-ignore
      initIntelligentTagsParams(ModulesEnum.PAAS, query.formId ||  store.state.common.detailPageParams.formTemplateId);
    });


    return useTagsSingle(ModulesEnum.PAAS);
  }
});
