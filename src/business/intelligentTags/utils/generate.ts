import moment from "moment";
import { deleteObjectKeys, getIntelligentTagsModuleForKey } from '@src/business/intelligentTags/utils/utils';
import { FetchTagsLogParamsInterface, TagsItem } from "@src/business/intelligentTags/model/interface";
import { constModuleMap } from "@src/business/intelligentTags/model/const/module";
import { BizTaggingFrom, ModulesEnum } from "@src/business/intelligentTags/model/enum";

/**
 * @des 生成对应的打标需要的参数
 * @param {any} {appId
 * @param {any} bizObjsArray
 * @param {any} checkedTags
 * @param {any} setMethod=1
 * @returns {any}
 */
export const generateTaggingParams = ({ appId, bizObjsArray, checkedTags, setMethod = 1, bizType }: any) => {

  const bizIdKey = getIntelligentTagsModuleForKey(bizType, 'bizIdKey');

  const bizNoKey = getIntelligentTagsModuleForKey(bizType, 'bizNoKey');


  const bizObjs = bizObjsArray.map((item: any)=> {
    return {
      bizId: item[bizIdKey],
      bizNo: item[bizNoKey]
    };
  });

  // 管理员标签 企业标签
  const labels = checkedTags.filter((i: TagsItem) => i?.personalLabel == '0').map((item: TagsItem)=> {
    return {
        labelId: item.id,
        name: item.name
    }
  })

  // 个人标签
  const personalLabels= checkedTags.filter((i: TagsItem) => i?.personalLabel == '1').map((item: TagsItem)=> {
      return {
          labelId: item.id,
          name: item.name
      }
  })

  return  {
    appId,
    moduleId: undefined,
    bizObjs,
    labels,
    personalLabels,
    setMethod,
    bizType
  };
};



/**
 * @des 生成对应的日志列表请求的参数
 * @param {any} formValue:FetchTagsLogParamsInterface
 * @returns {any}
 */
export const generateLogListParams = (formValue: FetchTagsLogParamsInterface)=> {
  const cloneFormValue = { ...formValue };
  cloneFormValue.startTime = formValue.dateTime && formValue.dateTime[0] ? formValue.dateTime[0] : '';
  cloneFormValue.endTime = formValue.dateTime && formValue.dateTime[1] ? moment(formValue.dateTime[1]).add(1, 'days').valueOf(): '';

  deleteObjectKeys(cloneFormValue, ['dateTime', 'operator']);

  return cloneFormValue;
};

/**
 * @des 生成对应useTags hooks的初始化对应的组件需要的参数方法
 * @param type
 * @param appId
 * @param from
 */
export const generateUseTagsParams = (type: ModulesEnum, appId: string, from = BizTaggingFrom.List)=> {
  if(constModuleMap[type]) return { ...constModuleMap[type], appId, from };
  return {appId: '', bizType: ''};
};
