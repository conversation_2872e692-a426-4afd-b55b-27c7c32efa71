/* enum */
import { ModulesEnum } from "@src/business/intelligentTags/model/enum";
import { PageRoutesTypeEnum } from "pub-bbx-global/pageType/dist/enum/PageRoutesEnum";
/* model */
import { TagsGroupListItem, TagsItem } from "@src/business/intelligentTags/model/interface";
/* const */
import { constModuleForValueMap } from "@src/business/intelligentTags/model/const/module";
/* utils  */
import {isFunction, isObject, get, isPlainObject} from "lodash";
import { openAccurateTab } from "@src/platform";
/**
 * @des 根据 keys 删除对应的对象上的 key
 * @param {any} obj:T
 * @param {any} keys:string[]
 * @returns {any}
 */
export const deleteObjectKeys = <T>(obj: T, keys: string [])=> {
  if(!isObject(obj) || !keys.length) return;
  if(!Object.keys(obj).length) return;
  keys.forEach(item=>  Reflect.deleteProperty(obj, item));
};

/**
 * @des 相关根据tagsGroupList过滤对应没有开启和对应不是系统的分组
 * @param {any} tagsGroupList:TagsGroupListItem[]
 * @returns {any}
 */
export const filterTagGroupAndTagForUnEnabled = (tagsGroupList: TagsGroupListItem[]): TagsGroupListItem[]=> {
  if(!Array.isArray(tagsGroupList) || !tagsGroupList.length) return  [];
  return tagsGroupList.slice(0).reduce((acc: TagsGroupListItem[], item: TagsGroupListItem)=> {
    // 过滤未开启的分组
    if(Array.isArray(item.labelList) && item.labelList.length > 0) {
      item.labelList = item.labelList.filter(item=> item.enabled);
    }

    // 过滤系统分组
    if(item.type !== 1) {
      acc.push(item);
    }
    return acc;
  }, []);
};


/**
 * @des 根据对应模块去找对应的模块需要的key的value
 * @param {any} module:ModulesEnum
 * @param {any} key:string
 * @returns {any}
 */
export const getIntelligentTagsModuleForKey = (module: ModulesEnum, key: string): string  => {
  if(module) {
    return get(Reflect.get(constModuleForValueMap, module), key);
  }
  return '';
};

/**
 * @des 执行方法方法
 * @param {any} fun:Function|undefined
 * @param {any} cb?:Function
 * @returns {any}
 */
export const executeFunction =(fun: Function | undefined, ctx: any = {}, cb?: Function) =>  {
  if(!isFunction(fun)) return;
  fun.call(ctx);
  if(isFunction(cb)) cb();
};

/**
 * 根据标签分组聚合成对应的标签列表
 * @param tagsGroupList
 */
export const getTagsGroupToTagsList = (tagsGroupList: TagsGroupListItem[]): TagsItem []=> {
  return tagsGroupList.reduce((acc: TagsItem [], item: TagsGroupListItem)=> {
    if(Array.isArray((item.labelList))) {
      acc.push(...item.labelList);
    }
    return acc;
  }, []);
};



/**
 * @des 相关跳转到标签管理页面
 * @returns {any}
 */
export const linkJumpToManageTagsPage = ()=> {
  // @ts-ignore
  let fromId = window.frameElement.getAttribute('id') as unknown as string;

  openAccurateTab({
    fromId,
    type: PageRoutesTypeEnum.intelligentTags
  });
};
