import { TagsListMainItem, TagsItem, TagsModulesItem } from "@src/business/intelligentTags/model/interface";

/**
 * @des 转化对应模块列表对应为表单需要的模块列表
 * @returns {any}
 * @param modulesList
 */
export const conversionModulesListForSubmitLabelAppList = (modulesList: TagsModulesItem[])=> {
  return modulesList.map((item: TagsModulesItem) => {
    item.appName = item.name;
    return item;
  });
};


/**
 * @des 获取对应 log tab 中初始化参数转为对应搜索表单的参数
 * @returns {any}
 * @param currentTabParams
 */
export const conversionLogTabParamsFormValue = (currentTabParams: Record<string, any>)=> {
  return { groupId: currentTabParams?.group?.id, keyword: currentTabParams?.row?.name };
};


/**
 * @des 对应的详情列表的标签主要数据转完整的标签数据 （主要数据只有类型以及 id 两个）
 * @returns {any}
 * @param tagsList
 * @param tagsMainDataList
 */
export const conversionDetailTagsListForFullDataList = (tagsList: TagsItem [], tagsMainDataList: TagsListMainItem []): TagsItem [] => {

  return tagsList.map((tagsItem: TagsItem)=> {

    const tagMainDataListItem = tagsMainDataList.find((item: TagsListMainItem)=> item.labelId === tagsItem.id);
    if(tagMainDataListItem) {
      return Object.assign(tagsItem, tagMainDataListItem);
    }

    return tagsItem;
  });
};


/**
 * @des 将对应的标签的完整数据转为详情的的部分主要数据
 * @returns {any}
 * @param tagsList
 */
export const conversionTagsListForTagsListMainItem = (tagsList: TagsItem[])=>  {
  return tagsList.map((item: TagsItem)=> {
    return  {
      labelId: item.id,
      labelType: 1
    };
  });
};
