import http from '@src/util/http';
import { FetchGroupTagsListParamsInterface, FetchTaggingParamsInterface, FetchTagsLogParamsInterface } from '@src/business/intelligentTags/model/interface';

const prefix = '/api/voice';

/**
 * 获取对应分组标签列表
 * @returns Promise
 */
export function getIntelligentTagsGroupList(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/group/findList`, params);
}

/**
 * 相关标签分组删除
 * @param params
 */
export function delIntelligentTagsGroupListItem(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/group/delete`, params);
}


/**
 * 相关标签分组保存
 * @param params
 */
export function saveIntelligentTagsGroupListItem(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/group/save`, params);
}

/**
 * 更新标签分组
 * @param params
 */
export function updateIntelligentTagsGroupListItem(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/group/update`, params);
}


/**
 * 标签列表
 * @param params
 */
export function getIntelligentTagsList(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/findPageList`, params);
}

/**
 * 标签开启关闭
 * @param params
 */
export function enableIntelligentTagsListItem(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/enable`, params);
}

/**
 * 标签删除
 * @param params
 */
export function deleteIntelligentTagsListItem(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/delete`, params);
}


/**
 * 更新或者创建标签分组
 * @param params
 */
export function createOrEditIntelligentTagsListItem(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/saveAndUpdate`, params);
}


/**
 * 查找当前标签详情
 */
export function getIntelligentTagsListItem(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/findById`, params);
}

/**
 * 获取智能标签分组包含对应的标签列表
 * @param params
 */
export function getIntelligentGroupTagsList(params: Partial<FetchGroupTagsListParamsInterface> | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/getGroupLabelCombinationPage`, params);
}


/**
 * 获取相关标签日志列表
 * @param params
 */
export function getIntelligentTagsLogList(params: Partial<FetchTagsLogParamsInterface> | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/log/findPageList`, params);
}


/**
 * 相关打标动作请求接口
 * @param params
 */
export function fetchBizTaggingTags(params: Partial<FetchTaggingParamsInterface> | undefined): Promise<any> {
  if(params && params.fromList) {
    Reflect.deleteProperty(params, 'fromList');
    return http.post(`${prefix}/outside/label/biz/addAndDelLabelBusiness`, params);
  }

  params && Reflect.deleteProperty(params, 'fromList');

  return http.post(`${prefix}/outside/label/biz/setLabelBusiness`, params);
}

/**
 * 根据业务id获取对应的标签
 * @param params
 */
export function getIntelligentTagForBizIds(params: {} | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/findByBizObjIds`, params);
}


// 使用情况
export function getIntelligentTagsUseList(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/statistic/info`, params);
}

/**
 * 相关打标动作模块列表
 * @param params
 */
export function getTaggingModulesList(params: Partial<FetchTaggingParamsInterface> | undefined): Promise<any> {
  return http.get(`${prefix}/outside/label/getAppList`, params);
}


/**
 * @description 打标-获取智能标签表格列表
 * @param params
 */
export function getIntelligentTableList(params: any | undefined): Promise<any> {
  return http.post(`${prefix}/outside/label/marking/findPageList`, params);
}

/**
 * @description 打标-删除打标规则
 * @param params
 */
export function deleteIntelligentRule(params: {id: string}): Promise<any> {
  return http.get(`${prefix}/outside/label/marking/delete`, params);
}

/**
 * @description 打标-启用禁用
 * @param params
 * id: string
 * enabled: 0| 1 1表示启用 0表示禁用
 */
export function markSwitch(params: {id: string, enabled: 0 | 1}): Promise<any> {
  return http.get(`${prefix}/outside/label/marking/enable`, params);
}

/**
 * @description 打标-根据id查询
 * @param params
 * id: string
 */
export function getIdSelctMark(params: {id: string}): Promise<any> {
  return http.get(`${prefix}/outside/label/marking/findById`, params);
}

/**
 * @description 打标-保存打标规则
 * @param params
 * name: string
 * description: string
 */
export function saveMarkRule(params: {name: string;description: string}): Promise<any> {
  return http.post(`${prefix}/outside/label/marking/save`, params);
}

/**
 * @description 打标-编辑打标规则
 * @param params
 */
export function editMarkRule(params: { id: string; name: string; description: string; }): Promise<any> {
  return http.post(`${prefix}/outside/label/marking/update`, params);
}
/**
 * 根据对应的标签 id 查询对应的列表
 * @param params
 */
export function getTagListFromIds(params: (number | string) []): Promise<any> {
  return http.post(`${prefix}/outside/label/findByIds`, params);
}

/**
 * @description 获取次数
 */
export function getSatisfaction(params: any) {
  return http.post(`/api/dataware/outside/intelligentLabel/satisfaction`, params)
}

/**
 * @description 新增个人标签
 */
export function addPersonalLabel(params: any) {
  return http.post(`${prefix}/outside/label/personalLabel/save`, params)
}

/**
 * @description 编辑个人标签
 */
export function editPersonalLabel(params: any) {
  return http.post(`${prefix}/outside/label/personalLabel/update`, params)
}
