export enum IntelligentTagsComponentNameEnum {
    // 相关智能标签页面中顶部 header 组件
    IntelligentTagsComponentHomeHeader = 'intelligent-tags-home-header',

    // 智能标签标签列表组件
    IntelligentTagsList = 'intelligent-tags-list',
    // 智能标签分组组件
    IntelligentTagsGroupList = 'intelligent-tags-group-list',
    // 分组创建弹出
    IntelligentTagsGroupCreateModal = 'intelligent-tags-group-create-modal',
    // 智能标签 table 的列表组件
    IntelligentTagsTableList = 'intelligent-tags-table-list',

    IntelligentTagsTableListItemCreateFormModal = 'intelligent-tags-table-list-item-create-form-modal',

    // 智能标签的日志页面
    IntelligentTagsLogs = 'intelligent-tags-logs',

    // 智能标签的打标规则组件
    IntelligentTagsTaggingRules = 'intelligent-tags-tagging-rules',

    // 智能标签的相关使用情况组件
    IntelligentTagsUsage = 'intelligent-tags-usage',

    
    // 智能标签在详情页面使用的组件
    IntelligentTagsTaggingView = 'intelligent-tags-tagging-view'
    
}


export enum IntelligentTagsMainTabEnum {
    IntelligentTagsList = IntelligentTagsComponentNameEnum.IntelligentTagsList,
    IntelligentTagsLogs = IntelligentTagsComponentNameEnum.IntelligentTagsLogs,
    IntelligentTagsTaggingRules = IntelligentTagsComponentNameEnum.IntelligentTagsTaggingRules,
    IntelligentTagsUsage = IntelligentTagsComponentNameEnum.IntelligentTagsUsage
}

