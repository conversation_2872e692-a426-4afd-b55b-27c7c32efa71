
// 定死的模块的对应的 appId 然后 bizType 就是 key
export enum ModulesEnum {
    TASK = 'TASK',
    EVENT = 'EVENT',
    PRODUCT_TYPE = 'PRODUCT_TYPE',
    PRODUCT_LIST = 'PRODUCT_LIST',
    CUSTOMER = 'CUSTOMER',
    PROJECT_LIST = 'PROJECT_LIST',
    PROJECT_TASK_LIST = 'PROJECT_TASK_LIST',
    MALL_ORDER = 'MALL_ORDER',
    WIKI = 'WIKI',
    COURSE = 'COURSE',
    EXAMINATION= 'EXAMINATION',
    PAAS = 'PAAS',
    LINKMAN = 'LINKMAN'
}
