import { TagsItem } from "@src/business/intelligentTags/model/interface"
import { BizTaggingMethodEnum } from '@src/business/intelligentTags/model/enum'

// 获取相关分组以及对应的标签列表接口请求参数
export interface FetchGroupTagsListParamsInterface {
    pageNum: number,
    pageSize: number,
    keyword: string,
    appId: number | string,
    [paramsName: string]: any
}

// 日志列表接口请求参数接口
export interface FetchTagsLogParamsInterface {
    pageNum: number,
    pageSize: number,
    startTime: number | string,
    endTime: number | string,
    groupId: number | string,
    appId: string [],
    operateType: number | string,
    keyword: number | string,
    dateTime?: number []
}

export interface InitTagsGroupListPayloadIds {
    params:  Partial<Omit<FetchGroupTagsListParamsInterface, 'keyword'>>,
    commit: boolean
}


// 打标动作接口请求设置的业务对象列表
export interface FetchTaggingParamsBizObjsInterface {
    bizId: string,
    bizNo: string
}

/**
 * @des 打标动作接口请求参数接口
 * @params addPersonalLabels 用于列表参数
 * @params personalLabels 用于详情参数
 */
export interface FetchTaggingParamsInterface {
    appId: string | number,
    bizType: string | number,
    bizObjs: FetchTaggingParamsBizObjsInterface,
    labels: Partial<TagsItem>[],
    setMethod: BizTaggingMethodEnum,
    fromList?: boolean,
    addLabels?: Partial<TagsItem>[],
    delLabels?: Partial<TagsItem>[],
    addPersonalLabels?: Partial<TagsItem>[],
    personalLabels?: Partial<TagsItem>[],
    delPersonalLabels?: Partial<TagsItem>[]
}
