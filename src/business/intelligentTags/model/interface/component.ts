import { TagsGroupItem, TagsItem } from "@src/business/intelligentTags/model/interface";
import { TagsGroupListItem } from "./tags";

export interface TaggingComponentProps {
    key: string | number,
    tagsGroupList: TagsGroupListItem[],
    localGroupLabelList: TagsItem | {},
    value: TagsItem[],
    disabled: boolean,
    openPopoverHook?: Function,
    ref: string,
    operatorType: number,
    remoteFetchFun?: Function,
    remoteSearch: boolean
}
export interface TaggingComponentEvent {
    save: Function,
    hidePopover: Function,
    manageTagsClick: Function,
    change: Function,
    remoteFetchFun: Function,
    loadMore: Function,
    search: Function,
    clear: Function,
    radioChange: Function,
    personalSave: Function,
    personalLabelAdd: Function,
    personalLabelEdit: Function,
    personalLabelDelete: Function,
}


export interface TagsFilterPanelComponentProps {
    show: boolean,
    value: Object,
    ref: any,
    tagsGroupList: TagsGroupListItem[],
    remoteFetchFun: Function,
    remoteFetchIdsFun: Function,
    showLabelSettingSwitchValue: Boolean
}

export interface TagsFilterPanelComponentEvent {
    filterItemClick: Function,
    linkTagsSwitchChange: Function,
    close: Function,
    remoteFetchFun: Function,
    emptyLinkClick: Function,
    filterNoExistItemOfClick: Function,
    labelSettingSwitchChange: Function
}
