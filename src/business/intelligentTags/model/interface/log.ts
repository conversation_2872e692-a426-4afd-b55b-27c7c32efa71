import { ModulesEnum } from "../enum";

export interface LogItem {
    id: number
    bizType?: ModulesEnum,
    createTime: string
    groupName: string
    labelName: string
    operationType: string
    labelType: string,
    content: string
    markingRobot: string
    app: string
    bizNo: string
    createUserName: string
}

export interface TableLogColumnItem {
    name: string,
    prop: keyof LogItem
}
