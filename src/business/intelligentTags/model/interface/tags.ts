import { BizTaggingFrom, ModulesEnum } from "@src/business/intelligentTags/model/enum";

export interface TableColumnItem {
    name: string,
    prop: keyof TagsItem
}

// 标签分组
export interface TagsGroupItem {
    id: number | string,
    name: string,
    type?: number
}


// 标签
export interface TagsItem {
    id: number | string,
    name: string,
    logoColor: string,
    description?: string,
    updateTime?: string,
    enabled?: boolean | number,
    appList?: TagsModulesItem[],
    labelId?: number | string,
    labelType?: number,
    personalLabel?: string | number,
}
export interface TagLabel {
    [key: string]: any,
    id?: string | number,
    name: string,
    logoColor: string,
    status: string
}

// 标签分组聚合标签
export interface TagsGroupListItem {
    groupName: string,
    labelList: TagsItem[],
    type: number,
    personalLabel?: string | number,
}


// 打标的对应模块
export interface TagsModulesItem {
    appId: string
    bizType: string
    name: string
    logo: string
    appName?: string
}



export interface InitTagsComponentParams {
    appId: ModulesEnum | undefined,
    bizType: string | undefined,
    from?: BizTaggingFrom
}


// 在详情页面里对应的的标签项 （不同模块可能在不同的对象中比如工单在attribute中）
export interface TagsListMainItem {
    labelId: number | string,
    labelType: number
}
