import { ModulesEnum } from "@src/business/intelligentTags/model/enum/modulesEnum";
import { ModuleBizDataKeyValue } from '@src/business/intelligentTags/model/biz/moduleBizDataKeyValue';

const TAG_KEY = 'label';

const generateValue = (bizType:ModulesEnum, appId: string)=> {
  return  {bizType, appId};
};

export const constModuleMap: Record<string, any> = {
  PAAS: generateValue(ModulesEnum.PAAS, 'PAAS'),
  LINKMAN: generateValue(ModulesEnum.LINKMAN, 'LINKMAN'),
};


// public key: string 模块的 key
// public singleBizDataKeyArray: string[] 单个业务数据 key
// public multiBizDataKeyArray: string[] 多个业务数据 key
// public searchFunKey: string 搜索的方法
// public bizIdKey?: string 业务数据 id key
// public bizNoKey?: string 业务数据 value key

export const constModuleForValueMap = {
  PAAS: new ModuleBizDataKeyValue('initValue', [TAG_KEY], ['multipleSelection'], ['handleBaseSearch'], 'bizId', 'serialNumber', ['fetchProcessLog']),
};
