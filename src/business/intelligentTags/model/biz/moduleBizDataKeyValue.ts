export class ModuleBizDataKeyValue {
  public key: string; // 模块的 key
  public singleBizDataKeyArray: string[]; //单个业务数据 key
  public multiBizDataKeyArray: string[]; //多个业务数据 key
  public searchFunKey: (string | string[])[]; //搜索的方法 key
  public bizIdKey?: string; //业务数据 id key
  public bizNoKey?: string; //业务数据 value key
  public refreshKeyArr?:(string | string[]) []; // 每个模块页面刷新数据的方法的取值 key
  public deleteKey: string; // 业务实例中对应是否删除数据的 key


  constructor(
    key: string = '',
    singleBizDataKeyArray: string[] = [],
    multiBizDataKeyArray: string[] = [],
    searchFunKey: (string | string[])[] = [] ,
    bizIdKey?: string,
    bizNoKey?: string,
    refreshKeyArr?: (string | string[]) [],
    deleteKey: string = 'isDelete'
  ) {
    this.key = key;
    this.singleBizDataKeyArray = singleBizDataKeyArray;
    this.multiBizDataKeyArray = multiBizDataKeyArray;
    this.searchFunKey = searchFunKey;
    this.bizIdKey = bizIdKey;
    this.bizNoKey = bizNoKey;
    this.refreshKeyArr = refreshKeyArr;
    this.deleteKey = deleteKey;
  }
}
