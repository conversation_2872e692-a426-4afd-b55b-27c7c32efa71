import {computed, defineComponent, PropType, ref, toRefs, watchEffect, onBeforeUnmount} from "vue";
/* component */
import BizIntelligentTagsView from '@src/component/business/BizIntelligentTags/BizIntelligentTagsView';
import BizIntelligentTaggingButton from '@src/component/business/BizIntelligentTags/BizIntelligentTaggingButton';
/* model */
import { TagsItem, TagsListMainItem, TagLabel } from "@src/business/intelligentTags/model/interface";
/* enum */
import { BizTaggingFrom, IntelligentTagsComponentNameEnum, ModulesEnum } from '@src/business/intelligentTags/model/enum';
/* utils */
import { conversionDetailTagsListForFullDataList } from "@src/business/intelligentTags/utils/conversion";
import { generateUseTagsParams } from "@src/business/intelligentTags/utils/generate";
import { getIntelligentTagsModuleForKey } from "@src/business/intelligentTags/utils/utils";
import { cloneDeep, isFunction } from "lodash";
import { t } from "@src/locales";
/* hooks */
import { useTags } from "@src/business/intelligentTags/hooks/useTags";
import { useRouter } from "@src/view/designer/workflow/viewV2/hooks/useVC";
/* api */
import { getTagListFromIds } from "@src/business/intelligentTags/api/main";
/* style */
import '@src/business/intelligentTags/components/IntelligentTagsTaggingView/style.scss';

export default defineComponent({
  name: IntelligentTagsComponentNameEnum.IntelligentTagsTaggingView,
  props: {
    // 是否显示
    show: {
      type: Boolean,
      default: ()=> true
    },
    // 来源 比如在工单中使用来源就是工单 TASK
    from: {
      type: String as PropType<ModulesEnum>,
      default: ()=> 'PAAS'
    },
    // 标签的主要数据（只有 id 和 类型））
    tagsMainDataList: {
      type: Array as PropType<TagsListMainItem[]>,
      default: ()=> []
    },
    // 业务数据（比如在工单详情中业务数据也就是工单详情数据）
    bizData: {
      type: Object as PropType<Record<string, any>>,
      default: ()=> ({})
    },
    // 更新主要的标签数据的方法
    updateTagsMainDataList: {
      type: Function
    },
    // 显示详情的icon是文本还是按钮
    showType: {
      type: String as PropType<'icon' | 'text'>,
      default: ()=> 'text'
    },
    // 是否显示对应的打标按钮
    showTaggingButton: {
      type: Boolean,
      default: ()=> true
    }
  },
  setup(props, { expose }) {
    const { show, from, tagsMainDataList, bizData, updateTagsMainDataList, showType, showTaggingButton } = toRefs(props);

    const { query } = useRouter();

    // 视图配置
    const viewConfig = computed(() => {
      if (showType.value === 'icon') {
        return {
          normalShowType: 'icon',
        };
      }
      return {
        normalShowType: 'text',
      };
    });
    // 打标组件的配置
    const taggingConfig = computed(() => {
      if (showType.value === 'icon') {
        return {
          showText: false,
        };
      }
      return {
        showText: true,
      };
    });
    // 标签列表
    const tagsList = ref<TagsItem []>([]);
    // 打标对应的组件的 props 以及事件
    const { taggingBindAttr, taggingBindOn, taggingCheckedTags } = useTags(generateUseTagsParams(from.value, query.formId as string, BizTaggingFrom.Detail));
    // 自定义相关打标组件对应的 props
    const bizIntelligentTaggingButtonProps = computed(()=> Object.assign(taggingBindAttr.value, { placement: 'bottom', showGroupIcon: false  }));


    // 相关刷新列表数据的方法 key
    const bizIdKey = computed(()=> getIntelligentTagsModuleForKey(from.value, 'bizIdKey'));

    const bizNoKey = computed(()=> getIntelligentTagsModuleForKey(from.value, 'bizNoKey'));


    /**
     * @des 获取对应标签主要数据过滤方法（ 这里是包含了相关智能标）
     * @param {any} defaultTagsMainDataList:TagsListMainItem[]
     * @returns {any}
     */
    const getTasListForIdsParams = (defaultTagsMainDataList: TagsListMainItem [])=> {
      return defaultTagsMainDataList.map(item=> item.labelId);
      // return defaultTagsMainDataList.reduce((acc: (number | string)[], item)=> {
      //     if(item.labelType === 1) {
      //         acc.push(item.labelId)
      //     }
      //     return acc
      // }, [])
    };

    /**
         * @des 根据对应标签主要数据请求获取对应的每个标签的数据的方法
         * @param {any} defaultTagsMainDataList=tagsMainDataList.value
         * @returns {any}
         */
    const fetchTagsListForIds = async (defaultTagsMainDataList = tagsMainDataList.value)=> {
      if(!defaultTagsMainDataList.length) return tagsList.value = [];
      try{
        const { data = [], success } = await getTagListFromIds(getTasListForIdsParams(defaultTagsMainDataList));
        if(success) {
          // 聚合处理对应的返回值数据主要数据跟接口返回的数据组合
          tagsList.value = conversionDetailTagsListForFullDataList(data, defaultTagsMainDataList);
          // 更新下对应的选中的标签数据
          taggingCheckedTags.value = cloneDeep(tagsList.value);
        }
      } catch(e){
        console.error('[ getTagListFromIds error ]', e);
      }
    };

    const getBizObjArrayData = ()=> {
      return [{
        [bizIdKey.value]: bizData.value[bizIdKey.value],
        [bizNoKey.value]: bizData.value[bizNoKey.value]
      }];
    };

    /**
     * @des 刷新数据
     * @param {any} currentTagsMainDataList:TagsListMainItem[]
     * @returns {any}
     */
    const refreshData = (currentTagsMainDataList: TagsListMainItem [])=> {
      // const defaultIntelligentTags = tagsMainDataList.value.filter(item=> item.labelType === 0)
      fetchTagsListForIds(currentTagsMainDataList);

      // 判断updateTagsMainDataList是否是方法 是的话调用一下刷新源传过来的数据 也就是props中tagsMainDataList的
      if(isFunction(updateTagsMainDataList.value)) {
        updateTagsMainDataList.value(currentTagsMainDataList);
      }
    };

    /**
     * @des 本地修改标签的值
     */
      const handleDetailViewLabel = (val: TagLabel) => {
        // because label's count not much,so use map Array api to change it
        tagsList.value = tagsList.value.map(item=> {
            // 因为是个人标签 所以通过id判断也可以
            let _id = item.labelId || item.id || ''
            if(_id === val.id) {
                item.name = val.name
                item.logoColor = val.logoColor
            }
            return item
        })
      }
  

    const stop = [watchEffect(()=> {
      fetchTagsListForIds();
    })];

    onBeforeUnmount(() => {
      stop.forEach(item=> item());
    })

    expose({
      getBizObjArrayData,
      refreshData
    });

    return ()=> {

      if(!show.value) return null;

      return (
        <div class="biz-intelligent-tags__tagging-view">
          <div class="biz-process-intelligent-tags">
            {/* @ts-ignore */}
            <BizIntelligentTagsView type="detail" tagsList={tagsList.value} config={viewConfig.value}/>
          </div>
          <div class={['biz-process-intelligent-tag-button', tagsList.value.length === 0 ? 'biz-process-intelligent-tag-button__plain' : null]}>
            {/* @ts-ignore */}
            { showTaggingButton.value ? <BizIntelligentTaggingButton ref="taggingCompRef" tagsList={tagsList.value} onDetailViewLabel={handleDetailViewLabel} {...{ props:{ ...bizIntelligentTaggingButtonProps.value, value: tagsList.value, showText: taggingConfig.value.showText, buttonText:'标签' }, on: taggingBindOn.value }}/> : null }
          </div>
        </div>
      );
    };
  }
});
