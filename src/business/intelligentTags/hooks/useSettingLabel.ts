import { ref, computed, reactive, onBeforeMount } from 'vue'
/* http */
import { creatServerCachApi, getServerCachApi } from '@src/api/GuideApi.js'
/* utils */
import { getRootWindow } from '@src/util/dom'
import { isFunction } from "lodash";

/**
 * {
 *  labelTableShow: 0 | 1  全局标签显示逻辑
 * 1表示显示文字 0表示显示图标
 * linksLabel: 0 表示关闭 1表示开启  关联标签信息存储
 * }
 */

interface ILabelInfo {
	labelTableShow: 0 | 1
	linksLabel: 0 | 1
}

export const useSettingLabelInNet = () => {
	// 定义存储标签type
	const _type = ref('AllLabelTableStorageKey')
	// 默认参数
	const _labelInfo = reactive<ILabelInfo>({
		labelTableShow: 1,
		linksLabel: 0,
	})

	/**
	 * @desc 信息存储网络接口
	 */
	const setLabelToNet = async (params: ILabelInfo) => {
		try {
			await creatServerCachApi({
				isComplete: 1,
				step: 1,
				type: _type.value,
				userConfig: JSON.stringify(params) ?? JSON.stringify({}),
			})
		} catch (error) {
			console.error(error)
		}
	}

	/**
	 * @description 获取标签信息
	 */
	const getLabelToNet = async () => {
		try {
			const res = await getServerCachApi(_type.value)
			if (!res.succ) {
				return false
			}
			if (res?.data && Array.isArray(res?.data) && res?.data.length > 0) {
				const { labelTableShow, linksLabel } = JSON.parse(res?.data?.[0].userConfig)
				return {
					labelTableShow,
					linksLabel,
				}
			}
		} catch (error) {
			console.error(error)
		}
	}

	return {
		_labelInfo,
		AllLabelTableStorageKey: _type,
		getLabelToNet,
		setLabelToNet,
	}
}

/**
 * @desc 实际运用
 */
export const useSettingLabel = () => {
	const config = ref({
		tableShowType: 'icon',
	})

	/**
	 * @desc 改变标签显示逻辑
	 * @param v true表示显示文字 false表示显示图标
	 */
	const changeLabelShowTable = (v: any) => {
		if (v) {
			config.value.tableShowType = 'text'
		} else {
			config.value.tableShowType = 'icon'
		}
	}

	/**
	 * @desc 标签更改，通过window调用changeLabelView 走到全局的标签显示逻辑
	 */
	const changeLabelShowInLocal = (v1: any, v2: any) => {
		changeLabelShowTable(v1)
		getRootWindow(window)?.changeLabelView({
			labelTableShow: v1,
			linksLabel: v2,
		})
	}

	/**
	 * @desc 获取最外层window的 标签显示函数
	 */
	const getLabelShowInLocal = () => {
		try {
			return getRootWindow(window)?.getLabelInfo()
		} catch (error) {
			console.warn(error)
			return
		}
	}

	/**
	 * @desc 获取标签表格显示数据
	 */
	const getLabelTableShow = () => {
		try {
			return getLabelShowInLocal()?.labelTableShow ?? 0
		} catch (error) {
			console.warn(error)
			return
		}
	}

	/**
	 * @desc 获取关联标签显示
	 */
	const getLinksLabelShow = () => {
		try {
			return getLabelShowInLocal()?.linksLabel ?? 0
		} catch (error) {
			console.warn(error)
			return
		}
	}

	/**
	 * @desc 更改标签表格显示类型
	 */
	const changeTableShowType = (type: 'text' | 'icon') => {
		config.value.tableShowType = type
	}

	/**
	 * @desc 初始化
	 */
	const init = () => {
		config.value.tableShowType = getLabelTableShow() == 1 ? 'text' : 'icon'
	}

	onBeforeMount(() => {
		init()
	})

	return {
		config,
		changeTableShowType,
		getLabelShowInLocal,
		changeLabelShowInLocal,
		changeLabelShowTable,
		getLabelTableShow,
		getLinksLabelShow,
	}
}
