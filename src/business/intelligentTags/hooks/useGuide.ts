import {computed, getCurrentInstance, ref} from "vue";
import { getUserGuideStorageFor, setUserGuideStorage } from "@src/api/GuideApi";
import { getRootWindow } from "pub-bbx-utils";

const guideStorageKey = 'INTELLIGENT_TAGS_GUIDE';

export const useIntelligentTagsGuide =  ()=> {

  const { proxy: ctx } = getCurrentInstance() as unknown as any;
  const isComplete = ref(false);

  const rootWin = computed(()=> getRootWindow());

  const showGuide = ()=> {
    const id = 'intelligentTagsGuide';
    const lastFinish = 1;
    const totalStep = 2;
    const arr = [
      {
        title: '标签筛选（1/2）',
        diyClass: 'biz-intelligent-tag__guide',
        content: '点击通过标签进行数据筛选',
        haveStep: true,
        direction: 'row',
        needCover: true,
        nowStep: 1,
        totalStep,
        id,
        domObj: () => {
          return document.querySelector('.biz-intelligent-tags__operator-button');
        },
        lastFinish,
        show: true,
      },
      {
        title: '添加标签（2/2）',
        content: '对勾选的数据进行标签管理',
        haveStep: false,
        direction: 'row',
        nowStep: 2,
        needPreviousStep: true,
        id,
        domObj: () => {
          return document.querySelector('.biz-intelligent-tagging__button');
        },
        needCover: true,
        lastFinish,
        totalStep,
        show: true,
        finishBtn: '完成'
      },
    ];


    rootWin.value.$shbGuide(arr, 0, '', (e: any) => {
      if(e.type === 'finish') {
        completeGuide(1, 1);
      }
      return new Promise<void>((resolve, reject) => {
        resolve();
      });
    }).create();
  };


  const completeGuide = ( step: number, isComplete: number )=> {
    const params: Record<string, any> = {};
    params.type = guideStorageKey;
    params.step = step;
    params.isComplete = isComplete;
    params.desc = '';
    setUserGuideStorage(params);
  };


  const getGuide = async ()=> {
  // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject)=> {
      try {
        const { data = {} } = await getUserGuideStorageFor({type: guideStorageKey});
        isComplete.value = Boolean(data?.isComplete) || false;
        resolve(isComplete.value);
      } catch(e) {
        reject(false);
      }
    });
  };

  return {
    showGuide,
    getGuide,
    isComplete
  };
};
