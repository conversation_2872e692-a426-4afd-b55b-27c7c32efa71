import { computed, getCurrentInstance, onBeforeMount, ref, unref, watchEffect, onMounted, onBeforeUnmount } from "vue";
/* store */
import store from '@src/store';
/* model */
import {  TagsFilterPanelComponentEvent, TagsFilterPanelComponentProps } from "@src/business/intelligentTags/model/interface";
/* enum */
import { StoreModuleEnum, BizTagFilterItemEnum } from "@src/business/intelligentTags/model/enum";
/* hooks */
import { useSettingLabel } from '@src/business/intelligentTags/hooks/useSettingLabel'
/* utils */
import { executeFunction, getIntelligentTagsModuleForKey, linkJumpToManageTagsPage } from "@src/business/intelligentTags/utils/utils";
import { cloneDeep, get } from "lodash";

export const useTagFilterPanel = (fetchFun: Function | undefined = undefined) => {
  // 当前所在的组件实例
  const { proxy } = getCurrentInstance() as unknown as any;
  // 面板选中的 value
  const tagFilterPanelValue = ref<Record<string, any>[]>([{ value: BizTagFilterItemEnum.AllTags }]);
  // 是否显示对应的过滤面板
  const filterTagPanelShow = ref(false);
  // 相关聚合到 search 方法中的参数
  const filterTagPanelSearchParams = ref<any>({ selectValue: [{ value: BizTagFilterItemEnum.AllTags}], linkTag: false});
  // 相关标签过滤面板需要的 props
  const filterTagsPanelBindAttr = ref<Partial<TagsFilterPanelComponentProps>>({});
  // 相关标签过滤面板需要的事件
  const filterTagsPanelBindOn = ref<Partial<TagsFilterPanelComponentEvent>>();
  // 设置标签显示文字或标签逻辑
  const { config, changeLabelShowInLocal, getLabelTableShow, getLinksLabelShow, changeLabelShowTable } = useSettingLabel()
  // 设置按钮状态
  const settingLabel = ref(false)

  // 是否显示关联标签
  const showLinkTags = computed(()=> filterTagPanelSearchParams.value.linkTag);

  // 对应的组件初始化的参数（比如工单模块 里面有对应 appId 为 TASK）
  const initParams = computed(()=> store.getters.initComponentParams);

  // 相关刷新列表数据的方法 key
  const searchFunKey = computed(()=> getIntelligentTagsModuleForKey(initParams.value.bizType, 'searchFunKey'));

  // 相关标签包含分组的列表
  const tagsGroupList = computed(()=> {
    // @ts-ignore
    return store.state.tags.tagsGroupList;
  });

  // 是否显示按钮上的小红点
  const showTagOperatorButtonDot = computed(()=> {
    const selectValues = unref(tagFilterPanelValue).map(item=> item.value);
    return selectValues.length && ![BizTagFilterItemEnum.AllTags].some(item=> selectValues.includes(item));
  });

  /**
     * @des 显示对应的标签过滤面板方法
     * @param {any} v:boolean=true
     * @returns {any}
     */
  const changeIntelligentTagsFilterPanelShow = (v: boolean = true)=> {
    filterTagPanelShow.value = v;
  };

  /**
     * @des 过滤标签的面板中每项点击的方法
     * @param {any} clickValue:any
     * @returns {any}
     */
  const handleFilterItemClick = (clickValue: any)=> {
    const selectValue = clickValue.id;
    const v = { value: selectValue };

    const existIndex = tagFilterPanelValue.value.findIndex(item=> item.value === selectValue);
    if(existIndex > -1) {
      tagFilterPanelValue.value.splice(existIndex, 1);
    } else {
      const specialFilterValueArray = [BizTagFilterItemEnum.NoTags, BizTagFilterItemEnum.AllTags];
      if(specialFilterValueArray.includes(selectValue)) {
        tagFilterPanelValue.value = [v];
      } else {
        tagFilterPanelValue.value = tagFilterPanelValue.value.concat([v]).reduce((acc: Record<string, any>[], item)=> {
          if(!specialFilterValueArray.includes(item.value)) {
            acc.push(item);
          }
          return acc;
        }, []);
      }
    }

    filterTagPanelSearchParams.value.selectValue = tagFilterPanelValue.value;
    // 刷新列表页面数据
    executeFunction(get(proxy, searchFunKey.value), proxy);
    // 点击的时候 清空session数据
    clearSessionFn()
  };

  /**
   * @des 标签管理设置switch回调方法
   * @param v boolean
   */
  const handleChangeLabelSettingSwitch = (v: boolean)=> {
    settingLabel.value = v
    changeLabelShowInLocal(Number(settingLabel.value), Number(filterTagPanelSearchParams.value?.linkTag || 0))
  }

  /**
   * @des 是否显示关联标签的switch回调方法
   * @returns {any}
   * @param v
   */
  const handleChangeLinkTagsSwitch = (v: boolean)=> {
    filterTagPanelSearchParams.value =  { ... filterTagPanelSearchParams.value, linkTag: v };
    changeLabelShowInLocal(Number(settingLabel.value), Number(v || 0))
  };

  /**
   * @description 过滤不存在标签
   */
    const filterNoExistItemOfClickHandler = (labelIds: Set<number>) => {
      if (!labelIds.size) return
      tagFilterPanelValue.value = tagFilterPanelValue.value.filter(item => !labelIds.has(item.value));
    }


  /**
     * @des 重置搜索参数
     * @returns {any}
     */
  const resetIntelligentTagsSearchParams = ()=> {
    filterTagPanelSearchParams.value = { selectValue: [{ value: BizTagFilterItemEnum.AllTags}], linkTag: showLinkTags.value };
    tagFilterPanelValue.value = [{ value: BizTagFilterItemEnum.AllTags}];
  };

  /**
     * @des 构建搜索时候的参数给每个列表页面使用
     * @returns {any}
     */
  const builderIntelligentTagsSearchParams = () => {
    const selectedValue = filterTagPanelSearchParams.value.selectValue.map((item: Record<string, any>)=> item.value);
    let intelligentTagsQueryParamsObj:Record<string, any> = {};

    const checkedFilterValueFun = (checkValue: string | number)=> selectedValue.length ? selectedValue.every((v: string | number)=> v === checkValue) : false;

    if(checkedFilterValueFun(BizTagFilterItemEnum.NoTags)) {
      intelligentTagsQueryParamsObj = {
        labelIds: null,
        labelExists: false
      };
    } else if(checkedFilterValueFun(BizTagFilterItemEnum.AllTags)) {
      intelligentTagsQueryParamsObj = {
        labelIds: null,
        labelExists: null
      };
    } else {
      intelligentTagsQueryParamsObj = {
        labelIds: selectedValue,
        labelExists: null
      };
    }

    return { labelQuery: intelligentTagsQueryParamsObj };
  };

  /**
     * @des 相关请求分组标签聚合接口方法
     * @param {any} params:any
     * @returns {any}
     */
  const fetchGroupTagData = async (params: any) => {
    const currentParams = { ...cloneDeep(initParams.value), ...params};
    const data = await store.dispatch(`${StoreModuleEnum.Tags}/initTagsGroupList`, { params: currentParams, commit: false });
    return data;
  };

  /**
   * @des 相关请求分组标签id聚合接口方法
   */
    const fetchGroupTagDataIds = async (params: any) => {
      const currentParams = { ...cloneDeep(initParams.value), ...params}
      Reflect.has(currentParams, 'keyword') && Reflect.deleteProperty(currentParams, 'keyword')
      const data = await store.dispatch(`${StoreModuleEnum.Tags}/initTagsGroupListIds`, { params: currentParams, commit: false })  
      return data
  }


  /**
   * @description 删除标签时 次数变化
   */
    const deleteTagFetch = () => {
      try {
          proxy?.$refs?.taggingLabelPanelRef?.deleteTagFetch()
      } catch (error) {
          console.warn(error)
      }
    }

  /**
     * @des 初始化 init 事件（初始化组件需要的参数）
     * @returns {any}
     */
  const init =()=> {
    filterTagsPanelBindAttr.value = {
      show: filterTagPanelShow.value,
      value: tagFilterPanelValue.value,
      ref: 'taggingLabelPanelRef',
      tagsGroupList: tagsGroupList.value,
      remoteFetchFun: fetchGroupTagData,
      remoteFetchIdsFun: fetchGroupTagDataIds,
      // @ts-ignore
      showLinkTagsSwitchValue: filterTagPanelSearchParams.value.linkTag,
      showLabelSettingSwitchValue: settingLabel.value
    };

    filterTagsPanelBindOn.value = {
      filterItemClick: handleFilterItemClick,
      linkTagsSwitchChange: handleChangeLinkTagsSwitch,
      close: ()=> changeIntelligentTagsFilterPanelShow(false),
      emptyLinkClick: ()=> linkJumpToManageTagsPage(),
      filterNoExistItemOfClick: filterNoExistItemOfClickHandler,
      labelSettingSwitchChange: handleChangeLabelSettingSwitch,
    };

    if(fetchFun) {
      filterTagsPanelBindOn.value.remoteFetchFun = fetchFun;
    }

    try {
      // 初始化开关按钮数据
      settingLabel.value = Boolean(getLabelTableShow())
      filterTagPanelSearchParams.value.linkTag = Boolean(getLinksLabelShow())
    } catch (error) {
        console.error(error)
    }

  };

  /**
   * @description 获取session数据执行函数
   */
  const getSessionFn = () => {
    if (!getUrlParams.value) return
    if (!sessionStorage.getItem(getUrlParams.value)) return
    const data = JSON.parse(sessionStorage.getItem(getUrlParams.value) as string)
    if (Array.isArray(data)) {
        tagFilterPanelValue.value = data
    } else if (typeof data === 'object' && !Array.isArray(data) && data !== null) {
        tagFilterPanelValue.value = [{ value: data.labelId }]
        filterTagPanelSearchParams.value.selectValue = [{ value: data.labelId }]
    }
    changeIntelligentTagsFilterPanelShow(true)
    executeFunction(get(proxy, searchFunKey.value), proxy)
    clearSessionFn()
  }

  const getUrlParams = computed(() => {
    const { hash } = window.location
    const params = new URLSearchParams(hash.substring(hash.indexOf('?') + 1));
    const formId = params.get('formId')
    return formId
  })

  /**
   * @description 清除session数据执行函数
   */
  const clearSessionFn = () => {
    if (!sessionStorage.getItem(initParams.value?.appId)) return
    sessionStorage.removeItem(initParams.value?.appId)
  }

  /* 监听相关依赖变重新改变对应组件的 prop 参数 */
  watchEffect(()=> {
    filterTagsPanelBindAttr.value.tagsGroupList = tagsGroupList.value;
    filterTagsPanelBindAttr.value.value = tagFilterPanelValue.value;
    filterTagsPanelBindAttr.value.show = filterTagPanelShow.value;
    //@ts-ignore
    filterTagsPanelBindAttr.value.showLinkTagsSwitchValue = filterTagPanelSearchParams.value.linkTag;
    filterTagsPanelBindAttr.value.showLabelSettingSwitchValue = settingLabel.value
  });

  /**
   * @desc message事件处理函数
   */
  const messageHandler = (event: MessageEvent) => {
    const { action, data } = event.data;
      if (action == 'shb.frame.activatedPage') {
        getSessionFn();
      }
      if (action == 'changeAllLabelView') {
        settingLabel.value = Boolean(data?.labelTableShow)
        filterTagPanelSearchParams.value.linkTag = Boolean(data?.linksLabel)
        // 更新标签列表 不走接口 (ps: 走接口的已经在点击标签的时候发送请求了)
        changeLabelShowTable(data?.labelTableShow)
        filterTagPanelSearchParams.value.linkTag = Boolean(data?.linksLabel)
    }
  }

  onMounted(() => {
    window.addEventListener('message', messageHandler);
  })

  onBeforeUnmount(() => {
    window.removeEventListener('message', messageHandler);
  })

  /* onBeforeMount  */
  onBeforeMount(() => {
    init();
  });

  return {
    labelConfigTable: config.value,
    filterTagPanelShow,
    showTagOperatorButtonDot,
    filterTagsPanelBindAttr,
    filterTagsPanelBindOn,
    showLinkIntelligentTags: showLinkTags,
    changeIntelligentTagsFilterPanelShow,
    builderIntelligentTagsSearchParams,
    resetIntelligentTagsSearchParams,
    getSessionFn,
    deleteTagFetch
  };
};
