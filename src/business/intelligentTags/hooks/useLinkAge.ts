// 标签联动hooks
import { ref } from 'vue';

/**
 * @desc 获取url中appId字符数据
 * @param {*} item
 * @return {*} 
 */
export const regAppId = (item: any) => {
  const url = item?.url || ''
  const match = url.match(/appId=([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/);
  const appId = match ? match[1] : null;

  return appId || '';
}


/**
 * @description 通过session获取本地标签
 */
export const useSessionLocal = () => {
  const sessionLocalKey = ref('im_linkage_session_local_');

  const setSessionLocal = (key: string, val: any) => {
    sessionStorage.setItem(key, JSON.stringify(val));
  }

  const getSessionLocal = (key: string) => {
    // @ts-ignore
    return JSON.parse(sessionStorage.getItem(key));
  }

  const clearSessionLocal = (key: string) => {
    sessionStorage.removeItem(key);
  }

  return {
    sessionLocalKey,
    setSessionLocal,
    getSessionLocal,
    clearSessionLocal
  }
}