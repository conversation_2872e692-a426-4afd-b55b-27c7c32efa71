import { computed, getCurrentInstance, onBeforeMount, ref, watchEffect, onBeforeUnmount } from "vue";
/* store */
import store from '@src/store';
/* model */
import { FetchTaggingParamsInterface, InitTagsComponentParams, TaggingComponentEvent, TaggingComponentProps, TagsGroupListItem, TagsItem } from "@src/business/intelligentTags/model/interface";
/* enum */
import { BizTaggingFrom, BizTaggingMethodEnum, StoreModuleEnum } from '@src/business/intelligentTags/model/enum';
/* api */
import {fetchBizTaggingTags, getIntelligentTagForBizIds, addPersonalLabel, editPersonalLabel, deleteIntelligentTagsListItem} from "@src/business/intelligentTags/api/main";
/* utils */
import { generateTaggingParams } from '@src/business/intelligentTags/utils/generate';
import { Message, MessageBox } from 'element-ui';
import {
  deleteObjectKeys,
  executeFunction,
  filterTagGroupAndTagForUnEnabled,
  getIntelligentTagsModuleForKey,
  linkJumpToManageTagsPage
} from "@src/business/intelligentTags/utils/utils";
import { conversionTagsListForTagsListMainItem } from "@src/business/intelligentTags/utils/conversion";
import {get, cloneDeep, uniqBy, set} from "lodash";
import { t } from "@src/locales"
/* hooks */
import useFetch from "@hooks/useFetch";

export const useTagging = (params?: InitTagsComponentParams, fetchFun: Function | undefined = undefined)=> {
  // 当前所在的组件实例
  const { proxy } = getCurrentInstance() as unknown as any;
  // 选中的标签列表
  const checkedTags = ref<TagsItem []>([]);
  // 选中标签临时变量
  const checkedTagsTemp = ref<(TagsItem & {status: 'add' | 'delete'}) []>([]);
  // 相关标签打标面板需要的 props
  const taggingBindOn = ref<Partial<TaggingComponentEvent>>();
  // 相关标签打标面板需要的 event
  const taggingBindAttr = ref<Partial<TaggingComponentProps>>({});
  //  相关列表选中的业务标签(仅仅供列表的时候使用)
  const bizTagListData = ref<TagsItem[]>([]);
  //  相关列表选中的业务标签(仅仅供列表的时候使用--删除的)
  const bizTagDeleteListData = ref<TagsItem[]>([]);
  // 相关打标保存的fetch方法 hooks
  const [taggingResult, handleFetchTaggingFetch, loading, isSuccess, taggingErrorMessage] = useFetch<FetchTaggingParamsInterface | undefined, boolean>(fetchBizTaggingTags, undefined, false);
  // 相关个人标签新增&编辑的fetch方法hooks
  const [addPersonalTaggingResult, handleFetchAddPersonalTaggingFetch, addPersonalLoading, addPersonalIsSuccess, addPersonalErrorMessage] = useFetch<FetchTaggingParamsInterface | undefined, TagsItem[]>(addPersonalLabel, undefined)
  // 相关个人标签编辑的fetch方法hooks
  const [editPersonalTaggingResult, handleFetchEditPersonalTaggingFetch, editPersonalLoading, editPersonalIsSuccess, editPersonalErrorMessage] = useFetch<FetchTaggingParamsInterface | undefined, TagsItem[]>(editPersonalLabel, undefined)
  // 相关个人标签删除fetch方法hooks
  const [deleteResult, handleFetchDelTagsListItem, delFetchLoading, delFetchISsSuccess, delFetchMessage] = useFetch<FetchTaggingParamsInterface | undefined, TagsItem[]>(deleteIntelligentTagsListItem, undefined)
  // 对应的组件初始化的参数（比如工单模块 里面有对应 appId 为 TASK）
  const initParams = computed(()=> params ? params : store.getters.initComponentParams);

  // 相关标签包含分组的列表（使用接口分页了 所以暂时不用了）
  // const tagsGroupList = computed(()=> {
  //     return filterTagGroupAndTagForUnEnabled(store.state.tags.tagsGroupList)
  // })

  // 相关打标按钮的组件 Ref
  const taggingCompRef = computed(()=> {
    return proxy.$refs.taggingCompRef;
  });

  // 相关刷新列表数据的方法 key
  const searchFunKey = computed(()=> getIntelligentTagsModuleForKey(initParams.value.bizType, 'searchFunKey'));

  // 相关列表选中的业务数据
  const bizListData = computed (()=> get(proxy, getIntelligentTagsModuleForKey(initParams.value.bizType, 'multiBizDataKeyArray')));

  // 打标是否是来源于详情
  const isFromDetailTagging = computed(()=> initParams.value.from === BizTaggingFrom.Detail );

  /**
     * @des 相关打开打标的面板的 popover 的前置事件
     * @returns {any}
     */
  const openPopoverHook = ()=> {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject)=> {
      if(bizListData.value && bizListData.value.length === 0) {
        MessageBox.confirm(t('common.base.intelligentTag.checkSelectTagTip'), t('common.base.toast'), {
          showCancelButton: false,
          confirmButtonText: t('common.base.confirm'),
        })
        return resolve(false);
      }

      checkedTags.value = [];

      const params = generateTaggingParams({
        appId: initParams.value.appId,
        bizType: initParams.value.bizType,
        bizObjsArray: bizListData.value,
        checkedTags: []
      });

      try {
        // 根据业务id去获取对应的标签列表
        const { success, data = [] } = await getIntelligentTagForBizIds({
          appId: params.appId,
          bizObjIds: params.bizObjs.map((item: { bizId: string | number })=> item.bizId)
        });
        if(success) {
          bizTagListData.value = data;
        }
      } catch(e){
        console.error('[ getIntelligentTagForBizIds error ]', e);
      }

      // 获取下对应选择数据的标签
      bizTagDeleteListData.value = [];

      return resolve(true);
    });
  };
  /**
     * @des 相关打标面板中按钮保存事件
     * @returns {any}
     */
  const handleTaggingSave = async (type: BizTaggingMethodEnum)=> {
    const bizObjsArray = isFromDetailTagging.value ? proxy.getBizObjArrayData() : bizListData.value;
    // 获取选中是对应开启的标签 （因为有可能有未开启的标签） 但是个人标签不存在开启关闭的属性
    const enabledCheckedTags = checkedTags.value.filter(item=>item.enabled || Reflect.has(item, 'personalLabel'))
    const params = generateTaggingParams({
      //@ts-ignore
      appId: initParams.value.appId || store.state.common.detailPageParams.formTemplateId,
      bizType: initParams.value.bizType,
      bizObjsArray,
      checkedTags: enabledCheckedTags,
      setMethod: type
    }) as unknown as FetchTaggingParamsInterface;

    // 列表修改支持批量操作（新增跟删除）
    if(!isFromDetailTagging.value) {
      params.fromList = true;
      params.addLabels = params.labels;
      params.addPersonalLabels = params.personalLabels,
      params.delLabels = bizTagDeleteListData.value.filter(item => item?.personalLabel == '0').map(item=> ({
        labelId: item.id,
        name: item.name
    }))
    // 删除的个人标签
    params.delPersonalLabels = bizTagDeleteListData.value.filter(item => item?.personalLabel == '1').map(item=> ({
      labelId: item.id,
      name: item.name
    }))

      // 删除不需要的传参的key
      deleteObjectKeys(params, ['labels', 'setMethod', 'personalLabels']);
    }

    await handleFetchTaggingFetch(params);

    if(isSuccess.value) {
      Message.success(t('common.base.saveSuccess'))
      // 隐藏对应的 popover
      if(taggingCompRef.value) {
        restCheckedTagTemp();
        taggingCompRef.value.hidePopover();

        if(!isFromDetailTagging.value) {
          checkedTags.value = [];
          taggingBindAttr.value.value = [];
          setTimeout(() => {
            executeFunction(get(proxy, searchFunKey.value), proxy);
            try {
              // 不用轮询
              proxy.$refs?.taggingLabelPanelRef?.plainRestFetch()
            } catch (error) {
              console.error('useTagging: ',error)
            }
          }, 500);
        }
      }

      // 判断是否在详情的打标 然后调用对应的刷新方法
      if(isFromDetailTagging.value) {
        proxy.refreshData(conversionTagsListForTagsListMainItem(checkedTags.value));
      }
    } else {
      Message.warning(taggingErrorMessage.value);
    }
  };


    /**
     * @description 隐藏打标面板处理逻辑
     */
    const handleTaggingHide = () => {
        for (let tagItem of checkedTagsTemp.value) {
            if (tagItem.status === 'add') {
                checkedTags.value = checkedTags.value.filter(item => item.id !== tagItem.id)
            }
            if (tagItem.status === 'delete') {
              if (!checkedTags.value.some(item => item.id === tagItem.id)) {
                checkedTags.value.push(tagItem);
              }
            }
        }

        restCheckedTagTemp();
    }

    // 重置临时标签数据
    const restCheckedTagTemp = () => {
      checkedTagsTemp.value = []
    }

    const handlePersonSave = (type: BizTaggingMethodEnum) => {
      handleTaggingSave(type)
    }

    /**
     * @des 个人标签的新建
     */
    const personalLabelAdd = async (val: any)=> {
        await handleFetchAddPersonalTaggingFetch({ name: val.labelName,logoColor: val.color, description: '' })
        if(addPersonalIsSuccess.value) {
            Message.success('新建成功')
            taggingBindAttr.value.localGroupLabelList = { id: addPersonalTaggingResult.value.result, name: val.labelName, logoColor: val.color, status: 'add'}
            taggingCompRef.value?.handleBack()
        } else {
            Message.warning(addPersonalErrorMessage.value)
        }
    }

    /**
     * @des 个人标签的编辑
     */
    const handlePersonEdit = async (val: any) => {
        await handleFetchEditPersonalTaggingFetch({ id: val.id, name: val.labelName, logoColor: val.color, description: '' })
        if (editPersonalIsSuccess.value) {
            Message.success('编辑成功')
            taggingBindAttr.value.localGroupLabelList = { id: val.id, name: val.labelName, logoColor: val.color, status: 'edit'}
            taggingCompRef.value?.handleBack()
        } else {
            Message.warning(editPersonalErrorMessage.value)
        }
    }

    /**
     * @des 个人标签的删除
     */
    const handlePersonDelete = async (id: number | string) => {
        await handleFetchDelTagsListItem({ ids: [id] })
        if (delFetchISsSuccess.value) {
            Message.success('删除成功')
            taggingBindAttr.value.localGroupLabelList = { id: id, status: 'delete'}
            taggingCompRef.value?.handleBack()
        } else {
            Message.warning(delFetchMessage.value)
        }
    }

  /**
     * @des 相关管理标签的点击事件
     * @returns {any}
     */
  const handleManageTagsClick = ()=> {
    linkJumpToManageTagsPage();
  };

    /**
     * @des 切换标签按钮情况逻辑
     */
    const handleRadioChange = () => {
      // checkedTags.value = []
    }

  /**
     * @des popover 中每项的checkbox的点击事件
     * @param {any} checked:boolean
     * @param {any} tagsItem:TagsItem
     * @returns {any}
     */
  const handleCheckBoxCheckChange = (checked: boolean, tagsItem: TagsItem)=> {
    if(!Array.isArray(tagsItem)) {
      if(checked) {
        checkedTags.value.push(tagsItem);
        handlerTempCheckTags(tagsItem, 'add');
      } else {
        const index = checkedTags.value.findIndex(item=> tagsItem.id === item.id);
        if(index > -1 ) checkedTags.value.splice(index, 1);
        handlerTempCheckTags(tagsItem, 'delete');
      }
    } else {
      checkedTags.value = tagsItem;
    }
  };

    // 临时标签处理函数
    const handlerTempCheckTags = (item: TagsItem, status: 'add' | 'delete') => {
      const index = checkedTagsTemp.value.findIndex(tagItem => tagItem.id === item.id);
      
      if (index > -1) {
        const existItem = checkedTagsTemp.value[index];
        if (existItem?.status !== status) {
          checkedTagsTemp.value.splice(index, 1);
        }
      } else {
        checkedTagsTemp.value.push({ ...item, status });
      }
    };
  

  /**
     * @des 获取对应的打标操作类型
     * @returns {any}
     */
  const getOperatorType = () => {
    const operatorTypeMap = {
      'list': BizTaggingMethodEnum.Increment,
      'detail': BizTaggingMethodEnum.Cover
    };

    if(initParams.value.from) {
      return Reflect.get(operatorTypeMap, initParams.value.from) || 1;
    }
    return 1;
  };


  /**
     * @des 相关请求分组标签聚合接口方法
     * @param {any} params:any
     * @returns {any}
     */
  const fetchGroupTagData = async (params: any) => {
    //@ts-ignore
    const currentParams = { ...cloneDeep(initParams.value), appId: initParams.value.appId || store.state.common.detailPageParams.formTemplateId, ...params};
    const data = await store.dispatch(`${StoreModuleEnum.Tags}/initTagsGroupList`, { params: currentParams, commit: false });
    data.list = filterTagGroupAndTagForUnEnabled(data.list);
    return data;
  };


  /**
   * @des 相关选中的标签删除事件
   * @param tagItem
   */
  const handleClearValueItem = (tagItem: TagsItem)=> {
    if(tagItem.id) {
      const existIndex = bizTagListData.value.findIndex(item=> item.id === tagItem.id);
      if(existIndex > -1 ) {
        const spliceTagItem = bizTagListData.value.splice(existIndex, 1) as unknown as TagsItem[];
        bizTagDeleteListData.value.push(...spliceTagItem);
      }
    }
  };


  /**
     * @des 初始化 init 事件（初始化组件需要的参数）
     * @returns {any}
     */
  const init =()=> {
    taggingBindOn.value = {
      save: handleTaggingSave,
      hidePopover: handleTaggingHide,
      manageTagsClick: handleManageTagsClick,
      change: handleCheckBoxCheckChange,
      clear: handleClearValueItem,
      radioChange: handleRadioChange,
      personalSave: handlePersonSave,
      personalLabelAdd: personalLabelAdd,
      personalLabelEdit: handlePersonEdit,
      personalLabelDelete: handlePersonDelete
    };

    taggingBindAttr.value = {
      // tagsGroupList: tagsGroupList.value,
      value: isFromDetailTagging.value ? checkedTags.value : bizTagListData.value,
      disabled: loading.value,
      openPopoverHook: isFromDetailTagging.value ? undefined : openPopoverHook,
      ref: 'taggingCompRef',
      remoteFetchFun: fetchGroupTagData,
      operatorType: getOperatorType(),
      remoteSearch: true,
      localGroupLabelList: {},
    };

    if(fetchFun) {
      taggingBindAttr.value.remoteFetchFun = fetchFun;
    }
  };

  /* 监听相关依赖变重新改变对应组件的 prop 参数 */
  const stop = [watchEffect(()=> {
    // taggingBindAttr.value.tagsGroupList = tagsGroupList.value
    taggingBindAttr.value.disabled = loading.value || addPersonalLoading.value || delFetchLoading.value || editPersonalLoading.value;

    taggingBindAttr.value.value = isFromDetailTagging.value ? checkedTags.value : bizTagListData.value;
  })];

  /* onBeforeMount  */
  onBeforeMount(() => {
    init();
  });

  onBeforeUnmount(() => {
    stop.forEach((item: any)=> item());
  })

  return {
    checkedTags,
    taggingBindAttr,
    taggingBindOn
  };
};
