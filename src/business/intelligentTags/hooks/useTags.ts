
import { computed, ref } from 'vue';
/* hooks */
import { useTagFilterPanel } from '@src/business/intelligentTags/hooks/useTagsFilter';
import { useTagging } from '@src/business/intelligentTags/hooks/useTagging';
// import { useTagsGroupList } from '@src/business/intelligentTags/hooks/useTagsGroupList'
import { useCurrentInstance } from "@src/view/designer/workflow/viewV2/hooks/useVC";
/* model */
import { InitTagsComponentParams } from '@src/business/intelligentTags/model/interface';
import { constModuleForValueMap } from '@src/business/intelligentTags/model/const/module';
import { ModuleBizDataKeyValue } from '@src/business/intelligentTags/model/biz/moduleBizDataKeyValue';
/* enum */
import { BizTaggingFrom, ModulesEnum } from '@src/business/intelligentTags/model/enum';
/* store */
import store from '@src/store';
/* utils */
import {get, isFunction, isObject, set} from 'lodash';



/**
 * @des 相关智能标签对应的初始化props参数以及事件参数等（在intelligentTagsListMixin.ts文件使用以及IntelligentTagsTaggingView组件使用初始化打标组件参数）
 * @param {any} param?:InitTagsComponentParams
 * @returns {any}
 */

export const useTags = (param?: InitTagsComponentParams)=> {
  //  对应的组件初始化的参数（比如工单模块 里面有对应 appId 为 TASK）
  const initParams = computed(()=> param ? param : store.getters.initComponentParams);

  // const { tagsGroupList } = useTagsGroupList()

  // 初始化对应的打标组件的 props 参数以及对应 事件监听
  const { taggingBindAttr, taggingBindOn, checkedTags } = useTagging(param);

  // 如果只详情不需要对应面板的返回值 所以直接 return
  if(initParams.value.from === BizTaggingFrom.Detail) {
    return {
      taggingCheckedTags: checkedTags,
      taggingBindAttr,
      taggingBindOn,
    };
  }

  // 初始化对应的列表过滤标签组件的 props 参数以及对应 事件监听
  const useTagFilterPanelResult = useTagFilterPanel();

  return {
    ...useTagFilterPanelResult,
    taggingCheckedTags: checkedTags,
    taggingBindAttr,
    taggingBindOn
  };
};


/**
 * @des 相关单个打标动作的 hook 也就是指在详情使用（在intelligentTagsDetailMixin.ts使用）
 * @param {any} module?:ModulesEnum
 * @returns {any}
 */
export const useTagsSingle = (module?: ModulesEnum) => {
  const [ ctx ] = useCurrentInstance() as unknown as any;
  // 当前的模块
  const currentModule = ref(module);

  // 当前模块对应的需要的 value 的 map（例如 工单key就是工单，getKeyArray也就是对应需要在工单详情取到的label的的主要数据只有id等参数 ）
  const currentModuleForValueMap = computed<ModuleBizDataKeyValue>(()=> {
    if(currentModule.value) {
      return Reflect.get(constModuleForValueMap, currentModule.value);
    }
    return new ModuleBizDataKeyValue();
  });

  // 在模块对应主对象数据上的需要取的层次 key array（比如 工单 就是 ['attribute', 'label']）
  const deepObjectKeysArray = computed(()=> currentModuleForValueMap.value.singleBizDataKeyArray);

  // 在详情数据中对应模块的主数据的对象的 key （比如工单就是 task）
  const moduleValueMainKey = computed(()=> currentModuleForValueMap.value.key);

  // 相关模块的业务数据从组件的实例上取
  const moduleBizData = computed(()=> ctx[moduleValueMainKey.value]);

  // 获取相关模块是否显示对应的打标按钮
  const showTaggingButton = computed(()=> get(ctx, currentModuleForValueMap.value.deleteKey));

  // 相关编辑权限
  const editAuth = computed(()=> get(ctx, 'editOperatorValue'));

  // 刷新页面数据的方法列表（每个模块都不一样）
  const modulesRefreshKeyArrFun = computed(()=> {
    const refreshKeyArr = currentModuleForValueMap.value.refreshKeyArr;
    if(!refreshKeyArr?.length) return [];
    return refreshKeyArr?.map(refreshKey=> {
      return get(ctx, refreshKey);
    });
  });


  // 模块的中标签的主要数据
  const tagsMainDataList = computed(()=> {
    if(isObject(ctx[moduleValueMainKey.value])) {
      return get(ctx[moduleValueMainKey.value], deepObjectKeysArray.value) || [];
    }
    return [];
  });


  /**
   * @des 更新模块
   * @param {any} v:ModulesEnum
   * @returns {any}
   */
  const updateTagsModule = (v: ModulesEnum)=> {
    currentModule.value = v;
  };


  /**
   * @des 更新标签的主要数据的方法
   * @param {any} v:any
   * @returns {any}
   */
  const updateTagsMainDataList = (v: any)=> {
    if(modulesRefreshKeyArrFun.value?.length) {
      modulesRefreshKeyArrFun.value.forEach(funItem=> isFunction(funItem) && funItem.call(ctx));
    }

    // if(!ctx[moduleValueMainKey.value]) return;
    // set(ctx[moduleValueMainKey.value], deepObjectKeysArray.value, v);
  };


  /* 相关详情页面中对应的 view 组件对应 props 以及事件 */
  const tagsSingleComponentAttrs = computed(()=> {
    return {
      show: !ctx.loading,
      from: currentModule.value,
      tagsMainDataList: tagsMainDataList.value,
      bizData: moduleBizData.value,
      updateTagsMainDataList: updateTagsMainDataList,
      showTaggingButton: !showTaggingButton.value
    };
  });


  return {
    tagsSingleComponentAttrs,
    moduleValueMainKey,
    tagsMainDataList,
    updateIntelligentTagsModule: updateTagsModule,
    updateTagsMainDataList
  };
};
