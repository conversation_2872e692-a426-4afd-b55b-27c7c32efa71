import { computed, onBeforeMount } from 'vue';
import store from '@src/store';
import { StoreModuleEnum } from '@src/business/intelligentTags/model/enum';
import { cloneDeep } from 'lodash';

export const useTagsGroupList = ()=> {

  const tagsGroupList = computed(()=> {
    //@ts-ignore
    return store.state.tags.tagsGroupList;
  });

  const initParams = computed(()=> store.getters.initComponentParams);

  onBeforeMount(() => {
    store.dispatch(`${StoreModuleEnum.Tags}/initTagsGroupList`, cloneDeep(initParams.value));
  });

  return {
    tagsGroupList
  };
};
