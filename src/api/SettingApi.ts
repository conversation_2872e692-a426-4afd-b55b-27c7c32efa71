/* util */
import http from '@src/util/http';

/** 
 * 获取灰度权限
*/
export function getGrayAuth(params = {}) {
  return http.get('/setting/getGrayFunctionInfo', params);
}

/** 
 * @des 获取附加组件权限
*/
export function getPaasCardAuth(params = {}) {
  return http.get('/api/paas/outside/pc/card/auth/range/getAuthRangeList', params);
}

/** 
 * @des 设置附加组件权限
*/
export function setPaasCardAuth(params = {}) {
  return http.post('/api/paas/outside/pc/card/auth/range/saveOrUpdateTaskRange', params);
}

/** 
 * 获取角色列表
*/
export function getSettingRoleList(params: {} | undefined) {
  return http.get('/setting/role/list', params)
}

/**
 * @des 获取按钮绑定的触发器
 */
export function getButtonConcatTriggerList (params: {} | undefined) {
	return http.post('/api/application/outside/trigger/getTriggerByBizTypeInfo', params);
}
/**
 * @see https://yapi.shb.ltd/project/7989/interface/api/62906
 */
export function getSettlementPoolByModuleCheck (params: {} | undefined) {
	return http.get('/api/voice/outside/settlement/pool/getSettlementPoolByModuleCheck', params);
}




