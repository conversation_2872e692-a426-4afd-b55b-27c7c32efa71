import http from '@src/util/http';
let prefix = '/api/paas';

/**
 * 父表单内容唯一性校验
 * @param {Object} params - 参数
 * @param {String} params params.fieldName - 自定义字段fieldName
 * @param {String} params params.fieldValue - 自定义字段value
 * @param {String} params params.contentId - 内容id
 * @param {String} params params.templateBizId - 表单类型bizId
 */
export function formValueRepeat(params: {} | undefined) {
  return http.post(`${prefix}/outside/es/formContent/formValueRepeat`, params);
}

/**
 * 子表单内容唯一性校验
 * @param {Object} params - 参数
 * @param {String} params params.fieldName - 自定义字段fieldName
 * @param {String} params params.fieldValue - 自定义字段value
 * @param {String} params params.contentId - 内容id
 * @param {String} params params.templateBizId - 表单类型bizId
 */
export function subFormValueRepeat(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/subFormContent/subFormValueRepeat`,
    params
  );
}

/**
 * 外部父表单内容唯一性校验,供外部表单调用
 * @param {Object} params - 参数
 * @param {String} params params.fieldName - 自定义字段fieldName
 * @param {String} params params.fieldValue - 自定义字段value
 * @param {String} params params.contentId - 内容id
 * @param {String} params params.templateBizId - 表单类型bizId
 */
export function externalFormValueRepeat(params: {} | undefined) {
  return http.post(`${prefix}/inside/es/form/formValueRepeat`, params);
}

/**
 * 外部子表单内容唯一性校验
 * @param {Object} params - 参数
 * @param {String} params params.fieldName - 自定义字段fieldName
 * @param {String} params params.fieldValue - 自定义字段value
 * @param {String} params params.contentId - 内容id
 * @param {String} params params.templateBizId - 表单类型bizId
 */
export function externalsubFormValueRepeat(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/form/outer/subFormValueRepeat`,
    params
  );
}
