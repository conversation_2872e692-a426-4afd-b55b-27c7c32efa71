import http from '@src/util/http';

import { getCustomerDetailResult } from '@model/param/out/Customer';
let preEsfix = '/api/elasticsearch';

/**
 * 获取客户信息
 * @param {string} id - 客户id
 * @returns Promise<Customer>
 */
export function getCustomer(id: string): Promise<getCustomerDetailResult> {
  return http.get('/customer/get', { id });
}

/**
 * 获取客户列表
 * @param {Object} params - 参数
 * @returns {*}
 */
export function getCustomerListAsyn(params: {} | undefined) {
  return http.get(`/customer/getListAsyn`, params);
}

/**
 * 查询客户列表
 * @param {Object} params - 参数
 */
export function getCustomerList(params: {} | undefined) {
  return http.post(`/customer/list`, params);
}
/**
 * @description 获取客户联系人数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerLinkman(params: {} | undefined) {
  return http.get(`${preEsfix}/outside/es/linkman/list`, params);
}
/**
 * @description 获取客户联系人和地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.customerId -- 客户id
 * @param {String} params.productId -- 产品id
 * @param {String} params.notNull -- 分页数
 */
export function getTaskDefaultInfo(params: {} | undefined) {
  return http.get('/task/defaultInfo', params);
}
/**
 * @description 通过联系人id获取地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.lmId -- 产品id
 */
export function getLmBindAddress(params: {} | undefined) {
  return http.get('/task/getLmBindAddress', params);
}

// 客户关联地址/新建地址
export function addressCreate(params: {} | undefined) {
  return http.post('/customer/address/create', params, false);
}

/**
 * @description 获取客户地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerAddress(params: {} | undefined) {
  return http.get('/api/customer/outside/pc/product/address', params);
}
/**
 * @description 获取客户产品数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerProduct(params: {} | undefined) {
  return http.post('/task/customer/product', params);
}

/**
 *
 * @param {Object} params - 参数
 * @param {String} params.customerId - 客户id
 * @param {Number} params.pageSize -
 * @param {Number} params.pageNum -
 * @returns {*}
 */
export function getLinkmanOfCustomer(params: {} | undefined) {
  return http.get('/customer/linkman/list', params);
}

/**
 * @description 获取客户产品关联和未关联数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getCustomerLinkProduct(params: {} | undefined) {
  return http.post(`${preEsfix}/outside/es/product/pass/search`, params);
}

/**
 *
 * @param {Object} params - 参数
 * @param {String} params.customerId - 客户id
 * @param {Number} params.pageSize -
 * @param {Number} params.pageNum -
 * @param {Number} params.keyword -
 * @returns {*}
 */
export function getUserTag(params: {} | undefined) {
  return http.get('/customer/userTag/list', params);
}

/**
 * 获取客户标签
 */
export function getCustomerTag(params = {id: ''}) {
  return http.post(`/api/customer/outside/customer/tag/list?customerId=${params?.id}`, params);
}

/**
* 客户表单字段列表
* @param {Object} params - 参数
* @param {String} params.isFromSetting - 是否用于设置页，是：true 否：false
*/
export function getCustomerFields(params: {} | undefined) {
  return http.get('/customer/getCustomerFields', params);
}

/**
 * @param {Object} params - 校验联系人是否存在
 * @param {String} params.customerId - 客户id
 * @param {String} params.phone - 联系人手机号
 * @returns {*}
 */
export function checkLinkmanByPhone(params: {} | undefined) {
  return http.post('/api/customer/outside/pc/customer/checkLinkmanByPhone', params);
}
/**
 *
 * @param {Object} params - 添加联系人信息
 * @returns {*}
 */
export function addLinkman(params: {} | undefined) {
  return http.post('/api/customer/outside/pc/customer/addLinkman', params);
}

