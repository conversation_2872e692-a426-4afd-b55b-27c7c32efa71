/* model */
import MsgModel from '@src/model/MsgModel';
import { ConnectorInsertSelectCallParams } from '@src/model/param/in/Connector';
import { ConnectorInsertSelectCallResult } from '@src/model/param/out/Connector';
/* util */
import http from '@src/util/http';

const prefix = '/api/application';
/**
 * @description 连接器 新增时用于回显填充数据的查询接口
 * @see http://yapi.shb.ltd/project/3088/interface/api/36732
 */
export function getConnectorInsertSelectCall(params: ConnectorInsertSelectCallParams): Promise<MsgModel<ConnectorInsertSelectCallResult>> {
  return http.post('/api/application/outside/call/insertSelectCall', params);
}


export function connectorFormSettingInterfaceList(params: {} | undefined): Promise<any> {
  return http.get(`${prefix}/outside/form/getFormControlToOptions`, params);
}



export function getFiledLaForHttp(params = {}) {
  return http.post('/api/application/outside/connectorV2/field/language', params);
}