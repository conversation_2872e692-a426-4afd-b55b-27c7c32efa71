import http from '@src/util/http';
let prefix = '/api/paas';
/**
 * 获取组织架构树形结构数据
 * @param {Object} params - 参数
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 页面大小
 * @returns Promise<Team>
 */
export function tagV2List(params: {} | undefined) {
  return http.post('/security/tag/treeV2', params);
}

/**
 * 查询团队下的人员
 * @param {Object} params
 * @param {Number} params.pageNum -- 页码
 * @param {Number} params.pageSize -- 页面大小
 * @param {String} params.id -- 团队id
 * @returns Promise<Team>
 */
export function userList(params: {} | undefined) {
  return http.post(`/security/tag/userList`, params, false);
}

/**
 * @description 获取角色列表
 * @param {Object} params -- 参数对象
 */
export function getRoleList(params: {} | undefined) {
  return http.get(`/setting/role/list`, params);
}

/**
 * @description 查询流程信息
 * @param {Object} params -- 参数对象
 * @param {String} params.formTemplateId -- 表单模板id
 * @param {String} params.appId -- 应用id
 */
export function getProcess(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/get`, params);
}

/**
 * @description 部署流程
 * @param {Object} params -- 参数对象
 * @param {String} params.formContentId -- 表单id
 * @param {String} params.antvJson -- 流程json
 */
export function deploymentProcess(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/deployment`, params);
}

/**
 * @description 重新部署流程/修改流程
 * @param {Object} params -- 参数对象
 * @param {String} params.formContentId -- 表单id
 * @param {String} params.antvJson -- 流程json
 */
export function reDeploymentProcess(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/reDeployment`, params);
}

/**
 * @description 重新部署流程/修改流程
 * @param {Object} params -- 参数对象
 * @param {String} params.formContentId -- 表单id
 * @param {String} params.antvJson -- 流程json
 */
export function updateProcess(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/version/edit`, params);
}

/**
 * @description 催办
 * @param {Object} params -- 参数对象
 * @param {String} params.formContentId -- 表单id
 * @param {String} params.pushTypes -- 推送类型1短信2钉钉
 */
export function urgeProcess(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/urge`, params);
}

/**
 * @description 流程日志导出
 * @param {Object} params -- 参数对象
 * @param {String} params.processorInstanceId
 */
export function exportLog(params: {} | undefined) {
  return http.post(`/excels/paas/flow/log/export`, params);
}

/**
 * @description 获取当前节点可以回退的节点
 * @param {Object} params -- 参数对象
 * @param {Array} params.flowNodeFormList -- 流程节点集合
 * @param {String} params.curNodeId -- 当前节点id
 */
export function findBackNodeList(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/findBackNodeList`, params);
}

/**
 * @description 获取当前节点之前的审批节点和流程节点
 * @param {Object} params -- 参数对象
 */
export function beforeTheSpecifiedNode(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/beforeTheSpecifiedNode`, params);
}

export function fetchLogRemark(urlParams: string, body: Record<string, string>) {
  return http.post(`${prefix}/outside/pc/processor/addLogRemark?${urlParams}`, body);
}

/**
 * @description 获取当前流程实例的当时版本流程
 * @param {Object} params -- 参数对象
 * @param {Array} params.processorInstanceId -- 流程实例id
 */
export function getProcessByProcessId(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/getAttribute`, params);
}

/**
 * @description 根据nodeID获取对应的超时设置
 * @param {Object} params -- 参数对象
 * @param {Array} params.nodeId -- 流程实例id
 */
export function getTimeoutSettingOrderNodeId(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/message/rule/getTimeOutRuleByNode`, params);
}
/**
 * @description 获取当前流程实例的节点配置信息
 */
export function getNodeStockSetting(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/getNodeStockSetting`, params);
}

/**
 * @description 获取选人控件的服务商列表
 * @see http://yapi.shb.ltd/project/307/interface/api/35310
 */
export function getSelectServiceProviderList(params: {} | undefined) {
  return http.post('/api/user/outside/select/getSpList', params);
}

/**
 * @description 获取选人控件 服务商 用户列表
 * @see http://yapi.shb.ltd/project/307/interface/api/35412
 */
export function getSelectServiceProviderUserList(params: {} | undefined) {
  return http.post('/api/user/outside/select/getUserFromSp', params);
}

/**
 * @description 工单高级审批--获取审批人
 */
export function getLeaderForSetting(params = {}) {
  return http.post('/api/app/outside/taskVipApprove/getLeaderForSetting', params);
}
/**
 * @description 备件高级审批--获取审批人
 */
export function getPartLeaderForSetting(params = {}) {
  return http.get('/api/part/outside/vip/approve/getLeaderForSetting', params);
}

/**
 * @description 事件高级审批--获取审批人
 */
export function getPartEventLeaderForSetting(params = {}) {
  return http.post('/api/event/service_case/outside/event/vipApprove/getLeaderForSetting', params);
}


/**
 * 导出获取对应的字段
 * @param params
 */

export function getFlowExportNode(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/node/all`, params);
}


/**
 * 获取流程版本列表
 * @param params
 */
export function getFlowVersionList(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/version/template/all`, params);
}

/**
 * 创建流程
 * @param params
 */
export function createFlowVersion(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/version/create`, params);
}

/**
 * 启用流程
 * @param params
 */
export function enableFlowVersion(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/version/enable`, params);
}

/**
 * 删除流程
 * @param params
 */
export function deleteFlowVersion(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/processor/version/delete`, params);
}

/**
 * 获取根据 formType 支持的条件
 * @param params
 */
export function getConditionSupportExpression(params: {} | undefined) {
  return http.get(`${prefix}/outside/pc/processor/version/line/condition/expression/new`, params);
}





// 查询事件字段
export function getAllEventFields(params: {} | undefined) {
  return http.get('/setting/getEventTemplateFieldList', params);
}

/**
 * 获取条件的代码
 * @param {Object} params -- 参数对象
 * @param {Array} params.templateId -- 流程模板id
 * @param {String} params.codeLocation -- paasLine
 * @param {String} params.locationId -- 流程线id
 */
export function getConditionCode(params: {} | undefined) {
  return http.post(`${prefix}/outside/code/getCode`, params);
}

// 查询pass表单配置的智能派单机器人
export function getSmartDispatchAgentPageList(params: {} | undefined) {
  return http.post('/api/voice/outside/smart/agent/getPageList', params);
}

//查询服务商权限角色tab
export function getServiceRoleTabList(params: {} | undefined) {
  return http.get(`/api/user/outside/provider/role/list`, params);
}

//查询服务商权限角色数据
export function getServiceRoleList(params: {} | undefined) {
  return http.post(`/api/user/outside/provider/role/getProviderList`, params);
}
