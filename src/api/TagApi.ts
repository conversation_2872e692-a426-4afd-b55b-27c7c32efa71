import http from '@src/util/http';
import { proxyMap } from './config';

const prefix = proxyMap.paas;

/**
 * 批量保存标签接口
 */
export function saveTagBatch(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/tag/saveBatch`,
    params
  );
}

/**
 * 查询所有标签集合
 */
export function getSelectList(params: {} | undefined) {
  return http.get(
    `${prefix}/outside/pc/tag/selectList`,
    params
  );
}
/**
 * 查询标签开关状态（设置）
 */
export function selectTagSwitchState(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/tag/selectTagSwitchState`,
    params
  );
}
/**
 * 设置标签开关状态（设置）
 */
export function setTagSwitchState(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/tag/setTagSwitchState`,
    params
  );
}
/**
 * 批量新增/更新标签与表单关联
 */
export function saveOrUpdateBatch(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/paasTagFormContent/saveOrUpdateBatch`,
    params
  );
}

/**
 * 查询标签（表单导航栏）
 */
export function selectTagNavigationBar(params: {} | undefined) {
  return http.get(
    `${prefix}/outside/pc/tag/selectTagNavigationBar`,
    params
  );
}

/**
 * @description 获取相关标签列表（智能标签--- 可以选择标签）
 * @param params 
 */
export function getSelectUserLabelListForAiLabel(params: any) {
  return http.post('/api/user/outside/select/getUserLabelList', params);
}

