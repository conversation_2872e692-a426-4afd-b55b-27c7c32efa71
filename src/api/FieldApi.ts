import http from '@src/util/http';
import { proxyMap, response } from './config';
/* params */
import {
  ClearSubFormDefaultValueParams,
  DownloadSubFormDefaultValueParams,
  GetSettingSubFormDefaultValueParams,
  GetSubFormDefaultValueParams,
  SubFormUploadDefaultValueParams
} from '@src/model/param/in/Field';
import {
  ClearSubFormDefaultValueResult,
  DownloadSubFormDefaultValueResult,
  GetSettingSubFormDefaultValueResult,
  GetSubFormDefaultValueResult,
  SubFormUploadDefaultValueResult
} from '@src/model/param/out/Field';

const prefix = proxyMap.paas;
const stockPrefix = proxyMap.warehouse;

/**
 * @description 获取指定的app
 * @param {Object}
 */
export const getAppInforn = (params = {}) => {
  return http.get(`${prefix}/outside/pc/app/getApp`, params);
};
/**
 * @description 修改app
 * @param {Object}
 */
export const updateAppInforn = (params = {}) => {
  return http.post(`${prefix}/outside/pc/app/update`, params);
};

/**
 * @description 新建/更新/删除表单元数据
 * @param {Object}
 */
export const creatFormFieldList = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formField/creatFormFieldList`, params);
};

/**
 * @description 查询表单元数据
 * @param {Object}
 */
export const getFormFieldListInfo = (params = {}) => {
  return http.get(
    `${prefix}/outside/pc/formField/getFormFieldInfo`,
    params,
    false
  );
};
/**
 * @description 查询详情表单元数据
 * @param {Object}
 */
export const getNodeFormFieldInfo = (params = {}, isNew = false) => {
  return http.get(
    `${prefix}${isNew ? '/outside/pc/formField/node/getNodeFormFieldInfo/new': '/outside/pc/formField/node/getNodeFormFieldInfo'}`,
    params,
    false
  );
};

/**
 * @description 批量删除表单内容
 * @param {Object}
 */
export const deleteFormContent = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formContent/deleteBatch`, params);
};

/**
 * @description 获取一个表单类型
 * @param {Object}
 */
export const getOneTemplate = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/getOneTemplate`, params);
};

/**
 * @description 创建一个表单类型
 * @param {Object}
 */
export const createOneTemplate = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formField/createOneTemplate`, params);
};

/**
 * @description 新建表单内容
 * @param {Object}
 */
export const createFormContent = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formContent/create`, params);
};

/**
 * @description 编辑表单内容
 * @param {Object}
 */
export const editeFormContent = (params = {}) => {
  return http.post(
    `${prefix}/outside/pc/formContent/editWfFormContent`,
    params
  );
};

/**
 * @description 连接器 暂存表单内容
 * @param {Object}
 */
export const connectorEditFormContent = (params = {}) => {
  return http.post(
    `${prefix}/outside/pc/card/editWfFormContent`,
    params
  );
};

/**
 * @description 编辑表单内容并开启流程
 * @param {Object}
 */
export const startFlowContent = (params = {}) => {
  return http.post(`${prefix}/outside/pc/processor/startFlowContent`, params);
};

/**
 * @description 编辑表单内容并开启流程
 * @param {Object}
 */
export const startFlowContentForConnectorCard = (params = {}) => {
  return http.post(`${prefix}/outside/pc/card/startFlowContent`, params);
};

/**
 * @description 编辑表单内容并完成流程
 * @param {Object}
 */
export const finishFlowContent = (params = {}, isNew = false) => {
  return http.post(`${prefix}${isNew ? '/outside/pc/processor/finishedFlowContent/new': '/outside/pc/processor/finishedFlowContent'}`, params);
};

/**
 * @description 搜索表单内容
 * @param {Object}
 */
export const searchFormList = (params = {}, options = {}) => {
  return http.post(`${prefix}/outside/es/formContent/searchList`, params, true, { cancelable: false, ...options });
};
/**
 * @description 删除表单列表
 * @param {Object}
 */
export const deleteBatch = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formContent/deleteBatch`, params);
};

/**
 * @description 根据bizId获取单个的表单内容
 * @param {Object}
 */
export const getOneByBizId = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formContent/getOneByBizId`, params);
};
/**
 * @description 获取当前行的节点按钮
 * @param {Object}
 */
export const getButtonList = (params = {}) => {
  return http.get(`${prefix}/outside/pc/processor/flow/list/button`, params);
};
/**
 * @description 获取流程按钮
 * @param {Object}
 */
export const getFlowPermiss = (params = {}, isNew = false) => {
  return http.get(`${prefix}${isNew ? '/outside/pc/processor/flow/button/new': '/outside/pc/processor/flow/button'}`, params);
};
/**
 * @description 获取拒绝回退节点状态
 * @param {Object}
 */
export const getNodes = (params = {}) => {
  return http.get(`${prefix}/outside/pc/processor/back/nodes`, params);
};
/**
 * @description 完成流程
 * @param {Object}
 */
export const finishedFlow = (params = {}) => {
  return http.post(`${prefix}/outside/pc/processor/finished`, params);
};

/**
 * @description 开始流程
 * @param {Object}
 */
export const startProcess = (params = {}) => {
  return http.get(`${prefix}/outside/pc/processor/start`, params);
};

/**
 * @description 流程日志
 * @param {Object}
 */
export const processLog = (params = {}, isNew = false) => {
  return http.get(`${prefix}${isNew ? '/outside/pc/processor/flow/log/new': '/outside/pc/processor/flow/log'}`, params);
};

/**
 * @description 获取导出列信息
 * @param {Object}
 */
export const getAllNodeField = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/getAllNodeField`, params);
};

/**
 * @description 获取导入模板
 * @param {Object}
 */
export const importTemplate = (params = {}) => {
  return http.get(`${prefix}/outside/pc/form/excel/import/template`, params);
};

/**
 * @description 导入数据
 * @param {Object}
 */
export const excelsImport = (params = {}) => {
  return http.post(`/excels/paas/form/import`, params);
};
/**
 * @description 获取流程状态以及流程对应的数量
 * @param {Object}
 */
export const statusNum = (params = {}) => {
  return http.post(`${prefix}/outside/pc/processor/status/search/num`, params);
};

/**
 * @description 获取流程状态以及流程对应的数量
 * @param {Object}
 */
export const statusNumPrev = (params = {}) => {
  return http.get(`${prefix}/outside/pc/processor/status/num`, params);
};

/**
 * @description 获取当前下拉菜单的选项内容
 * @param {Object}
 */
export const getRelatedOption = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formField/getSelectInfo`, params);
};

/**
 * @description 获取需要自动填充的单行文本的值
 * @param {Object}
 */
export const getRelatedTextValue = (params = {}) => {
  return http.post(
    `${prefix}/outside/pc/formField/getRelatedTextValue`,
    params
  );
};

/**
 * 获取所有表单模板 - 字段
 * @param {Object} params - 参数
 * @returns Promise<Team>
 */
export const getAllFieldList = (params = {}) =>
  http.get(`${prefix}/outside/pc/formField/getAllFieldList`, params);

/**
 * 获取所有表单模板
 * @param {*} params
 * @returns Promise<Team>
 */
export const getAllTemplateList = (params = {}) =>
  http.get(`${prefix}/outside/pc/formField/getAllTemplateList/v2`, params);

/**
 * 根据表单模板 ID 获取表单模板字段
 * @param params 参数
 * @returns
 */
export const getFieldsByTemplate = (params = {}) =>
  http.get(`${prefix}/outside/pc/formField/getFieldsByTemplate`, params);

/**
 * 查询当前表单中所有下拉选项的关联的字段
 * @param {*} params
 * @returns Promise<Team>
 */
export const getRelatedFieldList = (params = {}) =>
  http.get(`${prefix}/outside/pc/formField/getRelatedFieldList`, params);

/**
 * @description 获取产品子表单可以使用的产品字段
 */
export const getProductFieldList = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/getProductFieldList`, params);
};

/**
 * @description 重置分享弹窗中的密码
 * @param {Object}
 */
export const resetPwd = (params = {}) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/pwd/reset`,
    params
  );
};

/**
 * @description 获取分享弹窗中的密码
 * @param {Object}
 */
export const getDialogInfo = (params = {}) => {
  return http.post(`${prefix}/outside/pc/form/admin/outer/share`, params);
};

/**
 * @description 获取物流公司列表
 * @param {Object}
 */
export const getLogisticsCompanyList = async (params = {}) => {
  const listCache = localStorage.getItem('allLogisticsCompany');
  if (listCache) {
    // 有缓存 直接拿缓存的数据
    return JSON.parse(listCache);
  }
  const res = await http.get(
    `${proxyMap.paas}/outside/pc/logistics/courier-company/list`,
    params
  );
  const list = await response(res);
  localStorage.setItem('allLogisticsCompany', JSON.stringify(list));
  return list;
};

/**
 * 查询快递额度
 */
export const getLogisticsQuotaDetail = async (params = {}) => {
  const res = await http.get(
    `${proxyMap.paas}/outside/pc/logistics/quota/detail`,
    params
  );
  const data = await response(res);
  const {
    totalQuota,
    totalUse,
    isDailyLimit,
    dailyQuotaLimit,
    todayUse
  } = data;
  return totalUse < totalQuota && (!isDailyLimit || todayUse < dailyQuotaLimit);
};

export const getLogisticsDetail = async (params = {}) => {
  const res = await http.get(
    `${proxyMap.paas}/outside/pc/logistics/query`,
    params
  );
  return response(res);
};

/**
 * @description 获取processId
 * @param {String} processorInstanceId - 流程实例id
 */
export const getProcessId = (params='') => {
  return http.post(`${prefix}/outside/pc/processor/flow/info?formContentId=${params}`);
};

/**
 * @description 获取已经提交的产品的子表单的内容
 * @param {String} templateBizId - 表单模板id
 * @param {String} contentBizId - 表单内容id
 * @param {String} curFieldName - 质检结果字段fieldName
 */
export const getInitQualityResultList = (params = {}) => {
  return http.post(
    `${prefix}/outside/pc/subFormContent/getInitQualityResultList`,
    params
  );
};

/**
 * @description 入库申请单
 * @param {String} processorInstanceId - 流程实例id
 */
export const getInstockList = (params = {}) => {
  return http.get(`${stockPrefix}/outside/in/warehouse/flow/view/paas`, params);
};

/**
 * @description 出库申请单
 * @param {String} processorInstanceId - 流程实例id
 */
export const getOutstockList = (params = {}) => {
  return http.get(
    `${stockPrefix}/outside/out/warehouse/flow/view/paas`,
    params
  );
};

/**
 * @description 缺货申请单
 * @param {String} processorInstanceId - 流程实例id
 */
export const getLackstockList = (params = {}) => {
  return http.get(`${stockPrefix}/outside/backorder/findBySourceId`, params);
};


/**
 * @description 删除应用表单
 * @param {String} processorInstanceId -
 */
export const delFormField = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/logicDeleteTemplate`, params);
};

/**
 * @description 判断一下这个表单是否被进行中的流程引用着
 * @param {String} processorInstanceId -
 */
export const checkFormFieldState = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/checkTemplateState`, params);
};

/**
 * @description 删除应用
 * @param {String} processorInstanceId -
 */
export const delAppForms = (params = {}) => {
  return http.get(`${prefix}/outside/pc/app/deleteOneApp`, params);
};

/**
 * @description 判断一下这个应用是否被进行中的流程引用着
 * @param {String} processorInstanceId -
 */
export const checkDelAppState = (params = {}) => {
  return http.get(`${prefix}/outside/pc/app/checkAppState`, params);
};

/**
 * @description 获取表单相关的权限
 * @param {String} processorInstanceId -
 */
export const getAuthTemplateList = (params = {}) => {
  return http.get(`${prefix}/outside/pc/form/auth/getCurUserFormAuth`, params);
};
/**
 * @description 在table list列表的时候操作按钮判断是否有必填项
 * @param {String} params -
 */
export const fetchCheckListItemIsRequired = (params = {}) => {
  return http.get(`${prefix}/outside/pc/formField/field/required`, params);
};
/*
* @description 计算公式计算值
* @param {String} params -
*/
export const fetchFormulCaluation = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formContent/getFormulaVale`, params);
};


export const fetchGetShowPrint = (params = {}) => {
  return http.get(`${prefix}/outside/pc/form/admin/print/setting/button`, params);
};

export const getPrintTemplateSetting= (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/field`,
    params
  );
};

/**
 * @description 查看满意度问卷列表
 * @param {String} formContentId - 表单内容id
 */
export const getEvaluates = (params = {}) => {
  return http.get(`${prefix}/outside/pc/returnVisit/getEvaluates`, params);
};



/**
 * @description 查询备件出库申请单列表
 */
export const getOutSparepartStockList = (params = {}) => {
  return http.post(`${prefix}/outside/pc/sparepart/out/list`, params);
};

export const getInSparepartStockList = (params = {}) => {
  return http.post(`${prefix}/outside/pc/sparepart/in/list`, params);
};

export const getPrintTemplateBizId= (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/buttonBizId`,
    params
  );
};

export const getPrintSettingButtonNewApi = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/buttonNew`,
    params
  );
};

/**
 * @description 获取子表单数据源的数据
 * @param {String} formContentId - 表单内容id
 * @param {String} fieldName -  父表单的fieldName
 * @param {String} keyword - 高级搜索关键字
 * @param {String} pageNum - 页码
 * @param {String} pageSize - 每页条数
 */

export const getRequestApi = (params = {}) => {
  return http.post(`${prefix}/outside/pc/formContent/api/request`, params);
};

/**
 * @description - 查询服务商列表
 * @param {Object} params - 参数
 */
export function getProviderData(params: any) {
  return http.post('/api/app/outside/provider/getProviderData', params);
}

/**
 * @description - 获取编辑参数nodeInstanceId
 * @param {Object} params - 参数
 */
export function getCanEdit(params: any) {
  return http.get(`${prefix}/outside/pc/processor/getNodeInstanceId`, params);
}

/**
 * @description 清空子表单默认数据
 * @see http://yapi.shb.ltd/project/3448/interface/api/38234
 */
export function clearSubFormDefaultValue(params: ClearSubFormDefaultValueParams): Promise<ClearSubFormDefaultValueResult> {
  return http.get(`${prefix}/outside/pc/subFormContent/clearSubFormDefaultValue`, params);
}

/**
 * @description 表单设置 获取子表单默认数据
 * @see http://yapi.shb.ltd/project/3448/interface/api/38246
 */
export function getSettingSubFormDefaultValue(params: GetSettingSubFormDefaultValueParams): Promise<GetSettingSubFormDefaultValueResult> {
  return http.get(`${prefix}/outside/pc/subFormContent/getSettingSubFormDefaultValue`, params);
}

/**
 * @description 新建表单 获取子表单默认数据
 * @see http://yapi.shb.ltd/project/3448/interface/api/38238
 */
export function getSubFormDefaultValue(params: GetSubFormDefaultValueParams): Promise<GetSubFormDefaultValueResult> {
  return http.get(`${prefix}/outside/pc/subFormContent/getSubFormDefaultValue`, params);
}

/**
 * @description 获取子表单 导入 默认数据
 * @see http://yapi.shb.ltd/project/3448/interface/api/48222
 */
export function subFormUploadDefaultValue(params: SubFormUploadDefaultValueParams): Promise<SubFormUploadDefaultValueResult> {
  return http.post(`${prefix}/outside/pc/form/excel/uploadDefaultValue`, params);
}

/**
 * @description 下载子表单 导入 默认数据
 * @see http://yapi.shb.ltd/project/3448/interface/api/49266
 */
export function downloadSubFormDefaultValue(params: DownloadSubFormDefaultValueParams): Promise<DownloadSubFormDefaultValueResult> {
  return (
    http.post(
      `${prefix}/outside/pc/form/excel/downloadSubFormDefaultValue`,
      params,
      true,
      {
        responseType: 'blob'
      }
    )
  );
}

/**
 * @description 表单撤回
 * @see https://yapi.shb.ltd/project/375/interface/api/56208
 */
export function formBackToMe(params: any) {
  return http.post(`${prefix}/outside/pc/processor/backToMe`, params);
}

/**
 * @description 表单加签
 * @see https://yapi.shb.ltd/project/375/interface/api/56194
 */
export function formCountersign(params: any) {
  return http.post(`${prefix}/outside/pc/processor/flow/countersign`, params);
}
/**
 * @description 复制应用表单
 * @param {String} processorInstanceId -
 */
export const copyFormField = (params = {}) => {
  return http.post(`${prefix}/outside/pc/processor/flow/copy`, params);
};


/**
 *根据相关bizid去获取对应的sn id
 * @param params
 */
export const orderBizIdGetSnNumber = (params= {})=> {
  return http.get(`${prefix}/outside/pc/formContent/getSnByContentBizId`, params);
};

/**
 * @description 暂停流程
 * @param {Object}
 */
export const pauseFlowContent = (params = {}, isNew = false) => {
  return http.post(`${prefix}/outside/pc/processor/pause`, params);
};



/**
 * @description 暂停流程
 * @param {Object}
 */
export const recoverFlowContent = (params = {}, isNew = false) => {
  return http.post(`${prefix}/outside/pc/processor/pause/recover`, params);
};

