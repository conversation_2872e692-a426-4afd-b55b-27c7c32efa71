import http from '@src/util/http';
let prefix = '/api/paas';

/**
 * @description 获取所有其他关联模块的表单字段
 */
export const getCardAllFieldList = (params: {} | undefined) => {
  return http.get(`${prefix}/outside/pc/cardInfo/getAllFieldList`, params);
};

/**
 * @description 获取某个表单的组件设置
 */
export const getCardInfo = (params: {} | undefined) => {
  return http.get(`${prefix}/outside/pc/cardInfo/getListByFormBizId`, params);
};

/**
 * @description 编辑附加组件设置
 */
export const createCardSetting = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/cardInfo/batchCreateSetting`, params);
};

/**
 * @description 获取关联数据列表
 */
export const getRelatedDataList = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/cardInfo/getRelatedDataList`, params);
};

/**
 * @description 删除单个的关联数据
 */
export const deleteRelatedData = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/cardInfo/deleteRelatedData`, params);
};

/**
 * @description 添加关联数据
 */
export const batchCreateRelatedData = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/cardInfo/batchCreateRelatedData`, params);
};


/**
 * @description 查询关联组件列表
 */
export const getFormCardSettingList = (params = {}) => {
  return http.get(`${prefix}/outside/pc/card/get`, params);
};
/**
 * @description 查询关联组件列表详情
 */
export const getFormCardSettingListItem = (params = {}) => {
  return http.get(`${prefix}/outside/pc/card/get/bizId`, params);
};

/**
 * @description 更新节点规则
 */
export const updateCardRule = (params = {}) => {
  return http.post(`${prefix}/outside/pc/card/update`, params);
};

/**
 * @description 调用连接器删除数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36744
 */
export function deleteConnectorData(params = {}) {
  return http.post(`${prefix}/api/application/outside/call/deleteCall`, params);
}

export function deleteConnectorDataFromPaasItem(params: Record<string, string>) {
  return http.post(`${prefix}/outside/pc/card/delete?cardBizId=${params?.cardBizId}`, params);
}

// 连接器重命名
export function updateConnectorName(params: any): Promise<any> {
  return http.post(`/api/application/outside/connector/updateConnectorName`,params);
}

// 获取连接器名称
export function getConnectorName(params: any): Promise<any> {
  return http.post(`/api/application/outside/connector/getConnectorName`,params);
}





