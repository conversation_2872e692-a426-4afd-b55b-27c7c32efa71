import http from '@src/util/http';
import useFormMultiLanguage from '@src/hooks/useFormMultiLanguage';
import { GrayFunctionEnum } from '@model/grayFunction';


/**
 * 地址组件-获取所有国家
 */
function getCountries (params) {
  return http.get('/api/workbench/outside/area/countries/get', params);
}

/**
 * 地址组件-获取国家/地区下的城市信息
 */
function getAreaDivisionsInfo (params) {
  return http.get('/api/workbench/outside/area/divisionsInfo/get', params);
}

export function getBatchArea(params) {
  return http.post(`/api/workbench/outside/area/divisionsInfo/getBatch`, params);
}

/**
 * 地址预览-判断当前ip
 */
export async function checkIp() {
  // 国际化灰度没开默认国内
  if(!useFormMultiLanguage[GrayFunctionEnum.INTERNATIONAL]) return true;
  let is = JSON.parse(sessionStorage.getItem('isDomestic'));
  // 没有缓存判断ip
  if(is === null){
    const { status, data } = await http.get('/api/workbench/outside/area/checkIp');
    is = !(status === 0 && !data);
    sessionStorage.setItem('isDomestic', JSON.stringify(is));
  }

  return is;
}


/**
 * 地址组件-解析地址经纬度，目前系统使用了三种地图，坐标系不一样，导致使用不同地图会有偏差，接口做了转换
 * https://publink.yuque.com/staff-ubm6dv/sufqv3/lqmgew5t72yb3nai
 */
function getLngLat (params) {
  return http.get('/api/workbench/outside/area/getLngLat', params);
}


/**
 * 经纬度转换-火星转百度,使用百度地图时，需要将火星坐标系转成百度坐标系
 * https://publink.yuque.com/staff-ubm6dv/sufqv3/lqmgew5t72yb3nai
 */
function gcj02ToBd09 (params) {
  return http.post('/api/workbench/outside/area/gcj02ToBd09', params);
}

export {
  getCountries,
  getAreaDivisionsInfo,
  getLngLat,
  gcj02ToBd09
};