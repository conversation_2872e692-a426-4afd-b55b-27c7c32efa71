import http from '@src/util/http';
let prefix = '/api/paas';

/**
 * @des 获取单个看板list的详情
 * @param {any} params={}
 * @returns {any}
 */
export const fetchListCardViewDetail = (params= {})=> {
  return http.get(`${prefix}/outside/pc/dataView/get`, params);
};


/**
 * @des 获取相关看板的列表
 * @param {any} params={}
 * @returns {any}
 */
export const fetchListCardViewList = (params= {})=> {
  return http.post(`${prefix}/outside/pc/dataView/getList`, params);
};



/**
 * @des 添加对应的看板
 * @param {any} params={}
 * @returns {any}
 */
export const createListCardView = (params= {})=> {
  return http.post(`${prefix}/outside/pc/dataView/add`, params);
};


/**
 * @des 更新对应的看板
 * @param {any} params={}
 * @returns {any}
 */
export const updateListCardView = (params= {})=> {
  return http.post(`${prefix}/outside/pc/dataView/edit`, params);
};



/**
 * @des 删除对应的看板
 * @param {any} params={}
 * @returns {any}
 */
export const deleteListCardView = (params= {})=> {
  return http.get(`${prefix}/outside/pc/dataView/remove`, params);
};


/**
 * @des 根据分组查对应的看板每列的数据
 * @param {any} params={}
 * @returns {any}
 */
export const getListForCardViewDataList = (params= {})=> {
  return http.post(`${prefix}/outside/pc/dataView/searchBoardViewData`, params);
};
