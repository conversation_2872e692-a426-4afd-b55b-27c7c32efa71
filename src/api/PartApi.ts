import http from '@src/util/http';
import { proxyMap } from './config';

const prefix = proxyMap.paas;
let prefixAppPath = '/api/part';


/**
 * 获取出入库记录
 * @param {Object} params - 参数
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 页面大小
 * @returns Promise<Team>
 */
export function approveList(params: {} | undefined) {
  return http.get('/partV2/approve/approveList/list', params);
}
/**
 * 备件品类列表
 * @param {*} params = {
 * keyWord  模糊搜索关键字
 * sparepartTypes 备件类型
 * }
 */
export function partTypeList(params: {} | undefined) {
  return http.post(
    `${prefix}/outside/pc/sparepart/page`,
    params
  );
}
/**
 * @param {*} 服务项目列表
 */
export function serviceList(params: {} | undefined) {
  return http.post(`/task/service/list`, params);
}

/**
 * @param {*} 仓库
 */
export function getSparepartRepertory(params: {} | undefined) {
  return http.post(`${prefix}/outside/pc/sparepart/repertory/page`, params);
}

/**
 * @description 获取备件配置信息
 */
export function getSparepartConfig(params = {}) {
  return http.post('/partV2/repertory/sparepartConfig', params);
}
/**
 * @description 获取目标仓库所有仓库
 */
export function getallRepertory(params = {}) {
  return http.get('/partV2/repertory/allRepertory', params);
}

/**
 * @description 获取合同列表
 */
export function getContractList(params = {}) {
  return http.post('/api/elasticsearch/outside/es/contract/listV2', params);
}