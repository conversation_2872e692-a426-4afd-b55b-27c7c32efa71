import http from '@src/util/http';

/**
 * @des 获取高级搜索选择列导出等地方的前端缓存
 * @param {String} type 唯一标识
 */
export function getServerCachApi(type:string){
  return http.get(`/userGuide/queryUserGuideList?type=${type}`)
}

/**
 * @des 高级搜索选择列导出等地方的前端缓存
 * @param {String} type 唯一标识
 * @param {Number} step 写死的 1
 * @param {String} isComplete 写死的 1
 * @param {String} userConfig 缓存内容, JSON字符串
 */
export function creatServerCachApi (params:any){
  return http.post('/userGuide/addOrUpdate', params)
}


/**
 * @des 修改当前用户关于引导通知的缓存
 * @param {String} type 引导类型
 * @param {Number} step 当前步骤数量
 * @param {Number} isComplete 引导是否完成 0 完成 1未完成 -1引导已失效
 * @param {String} desc 引导描述
 */
export function setUserGuideStorage(params = {}){
  return http.post('/userGuide/completeUserGuide', params)
}


/**
 * @des 获取当前用户某个引导通知的缓存
 * @param {String} type 引导类型
 */
export function getUserGuideStorageFor(params= {}){
  return http.get('/userGuide/queryUserGuide', params)
}



