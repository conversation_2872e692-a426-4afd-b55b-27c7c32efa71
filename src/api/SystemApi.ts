import http from '@src/util/http';

/**
 * @des 获取按钮绑定的触发器
 */
export function getButtonConcatTriggerList (params: {} | undefined) {
	return http.post('/api/application/outside/trigger/getTriggerByBizTypeInfo', params);
}

/**
 * @des 保存自定义的页面按钮
 * @see https://yapi.shb.ltd/project/5788/interface/api/59867
 */
export function setDataForButtonSet (params: {} | undefined) {
	return http.post('/api/voice/outside/container/button/updateButton', params);
}

/**
 * @des 获取对应应用的自定义按钮
 * @see https://yapi.shb.ltd/project/5788/interface/api/59885
 */
export function getDataForButtonSet (params: {} | undefined) {
	return http.post('/api/voice/outside/container/button/getButtonByModule', params);
}