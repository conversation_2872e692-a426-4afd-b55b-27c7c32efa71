
import http from '@src/util/http';
import { proxyMap } from './config';

const stockPrefix = proxyMap.warehouse;

/**
 * @description 获取物料表单
 * @param {String}  
 */
export const getMaterialFields= (params = {}) => {
  return http.get(`${stockPrefix}/outside/material/fields/paas`, params);
};


/**
 * @description 获取物料表单列表
 * @param {String}  
 */
export const getMaterialList= (params = {}) => {
  return http.post(`${stockPrefix}/outside/material/list`, params);
};

