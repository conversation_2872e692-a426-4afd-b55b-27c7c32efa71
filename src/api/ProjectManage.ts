import http from '@src/util/http';

const prefix = '/api/project/outside/project';


// 匹配任务设置规则的表单字段 回填
export function insertSelectCallByAdditionalId(params: {}) {
  return http.post(`/api/application/outside/call/insertSelectCallByAdditionalId`, params);
}

/**
 * @description 新建工单类型的任务 提交之前校验前置任务是否完成
 */
export function checkPreTaskIsComplete(params: {}) {
  return http.post(`${prefix}/task/checkPreTaskIsComplete`, params)
}

// 获取任务详情
export function taskDetailPro(params: {}) {
  return http.post(`${prefix}/task/detail`, params);
}

// 获取按钮权限
export function projectTypeRoleBtn(params: {}) {
  return http.get(`${prefix}/auth/button`, params);
}

/**
 * 任务管理详情更新进度
 * @param params
 */
export function updateTaskManageProgress(params: {}) {
  return http.post(`${prefix}/task/updateProgress`, params)
}

/**
 * 项目类型表单字段
 * @param params
 */
export function getProjectTypeField(params: {}) {
  return http.post(`/api/project/outside/field/list`, params);
}

/**
 * 工作日志列表
 */
export function workRecordList(params: {}) {
  return http.post(`${prefix}/work/record/list`, params);
}

/**
 * 工作日志新建
 */
export function workRecordCreate(params: {}) {
  return http.post(`${prefix}/work/record/create`, params);
}

/**
 * 工作日志更新
 */
export function workRecordUpdate(params: {}) {
  return http.post(`${prefix}/work/record/update`, params);
}

/**
 * 工作日志删除
 */
export function workRecordDelete(params: {}) {
  return http.post(`${prefix}/work/record/delete`, params, false);
}

/**
 * 获取工作日志配置
 */
export function getWorkRecord(params: {}) {
  return http.get(`${prefix}/config/workRecord`, params);
}

/**
 * @description 更新工单类型的任务
 */
export function taskEditTask(params: {}) {
  return http.post(`${prefix}/task/updateTask`, params)
}

// 项目详情里面的新建任务前置列表任务接口
export function queryPreTaskListPro(params: {}) {
  return http.post(`${prefix}/task/queryPreTaskList`, params);
}

// 任务类型
export function queryTaskType(params = {}) {
  return http.post(`${prefix}/config/task/queryAvailableTaskType`, params);
}

/**
 * 跳转paas获取一些paas信息
 */
export function getNodeInstanceRelation(params: {}) {
  return http.get('/api/paas/outside/pc/template/getNodeInstanceRelation', params);
}