import http from '@src/util/http';
import { proxyMap } from "@src/api/config";
const prefix = proxyMap.paas;
/**
 * 将字符串地址解析成结构化数据
 * @param {Object} params - 参数
 * @param {String} detailAddress - 地址字符串
 * @returns {*}
 */
export function parseAddress(params: any) {
  return http.get('/address/resolution', params);
}

/**
 * 获取灰度控制结果
 * @param {Object} params - 参数
 * @returns {*}
 */
export function getTenantGray(params: Record<string, string>) {
  return http.get(`${prefix}/outside/pc/form/auth/getGrayFunction`, params);
}

/**
 * 获取灰度控制-无参数
 * @param {Object} params - 参数
 * @returns {*}
 */
export function getAllGrayFunctionApi() {
  return http.get(`${prefix}/outside/pc/form/auth/getAllGrayFunction`);
}

/**
 * 描述 获取语言列表
 * @date 2022-07-27
 * @param {any} params={}
 * @returns {any}
 */
export function getLanguages(params = {}) {
  return http.get('/api/workbench/outside/form/getLanguages', params);
}

/**
 * 描述 获取币种列表
 * @param {string} params - 需要使用的语言
 */
export function getCurrencyAll(params = '') {
  return http.post('/api/workbench/outside/currency/list', params, false);
}

/**
 * 描述 获取单个银行汇率
 * @param {string} params - 参数
 * @param {string} params.source
 * @param {string} params.target
 */
export function getExchangeRate(params = '') {
  return http.post('/api/workbench/outside/exchange/rate/query', params);
}

/**
 * 描述 获取多个汇率列表
 * @param {string} params - 参数
 * @param {array} params.sourceList
 * @param {string} params.target
 */
export function getExchangeRateList(params: any = {}) {
  return http.post(`/api/workbench/outside/exchange/rate/queryBatch`, params);
}

/**
 * 相关js控件的控制
 * @param params
 */
export function getJsComponentValidate(params: any = {}) {
  return http.post(`${prefix}/outside/pc/cookieValidate/js`, params);
}

/**
 * 获取会员卡列表
 * @param params
 */
export function getMembershipList(params: any = {}) {
  return http.post(`/api/customer/outside/membership/search`, params);
}

/**
 * 获取会员卡对应的商品列表
 * @param params
 */
export function getGoodsList(params: any = {}) {
  return http.get(`/api/customer/outside/membership/card/getAllCardType`, params);
}

/**
 * 获取会员卡对应的商品列表-分页
 * @param params
 */
export function queryCardTypeRelGoods(params: any = {}) {
  return http.post(`/api/customer/outside/membership/card/queryCardTypeRelGoods`, params);
}

/**
 * 获取大文件的检查
 * @param params
 */
export function getBigFileCheck(params: any = {}) {
  return http.get(`/files/auth/bigFile/access/check`, params);
}

export async function exportCascaderOptions(params: any){
  await http.downloadFile('/excels/multileve/menu/export', params, 'POST');
}
