

import http from '@src/util/http';
import { proxyMap } from './config';

const stockPrefix = proxyMap.warehouse;
const paasPrefix = proxyMap.paas;

/**
 * @description 获取默认出库的仓库和仓位
 * @param {String}
 */

export const getWarehouseApi= (params = {}) => {
  return http.get(`${stockPrefix}/outside/warehouse/paas/default/out/warehouse`, params);
};

// 获取物料列表
export function materialList(params = {}) {
  return http.post(`${stockPrefix}/outside/material/list`, params);
}

// 获取跟库存相关的物料列表
export function getList(params = {}) {
  return http.post(`${stockPrefix}/outside/inventory/list`, params);
}

export function getWarehouseFields(params = {}) {
  return http.get(`${stockPrefix}/outside/setting/material/allFields`, params);
}

// 获取出库类型列表
export function getOutWarehouseTypeList(params = {}) {
  return http.get(`${stockPrefix}/outside/out/warehouse/type/setting/list/paas`, params);
}

// 获取入库类型列表
export function getInWarehouseTypeList(params = {}) {
  return http.get(`${stockPrefix}/outside/setting/in/warehouse/type/list/paas`, params);
}

// 获取备件出库类型列表
export function getOutSparepartTypeList(params = {}) {
  return http.get(`${paasPrefix}/outside/pc/sparepart/out/type`, params);
}

// 获取备件入库类型列表
export function getInSparepartTypeList(params = {}) {
  return http.get(`${paasPrefix}/outside/pc/sparepart/in/type`, params);
}

// 检查物料是否存在仓库中
export function fetchCheckMaterialInInventory(params = {}) {
  return http.post(`${stockPrefix}/outside/inventory/checkMaterial`, params);
}

// 检查备件与仓库的关联
export function fetchCheckSparepartRepertory(params = {}) {
  return http.post(`${paasPrefix}/outside/pc/sparepart/repertory/check`, params);
}

// 获取商米物料信息
export function getSunmiMaterialInfo(params = {}) {
  return http.post(`/api/middleware/outside/sunmi/get/mat/price`, params);
}

// 商米校验库存数量是否足够
export function sunmiInventoryConfirm(params = {}) {
  return http.post(`/api/middleware/outside/sunmi/sap/inventory/confirm`, params);
}

// 获取仓库列表-分页
export function getWarehouseList(params = {}) {
  return http.post(`${stockPrefix}/outside/warehouse/list/paas/search`, params);
}

// 获取仓位列表-分页
export function getWarehousePositionList(params = {}) {
  return http.post(`${stockPrefix}/outside/warehousePosition/search/paas/list`, params);
}

// 通过sn列表获取物料信息
export function getMaterialBySnList(params = {}) {
  return http.post(`${stockPrefix}/outside/material/listBySn`, params);
}

// 查询产品类型
export function getCatalogList(params = {}) {
  return http.post('/api/customer/outside/pc/catalog/simple/list', params);
}