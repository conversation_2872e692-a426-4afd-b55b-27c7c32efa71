import http from '@src/util/http';
let prefix = '/api/paas';

/**
 * @description 表单规则配置开关
 */
export const ruleFormSwitch = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/switch`,
    params
  );
};

/**
 * @description 表单配置编辑
 */
export const editRuleForm = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/save`,
    params
  );
};

/**
 * @description 获取表单配置字段
 */
export const getRuleForm = (params: {} | undefined) => {
  return http.get(`${prefix}/outside/pc/form/admin/outer/setting/get`, params);
};

/**
 * @description 获取规则配置表单字段权限列表
 */
export const getRuleFormField = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/outer/setting/field`,
    params
  );
};

/**
 * @description 保存规则配置表单字段权限列表
 */
export const saveRuleFormField = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/field/save`,
    params
  );
};

/**
 * @description 查询PaaS表单外链配置
 */
export const getLinkDefaultValue = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/link/defaultValue/get`,
    params
  );
};

/**
 * @description 保存PaaS表单外链配置
 */
export const saveLinkDefaultValue = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/link/defaultValue/save`,
    params
  );
};

/**
 * @description 添加手机号
 */
export const phoneSave = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/phone/save`,
    params
  );
};

/**
 * @description 获取手号列表
 */
export const getPhoneList = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/outer/setting/phone/list`,
    params
  );
};

/**
 * @description 删除手机号
 */
export const deletePhone = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/outer/setting/phone/delete`,
    params
  );
};

export const editFormAuth = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/auth/editFormAuth`,
    params
  );
};


export const getEditFormAuth = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/auth/getFormAuth`,
    params
  );
};

export const getAllPrintTemplateList = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/all`,
    params
  );
};
export const getPrintTemplateDetail = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/get`,
    params
  );
};

export const getPrintTemplateField = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/get`,
    params
  );
};

export const updatePrintTemplate = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/print/setting/update`,
    params
  );
};


export const addPrintTemplate = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/print/setting/save`,
    params
  );
};

export const delPrintTemplate = (params: {} | undefined) => {
  return http.post(
    `${prefix}/outside/pc/form/admin/print/setting/delete`,
    params
  );
};
export const getPrintTemplateNode = (params: {} | undefined) => {
  return http.get(
    `${prefix}/outside/pc/form/admin/print/setting/node/all`,
    params
  );
};

/**
 * @description 问券方案列表
 */
export const getSatisfactionList = (params: {} | undefined) => {
  return http.get('/api/customer/outside/satisfactionConfig/getConfigGroup', params);
};

/**
 * @description 创建满意度回访规则
 */
export const createSatisfactionSetting = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/returnVisit/batchCreateSetting`, params);
};

/**
 * @description 获取某个表单的回访规则列表
 */
export const getSatisfactionSettingList = (params: {} | undefined) => {
  return http.get(`${prefix}/outside/pc/returnVisit/getListByFormBizId`, params);
};

/**
 * @description 获取某个表单的回访规则列表
 */
export const sendMsg = (params: {} | undefined) => {
  return http.post(`${prefix}/outside/pc/returnVisit/sendMsg`, params);
};

/**
 * @description 获取打印模板列表
 */
export const printTemplateListApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/list`, params);
};

/**
 * @description 创建打印模板列表
 */
export const printTemplateCreateApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/create`, params);
};

/**
 * @description 更新打印模板
 */
export const printTemplateUpdateApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/update`, params);
};

/**
 * @description 获取打印内容详情
 */
export const printTemplateGetOneApi = (params: {} | undefined) => {
  return http.get('/api/app/outside/print/template/getOne', params);
};

/**
 * @description 删除打印模板
 */
export const printTemplateDeleteApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/delete`, params);
};

/**
 * @description paas详情页获取打印模板内容
 */
export const printTemplateSearchApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/search`, params);
};

/**
 * @description 初始化系统打印模板
 */
export const printSettingInitPrintTemplateApi = (params: {} | undefined) => {
  return http.get('/api/paas/outside/pc/form/admin/print/setting/initPrintTemplate', params);
};

/**
 * @description paas获取打印模板PDF文件
 */
export const printTemplatePreviewApi = (params: {} | undefined) => {
  return http.post(`/api/app/outside/print/template/preview`, params);
};