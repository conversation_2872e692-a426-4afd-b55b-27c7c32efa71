/**
 * 页面跳转
 */
/* utils */
import { openAccurateTab } from "@src/platform";
import platform from '@src/platform';
import router from '@src/router';
/* type */
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'

/**
 * 跳转pass新建页面
 */
export function goToCreatePage(templateName, templateId, config = {}) {
  const paramsStr = Object.keys(config?.params ?? {}).map(key => `${key}=${config.params[key]}`).join('&');
  let url = `/template/edit?formId=${templateId}${paramsStr ? `&${paramsStr}` : ''}`;
  let fromId = window.frameElement?.getAttribute('id');

  if (!fromId) return router.push(url);
  platform.openTab({
    id: `create_view_${templateId}`,
    title: `新建${templateName}`,
    close: true,
    reload: true,
    fromId,
    url: `/paas/#${url}&noHistory=1${paramsStr ? `&${paramsStr}` : ''}`
  });
}

/**
 * 跳转智能质检列表
 */
export function openSmartQualityList(config) {
  const fromId = window?.frameElement?.getAttribute('id')
  openAccurateTab({
    type: PageRoutesTypeEnum.IntelligentQuality,
    fromId
  })
}