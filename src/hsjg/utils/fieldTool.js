/**
 * 表单工具
 */
import Vue from 'vue';
import { expandField } from "@src/component/form/util/index.ts";

/**
 * 获取某个字段field
 */
export function findField(fields, fieldName) {
  return fields.find(item => item.fieldName === fieldName);
}

/**
 * 判断表单是否包含该字段
 */
export function hasField(fields, fieldName) {
  return !!findField(fields, fieldName);
}

/**
 * 修改字段属性
 */
export function setFieldAttr(fields, fieldName, attr, value) {
  const field = findField(fields, fieldName);
  if (field) {
    Vue.set(field, attr, value);
  } else {
    console.error(`字段${fieldName}不存在`);
  }
}

/**
 * 设置表单值
 */
export function setFormValue(formValue, fieldName, value) {
  Vue.set(formValue, fieldName, value);
}


/**
 * 获取某个分组下所有的字段，包括分割线
 * @param {*} dividerFieldName 分割线fieldName
 * @param {*} hasSeparator 是否追加分割线
 * @param {*} fields
 */
export function getGroupField(fields, dividerFieldName, hasSeparator = true) {
  let groupField = [];
  let groupItem = {
    separator: "",
    children: [],
  };
  expandField(fields).forEach((curr, idx, arr) => {
    if (curr.formType === "separator") {
      groupField.push(groupItem);
      groupItem = {
        separator: "",
        children: [],
      };
      groupItem.separator = curr;
    } else {
      groupItem.children.push(curr);
    }
    if (idx === arr.length - 1) {
      groupField.push(groupItem);
    }
  });
  let groupFields = [];
  let dividerField = groupField?.find(
    (v) => v.separator.fieldName == dividerFieldName
  );
  if (dividerField) {
    if (hasSeparator) {
      groupFields = [dividerField.separator, ...dividerField.children];
    } else {
      groupFields = dividerField.children;
    }
  }
  return groupFields;
}