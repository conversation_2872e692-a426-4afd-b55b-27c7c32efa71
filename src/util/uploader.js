import { toArray } from 'pub-bbx-utils';
import {notification} from '@src/platform/message';
import Exception from '@model/Exception';
import http from '@src/util/http';
import { userCenterGetTokenInfo } from '@src/util/userCenter';
import { localStorageKey } from 'pub-bbx-global/lang/dist';
import { bigFileUploadGray } from '@src/util/grayInfo';
import { isLocalDev } from '@src/util/index';
import { OssUpload, OssType } from "pub-bbx-utils";

export const FILE_MAX_SIZE = 200 * 1024 * 1024; // 单位字节(Byte) 【原先10MB，需求1102改成200MB】
export const WIKI_FILE_MAX_SIZE = 200 * 1024 * 1024; // 200M
export const FILE_MAX_NUM = 9;
export const WIKI_FILE_MAX_NUM = 30; //知识库上传附件数量限制

export const fileTypeObj = {
  xlsx : {
    fileName : 'xlsx',
    errMsg : '只支持[xlsx]格式的文件，请重新选择'
  },
  imageForQa:{
    fileName : 'xlsx',
    errMsg : '只支持[xlsx]格式的文件，请重新选择'
  }
};

/** 
 * 验证文件是否符合以下条件
 * 1. 10m以内
 * 2. 有后缀名 
 */
export function validate(file, source){
  let fileName = file.name;

  if(!bigFileUploadGray()) {

    if(source == 'wiki') {
      // 验证文件大小
      if(file.size > WIKI_FILE_MAX_SIZE) return new Error(`文件[${fileName}]的大小超过200MB，系统暂不支持上传`);
    } else {
      // 验证文件大小
      if(file.size > FILE_MAX_SIZE) return new Error(`文件[${fileName}]的大小超过200MB，系统暂不支持上传`);
    }
  }
  
  
  
  // 验证文件类型
  let lastDotIndex = fileName.lastIndexOf('.');
  if(lastDotIndex < 0) return new Error(`[${fileName}]的文件类型未知，系统暂不支持上传`);
  
  return null;
}

/** 解析错误 */
function getError(xhr, action) {
  let msg;
  if (xhr.response) {
    msg = `${xhr.response.error || xhr.response}`;
  } else if (xhr.responseText) {
    msg = `${xhr.responseText}`;
  } else {
    msg = `fail to post ${action} ${xhr.status}`;
  }
  
  const err = new Error(msg);
  err.status = xhr.status;
  err.method = 'post';
  err.url = action;
  return err;
}

/** 解析返回值 */
function getBody(xhr) {
  const text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }
  
  try {
    return JSON.parse(text);
  } catch (e) {
    return text;
  }
}

/** 
 * 异步上传附件
 * 
 * @param {File} file - 待上传的文件
 * @param {string} action - 上传地址
 * @param {object} [options] - 参数
 * @param {(boolean | function)} [options.validateStorage] - 是否验证容量限制 
 * @param {boolean} [options.silence ] - 是否不显示提示
 */
export function upload(file, action, options = {}){
  if(isLocalDev()) {
    action = ('/serve' + action).replace(/\/\//g, '/');
  }
  return validateTenantStorage(options.validateStorage, file)
    .then(() => {
      let xhr = new XMLHttpRequest();
      let form = new FormData();
      
      return new Promise((resolve, reject) => {
        
        if (options.uploadParamString) {
          form.append(options.uploadParamString, file);
        } else {
          form.append('upload', file);
        }
        
        if(options.tenantId) {
          form.append('tenantId', options.tenantId);
        }
        if(options.type) {
          form.append('type', options.type);
        }
        
        if( options.module) {
          form.append('module', options.module);
        }
        
        xhr.onerror = error => reject(error)
        xhr.onload = function onload() {
          if (xhr.status < 200 || xhr.status >= 300) {
            return reject(getError(xhr, action));
          }
          resolve(getBody(xhr));
        };
        
        xhr.open('post', action, true);
        xhr.setRequestHeader('shb-language', localStorage.getItem(localStorageKey));

        // 用户中心设置请求token
        const tokenInfo = userCenterGetTokenInfo()
        if (tokenInfo.token && tokenInfo.shbVersion) {
          xhr.setRequestHeader('token', tokenInfo.token);
          xhr.setRequestHeader('shbVersion', tokenInfo.shbVersion);
        }
        
        xhr.send(form);
      });
    })
    .catch(error => {
      if(error.code == 10404){
        if(options.silence !== false) notification({
          type: 'error',
          title: '文件上传失败',
          message: error.message
        })
        
        // 这里要继续扔出错误，以便让调用者处理
        throw error;
      }
      
      console.error('upload caught:', error)
    })
}

function getErrorResult(file, error){
  if(error) console.error(error);
  
  return new Error(`[${file.name}]上传失败`)
}

export function getResult(file, msg, isBigFile = false){
  let data = msg.data || {};
  
  // 如果上传失败或没有文件id，按失败处理
  if(msg.status != 0 || !data.id) return getErrorResult(file)
  
  return {
    id: data.id,
    filename: data.fileName,
    originFileName: file.name,
    // 如果后端返回url,必须使用。如果后端不返回，需要拼接
    url: data.ossUrl || data.url || `/files/get?fileId=${data.id}`,
    fileSize: data.fileSizeStr,
    size:data.size,
    isBigFile
  }
}

export function getResultFile(data){
  
  return {
    ...data,
    id: data.id,
    filename: data.fileName,
    // 如果后端返回url,必须使用。如果后端不返回，需要拼接
    url: data.ossUrl || data.url || `/files/get?fileId=${data.id}`,
    fileSize: data.fileSizeStr,
    size:data.size,
    
  }
}

export function resetResultFile(data){
  return {
    ...data,
    fileName:data.filename,
    ossUrl:data.url,
    fileSizeStr:data.fileSize
  }
}

/** 
 * 上传文件, 会解析返回值
 * 上传失败会返回Error对象
 */
export function uploadWithParse(file, action = '/files/upload', options = {}){
  
  return upload(file, action, options)
    .then(msg => getResult(file, msg))
    .catch(error => getErrorResult(file, error))
}

/**
 * 相关上传直走oss
 * @param file
 * @param options 
 * @returns {Promise<unknown>}
 */
export async function uploadWithParseOss(file, options = {}) {
  return new Promise ((resolve, reject) => {
    const ossUpload = new OssUpload({ ossType: OssType.Ali, remote: true, remoteConfigUrl: '/files/bigFile/generateStsToken' });
    ossUpload.executeInitOssClient({ fileName: file.name, fileSize: file.size }, ossUpload.ossType, true).then(initRes=> {
      if(initRes) {
        const { files, callbackUrl = '' } = initRes?.data || {};
        // 通过接口返回的localPath赋值给file的localPath 作为oss上传的路径地址
        file.localPath = files.localPath;
        ossUpload.largeFileSliceUpload(file, {
          customCallback: {
            url: callbackUrl,
            customValue: {
              fileId: files?.id || ''
            }
          },
          ...options
        })
          .then(msg => resolve(getResult(file, msg, true)))
          .catch(error => reject(getErrorResult(file, error)));
        // });
      }
    }).catch(error=> {
      resolve(getErrorResult(file, error,  true));
    });
  });
}

/** 
 * 批量上传
 * @param {FileList} files - 待上传的文件
 * @param {string} action - 上传地址
 * @param {object} [options] - 参数
 * @param source
 * @param actionStorage
 * @param actionOptions - 上传文件接口额外的参数
 * @param {(boolean | function)} [options.validateStorage] - 是否验证容量限制
 * @param {boolean} [options.silence] - 是否不显示提示
 */
export function batchUploadWithParse({files = {}, action = '/files/upload', options = {}, source = '', actionStorage= '/files/remainingStorage', actionOptions = {}} = {}){
  return validateTenantStorage(options.validateStorage, files, actionStorage)
    .then(async () => {
      let fileArr = toArray(files);
      
      let validateRes = fileArr.map(item => validate(item, source)).filter(item => item instanceof Error);
      if(validateRes.length > 0){ // 文件验证失败
        return Promise.resolve(validateRes);
      }
      let promises = await fileArr.reduce((acc, file) => {
        file.size > FILE_MAX_SIZE && bigFileUploadGray() ?
          acc.push(uploadWithParseOss(file, options))
          :
          acc.push(uploadWithParse(file, action, {validateStorage: false, ...actionOptions}));
        return acc;
      }, []);
      return Promise.all(promises);
    })
    .then(result => {
      return result.reduce(
        (acc, item) => (item instanceof Error ? acc.error.push(item) : acc.success.push(item)) && acc,
        {success: [], error: []}
      )
    })
    .catch(error => {
      if(error.code == 10404){
        if(options.silence !== false) notification({
          type: 'error',
          title: '文件上传失败',
          message: error.message
        })
        
        // 这里要继续扔出错误，以便让调用者处理
        throw error;
      }
      
      console.error('batchUploadWithParse caught:', error)
    })
}

/** 验证租户存储容量 */
export function validateTenantStorage(option, args, actionStorage = '/files/remainingStorage'){
  if(typeof option == 'function') return option();
  if(option === false) return Promise.resolve();

  let files = args instanceof FileList ? Array.from(args) : [args];
  let total = files.reduce((acc, file) => (acc += file.size) && acc, 0)
  return http.get(actionStorage).then(result => {
    let surplus = result.data;
    if(surplus < (total / 1024 / 1024)) return Promise.reject(new Exception('您的附件存储空间已经用尽，请联系管理员进行空间扩容', 10404))
  })
}

const uploader = {
  upload,
  uploadWithParseOss,
  uploadWithParse,
  batchUploadWithParse,
  validate,
  FILE_MAX_SIZE,
  FILE_MAX_NUM,
  fileTypeObj,
  WIKI_FILE_MAX_NUM
}

export default uploader;