input,
textarea {
  padding: 3px 10px;
  line-height: 24px;
  border: 1px solid #e0e1e2;
  border-radius: 4px;
  margin: 0;
  outline: none;
  color: #333 !important;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover {
    border-color: $input-border-hover-color;
  }

  &:focus {
    border-color: $color-primary;
  }

  &::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #b3b7c1;
  }
  &:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #b3b7c1;
  }
  &::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #b3b7c1;
  }
  &:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #b3b7c1;
  }

  &:disabled{
    cursor: not-allowed;
    background-color: #f5f7fa;
    border-color: #e4e7ed !important;
    color: #c0c4cc !important;
  }
}

textarea {
  min-height: 32px;
  max-height: 320px;
  display: block;
  word-break: break-all;
}

.el-popover.pass-manager-main-popover {
  min-width: 100px;
  padding: 0;
  .popover-list  {
    padding-left: 0;
    margin-bottom: 0;
    padding-bottom: 5px;
    padding-top: 5px;
    li{
      height: 32px;
      line-height: 32px;
      box-sizing: border-box;
      padding-left: 12px;
      //background: $color-primary-light-1;
      cursor: pointer;
      &:hover{
        background: $color-primary-light-1;
      }
    }
  }
}

.el-popover.paas-app-popover{
margin-top: -6px !important;
}

.qlbt-operation-menu {
  z-index: 9999999 !important;
}


.base-comment {
  .base-file-info {
    flex: 1;
    text-overflow: clip;
    display: flex;
    justify-content: center;
    .cur-point {
      display: inline-block;
      width: calc(100% - 60px);
      overflow: hidden;
      vertical-align: middle;
      text-overflow: ellipsis;
    }
    .base-file-del {
      vertical-align: middle;
    }
  }
}

.user-card-triggle{
  color: $color-primary !important;
  cursor: pointer;
}

.el-tooltip__popper {
  max-width: 90%;
}

.flex-column{
  flex-direction: column;
}


/* 表单多列 start */
.bbx-form-item {
  width: 100%;
}
.bbx-form-item-last {
  // margin-right: 0px !important;
}

.bbx-cell-form-builder, .bbx-cell-form-view{
  .form-view-row{
    align-items: flex-start;
  }
  @for $i from 2 through 4 {
    .bbx-cell-form-box-#{$i} {
      @include dynamic-form-list('form-wrap', $i);
      //@include dynamic-form-list('bbx-form-cell-item', $i);
      .bbx-cell-form-box {
        @include dynamic-form-list('bbx-form-cell-item', $i);
      }



      .bbx-form-item-view-customer{
        @include dynamic-form-list('bbx-form-cell-item', $i);
      }
      .form-calendar-time{
        @include dynamic-form-list('bbx-form-cell-item', 2);
        .bbx-timezone-time-picker__view-time{
          white-space: wrap;
        }
      }
      .bbx-form-item-view-quality{
        @include dynamic-form-list('bbx-form-cell-item', $i);
      }

      .form-connector{
        margin-right: 12px;
      }
      .bbx-form-cell-item-margin{
        margin-right: 12px;
      }
      .bbx-form-item-view-connector{
        padding-right: 12px;
      }
    }

    .bbx-from-item-cell-box-#{$i} {
      .form-logistics-content{
        @include dynamic-form-list('bbx-form-cell-item', 2);
      }

      .form-address, .form-address-international{
        @include dynamic-form-list('bbx-form-cell-item', 2);
        .form-address-picker-des{
          margin-top: 0;
        }
      }


    }

  }


}
.form-address-picker-des{
  margin-top: 16px;
}



/* 表单多列 end */

.detail-workflow-dialog{
  .base-modal-body{
    box-sizing: border-box !important;
    overflow: hidden !important;
  }
}

$text-color-regular: #595959 !default;
$text-color-secondary: #8C8C8C !default;
.table-min-width {
  font-size: 13px;
  min-width: 84px;
}

.flex-x {
  display: flex;
}

.flex-y {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.al-end {
  align-items: flex-end !important;
}

.al-s {
  align-items: stretch !important;
}

.al-base {
  align-items: baseline !important;
}

.al-center {
  align-items: center !important;
}

.flex-w {
  flex-wrap: wrap;
}

.al-start {
  align-items: flex-start !important;
}
.not-allowed{
  cursor: not-allowed !important;
}
