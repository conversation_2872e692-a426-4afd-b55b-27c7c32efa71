$btn-color-primary-light-1: $color-primary-light-9 !default;

.btn{
  display: inline-block;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  padding: 5px 12px;
  // line-height: 24px;
  border-radius: 2px;
  border: none;
  outline: none;
  cursor: pointer;
  color: $text-color-primary;
  min-width: 60px;

  &:active{
    opacity: 0.85;
  }

  &:disabled{
    cursor: not-allowed;
  }
}

.btn-text{
  outline: none;
  border:none;
  background-color: transparent;

  &:hover{
    background-color: transparent;
    border-color: transparent;
    color: $color-primary;
  }
}

.btn-primary{
  color: #fff;
  background-color: $color-primary;

  &:hover{
    color: #fff;
    background-color: $color-primary-light-7 !important;
  }

  &:disabled{
    color: #fff;
    background-color: $color-primary-light-7 !important;
    opacity: 0.65 !important;
  }
}

.btn-back{
  display: inline-block;
  padding: 7px 15px;
  color: #333;
}

// 背景白色按钮
.btn-ghost{
  background-color: #fff;
  color: $text-color-primary;
  border: 1px solid #E2E2E2;
  border-radius: 2px;
  padding: 4px 15px;

  &:hover {
    background-color: $btn-color-primary-light-1;
    border-color: $btn-color-primary-light-1;
    color: #fff;
  }
}