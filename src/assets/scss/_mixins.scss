@mixin mask{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.15);
}

@mixin text-ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字溢出 2行 (可以写成函数的) */
@mixin text-ellipsis-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 表单列表
@mixin dynamic-form-list($itemClass: 'dynamic-form-list__item', $count:3) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;

  &::after {
    content: '';
    flex: auto;
  }
  .#{$itemClass} {
    --cur-count: #{$count};
    margin-bottom: 12px;
    margin-right: 12px;
    width: calc(
            (100% / (var(--cur-count))) -
            (12px * (var(--cur-count)) / (var(--cur-count)))
    ) !important;
    box-sizing: border-box;
  }
}
