.map-user-head {
  border-radius: 50%;
  height: 42px;
  width: 42px;
}
.map-user-name {
  background-color: #fff;
  border-radius: 4px;
  line-height: 30px;
  text-align: center;
  min-width: 80px;
  position: relative;
  left: 10px;
}

// 客户地址地图
.map-address-title {
  width: 60px;
  color: red;
  font-weight: bold;
  position: absolute;
  left: -12px;
}

.bm-location-dot{
  display: block;
  background-color: #E23B41;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: 4px solid #f8ced0;
  box-shadow: 0 0 4px rgba(248, 206, 208, .25);
}

.map-info-window-content {
  width: 260px;
  padding: 12px 12px 8px;
  margin-bottom: 10px;
  position: relative;

  background-color: #fff;
  border-radius: $button-radius-base;

  .customer-name {
    margin-bottom: 6px;
    font-weight: 500;
  }

  p {
    font-size: $font-size-small;
    margin-bottom: 2px;

    label {
      color: $text-color-regular;
      margin-bottom: 0;
    }
  }

  .info-window-arrow {
    width: 10px;
    height: 10px;
    position: absolute;
    left: 50%;
    bottom: -5px;
    transform: translateX(-50%);
    background: #fff;
    border: 1px solid $color-border-l2;
    border-top-color: #fff;
    border-left-color: #fff;
    transform: rotate(45deg);
  }
}
