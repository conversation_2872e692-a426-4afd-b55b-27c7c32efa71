.pull-right{
  float: right !important;
}

.pull-left{
  float: left !important;
}

.hidden{
  display: none !important;
}

.no-margin{
  margin: 0 !important;
}

.no-padding{
  padding: 0 !important;
}

.no-border {
  border: none !important;
}

.no-padding-left{
  padding-left: 0 !important;
}
.no-position {
  position: relative !important;
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

.pre-line{
  white-space: pre-line !important;
  word-break: break-all;
}

.text-danger{
  color: $color-danger !important;
}

.font-size-14{
  font-size: 14px;
}

.font-size-16{
  font-size: 16px;
}

.font-w-600{
  font-weight: 600;
}

.align-items-center {
  display: flex;
  align-items: center;
}
.pull-right{
  float: right;
}

.text-right{
  text-align: right;
}

.clearfix:after {
  display: block;
  clear: both;
  content: "";
}

.pre{
  white-space: pre-line;
}

.text-overflow-hidden{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

/** 需要微调的样式(像素为4的倍数) */
@for $i from 1 through 20{
  @if $i % 4 == 0 {
    .mt_#{$i} {
      margin-top: 1px * $i !important;
    }  
  }
}

@for $i from 1 through 20{
  @if $i % 4 == 0 {
    .mb_#{$i} {
      margin-bottom: 1px * $i !important;
    }  
  }
}

@for $i from 1 through 20{
  @if $i % 4 == 0 {
    .ml_#{$i} {
      margin-left: 1px * $i !important;
    }  
  }
}

@for $i from 1 through 20{
  @if $i % 4 == 0 {
    .mr_#{$i} {
      margin-right: 1px * $i !important;
    }  
  }
}
@for $i from 1 through 20{
  @if $i % 4 == 0 {
    .pl_#{$i} {
      padding-left: 1px * $i !important;
    }
  }
}

@for $i from 0 through 10{
  .font-#{12 + $i} {
    font-size: (12 + $i) * 1px;
  }  
}

$px: 1px;
@for $i from 1 through 100 {
  .pad-#{$i} {
    padding: $i * $px;
  }
  .bor-ra-#{$i} {
    border-radius: $i * $px;
  }
  .pad-t-#{$i} {
    padding-top: $i * $px;
  }
  .pad-b-#{$i} {
    padding-bottom: $i * $px;
  }
  .pad-l-#{$i} {
    padding-left: $i * $px;
  }
  .pad-r-#{$i} {
    padding-right: $i * $px;
  }
  .mar-t-#{$i} {
    margin-top: $i * $px;
  }
  .mar-b-#{$i} {
    margin-bottom: $i * $px;
  }
  .mar-l-#{$i} {
    margin-left: $i * $px;
  }
  .mar-r-#{$i} {
    margin-right: $i * $px;
  }
  .w-#{$i}-box{
    width: $i * $px !important;
  }
  .mar-r-#{$i}-i {
    margin-right: $i * $px !important;
  }
}

.pointer{
  cursor: pointer;
}
.n-lick{
  pointer-events: none;
}