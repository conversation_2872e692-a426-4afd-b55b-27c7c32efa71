@import './theme_color/index.scss';
$color-primary: $color-primary-light-6 !default;

$color-danger: #f56c6c !default;
$color-success: #67c23a !default;
$color-warning: #FAAE14 !default;
$color-regular: #eef8f8 !default;
$color-ding-blue:#3aa3ff !default;

$color-primary-hover: $color-primary-light-1 !default;

$text-color-primary: #262626 !default;
$text-color-regular: #595959 !default;
$text-color-secondary: #8C8C8C !default;
$text-color-gray: #ccc !default;

$border-color-base: #e0e1e2 !default;
$border-radius-base: 2px !default; //边框圆角

$button-radius-base: 4px !default; //按钮圆角

$input-border-hover-color: $color-primary-light-4 !default;

$font-size-small: 12px !default;
$font-size-base: 14px !default;
$font-size-large: 16px !default;
$font-size-larger: 18px !default;
$text-color-main: #051A13 !default;

// 背景颜色
$bg-color-l1: #F5F5F5 !default;
$bg-color-l2: #fafafa !default;
$bg-color-l3: #f2f2f2 !default;
$bg-color-l4: #F5F7FA !default;

// 边框颜色
$color-border-l1: #D8D8D8 !default;
$color-border-l2: #E5E5E5 !default;
$color-border-l3: #EBEBEB !default;
$color-border-l4: #F0F0F0 !default;

// 导航颜色
$color-nav-primary: #2F4858 !default;
$color-nav-secondary: #22394B !default;
$color-nav-hover: rgba(255, 255, 255, .08) !default;

// 表格hover颜色
$color-td-hover: #F5FAFA !default;

$color-main: #00D1B2;

// 过渡
$fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;

// 选择拖动颜色
$select-draggable-color: #F5F7FA !default;