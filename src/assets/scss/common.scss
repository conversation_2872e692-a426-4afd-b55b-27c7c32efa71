// 三角形
.icon-down-fill {
  display: inline-block;
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}


.biz-intelligent-tag__guide{
  .tour-left-tips{
    display: none !important;
  }
}
.btn-text {
  outline: none;
  border: none;
  background-color: transparent;
  border-color: transparent !important;
  color: #606266;

  &:hover {
    background-color: transparent;
    border-color: transparent;
    color: $color-primary;
  }

  &:focus,
  &:hover {
    border-color: transparent !important;
    background-color: transparent !important;
  }
}
