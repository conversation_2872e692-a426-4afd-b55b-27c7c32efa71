/** 适用于一级列表页样式 */
@mixin list-section {
  padding: 16px;
  border-radius: 4px;
  background-color: #ffffff;
}

.common-list-container__v2 {
  height: 100%;
  overflow: auto;
  padding: 12px;
  .commonn-list-header__v2 {
    display: flex;
    align-items: center;
    // justify-content: flex-end;
    width: 100%;
    padding: 16px;
    border-radius: 4px;
    background-color: #ffffff;
  }

  /** 头部筛选 */
  .common-list-header-nav {
    padding-bottom: 8px;
    background-color: #ffffff;

    .list-header-nav-item {
      & > label {
        width: 100px;
        padding: 4px 4px 0 16px;
        color: #a7b5b5;
        &.multi-language__txt{
          width: auto;
          margin-right: 10px;
        }
      }

      .item {
        box-sizing: border-box;
        border: 1px solid transparent;
        display: inline-block;
        max-width: 160px;
        padding: 4px 8px;
        margin: 0 12px 8px 0;
        font-size: 13px;
        color: $text-color-regular;
        user-select: none;
        @include text-ellipsis();

        span {
          .iconfont {
            font-size: $font-size-small;
            color: $text-color-regular;
            margin-left: 4px;
          }
        }

        &.selected{
          color: $color-primary;
          background: $color-primary-light-1;
          border: 1px solid $color-primary-light-2;
          border-radius: $button-radius-base;
        }

        &:hover{
          color: $color-primary;
          cursor: pointer;
        }
      }
    }
  }
  /** 列表页表格 */
  .common-list-view__v2 {
    @include list-section;

    .common-list-view-header__v2 {
      display: flex;
      justify-content: space-between;
    }

    .common-list-selection__v2{
      span{
        color: $color-primary;
      }
    }

    .el-table--border {
      border: none;
      padding: 0;
      &::after {
        width: 0 !important;
      }
    }
  }
  .common-list-table-footer__v2 {
    /** 列表底部的分页样式 */
    .el-pagination {
      text-align: right;
      .el-pagination__total,
      .el-pagination__sizes {
        // float: left;
      }
    }
    .custom-pagination__total{
      // line-height: 32px
      float: left;
      color: #767e89;
      font-size: 13px;
      line-height: 32px;
      margin: 0;
      font-weight: normal;
      display: flex;
      align-items: center;
      .pagination-total__num{
        color: #767e89;
        font-style: normal;
      }
      .pagination-total__warn{
        font-style: normal;
        color:$color-primary;
      }
    }
  }
}

/** 列表页分区 */
.common-list-section__v2 {
  @include list-section;
}

/** 列表页表格样式 */
.common-list-table-header__v2 {
  background: #fafafa !important;

  th {
    padding: 0 !important;
    background: #fafafa !important;
    & > .cell {
      height: 40px;
      line-height: 40px;
      font-size: 14px !important;
      font-weight: 500 !important;
    }
  }

  .cell{
    font-weight: 500;
    color: #262626;
  }
}

.common-list-table__v2 {
  padding: 10px;

  &:before {
    height: 0 !important;
  }

  .el-table__row {
    td {
      padding: 0 !important;
    }
  }

  td > .cell {
    height: 40px;
    line-height: 40px;
    font-size: $font-size-base;
    color: $text-color-regular;
  }

  .view-detail-btn {
    color: $color-primary;
    &__normal{
      color: $text-color-primary;
      text-decoration: none;
      cursor: default;
    }
  }
  .operation—btn {
    flex-shrink: 0;
    font-size: 14px !important;
    .operation—primary {
      color: $color-primary !important;
      cursor: pointer;
    }
    :deep(.button){
      font-size: 14px !important;
      color: $color-primary !important;
    }
  }
}

.el-dropdown-btn {
  line-height: 32px;
  margin-left: 24px;
  display: inline-flex;
  color: $text-color-regular;

  .iconfont {
    margin-left: 4px;
    font-size: $font-size-large;
  }

  &:hover {
    cursor: pointer;
    color: $color-primary;
  }
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: $bg-color-l4 !important;
  color: $text-color-regular !important;
}
.flex-1{
  flex: 1;
}

.common-table-column__view-list{
  position: relative;
  width: 100%;
  display: inline-block;
  overflow: hidden;
  &-item{
    display: inline-flex;
    align-items: center;
    &:not(:last-child){
      margin-right: 4px;
    }
  }
  .biz-intelligent-tags__view{
    margin-left: 4px;
  }
  .biz-intelligent-tags__table-view-link{
    //display: none;
    width: 8px !important;
    overflow: hidden;
  }

  .biz-intelligent-tags__view-list-item{
    //background: transparent !important;
  }
}
