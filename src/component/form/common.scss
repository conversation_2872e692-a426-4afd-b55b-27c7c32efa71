.form-design {
  width: 100%;
  height: 100%;

  display: flex;
  flex-flow: row nowrap;
  font-size: 14px;
}

.form-design-panel {
  width: 335px;
  min-width: 335px;
  height: 100%;
  user-select: none;
  background-color: #fff;
  border-radius: 4px;
  .form-design-left{
    padding: 0 12px;
    overflow: auto;
    height: 100%;
    // margin-bottom: 20px;
    .form-design-widget{
      margin-bottom: 24px;
    }
  }
}

.form-design-tabs {
  display: flex;
  flex-flow: row nowrap;
}

.form-design-withSys .form-design-tab{
  text-align: center;
}

.form-design-tab {
  padding: 12px 0 6px 0;
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  font-family: PingFangSC-Medium;
  .form-design-tab-tips {
    margin-left: 8px;
  }
}

.form-design-tab-active{
  @include fontColor();
  border-bottom-color: $color-primary;
}

.form-design-tabs-content {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.form-design-field-wrap {
  margin: 10px 0 0 0;
  cursor: move;

  &.disabled {
    opacity: .4;
    cursor: not-allowed;

    .form-design-field:hover {
      background: none !important;
    }
  }
  &.hovered{
    .form-design-field {
      background-color:#F5F7FA;
    }
  }
}

.form-design-field {
  width: 148px;
  height: 32px;
  line-height: 24px;
  padding: 3px 12px;
  text-align: left;
  border: 1px solid #D8D8D8;
  font-size: 12px;
  transition: background-color ease .15s;
  border-radius: 3px;
  display: flex;

  .anticon{
    margin-right: 10px;
  }

  .field-name {
    flex: 1;
    @include text-ellipsis();
    word-break: break-all;
  }

  .field-label {
    padding: 0 6px;
    height: 20px;
    line-height: 20px;

    color: #ff9100;
    background: #fff3e0;
    border: 1px solid #ffe0b2;
    border-radius: $button-radius-base;
  }

  // &:hover {
  //   background-color:#F5F7FA;
  // }

  i.iconfont {
    font-size: 16px;
    margin-top: 3px;
    width: 24px;
    height: 24px;
    color: #666666;
  }
}

.form-design-main {
  // width: calc(100% - 700px);
  // min-width: 500px;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  margin: 0px 12px;
  border-top: none;
  border-bottom: none;
  user-select: none;
  background: #fff;
  border-radius: 4px;
  // overflow: auto;
  .form-design-box{
    height: 100vh;
  }
  // 手机边框
  .form-design-center {
    background: url('./../../assets/img/phone_icon.png') no-repeat center 0;
    background-size: 100% 100%;

    position: relative;
    max-height: 568px;
    width: 320px;
    margin: 0 auto;
    height: 83%;
  
    .form-design-phone {
      position: absolute;
      top: 39px;
      left: 15px;
      right: 23px;
      bottom: 22px;
      height: calc(100% - 80px);
      width: calc(100% - 31px);
      border: 1px solid #edf0f4;
      border-top: none;
      background-color: #F2F2F2;
      box-shadow: none;
      padding-bottom: 12px;
  
      &::-webkit-scrollbar-thumb {
        border-radius: 3px;
      }
  
    }
  }
  .form-design-hidden{
    font-size: 12px;
    color: $color-primary-light-6;
    height: 30px;
    text-align: right;
    margin: 12px 12px 0 0;
    cursor: pointer;
    font-family: PingFangSC-Medium; 
    display: flex;
    justify-content: flex-end;
    .iconfont{
      font-size: 12px;
      margin-right: 5px;
    }
  }

}
// @media screen and (max-width:1440px ) {
//   .form-design-main .form-design-center{
//     height: 86%;
//   }
// }
// @media screen and (max-width:1280px ) {
//   .form-design-main .form-design-center{
//     height: 80%;
//   }
// }
.form-type-text {
  max-width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.form-design-setting {
  min-width: 250px;
  width: 345px;
  height: 100%;
  background-color: #fff;
  overflow: auto;
  border-radius: 4px;

  .ql-snow .ql-tooltip {
    left: 0 !important;
  }
}

// 公用字段设置组件
.form-design-setting-disabled {
  cursor: not-allowed;

  .common-field-setting {
    padding: 23px 12px 0;
    cursor: auto;

    &-btn {
      button {
        width: 145px;
        padding: 9px;
        margin: 0;

        &:first-child {
          margin-right: 8px;
        }
      }
    }
  }

  .form-setting-panel, .flow-node-setting {
    opacity: .4;
    pointer-events: none;
  }
}

// 修改控件配置弹窗
.field-setting-modal {
  @include mask();
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;

  .base-panel {
    display: flex;
    flex-direction: column;

    &-title {
      min-height: 44px;
      padding: 6px 12px;
    }

    &-content {
      flex: 1;
      overflow: auto;
  
      .form-design-warning {
        margin: 12px 12px 0;
      }
    }
  
    &-footer {
      min-height: 52px;
      padding: 10px 20px;
      text-align: right;
      border-top: 1px solid #f2f8f7;
    }
  }
}

.form-setting-panel-title{
  line-height: 27px;
  font-size: 14px;
  font-weight: bold
}

.form-design-notification-content{
  padding-top: 10px;
  max-height: 320px;

  p{
    line-height: 24px;
  }
}

.form-design-field-empty{
  flex: 1;
  text-align: center;
  color: #9a9a9a;
  padding-top: 25px;
}

// ------------ setting ------------
.form-setting-panel {
  // height: 100vh;
  // overflow: auto;
  padding: 23px 12px;

  & > h3 {
    line-height: 24px;
    padding: 3px 0;
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  .icon-question {
    font-size: $font-size-small;
    color: $text-color-secondary;
    font-weight: normal;
  }
  .form-date-type{
    margin-top: 8px;
    .el-select {
      min-width: 210px;
    }
  }

  .form-setting-select {
    width: 100%;
    margin-top: 8px;
  }
}

.form-setting-group {
  margin-bottom: 24px;

  &-small {
    margin-bottom: 12px;
  }

  input[type='text'],
  textarea {
    width: 100%;
  }

  label {
    margin: 8px 5px 0 0;
    vertical-align: middle;
    cursor: pointer;
  }

  input[type="checkbox"] {
    margin-right: 3px;
  }

  textarea {
    display: block;
  }

  &.form-setting-item {
    .form-item-title {
      margin-bottom: 0px;
      font-family: PingFangSC-Medium;
    }
  
    label.el-checkbox {
      margin-top: 8px;
    }

    .form-item-tips {
      padding-top: 5px;
      margin: 0;
      color: $color-warning;
    }
  }
}
 
.form-common-setting {
  &-panel {
    display: flex;
    justify-content: space-between;
  }

  &-title {
    &-name {
      margin-right: 4px;
      font-family: PingFangSC-Medium;
    }

    &-remind,
    .iconfont {
      font-size: $font-size-small;
      color: $text-color-secondary;
      font-weight: normal;
    }

    .iconfont {
      margin-right: 4px;
    }
  } 
}

.form-label-margin {
  label.el-checkbox {
    margin-top: 8px;
  }
}

// ------------ preview ------------
.form-preview-group {
  display: flex;
  flex-flow: row nowrap;
  overflow: hidden;
  padding: 14px 10px;
  background-color: #fff;

  label {
    display: block;
    margin: 0;
    width: 110px;
    line-height: 20px;

    @include text-ellipsis();
  }
}

.form-preview-mock {
  flex: 1;
  overflow: hidden;
  pointer-events: none;
  text-align: right;
}

.form-preview-control {
  width: 100%;
  border-radius: 4px;
  // padding: 0 5px;
  line-height: 20px;
  height: 20px;
  color: #9a9a9a;
  text-align: right;
  margin: 0;
  @include text-ellipsis();
}

.form-preview-withIcon {
  padding-right: 24px;
  position: relative;

  i.iconfont {
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 18px;
  }
}

.form-preview-notNull {
  color: red;
}

// ------------ design ------------
.form-design-phone {
  position: relative;
  box-shadow: 0 0 8px rgba(0, 0, 0, .15);
  width: 100%;
  max-width: 414px;
  min-width: 240px;
  height: 100%;
  min-height: 50%;
  margin: 0 auto;
  // overflow: auto;
  border-radius: 1px;
  background-color: #F2F2F2;;
}

.form-design-phone-content {
  height: 100%;
  overflow: auto;
}

.form-preview {
  height: 100%;
  overflow: auto;
}

.form-design-tip {
  background: url('../../assets/img/form-design-tip.png') no-repeat 50%;
  background-size: 239px 96px;
  position: relative;
  min-height: 300px;

  p {
    text-align: center;
    position: absolute;
    bottom: 70px;
    left: 0;
    right: 0;
    color: #999;
    pointer-events: none;
    margin: 0;
  }
}

.form-design-silence {
  .form-design-preview:hover {
    border-color: transparent !important;
  }

  .form-design-operation {
    display: none !important;
  }
}

.form-design-ghost {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  cursor: move !important;
  background-color: rgba(255, 255, 255, .95);
  box-shadow: 0 0 8px rgba(0, 0, 0, .125);

  .form-design-field {
    background-color: #fff;
  }
}

.form-design-preview {
  position: relative;
  border: 1px dashed transparent;
  margin-top: 8px;

  &.form-design-selected {
    border-style: solid;
    border-color:$color-primary;
    box-sizing: border-box;
  }

  &.hovered {
    border-color:$color-primary;

    & > .form-design-operation {
      visibility: initial;
    }
  }
}

.form-design-dragging {
  border-color: $color-primary !important;
  border-style: solid !important;
  opacity: .65;
}
.form-design-operation{
  display: flex;
  justify-content: flex-start;
  position: absolute;
  padding: 0px 8px;
  top: -9px;
  right: -1px;
  height: 22px;
  line-height: 22px;
  background: #E5E5E5;
  border-radius: 9px;
  visibility: hidden;
}
.form-design-divider-separator{
  width: 1px;
  height: .9em;
  margin: 5px 6px 0 6px;
  vertical-align: middle;
  background-color: #D9D9D9;
}

.form-design-preview-btn {
  text-align: center;
  border: none;
  color: #999999;
  outline: none;
  height: 22px;
  padding: 0;
  margin: 0;
  z-index: 9;
  cursor: pointer;
  &:hover {
   color: $color-danger;
  }

  i {
    font-size: 14px;
  }
}
.form-design-preview-delete{
  &:hover {
    color: $color-danger;
  }
}
.form-design-preview-hidden{
  &:hover {
    @include fontColor();
  }
}
.form-design-preview-fieldName{
  margin-right: 10px;
  &:hover {
    @include fontColor();
  }
}

.form-design-cover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8;
  cursor: move;
}

.form-currency-setting {
  .el-select {
    width: 100%;
  }
}

// ------------ form-view ------------
.form-view{
  background-color: #fff;
  padding: 5px 0;
}

.form-view-row {
  display: flex;
  justify-content: flex-start;
  // padding: 8px 20px;
  padding: 6px 0 8px 12px;
  align-items: center;
  color: $text-color-primary;

  label {
    line-height: 20px;
    width: 98px;
    flex-shrink: 0;
    margin: 0 10px 0 0;
    color: $text-color-regular;
    word-break: break-word;
    &::after {
      content: ":";
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
    }
  }
  .form-view-row-content-address{
    cursor: pointer;
  }
  .form-view-row-content-address:hover{
    color: $color-primary-light-6;
  }
  .form-view-row-content{
    line-height: 20px;
    flex: 1;
    overflow-x: hidden;
    color: $text-color-primary;
    word-break: break-word;
    // white-space: pre-wrap;
    overflow-y: hidden;


    .iconfont {
      @include fontColor();
      &:hover {
        cursor: pointer;
      }
    }
    &__link-tags {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      height: auto;
      .biz-intelligent-tags__table-view{
        width: auto;
        height: 20px;
        &:not(:last-child) {
          //margin-right: 8px;
        }
      }
      .biz-intelligent-tags__table-view-link{
        height: auto;
      }
      .biz-intelligent-tags__list-column{
        height: auto;
        margin-right: 4px;
      }
    }
    
  }
  .view-detail-btn {
    color: $color-primary;
    cursor: pointer;
  }
}

.form-view-textarea-preview{
  white-space: pre-line;
}

.form-view-textarea-content {
  white-space: pre-wrap;
}

.section-title {
  padding: 8px 12px;
  font-size: 16px 0;
  margin: 8px 0;
  background: $bg-color-l2;
  font-weight: bold;
  position: relative;
  display: flex;
  align-items: center;

  .iconfont {
    position: absolute;
    right: 10px;
    height: 30px;
    font-weight: normal;
    line-height: 30px;
    font-size: 14px;
    
    &:hover {
      @include fontColor();
      cursor: pointer;
    }
  }

  .reversal {
    transform: rotateZ(180deg);
  }
}

.form-view-group{
  padding: 10px;
  border-radius: 2px;
  background-color: #fff;
}

.form-view-location-not {
  color: #9a9a9a !important;
}

.link-text {
  @include fontColorImportant();
  cursor: pointer;
  margin-right: 10px;
  user-select: none;
}

.form-view-info-content {
  background-color: #FAFAFA;
  padding: 3px 5px;
}

// 电子签名、客户签名
.form-view-autograph-content {
  max-width: 300px;
  height: 100px;
  border: 1px dashed #aaa;

  img {
    width: 100%;
    height: 100%;

    &[src=""], &:not([src]) {
      opacity: 0;
    }
  }
}

// ------------ form-builder ------------
.form-builder{
  margin: 0 auto;
  padding: 12px;
  flex: 1;
  width: 100%;
  max-width: 824px;
  background-color: #fff;
}

.form-builder-group{
  border-radius: 2px;
  margin-bottom: 10px;
}

.form-design-warning{
  padding: 12px;
  color: $text-color-regular;
  font-size: $font-size-small;

  background: #FFFBE6;
  border: 1px solid #FFE58F;
  border-radius: $border-radius-base;
  display: flex;

  .iconfont {
    color: #FAAD14;
    margin-right: 8px;
    font-size: $font-size-base;
  }
}

.form-item__text{
  padding: 3px 0;
  line-height: 24px;
  color: #666;
}

// 手机壳样式调整
.form-design {
  // background-color: #fff;
  // min-height: 600px;
  min-width: 800px;
}
.base-hidden-modal{
  .base-modal-body {
    margin: 23px auto;
    width: 350px;
  
  }
}

.setting-wrapper,
.portal-form-design,
.mform-design-setting {
  height: 100%;
}

/* start vue drag */
.ghost {
  background: #fff;
  border: 1px dashed #CBD6E2;
  
  &::after {
    background: #fff;
  }
}

div.ghost {
  position: relative;
  overflow: hidden;
  
  &::after {
    // TODO: css中不好处理
    // content: '放在这里';
    content: '\653e\5728\8fd9\91cc';
    display: block;
    background: #E6E8EB;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    text-align: center;
    font-size: 16px;
    color:#8C8C8C;
    z-index: 10;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}
/* end vue drag */

.form-design-preview-disabled {
  border-color: transparent !important;
  .form-design-cover {
    cursor: default;
  }
}

.flow-preview-header {
  width: 100%;
  height: 40px;
  background: #fafafa;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
      
  &-translate{
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
  }
}

// 表单中新建标签全局样式
.form-intLabel {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
  .form-item-label {
    position: relative;
    padding: 0; // 注意产品padding有一个padding-top: 4px;
    &::after {
      content: ':';
      position: absolute;
      right: -4px;
      top: 50%;
      transform: translateY(-50%);
      display: block;
    }
  }
  .form-item-label.form-item-label.form-item-label {
    width: fit-content;
    line-height: 26px;
  }
}