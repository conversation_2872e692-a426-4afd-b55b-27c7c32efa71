/** components */
import { FieldManager, PreviewComponents } from './components';
import draggable from 'vuedraggable';
/** util */
import { findComponentUpward } from '@src/util/assist';
import * as FormUtil from './util';
import i18n from '@src/locales'
/* enum */
import { FieldTypeMappingEnum } from '@model/enum/FieldMappingEnum';

const HideDesignPreviewCoverFields = [
  FieldTypeMappingEnum.Currency,
  FieldTypeMappingEnum.Connector,
]

/** 创建字段预览组件 */
export function createPreviewComp(h, field) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const _this = this
  let currFieldId = field._id;
  let previewComp = FieldManager.findField(field.formType);

  if (field.formType === 'tags') { //参照客户，预览先跟着select走~
    previewComp = FieldManager.findField('select');
  }
  if(previewComp == null){
    console.warn(`[not implement]: ${field.displayName}(${field.fieldName}): ${field.formType}. `)
    return null;
  }
  
  const disabledElement = <div class="disabled"></div>
  
  // 根据字段配置创建预览内容，无预览字段不渲染
  if (!previewComp) return disabledElement;
  
  // 隐藏字段不渲染
  if(field.isHidden == 1) return disabledElement;
  let mode_ = this.mode;
  let fieldPreview = h(previewComp.preview, {
    'class': 'form-design__ghost',
    props: { field, setting: previewComp, mode: mode_},
    on: {
      input: (event) => {
        if (event.isSetting) {
          _this.$set(field.setting, event.prop, event.value);
        } else {
          _this.$set(field, event.prop, event.value);
        }
      }
    }
  });
  
  let previewClass = {
    'form-design-preview': true,
    'form-design-preview-disabled': this.disabled,
    'form-design-selected': this.currField && (currFieldId == this.currField._id), // 被选中
    'form-design-dragging': field.dragging,
  }
  
  if (field?.setting?.autoSerialNumber) {
    field.defaultTips = i18n.t('common.form.tip.moduleWillCreateAfterSetTip1', {module:field.displayName})
  }
  
  // 关联产品 系统字段 显示隐藏按钮
  const isShowOperateContentRelationProductHidden = field.isSystem == 1 && field.formType == 'relationProduct';

  // 合同产品子表单的 服务开始时间 和 服务结束时间 和 服务金额 可删
  const isForceDeleteFields = ['serviceStartTime', 'serviceEndTime', 'serviceMoney'].includes(field.fieldName)

  // 默认显示条件 非系统字段
  const isShowOperateContentDefault = (field.isSystem == 0 || previewComp.forceDelete || isForceDeleteFields) && !this.disabled;
  // 是否显示隐藏按钮
  const isShowOperateContentHidden = isShowOperateContentDefault || isShowOperateContentRelationProductHidden;
  // 是否显示删除按钮
  const isShowOperateContentDelete = isShowOperateContentDefault && !(field.setting && field.setting.notEdit)
  // 是否显示操作栏
  const isShowOperateContent = isShowOperateContentHidden || isShowOperateContentDelete;
  // 是否显示分割线 - 隐藏和删除都有才显示
  const isShowOperateContentSeparator = isShowOperateContentHidden && isShowOperateContentDelete && !(field.setting && field.setting.notEdit)

  // 是否是子表单
  let isSubForm = FormUtil.isSubForm(field);

  // 是否展示表单预览组件遮罩层
  const isShowDesignPreviewCover = !isSubForm && !HideDesignPreviewCoverFields.includes(field.formType);

  const { setHoverEffect, hideHovered } = FormUtil.formPreviewCompHoverMethodsEffect;

  return (
    <div 
      class={previewClass} 
      key={currFieldId}
      onMouseover={setHoverEffect}
      onDragend={hideHovered}
      onMouseleave={hideHovered}
      onClick={event => this.handlerClickPreviewComponent && this.handlerClickPreviewComponent(event, field)}
    >
      
      {fieldPreview}
      
      {isShowOperateContent && (
        <div class="form-design-operation">

          {
            isShowOperateContentHidden && (
              <div class="form-design-preview-hidden form-design-preview-btn" onClick={e => this.hiddenField(field)}>
                <el-tooltip class="item" effect="dark" content={i18n.t('common.base.hide')} placement="top">
                  <i class="iconfont icon-fdn-hidden"></i>
                </el-tooltip>
              </div>
            )
          }
          
          {
            isShowOperateContentSeparator && <div class="form-design-divider-separator" role="separator"></div>
          }

          {
            isShowOperateContentDelete && (
              <div class="form-design-preview-delete form-design-preview-btn" onClick={e => this.deleteField(e, field)}>
                <el-tooltip class="item" effect="dark" content={i18n.t('common.base.delete')} placement="top">
                  <i class="iconfont icon-shanchu-copy"></i>
                </el-tooltip>
              </div>
            )
          }
        </div>
      )}
      
      {isShowDesignPreviewCover && <div class="form-design-cover"></div>}
      
    </div>
  )
}

const FormDesignPreview = {
  name: 'form-design-preview',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    validate: Function,
    disabled: {
      type: Boolean,
      default: false
    },
    draggableGroup: {
      type: [String, Object]
    },
    parentFieldId: {
      type: String
    },
    showFormDesignTip: {
      type: Boolean,
      default: true
    },
    mode:{
      type: String
    }
  },
  computed: {
    formDesignComponent() {
      return findComponentUpward(this, 'form-design') || {};
    },
    /** 当前选中的字段 */
    currField() {
      return this.formDesignComponent?.currField || null;
    },
    /** 字段是否为空 */
    isEmpty() {
      return !Array.isArray(this.value) || this.value.length == 0;
    }
  },
  methods: {
    handlerClickPreviewComponent(event, field) {
      if (this.disabled) return
      
      event.stopPropagation();

      this.chooseField(field);
    },
    /** 触发input事件 */
    emitInput(value) {
      this.$emit('input', value);
    },
    addEventHandler(newIndex) {
      // 当前拖拽的字段
      let field = this.value[newIndex];
      let message = this.validate && this.validate(field);
      
      if (message) {
        this.value.splice(newIndex, 1);
        return this.$platform.alert(message);
      }

      // 添加到子表单中需要添加子表单字段
      if (this.parentFieldId && !field.parentFieldId) {
        this.$set(field, 'parentFieldId', this.parentFieldId)
      }
      
      // 处理自定义字段名逻辑
      this.handleCustomFieldNameForDrag(field).then(() => {
        this.$nextTick(() => {
          this.chooseField(field);
        });
        
        this.emitInput(this.value);
      });
    },
    
    /** 处理拖拽时的自定义字段名 */
    async handleCustomFieldNameForDrag(field) {
      const formDesign = this.formDesignComponent;
      
      // 检查是否需要自定义字段名（与 insertField 中的条件保持一致）
      if (formDesign.enableCustomFieldName && field.isSystem !== 1 && field.isCommon !== 1) {
        try {
          const customFieldName = await formDesign.handleCustomFieldName(field);
          this.$set(field, 'fieldName', customFieldName);
        } catch (error) {
          console.warn('自定义字段名处理失败:', error);
          // 如果自定义字段名失败，保持原fieldName或生成随机字段名
          if (!field.fieldName) {
            this.$set(field, 'fieldName', `field_${Math.random().toString(36).substr(2, 8)}`);
          }
        }
      }
    },
    /** 渲染预览组件 */
    renderPreviewList(h) {
      if(this.isEmpty) {
        if (!this.showFormDesignTip) {
          return 
        }
        return (
          <div class="form-design-tip">
            <p>{i18n.t('common.form.tip.leftControlerTips')}</p>
          </div>
        )
      }
      
      return this.value.map(f => createPreviewComp.call(this, h, f));
    },
    /** 选中字段 */
    chooseField(field) {
      this.formDesignComponent.chooseField(field);
    },
    /** 删除字段 */
    async deleteField(event, field) {
      event.stopPropagation();
      if (field.parentFieldId) {
        this.deleteFieldSub(field)
        return
      }
      this.$emit('delete', field);
    },
    /** 隐藏字段 */
    async hiddenField(item) {
      if (item.parentFieldId) {
        this.hiddenFieldSub(item)
        return
      }
      this.$emit('hidden', item);
    },
    /** 删除字段（子表单） */
    async deleteFieldSub(field) {
      let tip = i18n.t('common.form.tip.deleteFieldTips');
      if (!(await this.$platform.confirm(tip))) return;

      let index = this.value.indexOf(field);
      console.log(this.value, index, '111223123')

      if (index >= 0) {
        // 如果是选中的字段，清除选中
        if (this.currField == field) this.formDesignComponent.currField = null;

        this.value.splice(index, 1);

        this.formDesignComponent.deleteDependencies(field);
      }

      this.emitInput(this.value);
    },
    /** 隐藏字段（子表单） */
    async hiddenFieldSub(item) {
      let tip =
        item.isSystem == 0
          ? i18n.t('common.form.tip.hideFieldTips1')
          : i18n.t('common.form.tip.hideFieldTips2');
      if (!(await this.$platform.confirm(tip))) return;

      let index = this.value.indexOf(item);
      this.value[index].isHidden = 1;

      this.emitInput(this.value);
    }
  },
  render(h) {
    let group = this.draggableGroup || {
      name: 'widget',
      pull: false, // 不允许移出（防止与子表单的交互）
      put: true // 允许加入
    }
    
    return (
      <draggable
        class={['form-preview', 'form-design-preview-container', this.isEmpty && 'form-preview-empty']}
        animation="200"
        ghostClass="ghost"
        group={group}
        list={this.value}
        filter='.disabled'
        onStart={() => {
          this.formDesignComponent.silence = true;
        }}
        onEnd={({ newIndex }) => {
          this.formDesignComponent.silence = false;
          this.formDesignComponent.currField = this.value[newIndex] || null;
        }}
        onAdd={({ newIndex }) => {
          this.addEventHandler(newIndex);
        }}
      >
        {this.renderPreviewList(h)}
      </draggable>
    );
  },
  components: {
    ...PreviewComponents,
    draggable
  }
};

export default FormDesignPreview;
