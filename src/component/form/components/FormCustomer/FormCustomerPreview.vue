<template>
  <div class="form-preview-task-customer">
    <div class="form-preview-group">
      <!-- displayName=客户的时候做了翻译 -->
      <!-- TODO 需要调整 -->
      <label>{{_displayName || $t('common.base.customer')}} <span class="form-preview-notNull" v-if="field.isNull == 0">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control">
          {{ _placeHolder2 || field.placeHolder }}
        </p>
      </div>
    </div>
    <div class="form-preview-group" v-if="address">
      <label>{{addressLabel}}<span class="form-preview-notNull" v-if="field.setting.customerOption && field.setting.customerOption.addressNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="linkman">
      <label>{{linkManLabel}}<span class="form-preview-notNull" v-if="field.setting.customerOption && field.setting.customerOption.linkmanNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="isShowCustomerManager">
      <label>{{customerManagerLabel}} <span class="form-preview-notNull" v-if="customerManagerNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="isShowCustomerContact">
      <label>{{customerContactLabel}} <span class="form-preview-notNull" v-if="customerContactNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="isShowCustomerLevel">
      <label>{{customerLevelLabel}} <span class="form-preview-notNull" v-if="customerLevelNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="isShowCustomerSource">
      <label>{{customerSourceLabel}} <span class="form-preview-notNull" v-if="customerSourceNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
    <div class="form-preview-group" v-if="product">
      <label>{{productLabel}} <span class="form-preview-notNull" v-if="productNotNull">*</span></label>
      <div class="form-preview-mock">
        <p class="form-preview-control form-preview-withIcon"></p>
      </div>
    </div>
  </div>
</template>

<script>
import { previewProps } from '@src/component/form/components/props';
import translate from '@src/component/form/mixin/translate.js'
import { transformI18n } from '@src/locales'

export default {
  name: 'form-customer-preview',
  props: previewProps,
  mixins: [translate],
  computed: {
    customerOption() {
      return this.field.setting.customerOption || {} 
    },
    address() {
      return this.customerOption.address 
    },
    linkman() {
      return this.customerOption.linkman 
    },
    product() {
      return this.customerOption.product 
    },
    productNotNull() {
      return this.customerOption.productNotNull 
    },
    addressLabel(){
      return this.mode == "product" ? transformI18n('common.form.preview.customer.customerAddressLabel', this.formPreviewLocale) : transformI18n('common.form.preview.customer.addressLabel', this.formPreviewLocale)
    },
    linkManLabel(){
      return transformI18n('common.form.preview.customer.linkmanLabel', this.formPreviewLocale)
    },
    productLabel(){
      return transformI18n('common.form.preview.customer.productLabel', this.formPreviewLocale)
    },
    customerManagerLabel(){
      return '客户负责人'
    },
    customerContactLabel(){
      return '联系方式'
    },
    customerLevelLabel(){
      return '客户等级'
    },
    customerSourceLabel(){
      return '客户来源'
    },
    customerManagerNotNullLabel(){
      return '客户负责人必填'
    },
    customerContactNotNullLabel(){
      return '联系方式必填'
    },
    customerLevelNotNullLabel(){  
      return '客户等级必填'
    },
    customerSourceNotNullLabel(){
      return '客户来源必填'
    },
    isShowCustomerManager(){
      return this.customerOption.customerManager ?? false
    },
    isShowCustomerContact(){
      return this.customerOption.customerContact ?? false
    },
    isShowCustomerLevel(){
      return this.customerOption.customerLevel ?? false
    },
    isShowCustomerSource(){
      return this.customerOption.customerSource ?? false
    },
    customerManagerNotNull(){
      return this.customerOption.customerManagerNotNull 
    },
    customerContactNotNull(){
      return this.customerOption.customerContactNotNull 
    },
    customerLevelNotNull(){
      return this.customerOption.customerLevelNotNull 
    },
    customerSourceNotNull(){
      return this.customerOption.customerSourceNotNull 
    },
  }
}
</script>
<style lang="scss" scoped>
  .form-preview-task-customer{
    display: flex;
    flex-direction: column;
    .form-preview-group{
      border-bottom: 1px solid  #F5F5F5;
    }
  }
</style>

