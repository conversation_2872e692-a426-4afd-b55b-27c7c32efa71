<template>
  <div class="form-setting-panel">
    <!-- start 标题 -->
    <h3 class="form-setting-panel-title">{{field.displayName}}</h3>   
    <!-- end 标题 -->
    <p class="form-design-warning" style="margin-bottom: 5px">{{$t('common.form.tip.systemTemplateTips')}}</p>
    <h3>{{$t('common.base.concatOption')}}</h3>
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.linkman" @input="updateOptions($event, 'linkman')">{{$t('common.form.preview.customer.linkmanLabel')}}</el-checkbox>
      <el-checkbox :value="field.setting.customerOption.linkmanNotNull" @input="updateOptions($event, 'linkmanNotNull')" :disabled="!field.setting.customerOption.linkman">{{$t('common.form.preview.customer.linkmanIsRequire')}}</el-checkbox>
    </div>
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.address" @input="updateOptions($event, 'address')">{{mode == "product" ? $t('common.form.preview.customer.customerAddressLabel') : $t('common.form.preview.customer.addressLabel')}}</el-checkbox>
      <el-checkbox :value="field.setting.customerOption.addressNotNull" @input="updateOptions($event, 'addressNotNull')" :disabled="!field.setting.customerOption.address">{{mode == "product" ? $t('common.form.preview.customer.customerAddressIsRequire') : $t('common.form.preview.customer.addressIsRequire')}}</el-checkbox>
    </div>
    <!-- 客户负责人 -->
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.customerManager" @input="updateOptions($event, 'customerManager')">客户负责人</el-checkbox>
      <!-- <el-checkbox :value="field.setting.customerOption.customerManagerNotNull" @input="updateOptions($event, 'customerManagerNotNull')" :disabled="!field.setting.customerOption.customerManager">客户负责人必填</el-checkbox> -->
    </div>
    <!-- 联系方式 -->
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.customerContact" @input="updateOptions($event, 'customerContact')">联系方式</el-checkbox>
      <!-- <el-checkbox :value="field.setting.customerOption.customerContactNotNull" @input="updateOptions($event, 'customerContactNotNull')" :disabled="!field.setting.customerOption.customerContact">联系方式必填</el-checkbox> -->
    </div>
    <!-- 客户等级 -->
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.customerLevel" @input="updateOptions($event, 'customerLevel')">客户等级</el-checkbox>
      <!-- <el-checkbox :value="field.setting.customerOption.customerLevelNotNull" @input="updateOptions($event, 'customerLevelNotNull')" :disabled="!field.setting.customerOption.customerLevel">客户等级必填</el-checkbox> -->
    </div>
    <!-- 客户来源 -->
    <div class="form-setting-group">
      <el-checkbox :value="field.setting.customerOption.customerSource" @input="updateOptions($event, 'customerSource')">客户来源</el-checkbox>
      <!-- <el-checkbox :value="field.setting.customerOption.customerSourceNotNull" @input="updateOptions($event, 'customerSourceNotNull')" :disabled="!field.setting.customerOption.customerSource">客户来源必填</el-checkbox> -->
    </div>
  </div>
</template>

<script>
import SettingMixin from '@src/component/form/mixin/setting';

export default {
  name: 'product-customer-extend-setting',
  mixins: [SettingMixin],
  props: {
    field: {
      type: Object,
      default: () => ({})
    },
    setting: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      deafault: '',
    },
  },
  computed: {
    isSystem() {
      return this.field.isSystem === 1;
    }
  },
  methods: {
    updateForDom(event){
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;
      
      this.update(value, prop)
    },
    updateOptions(value, prop, isSetting = false){
      this.$emit('updateOptions', {value, prop, isSetting})
    }
  }
}
</script>

<style lang="scss" scoped>
.form-setting-group{
  margin-bottom: 0;
}
</style>
