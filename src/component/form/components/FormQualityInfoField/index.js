import i18n from '@src/locales';
import FormQualityInfoSetting from './FormQualityInfoSetting.vue';
import FormQualityInfoPreview from './FormQualityInfoPreview.vue';
import FormQualityInfo from './FormQualityInfo.vue'
import FormQualityInfoBuild from './FormQualityInfoBuild/FormQualityInfoBuild.tsx'

let FormQualityInfoField = {
  formType: 'quality_info', // 字段类型
  name: i18n.t('common.form.type.quality_info'),
  isSystem: 1,
  forceDelete: true,
  component: {
    preview: FormQualityInfoPreview,
    build: FormQualityInfoBuild,
    setting: FormQualityInfoSetting,
  }
};

export default FormQualityInfoField;


