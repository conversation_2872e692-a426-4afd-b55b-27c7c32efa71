<template>
  <div class="form-select">
    <!-- start 下拉模式 -->
    <template v-if="selectType==1">
      <!-- 多选 -->
      <el-select
        :id="`form_${field.fieldName}`"
        :placeholder="placeholder"
        :clearable="clearable"
        :multiple="isMulti"
        ref="elSelect"
        filterable
        :value="currentValue"
        :disabled="disabled"
        @change="input"
      >
        <el-option
          v-for="(item, index) in options"
          :key="`${item.value}_${index}`"
          :label="item.text"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </template>
    <!-- end 下拉模式 -->

    <!-- start 平铺模式 -->
    <template v-else-if="selectType==2">
      <!-- start 单选 -->
      <el-radio-group v-model="currentValue" @change="input" v-if="!isMulti">
        <el-radio
          v-for="item in options"
          :label="item.value" 
          :key="item.value"
          :value="item.value">
          {{item.text}}
        </el-radio>
      </el-radio-group>
      <!-- end 单选 -->

      <!-- start 多选 -->
      <el-checkbox-group v-model="currentValue" @change="input" v-if="isMulti">
        <el-checkbox  
          v-for="item in options" 
          :label="item.text" 
          :key="item.id">
          {{item.text}}
        </el-checkbox>
      </el-checkbox-group>
      <!-- end 多选 -->
    </template>
    <!-- end 平铺模式 -->
  </div>
</template>

<script>
import FormMixin from '@src/component/form/mixin/form';

export default {
  name: 'form-select',
  mixins: [FormMixin],
  props: {
    value: [String, Number, Array],
    source: {
      type: Array
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data(){
    return{
    }
  },
  computed: {
    selectType() {
      let setting = this.field.setting || {};
      return setting.selectType || 1;
    },
    isMulti(){
      let setting = this.field.setting || {};
      return setting.isMulti;
    },
    dataSourceIds() {
      let setting = this.field.setting || {};
      return setting.dataSourceIds || [];
    },
    options(){
      let setting = this.field.setting || {};
      let dataSource = setting.dataSource || [];

      dataSource = dataSource.map(d => {
        if (typeof d === 'string') {
          return {
            text: d,
            value: d,
          }
        }
        return d;
      });
      return this.source || dataSource || [];
    },
    /** 当前选中的值，兼容新旧数据格式 */
    currentValue() {
      if (!this.value) return this.isMulti ? [] : '';

      // 如果是新格式 [{id:'x',name:''}]
      if (Array.isArray(this.value) && this.value.length > 0 && typeof this.value[0] === 'object' && this.value[0].hasOwnProperty('name')) {
        if (this.isMulti) {
          return this.value.map(item => item.name);
        } else {
          return this.value[0]?.name || '';
        }
      }

      // 旧格式，直接返回
      return this.value;
    }
  },
  methods: {
    input(newValue){
      let oldValue = null;
      if(this.selectType == 1) this.$refs.elSelect.blur();

      // 转换为新格式 [{id:'x',name:''}]
      const formattedValue = this.convertToNewFormat(newValue);

      this.$emit('update', {newValue: formattedValue, oldValue, field: this.field});
      this.$emit('input', formattedValue);
    },
    /** 转换为新格式 [{id:'x',name:''}] */
    convertToNewFormat(selectedValues) {
      if (!selectedValues) return this.isMulti ? [] : null;

      if (this.isMulti) {
        if (!Array.isArray(selectedValues)) return [];
        return selectedValues.map(value => ({
          id: this.getIdByValue(value),
          name: value
        })).filter(item => item.id);
      } else {
        const id = this.getIdByValue(selectedValues);
        return id ? [{ id, name: selectedValues }] : [];
      }
    },
    /** 根据value获取对应的ID */
    getIdByValue(value) {
      let setting = this.field.setting || {};
      let dataSource = setting.dataSource || [];

      const index = dataSource.findIndex(item => {
        if (typeof item === 'string') {
          return item === value;
        }
        return item?.value === value;
      });

      if (index >= 0) {
        // 如果有dataSourceIds，使用对应的ID，否则使用默认ID（index+1）
        return this.dataSourceIds[index] || String(index + 1);
      }

      return null;
    }
  }
}
</script>


<style lang="scss">
.form-select{
  width: 100%;

  .el-select{
    width: 100%;

    .el-input__inner{
      padding-left: 10px;
    }

    // 超出长度多行显示
    .el-tag {
      height: auto;
      .el-select__tags-text {
        white-space: pre-wrap;
      }
    }
    // 超出单行显示'...'
    // .el-tag {
    //   position: relative;
    //   max-width: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   .el-select__tags-text {
    //     white-space: nowrap;
    //     text-overflow: ellipsis;
    //     overflow: hidden;
    //     padding-right: 10px;
    //     display: inline-block;
    //     max-width: 100%;
    //   }
    //   .el-tag__close {
    //     position: absolute;
    //     right: 0;
    //     top: 4px;
    //   }
    // }

  }
}
</style>

<style lang="scss" scoped>
.el-radio-group,
.el-checkbox-group {
  width: 100%;

  label {
    width: auto;
    display: inline-block;
    margin-right: 20px;
    padding-left: 0;
  }

  ::v-deep .el-checkbox__label,
  ::v-deep .el-radio__label {
    overflow-wrap: break-word;
    text-overflow: ellipsis; 
    white-space: normal;
    word-break: break-all;
    padding-left: 6px;
  }
}
</style>