
<template>
  <div class="form-select">
    <!-- 多选 -->
    <el-select
      v-if="updata"
      :id="`form_${field.fieldName}`"
      popper-class="common-advance-popper"
      style="width: 100%;"
      :placeholder="placeholder"
      :clearable="clearable"
      :multiple="isMulti"
      ref="elSelect"
      filterable
      :value="value"
      @change="input"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.text"
        :value="item.value"
      >
      </el-option>
    </el-select>

  </div>
</template>

<script>
import FormMixin from '@src/component/form/mixin/form';

export default {
  name: 'form-select-search',
  mixins: [FormMixin],
  props: {
    value: [String, Number, Array],
    source: {
      type: Array,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      newValue: this.value,
      updata: true
    };
  },
  watch: {
    isMulti(v) {
      this.updata = false
      setTimeout(() => {
        this.updata = true
      })
    }
  },
  computed: {
    isMulti() {
      let setting = this.field.setting || {};
      return setting.isMulti;
    },
    dataSource() {
      let setting = this.field.setting || {};
      return setting.dataSource || [];
    },
    dataSourceIds() {
      let setting = this.field.setting || {};
      return setting.dataSourceIds || [];
    },
    options() {
      let setting = this.field.setting || {};
      let dataSource = setting.dataSource || [];

      dataSource = dataSource.map((d) => {
        if (typeof d === 'string') {
          return {
            text: d,
            value: d,
          };
        }
        return d;
      });
      return this.source || dataSource || [];
    },
  },
  methods: {
    input(newValue) {
      let oldValue = null;
      this.$refs.elSelect.blur();

      // 设置对应的ID字段值
      this.setIdFieldValue(newValue);

      this.$emit('update', { newValue, oldValue, field: this.field });
      this.$emit('input', newValue);
    },
    /** 设置对应的ID字段值 */
    setIdFieldValue(selectedValues) {
      const fieldName = this.field.fieldName;
      const idFieldName = `${fieldName}_id`;

      let selectedIds = [];

      if (this.isMulti) {
        // 多选情况
        if (Array.isArray(selectedValues)) {
          selectedIds = selectedValues.map(value => this.getIdByValue(value)).filter(Boolean);
        }
      } else {
        // 单选情况
        const id = this.getIdByValue(selectedValues);
        if (id) {
          selectedIds = [id];
        }
      }

      // 发送ID字段更新事件
      const idValue = this.isMulti ? selectedIds : (selectedIds[0] || null);
      this.$emit('update', {
        newValue: idValue,
        oldValue: null,
        field: { ...this.field, fieldName: idFieldName }
      });
    },
    /** 根据value获取对应的ID */
    getIdByValue(value) {
      const index = this.dataSource.findIndex(item => {
        if (typeof item === 'string') {
          return item === value;
        }
        return item?.value === value;
      });

      if (index >= 0) {
        // 如果有dataSourceIds，使用对应的ID，否则使用默认ID（index+1）
        return this.dataSourceIds[index] || String(index + 1);
      }

      return null;
    },
    setDefaultValue() {
      try {
        const defaultValue = this.field?.defaultValue
        const useDefaultValue = this.field?.useDefaultValue ?? false;
        if (defaultValue && useDefaultValue) {
          this.$emit('update', { newValue: defaultValue, oldValue: null, field: this.field });
          this.$emit('input', defaultValue);
        }
      } catch (error) {
        console.log('catch', error)
      }
    }
  },
  mounted() {
    this.$nextTick(()=>{
      this.setDefaultValue()
    })
  }
};
</script>


<style lang="scss">
.form-select {
  width: 100%;

  .el-select {
    width: 100%;

    .el-input__inner {
      padding-left: 10px;
    }

    // 超出长度多行显示
    .el-tag {
      height: auto;
      .el-select__tags-text {
        white-space: pre-wrap;
      }
    }
    // 超出单行显示'...'
    // .el-tag {
    //   position: relative;
    //   max-width: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   .el-select__tags-text {
    //     white-space: nowrap;
    //     text-overflow: ellipsis;
    //     overflow: hidden;
    //     padding-right: 10px;
    //     display: inline-block;
    //     max-width: 100%;
    //   }
    //   .el-tag__close {
    //     position: absolute;
    //     right: 0;
    //     top: 4px;
    //   }
    // }
  }
}
</style>
