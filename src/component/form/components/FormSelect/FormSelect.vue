<template>
  <div class="form-select">
    <!-- start 下拉模式、标签模式 -->
    <template v-if="selectType==1 || selectType==3">
      <!-- 多选 -->
      <el-select
        :id="`form_${field.fieldName}`"
        :placeholder="placeholder"
        :clearable="clearable"
        :multiple="isMulti"
        :multiple-limit="multiLimt"
        :class="{'select-inline-edit': isSelectInlineEdit }"
        ref="elSelect"
        filterable
        :value="value"
        :disabled="disabled || disabledJustForSelect"
        @change="input"
      >
        <el-option
          v-for="(item, index) in options"
          :key="`${item.value}_${index}`"
          :label="item.text"
          :value="item.value"
          :style="optionStyle"
        >
          <slot name="option" :option="item"></slot>
        </el-option>
      </el-select>
    </template>
    <!-- end 下拉模式 -->

    <!-- start 平铺模式 -->
    <template v-else-if="selectType==2">
      <!-- start 单选 -->
      <el-radio-group v-model="newValue" @change="input" v-if="!isMulti">
        <el-radio
          v-for="item in options"
          :label="item.text" 
          :key="item.value"
          :value="item.value">
          {{item.text}}
        </el-radio>
      </el-radio-group>
      <!-- end 单选 -->

      <!-- start 多选 -->
      <el-checkbox-group v-model="newValue" @change="input" v-if="isMulti">
        <el-checkbox  
          v-for="item in options" 
          :label="item.text" 
          :key="item.id">
          {{item.text}}
        </el-checkbox>
      </el-checkbox-group>
      <!-- end 多选 -->
    </template>
    <!-- end 平铺模式 -->
  </div>
</template>

<script>
import FormMixin from '@src/component/form/mixin/form';
import { isEmpty, isString } from 'pub-bbx-utils';

export default {
  name: 'form-select',
  mixins: [FormMixin],
  props: {
    value: [String, Number, Array],
    source: {
      type: Array
    },
    clearable: {
      type: Boolean,
      default: true
    },
    optionStyle: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    disabledJustForSelect:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      newValue: this.value
    }
  },
  watch: {
    value(newValue) {
      this.newValue = newValue
      // 设置对应的ID字段值
      this.setIdFieldValue(newValue);
    }
  },
  computed: {
    selectType() {
      let setting = this.field.setting || {};
      return setting.selectType || 1;
    },
    isMulti(){
      let setting = this.field.setting || {};
      return setting.isMulti;
    },
    multiLimt() {
      let setting = this.field.setting || {};
      return setting.limit || 0;
    },
    dataSource() {
      let setting = this.field.setting || {};
      return setting.dataSource || [];
    },
    dataSourceIds() {
      let setting = this.field.setting || {};
      return setting.dataSourceIds || [];
    },
    options(){
      // 过滤掉空值，兼容多语言环境下某个语言有数据，其它数据里面都是空值的情况
      const dataSource = this.dataSource.filter(Boolean).map(d => {
        if (typeof d === 'string') {
          return {
            text: d,
            value: d,
          }
        }
        return d;
      });
      const source = this.source?.filter(s => s && !isEmpty(s.value))
      return source || dataSource || [];
    },

  },
  mounted() {
    if(this.isMulti && this.value?.length && Array.isArray(this.value)) {
      // fix bug 20153
      const correctNewValue = this.value.filter(item => {
        return this.dataSource.find((sourceItem=> {
          if(isString(sourceItem)) {
            return sourceItem == item
          }
          return sourceItem?.value == item
        }))
      });
      this.$emit('update', {newValue: correctNewValue, oldValue: this.value, field: this.field});
    }
  },
  methods: {
    input(newValue){
      let oldValue = null;
      if(this.selectType == 1) this.$refs.elSelect.blur();
      this.$emit('update', {newValue, oldValue, field: this.field});
      this.$emit('input', newValue);
    },
    /** 设置对应的ID字段值 */
    setIdFieldValue(selectedValues) {
      const fieldName = this.field.fieldName;
      const idFieldName = `${fieldName}_id`;

      let selectedIds = [];

      if (this.isMulti) {
        // 多选情况
        if (Array.isArray(selectedValues)) {
          selectedIds = selectedValues.map(value => this.getIdByValue(value)).filter(Boolean);
        }
      } else {
        // 单选情况
        const id = this.getIdByValue(selectedValues);
        if (id) {
          selectedIds = [id];
        }
      }

      // 发送ID字段更新事件
      const idValue = this.isMulti ? selectedIds : (selectedIds[0] || null);
      this.$emit('update', {
        newValue: idValue,
        oldValue: null,
        field: { ...this.field, fieldName: idFieldName }
      });
    },

    /** 根据value获取对应的ID */
    getIdByValue(value) {
      const index = this.dataSource.findIndex(item => {
        if (typeof item === 'string') {
          return item === value;
        }
        return item?.value === value;
      });

      if (index >= 0) {
        // 如果有dataSourceIds，使用对应的ID，否则使用默认ID（index+1）
        return this.dataSourceIds[index] || String(index + 1);
      }

      return null;
    }
  }
}
</script>


<style lang="scss">
.form-select{
  width: 100%;

  .el-select{
    width: 100%;

    .el-input__inner{
      padding-left: 10px;
    }

    // 超出长度多行显示
    .el-tag {
      height: auto;
      .el-select__tags-text {
        white-space: pre-wrap;
      }
    }
    // 超出单行显示'...'
    // .el-tag {
    //   position: relative;
    //   max-width: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   .el-select__tags-text {
    //     white-space: nowrap;
    //     text-overflow: ellipsis;
    //     overflow: hidden;
    //     padding-right: 10px;
    //     display: inline-block;
    //     max-width: 100%;
    //   }
    //   .el-tag__close {
    //     position: absolute;
    //     right: 0;
    //     top: 4px;
    //   }
    // }

  }
}
</style>

<style lang="scss" scoped>
.el-radio-group,
.el-checkbox-group {
  width: 100%;

  label {
    width: auto;
    display: inline-block;
    margin-right: 20px;
    padding-left: 0;
  }

  ::v-deep .el-checkbox__label,
  ::v-deep .el-radio__label {
    overflow-wrap: break-word;
    text-overflow: ellipsis; 
    white-space: normal;
    word-break: break-all;
    padding-left: 6px;
  }
}
.select-inline-edit{
  ::v-deep .el-select__tags{
    overflow-x: auto !important;
    scrollbar-width: none; /* 针对Firefox, IE和Edge隐藏滚动条 */
    height: 30px;
    .el-select__input {
      margin-left: 8px;
      background-color: transparent !important;
    }
    &::-webkit-scrollbar {
      display: none; /* 针对WebKit浏览器隐藏滚动条 */
    }
    span{
      display: flex;
      .el-tag{
        .el-select__tags-text{
          word-break: normal;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>