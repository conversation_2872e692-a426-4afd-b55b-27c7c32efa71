# FormSelect工具函数使用示例

这个文件提供了一系列工具函数来快速获取和处理FormSelect组件的ID值。

## 导入方式

```javascript
import { 
  getSelectId, 
  getSelectValueById, 
  getFormSelectId, 
  getFieldSelectId,
  getFieldSelectValueById,
  getAllFormSelectIds,
  validateSelectId
} from '@src/component/form/util'
```

## 基础用法

### 1. 根据选中值获取ID

```javascript
// 单选情况
const dataSource = ['选项1', '选项2', '选项3'];
const dataSourceIds = ['opt1', 'opt2', 'opt3'];
const selectedValue = '选项2';

const id = getSelectId(selectedValue, dataSource, dataSourceIds, false);
console.log(id); // 'opt2'

// 多选情况
const selectedValues = ['选项1', '选项3'];
const ids = getSelectId(selectedValues, dataSource, dataSourceIds, true);
console.log(ids); // ['opt1', 'opt3']
```

### 2. 根据ID获取对应的值

```javascript
// 单选情况
const selectedId = 'opt2';
const value = getSelectValueById(selectedId, dataSource, dataSourceIds, false);
console.log(value); // '选项2'

// 多选情况
const selectedIds = ['opt1', 'opt3'];
const values = getSelectValueById(selectedIds, dataSource, dataSourceIds, true);
console.log(values); // ['选项1', '选项3']
```

### 3. 从表单数据中获取ID

```javascript
const formData = {
  field_abc: '选项2',
  field_abc_id: 'opt2',
  field_xyz: ['选项1', '选项3'],
  field_xyz_id: ['opt1', 'opt3']
};

// 获取单个字段的ID
const id = getFormSelectId(formData, 'field_abc');
console.log(id); // 'opt2'

const ids = getFormSelectId(formData, 'field_xyz');
console.log(ids); // ['opt1', 'opt3']
```

### 4. 从字段配置中获取ID

```javascript
const field = {
  fieldName: 'field_abc',
  formType: 'select',
  setting: {
    dataSource: ['选项1', '选项2', '选项3'],
    dataSourceIds: ['opt1', 'opt2', 'opt3'],
    isMulti: false
  }
};

const selectedValue = '选项2';
const id = getFieldSelectId(field, selectedValue);
console.log(id); // 'opt2'
```

### 5. 批量获取所有FormSelect字段的ID

```javascript
const formData = {
  field_abc: '选项2',
  field_xyz: ['选项1', '选项3'],
  other_field: 'some value'
};

const fields = [
  {
    fieldName: 'field_abc',
    formType: 'select',
    isSystem: false,
    setting: {
      dataSource: ['选项1', '选项2', '选项3'],
      dataSourceIds: ['opt1', 'opt2', 'opt3'],
      isMulti: false
    }
  },
  {
    fieldName: 'field_xyz',
    formType: 'select',
    isSystem: false,
    setting: {
      dataSource: ['选项1', '选项2', '选项3'],
      dataSourceIds: ['opt1', 'opt2', 'opt3'],
      isMulti: true
    }
  }
];

const allIds = getAllFormSelectIds(formData, fields);
console.log(allIds);
// {
//   field_abc_id: 'opt2',
//   field_xyz_id: ['opt1', 'opt3']
// }
```

### 6. 验证ID是否有效

```javascript
const field = {
  formType: 'select',
  setting: {
    dataSource: ['选项1', '选项2', '选项3'],
    dataSourceIds: ['opt1', 'opt2', 'opt3'],
    isMulti: false
  }
};

console.log(validateSelectId(field, 'opt2')); // true
console.log(validateSelectId(field, 'invalid_id')); // false
console.log(validateSelectId(field, '2')); // true (默认ID规则)
```

## 实际应用场景

### 场景1：表单提交时获取所有ID值

```javascript
// 在表单提交时
function onSubmit() {
  const formData = this.form;
  const fields = this.fields;
  
  // 获取所有select字段的ID值
  const selectIds = getAllFormSelectIds(formData, fields);
  
  // 合并到提交数据中
  const submitData = {
    ...formData,
    ...selectIds
  };
  
  // 提交数据
  this.submitForm(submitData);
}
```

### 场景2：根据ID回显选中值

```javascript
// 从服务器获取数据后回显
function loadFormData(serverData) {
  const fields = this.fields;
  
  fields.forEach(field => {
    if (field.formType === 'select') {
      const fieldName = field.fieldName;
      const idFieldName = `${fieldName}_id`;
      
      if (serverData[idFieldName]) {
        // 根据ID获取对应的值进行回显
        const value = getFieldSelectValueById(field, serverData[idFieldName]);
        this.form[fieldName] = value;
      }
    }
  });
}
```

### 场景3：数据验证

```javascript
// 验证表单数据的完整性
function validateFormData() {
  const formData = this.form;
  const fields = this.fields;
  
  let isValid = true;
  
  fields.forEach(field => {
    if (field.formType === 'select' && field.isNull === 0) { // 必填字段
      const fieldName = field.fieldName;
      const selectedValue = formData[fieldName];
      
      if (selectedValue) {
        const id = getFieldSelectId(field, selectedValue);
        if (!id || !validateSelectId(field, id)) {
          console.error(`字段 ${fieldName} 的ID值无效`);
          isValid = false;
        }
      }
    }
  });
  
  return isValid;
}
```

## 注意事项

1. **默认ID规则**：如果没有配置`dataSourceIds`，工具函数会使用默认ID规则（index+1）
2. **数据类型**：确保传入的参数类型正确，特别是多选时的数组类型
3. **空值处理**：工具函数会正确处理空值情况
4. **兼容性**：工具函数兼容新旧数据格式
