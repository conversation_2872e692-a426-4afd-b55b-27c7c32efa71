/**
 * FormSelect组件相关的工具函数
 * 用于快速获取和处理下拉选择组件的ID值
 */

/**
 * 根据选中的值获取对应的ID
 * @param {string|Array} selectedValue - 选中的值（单选为字符串，多选为数组）
 * @param {Array} dataSource - 数据源数组
 * @param {Array} dataSourceIds - ID数组，与dataSource对应
 * @param {boolean} isMulti - 是否多选
 * @returns {string|Array|null} 对应的ID值
 */
export function getSelectId(selectedValue, dataSource = [], dataSourceIds = [], isMulti = false) {
  if (!selectedValue) return isMulti ? [] : null;
  
  if (isMulti) {
    if (!Array.isArray(selectedValue)) return [];
    return selectedValue.map(value => getSingleSelectId(value, dataSource, dataSourceIds)).filter(Boolean);
  } else {
    return getSingleSelectId(selectedValue, dataSource, dataSourceIds);
  }
}

/**
 * 根据单个值获取对应的ID
 * @param {string} value - 选中的值
 * @param {Array} dataSource - 数据源数组
 * @param {Array} dataSourceIds - ID数组
 * @returns {string|null} 对应的ID值
 */
function getSingleSelectId(value, dataSource, dataSourceIds) {
  const index = dataSource.findIndex(item => {
    if (typeof item === 'string') {
      return item === value;
    }
    return item?.value === value;
  });
  
  if (index >= 0) {
    // 如果有dataSourceIds，使用对应的ID，否则使用默认ID（index+1）
    return dataSourceIds[index] || String(index + 1);
  }
  
  return null;
}

/**
 * 根据ID获取对应的值
 * @param {string|Array} selectedId - 选中的ID（单选为字符串，多选为数组）
 * @param {Array} dataSource - 数据源数组
 * @param {Array} dataSourceIds - ID数组，与dataSource对应
 * @param {boolean} isMulti - 是否多选
 * @returns {string|Array|null} 对应的值
 */
export function getSelectValueById(selectedId, dataSource = [], dataSourceIds = [], isMulti = false) {
  if (!selectedId) return isMulti ? [] : null;
  
  if (isMulti) {
    if (!Array.isArray(selectedId)) return [];
    return selectedId.map(id => getSingleSelectValueById(id, dataSource, dataSourceIds)).filter(Boolean);
  } else {
    return getSingleSelectValueById(selectedId, dataSource, dataSourceIds);
  }
}

/**
 * 根据单个ID获取对应的值
 * @param {string} id - ID值
 * @param {Array} dataSource - 数据源数组
 * @param {Array} dataSourceIds - ID数组
 * @returns {string|null} 对应的值
 */
function getSingleSelectValueById(id, dataSource, dataSourceIds) {
  let index = dataSourceIds.findIndex(sourceId => sourceId === id);
  
  // 如果在dataSourceIds中没找到，尝试按默认ID规则查找（index+1）
  if (index < 0) {
    const numId = parseInt(id);
    if (!isNaN(numId) && numId > 0 && numId <= dataSource.length) {
      index = numId - 1;
    }
  }
  
  if (index >= 0 && index < dataSource.length) {
    const item = dataSource[index];
    return typeof item === 'string' ? item : item?.value;
  }
  
  return null;
}

/**
 * 从表单数据中获取FormSelect字段的ID值
 * @param {Object} formData - 表单数据对象
 * @param {string} fieldName - 字段名
 * @returns {string|Array|null} 对应的ID值
 */
export function getFormSelectId(formData, fieldName) {
  const idFieldName = `${fieldName}_id`;
  return formData[idFieldName] || null;
}

/**
 * 从表单字段配置中获取选项的ID
 * @param {Object} field - 字段配置对象
 * @param {string|Array} selectedValue - 选中的值
 * @returns {string|Array|null} 对应的ID值
 */
export function getFieldSelectId(field, selectedValue) {
  if (!field || field.formType !== 'select') return null;
  
  const setting = field.setting || {};
  const dataSource = setting.dataSource || [];
  const dataSourceIds = setting.dataSourceIds || [];
  const isMulti = setting.isMulti || false;
  
  return getSelectId(selectedValue, dataSource, dataSourceIds, isMulti);
}

/**
 * 从表单字段配置中根据ID获取对应的值
 * @param {Object} field - 字段配置对象
 * @param {string|Array} selectedId - 选中的ID
 * @returns {string|Array|null} 对应的值
 */
export function getFieldSelectValueById(field, selectedId) {
  if (!field || field.formType !== 'select') return null;
  
  const setting = field.setting || {};
  const dataSource = setting.dataSource || [];
  const dataSourceIds = setting.dataSourceIds || [];
  const isMulti = setting.isMulti || false;
  
  return getSelectValueById(selectedId, dataSource, dataSourceIds, isMulti);
}

/**
 * 批量获取表单中所有FormSelect字段的ID值
 * @param {Object} formData - 表单数据对象
 * @param {Array} fields - 字段配置数组
 * @returns {Object} 包含所有select字段ID的对象
 */
export function getAllFormSelectIds(formData, fields) {
  const result = {};
  
  fields.forEach(field => {
    if (field.formType === 'select' && !field.isSystem) {
      const fieldName = field.fieldName;
      const selectedValue = formData[fieldName];
      const id = getFieldSelectId(field, selectedValue);
      
      if (id !== null) {
        result[`${fieldName}_id`] = id;
      }
    }
  });
  
  return result;
}

/**
 * 验证FormSelect字段的ID是否有效
 * @param {Object} field - 字段配置对象
 * @param {string|Array} selectedId - 选中的ID
 * @returns {boolean} 是否有效
 */
export function validateSelectId(field, selectedId) {
  if (!field || field.formType !== 'select') return false;
  
  const setting = field.setting || {};
  const dataSource = setting.dataSource || [];
  const dataSourceIds = setting.dataSourceIds || [];
  const isMulti = setting.isMulti || false;
  
  if (!selectedId) return true; // 空值认为有效
  
  if (isMulti) {
    if (!Array.isArray(selectedId)) return false;
    return selectedId.every(id => isValidSingleId(id, dataSource, dataSourceIds));
  } else {
    return isValidSingleId(selectedId, dataSource, dataSourceIds);
  }
}

/**
 * 验证单个ID是否有效
 * @param {string} id - ID值
 * @param {Array} dataSource - 数据源数组
 * @param {Array} dataSourceIds - ID数组
 * @returns {boolean} 是否有效
 */
function isValidSingleId(id, dataSource, dataSourceIds) {
  // 检查是否在dataSourceIds中
  if (dataSourceIds.includes(id)) return true;
  
  // 检查是否符合默认ID规则（index+1）
  const numId = parseInt(id);
  if (!isNaN(numId) && numId > 0 && numId <= dataSource.length) {
    return true;
  }
  
  return false;
}
