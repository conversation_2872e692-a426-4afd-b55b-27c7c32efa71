<template>
  <div :class="['base-dist-picker','international-pick']">
    <!-- 国家 -->
    <el-select 
      v-if="internationalGray"
      class="country-cls bbx-form-cell-item"
      v-model="countryValue" 
      filterable
      :disabled="disabled"
      :clearable="countryClearable"
      :placeholder="$t('common.placeholder.selectCountry')"
      @change="countryChangeHandle"
    >
      <el-option
        v-for="(item,index) in countryList"
        :key="`${item.key}${index}`"
        :label="item.name"
        :value="item.key">
      </el-option>
    </el-select>

    <el-cascader
      ref="addressCascader"
      class="bbx-form-cell-item flex-1"
      clearable
      :props="props"
      :placeholder="$t('common.placeholder.searchCity')"
      :options="areaList"
      v-model="areaValue"
      :disabled="disabled"
      filterable
      @change="areaChangeHandle"
    >
    </el-cascader>
  </div>
</template>

<script>
import i18n from '@src/locales';

import { getCountries, getAreaDivisionsInfo } from '@src/api/Address';
// 国际化灰度
import useFormMultiLanguage from '@hooks/useFormMultiLanguage'
const { internationalGray } = useFormMultiLanguage()

// api
import { creatServerCachApi } from '@src/api/GuideApi'
const countryCacheKey = 'INTERNATIONAL_DIST_COUNTRY'

import { getRootWindow } from '@src/util/dom';

import { findComponentUpward } from '@src/util/assist';
import { NEED_ADDRESS_STREET_MODE } from '@src/component/form/components/FormAddress/model/const.ts';


export default {
  name: 'base-dist-picker-international',
  data() {
    return {
      internationalGray,
      key: 0,
      // 是否监听value,
      isWatchValue: true,
      options: [],
      props: {
        value: 'key',
        label: 'name',
        children: 'dist',
        checkStrictly: true
      },

      // 选中的国家数据
      countryValue:'',
      // 国家列表
      countryList:[],
      // 选中的省市区值
      areaValue:[],
      // 当前国家下面的省市区列表数据
      areaList:[],
      // {国家：省市区数据}国家和省市区列表数据集合
      areaObj:{},
      isSetDefaultCountry: false
    }
  },

  props: {
    value: {
      type: Array,
      default: () => ([]),
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.searchCity')
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 国家和省市区展示在一行（搜索组件需要展示一行），默认展示两行
    showOneLine: {
      type: Boolean,
      default: false
    },
    // 是否默认选中中国
    showDefaultCountry: {
      type: Boolean,
      default: true
    },
    countryClearable: {
      type: Boolean,
      default: false
    },
    // 因为showDefaultCountry没有对默认选中中国严格控制，没值的时候还是会选中国，又不确定该不该改造，所以暂时另加个字段~
    noDefaultCountry: {
      type: Boolean,
      default: false
    },
    // 模块
    module: {
      type: String,
      default: '',
    }
  },
  computed: {
    mode() {
      let parent = findComponentUpward(this, 'form-builder');
      return parent?.mode || this.module || '';
    },
    /**todo:是否需要获取街道（客户模块且未开启国际化） */
    isNeedGetStreet() {
      return NEED_ADDRESS_STREET_MODE.includes(this.mode) && !internationalGray && this.isOpenAddressStreet
    },
    /** 是否开启地址街道灰度 */
    isOpenAddressStreet() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.CUSTOMER_ADDRESS_STREET_CONTROL ?? false;
    },
  },

  watch: {
    // 监听value主要用于编辑页面的数据回显，当用户改动地址后，就不需要监听value
    value:{
      async handler(newVal,oldVal){
        // 级联选项监听如果点击最后一级选项关闭
        if (this.$refs.addressCascader) {
          let children = this.$refs.addressCascader.getCheckedNodes();
          if (
            children &&
            children.length > 0 &&
            children[0]?.children?.length == 0
          ) {
            //判断有没有下级
            this.$refs.addressCascader.dropDownVisible = false; //监听值发生变化就关闭它
          }
        }
        
        // if(!this.isWatchValue) return;
        // 老数据没有国家字段，默认的国家是中国，此处是中文汉字，接口那边存储的key都是中文，不需要多语言！！！！！！！！！不需要多语言！！！！！！！！！
        // !newVal[0] && newVal[1] && (newVal[0] = '中国');

        // // 新建数据，需要根据ip定位，自动设置国家
        // if(!newVal.length){
        // }
        // / 给国家字段赋值默认值的情况
        // 1、传入的数据中没有国家字段时，给国家字段赋值中国
        // 2、传入的是空数组时，且showDefaultCountry为true时，给国家字段赋值中国

        if(
          !this.isSetDefaultCountry
          && !this.noDefaultCountry
          && ((newVal.length > 0 && !newVal[0]) ||
          (this.showDefaultCountry && !newVal.length))
        ){
          let val = [ ...newVal ];
            val[0] = '中国';
          this.$emit('input',val);
        }
        // 目前清空时默认还是会设置中国，所以先加个isSetDefaultCountry判断下
        if(this.countryClearable) {
          this.isSetDefaultCountry = true
        }

        await this.getCountriesCity();

        const [ newCountryVal ] = newVal || [];
        const [ oldCountryVal ] = oldVal || [];
        // 通过国家字段获取省市区数据
        if(newCountryVal !== oldCountryVal ){
          this.setDist(newCountryVal);
        }
        // 非国际化虽然不显示国家，但是要获取省市区数据
        if (!internationalGray && !oldVal) {
          // 只在第一次进入需要吧？？？
          this.setDist('中国');
        }
      },

      immediate: true
    },
  },

  mounted(){
    try {
      this.getInternatilonalCountries()
    } catch (error) {
      console.error(error)
    }
  },

  methods: {
    // 存储国家字段到缓存
    async saveCountryCache(country){
      if(!country) return
      sessionStorage.setItem('country', country)
      creatServerCachApi({
        type: countryCacheKey,
        step: 1,
        isComplete: 1,
        userConfig: country
      })
    },
    // 编辑页面用于数据回显的方案
    async getCountriesCity(){
      // 先判断是this.options是否有值（国家数据）
      if(!this.countryList?.length){
        // 没有说明在mounted接口调用的获取所有国家的接口还没有返回数据，不采用定时器等待了直接再调用一次
        await this.getInternatilonalCountries()
      }

      this.countryValue = this.value[0] || '';
      let areaValue = [...this.value];
      areaValue.length && areaValue.shift();
      this.areaValue = areaValue;
     
    },

    // 获取国家列表
    async getInternatilonalCountries() {
      if(this.countryList?.length) return;
      const { status, data } = await getCountries();
      if(status === 0){
        this.countryList = data || []
      }
    },

    // 获取指定国家的所有省市区数据
    async getArea(divisionKey){
      const params = {
        divisionKey,
        ...(this.isNeedGetStreet && { streetFlag: true })
      };
      const { status, data } = await getAreaDivisionsInfo(params)
      
      const dist = data.dist || []
      if(status === 0 && dist.length){
        // 设置叶子结点
        this.setLeaf(dist)
      }

      return dist
    },

    // 国家字段修改后触发
    async countryChangeHandle(value){
      // 当用户修改了value值，就不监听value了
      this.isWatchValue = false

      // 清空选中的省市区数据
      this.clearValue()

      this.saveCountryCache(value)
      const { key, haveStates, dist } = this.findCountryData(value)
      
      // 当前国家下面没有省市区数据时，将选中的国家数据传递给父组件
      if(!haveStates) {
        this.$emit('input', [value]);
        return
      }

      // 有省市区数据，但是没有获取这些数据时，调用接口
      if(!dist){
        this.setDist(key)
      }else{
        // 将省市区列表数据更新为当前国家下的省市区数据
        this.$set(this,'areaList',dist || [])
      }

      this.$emit('input', [this.countryValue]);
    },

    // 省市区字段修改后触发
    areaChangeHandle(value){
      // 当用户修改了value值，就不监听value了
      this.isWatchValue = false
      // 将国家和省市区数据一起传递给父组件
      // 设置大区adCode
      let adCode = '';
      if (value.length === 3) {
        adCode = this.findRegionAdCode(value)
      }
      this.$emit('input', [ this.countryValue, ...value], {adCode});
    },

    /**
     * 根据地区名称路径查找对应的地区代码
     * @param {Array} value - 地区名称数组，如['安徽省', '安庆市', '大观区']
     * @returns {String|null} - 最后一级地区的adCode，未找到时返回null
     */
    findRegionAdCode(value) {
      if (!value || !Array.isArray(value) || value.length === 0) {
        console.warn('findRegionAdCode: 参数无效', value)
        return null
      }

      if (!this.areaList || this.areaList.length === 0) {
        console.warn('findRegionAdCode: areaList为空')
        return null
      }

      // 从areaList开始递归查找
      const targetRegion = this.findAdCodeInTree(this.areaList, value, 0)
      return targetRegion?.adCode || null
    },

    /**
     * 在树形数据结构中递归查找地区
     * @param {Array} areaData - 当前层级的地区数据数组
     * @param {Array} searchPath - 查找路径数组
     * @param {Number} currentIndex - 当前查找的层级索引
     * @returns {Object|null} - 找到的地区对象，未找到时返回空字符串null
     */
    findAdCodeInTree(areaData, searchPath, currentIndex) {
      if (!areaData || !Array.isArray(areaData) || currentIndex >= searchPath.length) {
        return null
      }

      const targetName = searchPath[currentIndex]
      
      // 在当前层级查找匹配的地区
      const matchedRegion = areaData.find(region => 
        region.name === targetName || region.key === targetName
      )

      if (!matchedRegion) {
        return null
      }

      // 如果已经是最后一级，返回找到的地区
      if (currentIndex === searchPath.length - 1) {
        return matchedRegion
      }

      // 如果还有下一级，继续在子级中查找
      if (matchedRegion.dist && Array.isArray(matchedRegion.dist)) {
        return this.findAdCodeInTree(matchedRegion.dist, searchPath, currentIndex + 1)
      }

      // 没有子级但还没到最后一级，说明路径不完整
      return null
    },
    // 清空数据
    clearValue() {
      // 清空省市区列表数据
      this.areaList = [];
      // 清空选中的省市区数据
      this.areaValue = [];
    },

    // 通过国家获取该国家下的省市区数据，并记录下来
    async setDist(divisionKey){
      if(!divisionKey) return;
      const dist = await this.getArea(divisionKey)
      if(!dist?.length) return

      // 成功后将数据更新到areaObj
      this.$set(this.areaObj,divisionKey,dist)
      // 将省市区列表数据更新为当前国家下的省市区数据
      this.$set(this,'areaList',dist)
    },

    // 通过国家的key值筛选出this.options里面的该国家的其他数据，并返回
    findCountryData(key){
      if(!key) return {}
      let selectCountry = this.countryList.filter(country => {
        return country.key === key
      })
      return selectCountry[0] || {}
    },

    // 设置叶子结点
    setLeaf(arr){
      for(let item of arr){
        const { dist } = item
        if(dist?.length){
          item.leaf = false
          this.setLeaf(item.dist)
        }else{
          item.leaf = true
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
// 35196 【生产环境】【0304】高级搜索：区域字段高级搜索支持国家省市区，但省市区下拉菜单过窄
// 如果这里影响到别的模块的样式 需要删掉或修改 那需要把高级搜索的样式问题 35196 解决掉
.international-pick {
  .el-select,
  .el-cascader {
    flex: 1 !important;
  }
}
</style>
<style lang="scss">
  .location-cascader {
    .el-cascader-menu__item {
      display: flex;
      align-items: center;
      line-height: 21px;
      
      & > div {
        height: 21px;
        width: 100%;
        line-height: 21px;
        position: relative;
        padding-right: 8px;
      }
      
    }
    .el-cascader-menu {
      max-height: 204px;
    }
    
    .el-cascader-menu__item--extensible {
      font-family: "iconfont" !important;
      
      &::after {
        content: "";
        right: 5px;
      }
      
      .iconfont {
        color: #bfcbd9;
        position: absolute;
        right: 0px;
        top: 0;
      }
      
    }
  }
  
.base-dist-picker{
  .el-cascader__label{
    padding-left: 10px;
  }
  
  &:hover .el-input__inner{
    border-color: $input-border-hover-color;
  }
  .country-cls{
    margin-right: 8px;
    width:100%;
  }
}
.base-dist-picker.international-pick{
  display: flex;
  align-items: center;
}
.base-dist-picker.show-on-lLine{
  display:flex;
  .country-cls{
    margin:0 8px 0 0;
    width:auto;
  }
}
</style>