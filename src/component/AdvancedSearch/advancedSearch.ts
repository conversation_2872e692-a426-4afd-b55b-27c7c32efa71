// @ts-ignore
import type Field from '@src/model/Field';

import { t } from '@src/locales';
import { getCustomerListAsyn as getCustomerListAsync, getUserTag } from '@src/api/CustomerApi';
import { getTaskCustomerProductList, getTaskCustomerLinkman, getProductType } from '@src/api/TaskApi';
import { getServiceProviderList } from '@src/api/ServiceProvidersApi';
import { multiField, objectField } from './config';


// utils
import { genPlaceholder } from '@src/component/form/util';
import { OperatorType, isMultiValue } from './operator';
import { parse } from '@src/util/lang/object';
import { isString } from '@src/util/type';
/* enum */
import TableNameEnum from '@model/enum/TableNameEnum';
import { ProjectManageFieldNameMappingEnum } from '@src/model/enum/FieldMappingEnum';
import { WarrantyServiceItemLabelEnum } from '@src/model/enum/LabelEnum';

// 远程搜索方法类型
export type RemoteMethodType = (...args: any[]) => Promise<any>;
export type MethodType = (...args: any[]) => any;
// 组件绑定值类型
export type CompBindType = {
  placeholder?: string;
  remoteMethod?: RemoteMethodType;
  multiple?: boolean;
  collapsed?: boolean;
  on?: Record<string, FunctionConstructor>;
  [key: string]: any;
};

// 常用 item
export type commonUseItemType = {
  fieldName: string;
  isChecked: boolean;
};

export type SearchItemType = {
  id: number; // id 唯一值
  field?: Field | null; // 字段
  fieldName: string; // 字段名
  value: any; // 值
  comp: string; // 组件名称
  bind: CompBindType; // 组件绑定数据
  operator: string; // 选中条件
  operators: OperatorType[]; // 条件配置
  isCommonUse: boolean; // 常用
};

/**
 * 搜索客户
 */
export function searchCustomer(params: any = {}) {
  return getCustomerListAsync(params)
    .then((res: any) => {
      if (!res || !res.list) return Promise.reject('请求失败');

      res.list = res.list.map((customer: any) => {
        const { id, name, lmPhone, serialNumber } = customer;
        return { id, name, lmPhone, serialNumber };
      });
      return res;
    })
    .catch((e: any) => console.error(e));
}
/**
 * 搜索产品
 */
export function searchProduct(params: any = {}) {
  // params.customerId = this.form.customer || '';
  return getTaskCustomerProductList(params)
    .then((res: any) => {
      if (!res || !res?.list) return Promise.reject('请求失败');
      res.list = res.list.map((product: any) => {
        const { id, name, serialNumber, type, customerName, customerId } = product;
        return { id, name, serialNumber, type, customerName, customerId };
      });
      return res;
    })
    .catch((e: any) => console.error(e));
}

/**
 * 搜索联系人
 */
export function searchLinkman(params: any = {}) {
  return getTaskCustomerLinkman(params)
    .then((res: any) => {
      if (!res || !res.success || !res.result?.list) {
        return Promise.reject('请求失败');
      }
      res.result.list = (res.result.list || []).map(({ displayName, userId, name, id, phone = '' }: any) => {
        return Object.freeze({
          name: displayName || name || '',
          id: userId || id || '',
          phone,
        });
      });
      return res.result;
    })
    .catch((e: any) => console.error(e));
}

/**
 * 搜索用户
 */
export function searchUserTag(params: any = {}) {
  return getUserTag(params)
    .then((res: any) => {
      if (!res || !res.list) return Promise.reject('请求失败');
      res.list = res.list.map(({ displayName, userId, staffId, isDelete }: any) => {
        return Object.freeze({
          name: displayName,
          id: userId,
          staffId,
          isDelete
        });
      });
      return res;
    })
    .catch((e: any) => console.error(e));
}

/**
 * @description: 获取服务网点列表
 */
export async function searchService(params = {}) {
  return getServiceProviderList(params)
    .then((result: any) => {
      if (!result || !result.data) return Promise.reject('请求失败');

      result.data.list = result.data.list.map((res: any) =>
        Object.freeze({
          label: res.tagName,
          id: res.tagId,
          value: res.tagId,
          ...res
        }));
      return result.data;
    })
    .catch((e: any) => console.error(e));
}

/**
 * 搜索项 初始值
 * @param {SearchItemType} searchItem
 * @returns [] || {} || ''
 */
export function initValue(searchItem: SearchItemType) {
  const { field, operator } = searchItem;
  const { fieldName, formType, isSystem, setting } = field!;

  if (fieldName == 'status') return [];

  // object 处理
  if (objectField.includes(fieldName) || objectField.includes(formType)) return null;

  const isCommonUser = formType === 'user' && !isSystem; // 自定义user
  if (isCommonUser) return null;

  // 多选
  if (isMultiValue(operator) || multiField.includes(fieldName) || multiField.includes(formType)) return [];

  // select 处理
  if (formType === 'select') return '';

  // 多级选择
  if (formType === 'cascader') return [];

  // 多级选择
  if (formType === 'currency') return {
    number: null,
    currency: []
  };

  if (searchItem.comp === 'date-search') {
    // 日期类型处理
    if(['gt', 'lt', 'le', 'ge'].includes(searchItem.operator)){
      return '';
    }
    return searchItem.bind.type === 'date' ? '' : [];
  }

  return '';
}

// 特殊处理需要渲染的组件
const specialFieldCompMap: Record<string, string> = {
  // 自定义
  customer: 'customer-search', // 客户
  customerAddress: 'text-search', // 客户地址
  product: 'product-search', // 产品
  related_task: 'task-search', // 关联工单
  // 时间
  datetime: 'date-search', // 日期时间
  // 文本
  textarea: 'text-search', // 多行文本 ---> 单行文本
  phone: 'text-search', // 手机号
  email: 'text-search', // 邮箱
  location: 'text-search', // 位置
  code: 'text-search', // 扫码
  // 数字
  formula: 'number-search', // 计算公式 ---> number

  serialNumber: 'text-search', // 编号
  status: 'search-status-select',
  connector_related_task: 'connector-related-task-search', // 连接器项目中的关联工单
  role: 'remote-select-search', // 连接器项目中的知识库角色
  rich_text: 'rich-text-search', // 连接器项目中的知识库角色
  svcProjectName: 'remote-select-search', // 服务项目名称
  projectId: 'remote-select-search', // 任务管理-所属项目
  productCustomerMultiAddress: 'address-multi-search', // 产品模块客户地址多选搜索
};

export const createSearchItem = function (origin: Partial<SearchItemType> = {}): SearchItemType {
  return {
    id: ~~(Math.random() * 1000000),
    fieldName: '',
    field: null,
    value: null,
    comp: '',
    bind: {},
    operator: '',
    operators: [],
    isCommonUse: false,
    ...origin,
  };
};

type ComponentOption = {
  field: Field;
  operator: string;
  remoteMethod?: Record<string, RemoteMethodType>;
  isInsert?: boolean; // 连接器新增的时候日期组件不管操作符是什么只需要传一个值
  isConnector?: boolean; // 判断是不是连接器
};

// 组件绑定值
export const bindMap: Record<string, CompBindType> = {
  customer: {
    placeholder: t('common.placeholder.selectCustomer'),
    multiple: true,
    collapsed: true,
    remoteMethod: searchCustomer,
  },
  product: {
    placeholder: t('common.placeholder.selectProduct'),
    remoteMethod: searchProduct,
  },
  linkman: {
    // disableMap: true,
    placeholder: t('common.placeholder.selectContact'),
    remoteMethod: searchLinkman,
  },
  user: {
    multiple: true,
    remoteMethod: searchUserTag,
  },
  serialNumber: {
    placeholder: t('common.placeholder.input'),
  },
  serviceProvider: {
    placeholder: t('common.placeholder.select'),
    multiple: false,
    collapsed: true,
    remoteMethod: searchService,
  },
};

// 组件绑定值
export function genComponent(opts: ComponentOption) {
  const { field, operator, remoteMethod, isInsert, isConnector } = opts;
  const { fieldName, formType } = opts.field;

  const defaultComponentName = `${formType}-search`;
  // 计算渲染组件名称
  let component = specialFieldCompMap[fieldName] ?? specialFieldCompMap[formType] ?? defaultComponentName;
  // 空条件组件
  if(['isNull'].includes(operator)){
    component = '';
  }

  // 客户地址
  if (fieldName == ProjectManageFieldNameMappingEnum.Address) {
    component = 'text-search';
  }

  if (isConnector ) {
    switch (formType) {
    case 'related_task':
      // 如果是连接器的关联工单字段由于传值方式不一致使用connector-related-task-search跟之前的高级搜索里面的关联工单区分
      component = 'connector-related-task-search';
      break;

    default:
      break;
    }
  }

  // 设置组件绑定值
  const bind: CompBindType = {
    placeholder: genPlaceholder(field),
    ...(bindMap[fieldName] ?? bindMap[formType] ?? {}),
  };
  bind.multiple = isMultiValue(operator);
  bind.collapsed = bind.multiple; // 多选的折叠显示

  // setting 处理
  field.setting = isString(field.setting) ? parse(field.setting) : field.setting || {};

  if (field.formType === 'select' && !field.setting.isMulti && !field.isSystem) {
    field.setting.isMulti = true; // 自定义下拉菜单都是多选
    bind.multiple = true;
  } else if (field.formType === 'cascader') {
    // 多级菜单
    bind.multiple = !!field.setting.isMulti;
  } else {
    // 其他类型的下拉菜单
    field.setting.isMulti = bind.multiple;
  }

  if (field.formType === 'select' && isConnector) {
    // 连接器的下拉菜单都是单选
    field.setting.isMulti = false;
    bind.multiple = false;
  }

  field.setting.selectType = 1; // 改为下拉模式

  // date 处理

  // 需求时间只需要区间
  if (component === 'date-search') {
    bind.isRange = operator?.toLocaleLowerCase() === 'between';
    bind.type = field.formType === 'datetime' ? 'datetime' :'date'; // 这里只需要放 date 或者 dateTime
    // 'eq','notEq', 'gt','ge', 'lt', 'le', 'between'
    field?.setting?.dateType?.includes('HH')?
      bind.type = 'datetime'
      :
      bind.type = 'date';

    if(field?.setting?.dateType === 'yyyy-MM'){
      bind.type = 'month';
    }

    // if(['eq','notEq','gt', 'lt', 'le', 'ge', 'between'].includes(operator)){
    //   bind.type = 'datetime';
    // }

    if(['createTime', 'updateTime'].includes(fieldName))  bind.type = 'datetime';

    if (isInsert) {
      // 连接器新增日期类型字段type固定为date
      bind.isRange = false;
      bind.type = 'date';
    }
  }

  if(['formula', 'number'].includes(formType) && operator?.toLocaleLowerCase() === 'between'){
    component = 'number-range';
  }

  if ((isConnector && formType === 'number' && operator?.toLocaleLowerCase() === 'between') || operator?.toLocaleLowerCase() === 'not_between') {
    // 如果是连接器的关联工单字段由于传值方式不一致使用connector-related-task-search跟之前的高级搜索里面的关联工单区分
    component = 'number-range';
  }

  if (formType === 'tag') {
    component = 'tags-search';
  }

  // remote method
  const _remoteMethod = remoteMethod ? remoteMethod[fieldName] ?? remoteMethod[formType] : undefined;

  bind.remoteMethod = _remoteMethod ?? bind.remoteMethod;

  return {
    component,
    bind,
  };
}
