// 多选的值 fieldName
export const multiField = [
  'tags', // 部门
  'customer', // 客户 单选也传数组
  'related_customer', // 关联客户
  'related_customers', // 关联客户 -- 客户管理
  'serviceType', // 服务类型
  'serviceContent', //
  'level',
  'paymentMethod',
  'state',
  'allotTypeStr',
  'onceException',
  'allotUser',
  'synergyId',
  'createUser',
  'executor',
  'source', // 创建方式
  'refusedReason', // 拒绝原因
  'pausedReason', // 暂停原因
  'rollbackReason', // 退回原因
  'reallotReason', // 转派原因
  'offedReason', // 取消原因
  'svcProjectName',
  'productCustomerMultiAddress',
  'tag',
];

// Array
// const arrayField: string[] = ['area', 'cascader', 'user', 'tlmName'];
// object
export const objectField: string[] = [
  'tlmName', // 联系人 工单
  'linkman', // 联系人 客户管理
  'product', // 产品
  'customerAddress',
  'address', // 区域、地址
  'area', // 区域
  'link',
  'catalogId', //产品类型
];

export const isEmptyOrNotEmptyOperator  = ['empty', 'not_empty'];
