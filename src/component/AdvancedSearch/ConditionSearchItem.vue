<template>
  <div class="condition-search-item">
    <div class="condition-search-item__field-select">
      <el-select
        v-model="conditionItem.fieldName"
        :disabled="findFieldDisabled"
        :placeholder="$t('common.placeholder.inputKeyWordToSearch')"
        filterable
        @change="() => selectFiled(true)"
      >
        <el-option
          v-for="field of fields"
          :key="field.fieldName"
          :label="field.displayName"
          :value="field.fieldName"
          :disabled="getItemDisabled(field)"
        ></el-option>
      </el-select>
    </div>
    <div class="condition-search-item__condition-select">
      <el-select
        v-model="conditionItem.operator"
        :disabled="findOperatorDisabled"
        @change="() => changeCondition(true)"
      >
        <el-option
          v-for="field of conditionItem.operators"
          :key="field.value"
          :label="field.label"
          :value="field.value"
        ></el-option>
      </el-select>
    </div>
    <div class="condition-search-item__value">
      <component
        v-if="conditionItem.comp && conditionItem.operator && !isEmptyOrNotEmptyValue.includes(conditionItem.operator)"
        :is="conditionItem.comp"
        :disabled="findFormDisabled"
        :input-disabled="findFormDisabled"
        :label="conditionItem.field.displayName"
        :need-validate="false"
        :field="conditionItem.field"
        :multiple="conditionItem.multiple"
        :value="conditionItem.value"
        :operator="conditionItem.operator"
        v-bind="conditionItem.bind"
        :no-default-country="noDefaultCountry"
        :useSettingDateType="useTimeoutOperators"
        @update="changeValue"
      ></component>
      <el-input v-else :disabled="true"></el-input>
    </div>
  </div>
</template>
<script>
import { defineComponent, toRefs, unref, computed, nextTick } from 'vue';
// components
import FieldComponents from './FieldComponents';
import SearchStatusSelect from '@src/component/common/BaseSearchPanel/components/SearchStatusSelect.vue';
// util
import { genComponent, initValue } from './advancedSearch';
import { getFieldOperator } from './operator';
import conditionsMaps from '@src/view/designer/workflow/components/ConfigPanel/ConfigEdge/components/ConditionModal/maps';
import { isEmptyOrNotEmptyOperator } from '@src/component/AdvancedSearch/config';

import { searchItemTypeEnum, findThisConditionBanChooseOperationAndChoseField, findThisConditionBanEditForm } from '@src/component/form/components/FormConnector/components/advanceSearch/mock.ts'

export default defineComponent({
  name: 'ConditionSearchItem',
  components: {
    ...FieldComponents,
    [SearchStatusSelect.name]: SearchStatusSelect,
  },
  props: {
    fields: {
      type: Array,
      default: () => [],
    },
    conditionItem: {
      type: Object,
      default: () => ({
        field: {},
        fieldName: '',
      }),
    },
    // 远程搜索接口列表
    remoteMethod: {
      type: Object,
      default: () => ({}),
    },
    // 是否使用超时条件的操作符，目前用于流程超时设置
    useTimeoutOperators: {
      type: Boolean,
      default: false
    },
    mode:{
      type:String,
      default:''
    },
  },
  setup(props, { emit }) {
    const { conditionItem } = toRefs(props)

    const isConnectorMode = computed(()=>props.mode === 'connector')

    // 字段map
    let fieldMap = computed(() => {
      const map = {};
      props.fields.forEach(item => {
        if (!map[item.fieldName]) {
          map[item.fieldName] = item;
        }
      });
      return map;
    });

    const isEmptyOrNotEmptyValue = computed(()=> isEmptyOrNotEmptyOperator)

    /**
     * 选中field
     * @param {Boolean} needDefaultCondition 是否继续操作默认条件
     */
    async function selectFiled(needDefaultCondition = true) {
      const { fieldName } = unref(conditionItem);
      const field = fieldMap.value[fieldName];
      conditionItem.value.field = field;
      conditionItem.value.operators = isConnectorMode.value ? field.operators : props.useTimeoutOperators ? getTimeoutConditionOperators(field.formType) : getFieldOperator(field);
      if (!needDefaultCondition) return
      conditionItem.value.operator = isConnectorMode.value ? field.operator : unref(conditionItem).operators?.[0].value;
      conditionItem.value.comp = null;
      emit('selectFiled', fieldName);
      await nextTick();
      changeCondition();
    }

    // 获取超时条件操作符
    function getTimeoutConditionOperators(formType) {
      return conditionsMaps.formTypesConditions.get(formType).map(item => {
        item.value = item.val;
        item.label = item.name;
        return item;
      })
    }

    /**
     * 切换条件（等于……）
     * 生成对应组件、props、初始值
     * @param {Boolean} needInitValue 是否初始化value
     */
    function changeCondition(needInitValue = true) {
      const { field, operator } = unref(conditionItem);
      const { component, bind } = genComponent({
        remoteMethod: props.remoteMethod,
        field,
        operator,
      });
      conditionItem.value.bind = bind;
      conditionItem.value.comp = component;
      if(!needInitValue) return
      conditionItem.value.value = initValue(unref(conditionItem))
      emit('changeCondition', unref(conditionItem).value);
    }

    /**
     * 修改value
     * @param {*} value 组件回传值
     */
    function changeValue(value) {
      conditionItem.value.value = value;
      emit('changeValue', value);
    }

    /**
     * 初始化条件
     */
    function initConditionField() {
      const { fieldName, operator, value } = conditionItem.value || {};
      if (fieldName) {
        // 有条件不用走默认流程
        selectFiled(!operator)
        // 有条件和值不需要初始化值
        changeCondition(!operator && value !== null)
      }
    }

    {
      initConditionField(); // 初始化条件
    }

    const noDefaultCountry = computed(() => {
      return conditionItem.value.field?.fieldName === 'productCompleteAddress' ? true : undefined
    })

    const findFormDisabled = computed(()=>{
      return findThisConditionBanEditForm(props.conditionItem?.field)
    })

    const findOperatorDisabled = computed(()=>{
      if(!props.conditionItem.field){
        return true
      }
      if(findThisConditionBanChooseOperationAndChoseField(props.conditionItem?.field)){
        return true
      }
      return false
    })

    const findFieldDisabled = computed(()=>{
      if(findThisConditionBanChooseOperationAndChoseField(props.conditionItem?.field)){
        return true
      }
      return false
    })

    const getItemDisabled = (info)=>{
      return !!info?.disabled
    }

    return {
      selectFiled,
      changeCondition,
      changeValue,
      noDefaultCountry,
      isEmptyOrNotEmptyValue,
      findFormDisabled,
      findOperatorDisabled,
      findFieldDisabled,
      getItemDisabled,

    };
  },
});
</script>
<style lang="scss">
.condition-search-item {
  flex: 1;
  display: flex;
  align-items: center;

  &__field-select {
    .el-select {
      width: 150px;
    }
  }

  &__condition-select {
    margin: 0 12px;

    .el-select {
      width: 100px;
    }
  }

  &__value {
    flex: 1;

    .el-input,
    .el-select,
    .el-date-picker,
    .el-date-editor,
    .el-cascader {
      width: 100% !important;
    }
  }
  .condition-search-item__value{
    .international-pick{
      display: flex;
      .country-cls{
        margin:0 12px 0 0 !important;
      }
    }
  }
  .biz-team-select-block .biz-team-select {
    height: 100%;
  }
}
</style>
