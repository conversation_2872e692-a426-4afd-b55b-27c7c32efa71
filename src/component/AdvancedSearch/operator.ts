// @ts-ignore
import Field from '@src/model/Field';
import { t } from '@src/locales';

export type OperatorType = {
  label: string;
  value: string;
};

// 条件map
export const operatorMap: Record<string, OperatorType> = {
  like: { label: t('component.advancedSearch.conditionMap.like'), value: 'like' },
  noLike: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'not_like' },

  array_like: { label: t('component.advancedSearch.conditionMap.like'), value: 'array_like' },
  array_not_like: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'array_not_like' },

  in: { label: t('component.advancedSearch.conditionMap.in'), value: 'in' },
  not_in: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'not_in' },
  includedIn: { label: t('component.advancedSearch.conditionMap.includedIn'), value: 'includedIn' },

  cascader: { label: t('component.advancedSearch.conditionMap.eq'), value: 'cascader' },
  cascader_not_eq: { label: t('component.advancedSearch.conditionMap.uneq'), value: 'cascader_not_eq' },

  user: { label: t('component.advancedSearch.conditionMap.user'), value: 'user' },

  object_in: { label: t('component.advancedSearch.conditionMap.like'), value: 'object_in' },
  object_not_in: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'object_not_in' },

  ex: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'not_in' },
  eq: { label: t('component.advancedSearch.conditionMap.eq'), value: 'eq' },

  array_eq: { label: t('component.advancedSearch.conditionMap.eq'), value: 'array_eq' },
  array_not_eq: { label: t('component.advancedSearch.conditionMap.uneq'), value: 'array_not_eq' },

  array_in: { label: t('component.advancedSearch.conditionMap.like'), value: 'array_in' },
  array_not_in: { label: t('component.advancedSearch.conditionMap.not_in'), value: 'array_not_in' },

  multiSelect: { label: t('component.advancedSearch.conditionMap.like'), value: 'multiSelect' },
  logistics: { label: t('component.advancedSearch.conditionMap.eq'), value: 'logistics' },
  notEq: { label: t('component.advancedSearch.conditionMap.uneq'), value: 'not_eq' },
  uneq: { label: t('component.advancedSearch.conditionMap.uneq'), value: 'uneq' },
  gt: { label: t('component.advancedSearch.conditionMap.gt'), value: 'gt' },
  ge: { label: t('component.advancedSearch.conditionMap.ge'), value: 'ge' },
  lt: { label: t('component.advancedSearch.conditionMap.lt'), value: 'lt' },
  le: { label: t('component.advancedSearch.conditionMap.le'), value: 'le' },
  between: { label: t('component.advancedSearch.conditionMap.between'), value: 'between' },
  address: { label: t('component.advancedSearch.conditionMap.like'), value: 'address' },
  is_nul:{ label: t('component.advancedSearch.conditionMap.is_nul'), value: 'empty' },
  is_not_null: { label: t('component.advancedSearch.conditionMap.is_not_null'), value: 'not_empty' },

  empty: { label: t('component.advancedSearch.conditionMap.is_nul'), value: 'empty' },
  not_empty: { label: t('component.advancedSearch.conditionMap.is_not_null'), value: 'not_empty' },
};

// 所有自定义字段条件选择map，TODO：paas条件暂时和之前保持一致，需要增加时再处理
export const fieldOperatorMap: Record<string, string[]> = {
  // fieldName | formType
  serialNumber: ['like'], // 编号
  customer: ['array_eq', 'array_not_eq',  'is_nul', 'is_not_null'], // 客户
  product: ['array_eq', 'array_not_eq',  'is_nul', 'is_not_null'], // 产品
  linkman: ['array_eq', 'array_not_eq', 'is_nul', 'is_not_null'], // 客户联系人
  customerAddress:['array_like', 'array_not_like', 'is_nul', 'is_not_null'], // 客户地址
  tags: ['in','ex', 'is_nul', 'is_not_null'], // 创建人所属部门
  serviceProvider: ['array_eq'], // 服务商网点
  status: ['eq'], // 数据状态
  assignUserIdList: ['in', 'ex', 'is_nul', 'is_not_null'], // 处理人
  createUserIdList: ['in', 'ex', 'is_not_null'], // 创建人
  sourceTemplateName: ['eq', 'notEq', 'is_nul', 'is_not_null'], // 来源
  sourceBizNo: ['eq'], // 来源编号
  text: ['like', 'noLike','eq', 'notEq', 'is_nul', 'is_not_null'], // 单行文本
  textarea: ['like', 'noLike','eq', 'notEq', 'is_nul', 'is_not_null'], // 多行文本
  number: [ 'eq','notEq', 'gt','ge', 'lt', 'le',  'between', 'is_nul', 'is_not_null'], // 数字
  select: ['eq','notEq', 'like', 'is_nul', 'is_not_null'], // 下拉菜单
  cascader: ['cascader'], // 多级菜单
  user: ['object_in', 'object_not_in', 'is_nul', 'is_not_null'], // 人员
  date: ['eq','notEq', 'gt','ge', 'lt', 'le', 'between', 'is_nul', 'is_not_null'], // 日期
  updateTime:  ['eq','notEq', 'gt','ge', 'lt', 'le', 'between', 'is_not_null'],
  createTime:  ['eq','notEq', 'gt','ge', 'lt', 'le', 'between', 'is_not_null'],
  datetime: ['between'], // 日期时间
  phone: [ 'eq','notEq', 'like', 'noLike', 'is_nul', 'is_not_null'], // 电话
  email: ['eq', 'notEq', 'like', 'noLike', 'is_nul', 'is_not_null'], // 邮箱
  address: ['array_like', 'array_not_like', 'is_nul', 'is_not_null'], // 自定义地址
  formula: [ 'eq','notEq', 'gt','ge', 'lt', 'le', 'between', 'is_nul', 'is_not_null'], // 计算公式
  location: ['array_like'], // 位置
  related_task: ['array_eq'], // 关联工单
  currency: [ 'eq','notEq', 'gt','ge', 'lt', 'le', 'between', 'is_nul', 'is_not_null'], // 金额
  logistics: ['logistics'], // 物流组件
  tag: ['object_in', 'object_not_in', 'is_nul', 'is_not_null'], // 部门组件
};

// 多选操作符
const multipleValue = [
  operatorMap.in.value, // 包含
  operatorMap.ex.value, // 不包含
  operatorMap.user.value, // 人员控件
];
// 是否多选
export function isMultiValue(operator: string) {
  return multipleValue.includes(operator);
}

/**
 * 获取field对应条件列表
 * @param {Field} field
 * @returns {Array<OperatorType>} OperatorTypes
 */
export function getFieldOperator(field: Field) {
  const { fieldName, formType } = field;
  let operators = fieldOperatorMap[fieldName] ?? fieldOperatorMap[formType] ?? [];

  if (formType == 'select' && fieldName !== 'status') {
    if (field.isMulti) {
      operators =  ['array_eq','array_not_eq', 'array_in', 'array_not_in', 'is_nul', 'is_not_null']
    } else {
      operators = ['eq','notEq', 'is_nul', 'is_not_null'];
    }
  } else if (formType == 'cascader') {
    if(field.setting?.isMulti){
      operators = ['multiSelect', 'is_nul', 'is_not_null'];
    } else {
      operators = ['cascader','cascader_not_eq', 'is_nul', 'is_not_null'];
    }
  }

  return operators.map(op => operatorMap[op]);
}
