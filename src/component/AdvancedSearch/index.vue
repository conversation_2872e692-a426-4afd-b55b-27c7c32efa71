<template>
  <div class="advanced-search">
    <el-popover
      popper-class="advanced-search-popper"
      width="700px"
      trigger="click"
      v-model="advancedPopoverVisible"
    >
      <el-button
        type="plain-third"
        :class="[
          'advanced-search-btn just-cur-point',
          isUseConditionToSearch && 'filter',
        ]"
        slot="reference"
        @click="toggleAdvancedPopover"
      >
        <i
          :class="[
            'iconfont',
            isUseConditionToSearch ? 'icon-filter-fill' : 'icon-filter',
          ]"
        ></i>
        <el-popover
          popper-class="advanced-search-guide-tip-popper"
          :value="guideTipVisible"
          trigger="manual"
          width="172"
        >
          <div>{{$t('component.advancedSearch.guideTip')}}</div>
          <span slot="reference">{{$t('component.advancedSearch.title')}}</span>
          <!-- <span>{{conditionCount > 0 ? `${conditionCount}个筛选` : '高级筛选'}}</span> -->
        </el-popover>
      </el-button>
      <div class="advanced-search-popper__content">
        <slot name="prefix">
          <!-- 自定义的固定条件 -->
        </slot>
        <slot name="searchForm">
          <advanced-search-form
            ref="searchFormRef"
            v-bind="$attrs"
            v-on="$listeners"
            :search-model="searchModel"
            :needCommonUse="true"
            @change="changeForm"
            :mode="mode"
            @change-search-model="changeSearchModel"
          >
          </advanced-search-form>
        </slot>
      </div>
      <div class="advanced-search-popper__footer">
        <div class="advanced-search-popper__footer-left">
          <el-button v-if="hasCreate" type="plain-third" @click="create">
            {{$t('component.advancedSearch.modal.createTitle')}}
          </el-button>
          <el-button v-if="hasSave" type="plain-third" @click="save">
            {{$t('component.advancedSearch.modal.updateTitle')}}
          </el-button>
          <div v-if="mode==='pass'" class="advanced-search-popper__footer-tips">
            <i class="iconfont icon-info"></i>
            <p>{{$t('forPaas.passSave.passSaveTips')}}</p>
          </div>
        </div>
        <div class="advanced-search-popper__footer-right">
          <el-button type="primary" @click="search">{{$t('common.base.search')}}</el-button>
          <el-button type="plain-third" @click="clearField">
            {{$t('component.advancedSearch.clearCondition')}}
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import Vue, { defineComponent, ref, watch, nextTick, computed } from 'vue';
import AdvancedSearchForm from './AdvancedSearchForm.vue';
import { genConditions } from './searchCondition';
import { cloneDeep } from 'lodash';

export default defineComponent({
  name: 'AdvancedSearch',
  components: {
    AdvancedSearchForm,
  },
  props: {
    searchModel: {
      type: Array,
      default: () => [],
    },
    // 新建按钮
    hasCreate: {
      type: Boolean,
      default: true,
    },
    // 保存按钮
    hasSave: {
      type: Boolean,
      default: true,
    },
    mode: {
      type: String,
      default: ''
    }
  },
  emits: ['change', 'save', 'create', 'clear', 'reset'],
  setup(props, { emit }) {
    const Track = Vue.prototype.$track;

    const isConnectorMode = computed(()=>props.mode === 'connector')

    // form ref
    const advancedPopoverVisible = ref(false)
    const searchFormRef = ref();
    const conditionCount = ref(props.searchModel.length);
    // 是否使用了高级搜索的条件去查询了
    const isUseConditionToSearch = ref(false);
    const guideTipVisible = ref(false); // 引导提示
    let guideTipCache = !!localStorage.getItem('advancedSearchPopperGuideTip'); // 引导提示

    const close = () => advancedPopoverVisible.value = false

    function toggleAdvancedPopover() {
      // advancedPopoverVisible.value = !advancedPopoverVisible.value
      guideTipVisible.value = false;
      guideTipCache = true;
      localStorage.setItem('advancedSearchPopperGuideTip', true);
    }
    /**
     * 改变表单值
     */
    function changeForm({ list }) {
      emit('change');
    }
    /**
     * 改变条件列表
     */
    function changeSearchModel(conditions) {
      conditionCount.value = conditions.length;
    }
    /**
     * 保存
     */
    function save() {
      close();
      emit('save', getSearchModel());
    }
    /**
     * 新建
     */
    function create() {
      close();
      emit('create', getSearchModel());
    }
    /**
     * 清除所有值
     */
    function clearField(needSearch = false) {
      searchFormRef.value?.clear();
      emit('clear');
      if(needSearch){
        search();
      }
    }

    /**
     * 获取搜索条件
     */
    function getSearchModel() {
      return searchFormRef.value?.getRealSearchList();
    }

    /**
     * 点击搜索
     */
    function search(isAdvanced) {
      let isShowSave = false;
      if(isAdvanced) {
        isShowSave = true
      };

      close();
      const searchList = getSearchModel();
      
      let searchModel = genConditions(searchList);

      if(isConnectorMode.value){
        emit('search', cloneDeep(searchList), searchModel);
      }else{
        emit('search', {searchModel,searchList,isShowSave});
      }
      isUseConditionToSearch.value = !!searchList[0]?.value;
      if (!guideTipCache && isUseConditionToSearch.value)
        guideTipVisible.value = true;
      return searchModel;
    }

    // 设置按钮装填¿
    function setButtonStatus(state = true) {
      isUseConditionToSearch.value = state;
    }

    {
      watch(
        () => props.searchModel,
        async () => {
          // 接收到传入的新数据 会优先调用一次search
          await nextTick();
          search(false);
        }
      );
    }

    return {
      close,

      searchFormRef,
      isUseConditionToSearch,
      setButtonStatus,

      conditionCount,
      changeSearchModel,

      guideTipVisible,
      advancedPopoverVisible,
      toggleAdvancedPopover,
      changeForm,
      save,
      create,
      clearField,
      search,
    };
  },
});
</script>

<style lang="scss">
// .advanced-search {
//   margin-right: 12px;
// }
.advanced-search-popper {
  padding: 0 !important;
  width: 750px;
  z-index: 1900 !important;
}
.advanced-search-btn {
  user-select: none;
  &.filter {
    background: #e9f9f9 !important;
    color: $color-primary !important;
    border-color: #d0f3f4 !important;
  }
}
.advanced-search-popper__content {
  padding: 12px;
  max-height: 60vh;
  overflow: auto;
}
.advanced-search-popper__footer {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid $border-color-base;

  .advanced-search-popper__footer-left {
    display: flex;
    align-items: center;
    .advanced-search-popper__footer-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #595959;
      p {
        padding: 0px;
        margin: 0px;
        font-size: 12px;
      }
      i {
        font-size: 14px;
        margin: 0 6px 0 10px;
      }
    }
  }

  .advanced-search-popper__footer-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

// .advanced-search-guide-tip-popper {
//   border: none !important;
//   box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
//     0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
// }
</style>
