import customerSearch from './Customer.vue';
import productSearch from './Product.vue';
import linkmanSearch from './Linkman.vue';
import serviceProviderSearch from './ServiceProvider.vue';
import TaskSearch from './Task.vue';
import DateSearch from './Date.vue';
import SelectSearch from './Select.vue';
import TextSearch from './Text.vue';
import NumberSearch from './Number.vue';
import NumberRange from './NumberRange.vue';
import UserSearch from './User.vue';
import AddressSearch from './Address.vue';
import AddressMultiSearch from './AddressMulti.vue';
import CascaderSearch from './Cascader.vue';
import ProductTypeSearch from './ProductTypeCascader.vue'
import TagsSearch from './Tags.vue';
import RemoteSelectSearch from './RemoteSelect.vue';
import LogisticsSearch from './logistics.vue';

import ConnectorRelatedTaskSearch from './ConnectorRelatedTask.vue';
import NumberSection from './NumberSection.vue'

// 触发器知识库
import AttachmentSearch from './Attachment.vue'
// import WikiTypeSearch from './WikiType.vue'
import WikiLabelSearch from './WikiLabel.vue'
import RichTextSearch from './RichText.vue'

import CurrencySearch from './Currency.vue'


export default {
  customerSearch, // 客户
  productSearch, // 产品
  linkmanSearch, // 联系人
  serviceProviderSearch, // 服务商网点
  TaskSearch, // 关联工单
  TagsSearch, // 部门

  // form
  TextSearch, // 单行文本
  NumberSearch, // 数字
  CascaderSearch, // 多级
  ProductTypeSearch, // 产品类型多级
  UserSearch, // 用户
  DateSearch, // 日期
  AddressSearch, // 地址
  SelectSearch, // 下拉
  AttachmentSearch, // 附件

  RemoteSelectSearch, // 远程搜索--产品类型
  LogisticsSearch, // 物流搜索
  ConnectorRelatedTaskSearch, // 连接器关联工单

  NumberSection,// 里程 数字区间
  NumberRange,
  // WikiTypeSearch, // 知识库分类
  WikiLabelSearch, // 知识库标签
  AddressMultiSearch, // 多选地址
  RichTextSearch, // 富文本带插入表单字段,
  CurrencySearch,// 国际货币 金额
};
