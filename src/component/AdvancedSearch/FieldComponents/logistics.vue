<template>
  <div>
    <template v-if="label === $t('common.form.preview.logistics.companyLabel')">
      <el-select
      :value="value"
      :placeholder="$t('common.form.preview.logistics.pla1')"
      filterable
      @input="updateSelectValue"
      :disabled="disabled"
    >
      <el-option
        v-for="item in logisticsCompanyList"
        :key="item.id"
        :label="item.name"
        :value="item.name"
      ></el-option>
    </el-select>
    </template>
    <template v-else>
      <el-input  :placeholder="$t('common.form.preview.logistics.pla2')" :value="value" @input="updateSelectValue"></el-input>
    </template>
  </div>
</template>
<script>
import { getLogisticsCompanyList } from 'pub-bbx-api';
import i18n from '@src/locales';

export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    value: {
      type: [Number, String, Array],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      logisticsCompanyList: [],
    }
  },
  mounted() {
    if (this.label === i18n.t('common.form.preview.logistics.companyLabel')) {
      this.fetchLogisticsCompany()
    }
  },
  methods:{
    // 查询所有物流公司
    async fetchLogisticsCompany() {
      try {
        const list = await getLogisticsCompanyList();
        this.logisticsCompanyList = Object.freeze(list);
      } catch (error) {
        console.error('获取物流公司失败', error);
      }
    },
      // 选中更新值
    updateSelectValue(selected) {
      this.$emit('update', selected);
    }
  }
}
</script>