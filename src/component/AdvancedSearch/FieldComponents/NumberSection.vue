<template>
  <div class="Content">
    <input
      v-model="betweenValue1"
      :disabled="disabled"
      @change="Save"
      type="number"
      :placeholder="$t('task.list.numberSection.startDistance')"
      class="Content_Input"
    />
    -
    <input
      v-model="betweenValue2"
      @change="Save"
      type="number"
      :placeholder="$t('task.list.numberSection.endDistance')"
      class="Content_Input"
      :disabled="disabled"
    />
  </div>
</template>

<script>
import i18n from '@src/locales'
export default {
  name: 'number-section',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.input'),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      betweenValue1: '',
      betweenValue2: '',
    };
  },

  mounted() {},

  methods: {
    Save() {
      this.$emit('update', {
        betweenValue1: this.betweenValue1,
        betweenValue2: this.betweenValue2,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.Content {
  width: 100%;
  border-radius: 4px;
  color: #606266;
  min-width: 150px;
  border: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  .Content_Input {
    border: none;
    width: 100px;
    display: flex;
    align-content: center;
    text-align: center;
  }
}
</style>
