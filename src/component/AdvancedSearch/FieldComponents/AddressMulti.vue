<template>
  <div class="address-multi-search">
    <base-dist-picker-international-multi @input="handleCitySelectorChange" :value="distValue" />
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'address-multi-search',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const distValue = computed(() => {
      if (!props.value) return [];

      return props.value;
    });

    function handleCitySelectorChange(val) {
      emit('update', val);
    }

    return {
      distValue,
      handleCitySelectorChange,
    };
  },
});
</script>
