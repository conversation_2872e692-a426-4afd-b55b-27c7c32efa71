<template>
  <div class="cascader-search">
    <el-cascader
      :value="value"
      :props="cascadeProps"
      :options="options"
      :placeholder="placeholder"
      :show-all-levels="isShowAllLevels"
      clearable
      filterable
      @change="changeValue"
      :disabled="disabled"
    />
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'cascader-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.select'),
    },
    value: {
      type: [String, Array],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    // 是否多选
    const isMulti = !!props.field?.setting?.isMulti;
    // 选项显示模式
    const displayMode = props.field?.setting?.displayMode;
    // 是否显示所有层级
    const isShowAllLevels = computed(() => {
      if (isMulti) return displayMode == 2;
      return displayMode != 1;
    })
    const checkStrictly =  !!(props.field?.setting?.checkStrictly ?? true); // 需求默认允许选择任意层级
    const cascadeProps = {
        value: 'value',
        label: 'value',
        children: 'children',
        multiple: isMulti,
        checkStrictly: checkStrictly, // 支持任意一级选择
    };

    function disposeOption(item) {
      const _item = {
        label: item.label ?? item.value,
        value: item.value,
      }
      if (item.children?.length) {
        _item.children = item.children.map(disposeOption);
      }
      return _item;
    }
    const options = (props.field?.setting.dataSource || []).map(disposeOption)

    // 改变值
    function changeValue(value) {
      emit('update', value);
    }

    return {
      cascadeProps,
      options,
      isMulti,
      displayMode,
      isShowAllLevels,
      changeValue,
    };
  },
});
</script>

<style></style>
