<template>
  <div class="address-search">
    <base-dist
      :disabled="disabled"
      :value="distValue"
      :placeholder="placeholder"
      @input="handleCitySelectorChange"
      :showOneLine="true"
      :no-default-country="noDefaultCountry"
    />
  </div>
</template>

<script>
import { defineComponent, reactive, watch, toRefs } from 'vue';
import i18n from '@src/locales';
// 国际化灰度
import useFormMultiLanguage from '@src/hooks/useFormMultiLanguage';
const { internationalGray } = useFormMultiLanguage();
export default defineComponent({
  name: 'address-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.selectAddress'),
    },
    value: {
      type: Object,
      default() {
        let value = {
          /**
           * value值必须包含以下值:
           * province: String,
           * city: String,
           * dist: String,
           * address: String
           *
           * 以下值可选：
           * latitude： [String,Number],
           * longitude: [String,Number],
           * addressType: Number
           */
        };

        return {};
      },
    },
    noDefaultCountry: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    let state = reactive({
      distValue: {},
    });

    watch(
      () => props.value,
      newValue => {
        if (newValue) {
          let { country = '', province, city, dist } = newValue || {};
          state.distValue = {
            country,
            province,
            city,
            dist,
          };
        } else {
          state.distValue = {};
        }
      },
      { deep: true, immediate: true }
    );

    function handleCitySelectorChange(val) {
      updateValue(val);
    }

    function updateValue(newValue) {
      // 工单客户关联字段清除位置信息时候不调用update
      emit('update', newValue);
    }

    return {
      ...toRefs(state),
      internationalGray,
      handleCitySelectorChange,
      updateValue,
    };
  },
});
</script>
