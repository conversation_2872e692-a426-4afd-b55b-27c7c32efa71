<template>
  <biz-form-remote-select
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :value="selectValue"
    :multiple="multiple"
    value-key="id"
    v-bind="$attrs"
    cleared
    @input="updateSelectValue"
    :input-disabled="disabled"
  >
    <div class="service-item" slot="option" slot-scope="{ option }">
      <h3>{{ option.label }}</h3>
      <div class="service-item-list">
        <p>
          <label>{{ $t('common.base.contact') }}：</label>
          <span>{{ option.serviceProviderContact && option.serviceProviderContact.displayName }}</span>
        </p>
        <p>
          <span>
            <label>{{ $t('common.base.contactNumber') }}：</label>
            <span>{{ option.linkPhone || '' }}</span>
          </span>
        </p>
        <p>
          <span>
            <label>{{ $t('common.base.address') }}：</label>
            <span>{{ option.detailAddress || '' }}</span>
          </span>
        </p>
      </div>
    </div>
  </biz-form-remote-select>
</template>

<script>
import { defineComponent, computed } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'service-provider-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.select'),
    },
    remoteMethod: {
      type: Function,
    },
    value: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update', 'input'],
  setup(props, { emit }) {
    const selectValue = computed(() => {
      return props.value || [];
    });
    function updateSelectValue(value) {
      emit('update', value);
      emit('input', value);
    }
    return {
      selectValue,
      updateSelectValue,
    };
  },
});
</script>

<style lang="scss">
.service-item {
  h3 {
    font-size: 14px;
    margin: 0;
  }

  p {
    display: flex;
    margin: 0;
    line-height: 25px;

    span {
      label {
        display: inline-block;
        width: auto;
      }

      span {
        margin-right: 10px;
      }
    }
  }
}
</style>
