<template>
  <div class="select-search">
    <el-select
      :id="`form_${field.fieldName}`"
      :placeholder="placeholder"
      :multiple="isMulti"
      :value="value"
      clearable
      filterable
      @change="changeSelect"
      :disabled="disabled"
    >
      <el-option
        v-for="(item, index) in options"
        :key="`${item.value}_${index}`"
        :label="item.text"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'select-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.select'),
    },
    value: {
      type: [Number, String, Array],
      default: '',
    },
    isConnector: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    // 是否多选
    const isMulti = computed(() => {
      return props.field?.isMulti
    });
    const dataSource = computed(() => props.field?.setting?.dataSource || []);
    const options = computed(() =>
      dataSource.value.map(s =>
        typeof s === 'string' ? { text: s, value: s } : s
      )
    );

    // 改变值
    function changeSelect(value) {
      emit('update', value);
    }

    return {
      isMulti,
      options,
      changeSelect,
    };
  },
});
</script>

<style></style>
