<template>
  <div :key="isRange">
    <bbx-timezone-time-picker
      :value="bindTime"
      :field="field"
      :type="datePickerType"
      :is-edit="true"
      value-format="timestamp"
      :format="format"
      unlink-panels
      range-separator="-"
      :default-time="defaultTime"
      :start-placeholder="$t('common.base.startDate')"
      :end-placeholder="$t('common.base.endDate')"
      :placeholder="placeholder"
      @input="choose"
      @confirm="choose"
      :disabled="disabled"
    >
    </bbx-timezone-time-picker>
  </div>
</template>

<script>
import { defineComponent, ref, unref, toRefs, computed, watchEffect } from 'vue';
import BbxTimezoneTimePicker from '@src/component/Bbx/TimezoneTimePicker'
import { t } from '@src/locales';
import { isString, useDatePicker, useTimezone, getTimestamp, isEmpty } from 'pub-bbx-utils';


export default defineComponent({
  name: 'DateSearch',
  components: {
    BbxTimezoneTimePicker,
  },
  props: {
    field: {
      type: Object,
      default: () => ({}),
    },
    placeholder: {
      type: String,
      default: () => t('common.placeholder.selectTime'),
    },
    type: {
      type: String,
      default: 'date',
    },
    value: {
      type: [Array, String, Number],
      default: null,
    },
    pickerOptions: {
      type: Object,
      default: () => ({}),
    },
    // 使用dateType，目前只用于超时规则条件设置
    useSettingDateType: {
      type: Boolean,
      default: false
    },
    isRange: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const { isRange, type, value, field, useSettingDateType } = toRefs(props)
    const datePickerType = computed(() => {
      const dateType = unref(field)?.setting?.dateType;
      if (unref(useSettingDateType) && dateType && dateType.indexOf('HH:mm') > -1) {
        return 'datetime';
      }
      if (unref(isRange)) return `${unref(type)}range`
      return unref(type)
    })
    const format = computed(() => {
      const dateType = unref(field)?.setting?.dateType;
      if (unref(useSettingDateType) && dateType) return dateType;
      if (unref(type) === 'date') return 'yyyy-MM-dd';
      if (unref(type) === 'month') return 'yyyy-MM';
      return 'yyyy-MM-dd HH:mm:ss';
    })
    const defaultTime = computed(() => unref(isRange) ? ['00:00:00', '23:59:59'] : ['00:00:00'])

    const { formatDateByTimezone, timezone, serverTimezone } = useTimezone()
    const bindTime = ref(null)
    // 兼容旧数据--原数据存的是东八区的时间字符串
    const compatibleOldDate = (value) => {
      if(!value) return null
      // ! 此处兼容不能删除
      // 如果是字符串说明是旧数据，需要转换成当前时区的时间
      return getTimestamp(isString(value) ? formatDateByTimezone(value, timezone, serverTimezone) : value)
    }
    watchEffect(() => {
      if(unref(isRange)){
        bindTime.value = !isEmpty(unref(value)) ? (unref(value) || []).map(time => compatibleOldDate(time)) : ''
      }else {
        bindTime.value = !isEmpty(unref(value)) ? compatibleOldDate(unref(value)) : ''
      }
    })

    const { nearDate } = useDatePicker()
    const datePickerOptions = {
      shortcuts: [
        nearDate(t('common.time.lastWeek'), 1, 'week'),
        nearDate(t('common.time.lastMonth'), 1, 'month'),
        nearDate(t('common.time.lastThreeMonth'), 3, 'month'),
      ],
    }


    function choose(newValue){
      emit('update', newValue);
    }

    return {
      format,
      datePickerOptions,
      datePickerType,
      defaultTime,
      bindTime,
      choose,
    };
  },
});
</script>

<style lang="scss">
.el-date-editor {
  width: 100% !important;
}
</style>
