<template>
  <div class="file">
    <div class="base-comment-attachment base-file__preview file-item" v-if="attachments && attachments.length > 0">
      <base-file-item v-for="file in attachments" :disabled="disabled" :Source="attachments" :key="file.id" :file="file" @delete="deleteFile" size="small"></base-file-item>
    </div>
    <button type="button" class="base-comment-tool file-button" :disabled="disabled" @click="chooseFile">
      <i class="iconfont icon-attachment"></i> {{i18n.t('wiki.create.textTitle.btn2')}}
    </button> 
    <input type="file" ref="input" @change="handleChange" multiple>
    <div class="base-comment-cover loading" v-if="!allowOperate">
      <base-spin :text="i18n.t('common.base.waiting')"></base-spin>
    </div>
  </div> 
</template>
<script>
import platform from '@src/util/Platform';
import Uploader from '@src/util/uploader';
import i18n from '@src/locales';
export default {
  name: 'attachment-search',
  props: {
    value: {
      type: Array,
      default: () => ([])
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 加载显示
    allowOperate(){
      return !this.pending
    }
  },
  data() {
    return {
      i18n,
      attachments: this.value || [],
      pending: false,
    }
  },
  methods: {
    // 选择文件
    handleChange(event){
      const files = event.target.files;
      if(!files || !files.length) return;

      if(this.attachments.length + files.length > Uploader.FILE_MAX_NUM) {
        let message = this.$t('common.base.uploadModal.uploadCountTips', {count:Uploader.FILE_MAX_NUM});
        let max = 9 - this.attachments.length;

        if(max > 0 && files.length < 9){
          message +=  `, ${this.$t('common.base.uploadModal.canUploadCountTips',{count:max})}`;
        }

        return platform.alert(message)
      }

      this.pending = true;

      Uploader.batchUploadWithParse({files, action: '/files/upload/wiki', source: 'wiki'}).then(result => {
        let {success, error} = result; 
        if(error.length > 0){
          let message = error.map(item => item.message).join('\n');
          //此处不能return
          platform.alert(message)
        }

        if(success.length > 0){
          this.attachments = this.attachments.concat(success);
          this.$emit('update', this.attachments)
        }
      })
        .catch(err => console.error(err))
        .then(() => this.pending = false)
    },
    // 触发inputclick事件选择文件
    chooseFile () {
      if(this.pending) return platform.alert(this.$t('common.base.uploadModal.tips1'));
      if(this.attachments.length >= Uploader.FILE_MAX_NUM) {
        return platform.alert(this.$t('common.base.uploadModal.uploadCountTips', {count:Uploader.FILE_MAX_NUM}));
      }
        
      this.$refs.input.value = null;
      this.$refs.input.click();
    },
    // 删除文件
    deleteFile(file) {
      let index = this.attachments.indexOf(file);
      if(index >= 0) {
        this.attachments.splice(index, 1);
        this.$emit('update', this.attachments)
      }
    },
  },
};
</script>
<style lang="scss" scoped>
 input[type='file']{
  display: none !important;
}

.el-input__inner {
  border-color: #e0e1e2;
}

.search-tag {
  border: 1px solid #D9D9D9;
  background: #fff;
  color: #262626;
  line-height: 32px;
  height: 32px;
  vertical-align: top;
  font-size: 14px;
  font-weight: 400;
  margin-right: 12px;
  i {
    color: #595959;
    font-size: 16px;
    &:hover {
      background: #fff;
    }
  }
}

.input-new-tag {
  display: inline-block;
  width: 100px;
  margin-right: 10px;
  ::v-deep input:focus {
    border-color: #13c2c2;
  }
}

.input-new-tag {
  vertical-align: middle;
}
.icon-add-tags-btn {
  // display: inline-block;
  position: relative;
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
  border: 1px solid #13C2C2;
  color: #13c2c2;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  // margin-right: 12px;
  .icon-addTags {
    margin-right: 8px;
    // @include fontColor();
  }
}
    
</style>
