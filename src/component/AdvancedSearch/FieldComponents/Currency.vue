<template>
  <div class="currency-search flex-x">
    <!-- 金额 -->
    <!-- operator between 金额范围 -->
    <div v-if="currentOperator === 'between'" class="flex-x currency-number currency-number-range">
      <el-input v-model="numberMin" type="number" @change="changeNumberRange"></el-input>
      <span>~</span>
      <el-input v-model="numberMax" type="number" @change="changeNumberRange"></el-input>
    </div>

    <!-- operator others -->
    <el-input
      v-else
      v-model="number"
      type="number"
      maxlength="1000"
      :placeholder="$t('common.placeholder.inputCurrency')"
      @change="changeNumber"
      class="currency-number"
    ></el-input>
    
    <!-- 币种 -->
    <el-select 
      v-model="currency"
      multiple 
      collapse-tags 
      @change="changeCurrency"
      class="mar-l-4 currency-list" 
    >
      <el-option 
        v-for="item in currentOptions"
        :key="item.value"
        :label="item.value"
        :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { defineComponent, ref, watchEffect } from 'vue';
import i18n from '@src/locales';
import { currencyOption } from '@src/util/currency';
import { isEmpty } from '@src/util/type'

export default defineComponent({
  name: 'currency-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.input'),
    },
    value: {
      type: Object,
      default: () => {},
    },
    operator: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const number = ref('');
    const numberMin = ref('');
    const numberMax = ref('');
    const currency = ref([]);
    const currentOperator = ref('');
    const currentOptions = ref(currencyOption);

    watchEffect(() => {
      number.value = props.value.number || '';
      numberMin.value = !isEmpty(props.value.number) ? props.value.number[0] || '' : '';
      numberMax.value = !isEmpty(props.value.number) ? props.value.number[1] || '' : '';
      currency.value = props.value.currency || [];
      currentOperator.value = props.operator;
    });

    function changeNumber(value) {
      emit('update', {...props.value, number: value});
    }

    function changeNumberRange(value) {
      emit('update', {...props.value, number: [numberMin.value, numberMax.value]});
    }

    function changeCurrency(value) {
      emit('update', {...props.value, currency: value});
    }

    return {
      number,
      numberMin,
      numberMax,
      currency,
      currentOptions,
      currentOperator,
      changeNumber,
      changeNumberRange,
      changeCurrency,
    };
  },
});
</script>
<style lang="scss" scoped>
.flex-x {
  display: flex;
  align-items: center;
}
.currency-search {
  .currency-number {
    width: 160px !important;
  }
  .currency-number-range {
    border-radius: 4px;
    border: 1px solid #e0e1e2;
    height: 32px;
    line-height: 32px;
    overflow: hidden;
    ::v-deep .el-input__inner {
      border: none;
    }
  }
  .currency-list {
    flex: 1;
    ::v-deep .el-select__tags {
      max-width: inherit !important;
    }
  }
}
</style>