<template>
  <biz-form-remote-select
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :value="selectValue"
    :multiple="multiple"
    value-key="id"
    v-bind="$attrs"
    cleared
    @input="updateSelectValue"
    :input-disabled="disabled"
  >
    <div class="customer-template-option" slot="option" slot-scope="{ option }">
      <h3>{{ option.name }}</h3>
      <p class="customer-template-option-content">
        <span class="customer-template-option-content-text">
          <label>{{$t('common.fields.phone.displayName')}}：</label>
          <span>{{ option.lmPhone || '' }}</span>
        </span>
        <span class="customer-template-option-content-text">
          <label>{{$t('common.fields.serialNumber.displayName')}}：</label>
          <span>{{ option.serialNumber }}</span>
        </span>
      </p>
    </div>
  </biz-form-remote-select>
</template>

<script>
import { defineComponent, computed } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'customer-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.selectCustomer'),
    },
    remoteMethod: {
      type: Function,
    },
    value: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update', 'input'],
  setup(props, { emit }) {
    const selectValue = computed(() => {
      return props.value || [];
    });
    function updateSelectValue(value) {
      emit('update', value);
      emit('input', value);
    }
    return {
      selectValue,
      updateSelectValue,
    };
  },
});
</script>

<style lang="scss">
.customer-template-option-content {
  color: $text-color-regular;
  &-text {
    display: flex;
    align-items: center;

    label {
      padding-top: 0;
    }

    span {
      line-height: 24px;
    }
  }
}
</style>
