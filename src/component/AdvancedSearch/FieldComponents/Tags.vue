<template>
  <div>
    <biz-team-select
      :value="value"
      :multiple="multiple"
      @input="update"
      :disabled="disabled"
      :type-service-provider="typeServiceProvider"
    />
  </div>
</template>
<script>
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'tags-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    value: {
      type: Array,
      default: () => null,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update', 'input'],
  setup(props, { emit }) {
    function update(value){
      emit('update', value);
      emit('input', value);
    }
    // pass部门控件仅显示组织架构
    const typeServiceProvider = computed(() => {
      return props.field.formType === 'tag' ? 0 : '';
    });

    return {
      update,
      typeServiceProvider,
    };
  },
});
</script>
