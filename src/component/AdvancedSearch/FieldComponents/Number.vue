<template>
  <div class="text-search">
    <el-input
      v-model="realValue"
      v-only-number
      maxlength="1000"
      :placeholder="placeholder"
      @change="changeText"
      :disabled="disabled"
    ></el-input>
  </div>
</template>

<script>
import { defineComponent, ref, watchEffect } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'number-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.input'),
    },
    value: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const realValue = ref('');

    watchEffect(() => {
      realValue.value = props.value;
    });

    function changeText(value) {
      emit('update', value);
    }

    return {
      realValue,
      changeText,
    };
  },
});
</script>
