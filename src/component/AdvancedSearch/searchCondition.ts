import { SearchItemType } from '@src/component/AdvancedSearch/advancedSearch';
import { isArray } from '@src/util/type';
/* enum */
import { LogisticsFieldNameMappingEnum } from '@src/model/enum/FieldMappingEnum';
// @ts-ignore
import { fmtDist } from '@src/util/addressUtil';
// 国际化灰度
import useFormMultiLanguage from '@src/hooks/useFormMultiLanguage';
import { isEmptyOrNotEmptyOperator } from "@src/component/AdvancedSearch/config";
const { internationalGray } = useFormMultiLanguage();

/**
 * 判断空值
 * @param value
 * @returns
 */
export const isEmptyValue = (value: any) => {
  if (value === null || value === undefined) return true;
  if (value === '') return true;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

// 条件项类型
type ConditionItemType = {
  property: string;
  operator: string;
  value?: string | number | null;
  inValue?: Array<string | number>;
  betweenValue1?: string;
  betweenValue2?: string;
  key?: string;
};

// 异常原因
const abnormals = [
  'refusedReason',
  'pausedReason',
  'rollbackReason',
  'reallotReason',
  'offedReason',
];

// 业务search接口需要转换部分字段
const PaasInquireConvertFieldNamesToConditionsMap: Record<string, string> = {
  tags: 'createUserTagList',
};

/**
 * 生成查询条件
 * @param {Array<SearchItemType>} searchModel
 * @return conditionParams
 */
export function genConditions(searchModel: SearchItemType[] = []) {
  const conditions: ConditionItemType[] = [];
  const systemConditions: ConditionItemType[] = [];
  const balanceConditions: ConditionItemType[] = []; // 兼容结算自定义字段传参
  // 编号、关联工单、客户、客户地址、联系人、产品、服务商网点，在paas表单高级搜索时当做自定义字段传参
  const conditionFormType: string[] = ['serialNumber', 'related_task', 'customer', 'customerAddress', 'linkman', 'product', 'serviceProvider'];

  searchModel.forEach(item => {
    disposeConditionItem(
      item,
      item.field?.isSystem && !conditionFormType.includes(item.field?.formType) ? systemConditions : (item.field?.setting?.isOpen ? balanceConditions : conditions)
    );
  });
  const conditionParams = {
    conditions,
    systemConditions,
    balanceConditions
  };

  return conditionParams;
}

/**
 * 处理单个查询条件
 */
export function disposeConditionItem(
  item: SearchItemType,
  conditions: ConditionItemType[] = []
) {
  let { field, fieldName, operator, value } = item;

  if (!field) return;
  // 空值结束
  if (isEmptyValue(value) && !isEmptyOrNotEmptyOperator.includes(operator)) return;

  const { formType, setting } = field;
  const valueKey = isArray(value) ? 'inValue' : 'value';
  let condition: ConditionItemType = {
    property:
      PaasInquireConvertFieldNamesToConditionsMap[fieldName] ?? PaasInquireConvertFieldNamesToConditionsMap[formType] ?? fieldName,
    operator,
  };


  if(!isEmptyOrNotEmptyOperator.includes(operator)) {
    if (['tags','tag'].includes(formType)) {
      // 创建人所属部门
      condition.inValue = value.map((v: any) => v.id);
      if (formType === 'tag') {
        condition.key = 'id';
      }
    } else if (formType == 'address') {
      condition.key = 'all';
      condition.value = fmtDist(value, '');
      // TODO: 这里的中国不用翻译不用翻译不用翻译！！！！！！！！！！
      // 非国际化租户为了兼容老数据，需要把中国给去除
      // @ts-ignore
      if (!internationalGray && condition?.value?.substring(0, 2) == '中国') {
        // @ts-ignore
        condition.value = condition.value.substring(2);
      }
    } else if (formType === 'location') {
      // 位置
      condition.key = 'all';
      condition.value = value;
    } else if (formType == 'customer') {
      // 客户、 关联客户
      condition.key = 'id';
      condition.value = value[0].id;
    } else if (formType === 'customerAddress') {
      // 客户地址
      condition.key = 'label';
      condition.value = value;
    } else if (formType === 'product') {
      // 产品
      condition.key = 'productId';
      condition.value = value.id;
    } else if (formType === 'linkman') {
      // 客户联系人
      condition.key = 'id';
      condition.value = value.id;
    } else if (formType == 'serviceProvider') {
      // 服务商网点
      condition.key = 'id.keyword';
      condition.value = value[0]?.id;
    }  else if (formType === 'related_task') {
      // 工单编号
      condition.value = value;
      condition.key = 'taskNo';
    } else if (formType === 'user') {
      // 人员选择（创建人、派单人、负责人、协同人等）
      condition.key = 'userId';
      if (isArray(value)) {
        condition.inValue = value.map((item: { id: string }) => item.id);
      } else {
        condition.value = value.id;
      }
    } else if (['date', 'datetime'].includes(formType)) {
      if (['between'].includes(operator)) {
        condition.betweenValue1 = value[0];
        condition.betweenValue2 = value[1];
      } else {
        condition.value = value
      }
    } else if (formType === 'cascader') {
      // 多级菜单
      condition.inValue = value;
      if (setting?.isMulti && !isEmptyOrNotEmptyOperator.includes(operator)) {
        // 多选
        condition.inValue = value.map((item: string[]) => item.join('/'));
        condition.operator = 'multiSelect'; // 修改条件
      }
    } else if (formType === 'currency') {
      if (condition.operator === 'between') {
        condition.betweenValue1 = value && value.number && value.number[0];
        condition.betweenValue2 = value && value.number && value.number[1];
      } else {
        condition.value = value.number || '';
      }
      condition.inValue = value.currency || [];
    } else if (formType == 'logistics') {
      if (fieldName.indexOf(LogisticsFieldNameMappingEnum.LogisticsCompany) > -1) {
        condition.key = 'company.name'
      }
      if (fieldName.indexOf(LogisticsFieldNameMappingEnum.LogisticsNo) > -1) {
        condition.key = 'no';
      }
      condition.property = fieldName.split('_')[0]
      condition.value = value
    } else {
      if (condition.operator === 'between') {
        condition.betweenValue1 = value && value?.[0]
        condition.betweenValue2 = value && value?.[1]
      } else {
        // 默认不需要处理的值
        condition[valueKey] = value;
      }
    }
  }

  // 地址数据可能为空导致搜索不到
  if (formType == 'address' && condition.value == '' && !isEmptyOrNotEmptyOperator.includes(operator)) {
    condition = {
      property: '',
      operator: ''
    };
  }

  condition && condition.property && conditions.push(condition);
}
