<template>
  <div :class="ns.b()">
    <el-select
      v-model="type"
      :class="ns.e('select')"
      :placeholder="t('common.placeholder.select')"
      @change="handlePeriodTypeChange">
      <template v-for="item in options">
        <el-option :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </template>
    </el-select>
    <div :class="[ns.e('picker')]">
      <el-date-picker
        v-model="startTime"
        :class="ns.e('period-time-input')"
        :type="type"
        :disabled-date="startDisabledDate"
        :format="dateFormat"
        @change="updateDate"></el-date-picker>
      <template v-if="range">
        <span :class="ns.e('separator')"> - </span>
        <el-date-picker
          v-model="endTime"
          :class="ns.e('period-time-input')"
          :type="type"
          :disabled-date="endDisabledDate"
          :format="dateFormat"
          @change="updateDate"></el-date-picker>
      </template>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref, unref, computed, watchEffect } from 'vue'
import moment from 'moment'
// utils
import { useNamespace } from 'pub-bbx-utils'
// model
import { datePeriodProps, useOptions, formatMap, useDate } from './datePeriod'

import { t } from '@src/locales';

export default defineComponent({
  name: 'BbxDatePeriod',
  props: datePeriodProps,
  emits: ['input', 'change'],
  setup(props, { emit }) {
    const ns = useNamespace('date-period', 'bbx')
    const { endTimeOfType, transToDate, formatValue } = useDate()
    const options = useOptions(props)
    const type = ref((props.value?.type || unref(options)[0]?.value))
    const isQuarter = computed(() => unref(type) === 'quarter')
    const startTime = ref(props.value.startTime)
    const endTime = ref(props.value.endTime)
    const dateFormat = computed(() => {
      return formatMap[unref(type)]
    })

    const handlePeriodTypeChange = () => {
      startTime.value = null
      endTime.value = null
      updateDate()
    }

    watchEffect(() => {
      // 时间类型 
      const typeValue = props.value?.type
      if(typeValue && typeValue !== unref(type)){
        type.value = typeValue
        handlePeriodTypeChange()
      }
    })

    watchEffect(() => {
      // 开始时间 
      startTime.value = transToDate(props.value.startTime)
    })
		
    watchEffect(() => {
      // 结束时间
      endTime.value = transToDate(props.value.endTime)
    })

    // 更新数据
    const updateDate = () => {
      let emitInputValue = {
        startTime: unref(startTime),
        endTime: props.range ? unref(endTime) : null, // 区间选择处理结束时间
        type: unref(type),
      }
      if (emitInputValue.startTime) {
        // 开始时间格式化
        emitInputValue.startTime = formatValue(emitInputValue.startTime, props.valueFormat, '00:00:00')
      }
      if (emitInputValue.endTime) {
        // 处理真实结束时间
        emitInputValue.endTime = formatValue(endTimeOfType(emitInputValue.endTime, unref(type)), props.valueFormat)
      }

      emit('input', emitInputValue)
      emit('change', emitInputValue)
    }

    // 小于等于结束时间可以选
    const startDisabledDate = (date) => {
      if (!endTime.value) return false
      return moment(date).valueOf() > moment(unref(endTime)).valueOf()
    }

    // 大于等于结束时间可以选
    const endDisabledDate = (date) => {
      if (!startTime.value) return false
      return moment(unref(startTime)).valueOf() > moment(date).valueOf()
    }

    return {
      ns,
      options,
      type,
      isQuarter,
      startTime,
      endTime,
      dateFormat,
      t,
      handlePeriodTypeChange,
      startDisabledDate,
      endDisabledDate,
      updateDate,
    }
  },
})
</script>

<style lang="scss">
@import '../style/index.scss';
</style>