<template>
  <div class="flex-x bbx-form-cell-change-box" v-if="showCom">
    <div v-for="(item, index) in cellGroup" :key="index" class="just-cur-point cell-item" :class="count === item.value ? 'choosed-item' : ''" @click="changeCell(item)">
      <i class="iconfont" :class="item.icon"></i>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref, computed } from 'vue';
import { useStateSystemFormBuilderCell, getAllGrayInfo } from 'pub-bbx-utils';
export default defineComponent({
  name: 'FormCellChange',
  props: {
    count: {
      type: Number,
    },
    disabled:{
      type: Boolean,
      default: false,
    }
  },
  setup(props, { emit }) {
    const haveFormCellGray = ref(false);
    const showCom = computed(()=>{
      return !props.disabled && haveFormCellGray.value;
    });
    async function grayInfo (){
      let res = await getAllGrayInfo();
      if(res.FORM_MULTI_ROW){
        haveFormCellGray.value = true;
      }else{
        emit('update', 1);
      }
    }
    function createCellGroupItem(key, icon) {
      return {
        value: key,
        icon,
      };
    }
    const cellGroup = [createCellGroupItem(1, 'icon-danlie'), createCellGroupItem(2, 'icon-shuanglie'), createCellGroupItem(3, 'icon-sanlie'), createCellGroupItem(4, 'icon-silie')];
    function changeCell(item) {
      if(props.disabled || !haveFormCellGray.value) return;
      const { setSystemFormBuilderCell } = useStateSystemFormBuilderCell();
      setSystemFormBuilderCell(item.value);
      emit('update', item.value);
    }
    function watchRootChange() {
      window.addEventListener('message', event => {
        if(props.disabled || !haveFormCellGray.value) return;
        const { action, number } = event.data;
        if (action == 'changeAllFormBuilderCell') {
          emit('update', number);
        }
      });
    }
    async function initFormBuilderCellCount(){
      const { getSystemFormBuilderCell } = useStateSystemFormBuilderCell();
      let count = await getSystemFormBuilderCell();
      emit('update', count);
    }
    watchRootChange();
    grayInfo();
    initFormBuilderCellCount();
    return {
      cellGroup,
      changeCell,
      haveFormCellGray,
      showCom,
    };
  },
});
</script>
<style lang="scss" scoped>
.bbx-form-cell-change-box {
  display: flex;
  align-items: center;
  .cell-item {
    color: #8c8c8c;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:not(:last-child) {
      margin-right: 4px;
    }
  }
  .choosed-item {
    color: #000;
    background-color: #e8e8e8;
  }
}
</style>
