.biz-team-select{
  background-color: #fff;

  position: relative;
  cursor: pointer;
  padding: 1px 32px 1px 0;
  border: 1px solid #e0e1e2;
  border-radius: 2px;
  min-height: 32px;
  text-align: left;

  & > input{
    display: none;
  }

  .biz-team-select-placeholder{
    padding: 0 10px;
    color: #b3b7c1;
    font-size: 13px;
    line-height: 32px;
  }

  p{
    margin: 0;
    line-height: 24px;
    padding: 3px 0 2px 10px;
    @include text-ellipsis();
  }

  &.biz-team-select-open{
    border-radius: 2px 2px 0 0;
  }

  &:not(:hover) {
    .biz-team-select-clear {
      display: none;
    }
  }
}

.biz-team-select-disabled {
  cursor: not-allowed;
  color: #c0c4cc;
  .biz-team-select-clear {
    display: none;
  }
  .biz-team-select-tag {
    .iconfont {
      display: none;
    }
  }
}

.biz-team-select-popper{
  position: absolute;
  z-index: 99999;
  color: #333;
  background-color: #fff;
  border: 1px solid #e0e1e2;

  font-size: 14px;
}

.biz-team-select-popper[x-placement^="top"]{
  border-bottom: none;
}

.biz-team-select-popper[x-placement^="bottom"]{
  border-top: none;
}

.search-team-keyword {
  width: 100%;
  border: none;
}

.biz-team-select-panel{
  max-height: 240px;
  overflow: auto;
}

.biz-team-select-item{
  position: relative;

  display: flex;
  flex-flow: row nowrap;
  align-items: center;

  cursor: pointer;
  padding: 5px 10px;
  line-height: 24px;
  transition: background-color ease .15s;

  &:hover{
    background-color: #f5f7fa;
  }
}

.biz-team-select-name{
  @include text-ellipsis();
}

.biz-team-select-subItem{
  padding-left: 45px;
}

.biz-team-select-expand{
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.biz-team-select-line{
  width: 25px;
  height: 100%;
  border-left: 1px dotted #333;
  position: absolute;
  top: 0;
  left: 17px;

  &:after{
    content: "";
    position: absolute;
    width: 100%;
    border-bottom: 1px dotted #333;
    left: 0;
    top: 50%;
  }
}

.biz-team-select-subItem-end .biz-team-select-line{
  height: 50%;

  &:after{
    bottom: 0;
  }
}

.biz-team-select-subItem-only .biz-team-select-line{
  top: -7px;
  height: 70%;
  &:after{
    bottom: 0;
  }
}

.biz-team-select-subItem-start .biz-team-select-line{
  top: -8px;
  height: calc(100% + 8px);

  &:after{
    top: calc(50% + 4px);
  }
}

.biz-team-select-clear{
  position: absolute;
  top: 50%;
  right: 3px;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  width: 24px;
  height: 24px;
  line-height: 24px;
  color: $text-color-secondary;
  transform: translateY(-50%);

  i{
    font-size: 12px;
  }

  &:hover{
    color: $color-danger;
  }
}

.biz-team-select-tags {
  min-height: 28px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.biz-team-select-tag{
  background-color: #f0f2f5;
  height: 22px;
  margin: 3px;
  padding: 0 5px;
  color: #909399;
  border-radius: 4px;

  display: flex;
  flex-flow: row nowrap;
  align-items: center;

  span.biz-team-select-tag-text{
    display: inline-block;
    // max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    .open-data {
      margin-right: 0;
    }
  }

  i{
    border-radius: 50%;
    margin-left: 2px;
    font-size: 13px;
    line-height: 13px;
    font-size: 12px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
  }
}

.biz-team-select-loading{
  padding: 5px 10px;
  line-height: 24px;
  color: $text-color-secondary;
}

.biz-team-select-selected {
  background-color: $color-primary-light-1;
  color: $color-primary;
  font-weight: 500;
  padding-right: 20px;
  position: relative;

  .checked {
    position: absolute;
    right: 5px;
    top: calc(50% - 4px);
    width: 10px;
    height: 5px;
    border-left: 1px solid $color-primary;
    border-bottom: 1px solid $color-primary;
    border-color: $color-primary;
    transform: rotateZ(-45deg);
  }

}

.biz-team-select-empty{
  padding: 5px 10px;
  line-height: 24px;
  color: $text-color-secondary;
}

.biz-team-select-block {
  display: block;
  width: 100%;
  .biz-team-select {
    height: 100%;
    padding: 0;
  }
  .el-input__suffix {
    display: none;
  }
  .iconfont {
    transform: scale(0.9);
  }
}
