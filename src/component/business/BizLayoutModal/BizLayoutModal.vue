<template>
  <base-modal
    :title="$t('common.layout.label1')"
    :show.sync="visible"
    width="500px"
    @closed="close">
    <div class="biz-layout-modal__box">
      <template v-if="comType === 0">
        <p>{{ $t('common.layout.label2') }}</p>
        <div class="biz-layout-modal-select">
          <div @click="changeLayout(2)" :class="['biz-layout-modal-select-layout', 'margin-right-12', type === 2 && 'is-active']">
            <img :src="leftRightLayoutImg" :alt="$t('common.layout.label3')">
            {{ $t('common.layout.label3') }}
            <div class="biz-layout-modal-select-layout-checkbox-sub">
              <i class="iconfont icon-check"></i>
            </div>
          </div>
          <div @click="changeLayout(1)" :class="['biz-layout-modal-select-layout', type === 1 && 'is-active']">
            <img :src="tileLayoutImg" :alt="$t('common.layout.label4')">
            {{ $t('common.layout.label4') }}
            <div class="biz-layout-modal-select-layout-checkbox-sub">
              <i class="iconfont icon-check"></i>
            </div>
          </div>
        </div>
      </template>
      <p>{{ $t('common.layout.label5') }}</p>
      <div>
        <el-radio-group
          v-model="localeColumns"
          size="small">
          <template v-if="type===1">
            <el-radio v-model="localeColumns" :label="2" border>{{ $t('common.layout.label6') }}</el-radio>
            <el-radio v-model="localeColumns" :label="3" border>{{ $t('common.layout.label7') }}</el-radio>
            <el-radio v-model="localeColumns" :label="4" border>{{ $t('common.layout.label8') }}</el-radio>
          </template>
          <template v-if="type === 2">
            <el-radio v-model="localeColumns" :label="1" border>{{ $t('common.layout.label9') }}</el-radio>
          </template>
        </el-radio-group>
      </div>
    </div>
    <div slot="footer" class="">
      <el-button class="" @click="close">{{ $t('common.base.close') }}</el-button>
      <el-button type="primary" @click="save">{{ $t('common.base.save') }}</el-button>
    </div>
  </base-modal>
</template>

<script>
import leftRightLayoutImg from '@src/assets/img/left-right.png';
import tileLayoutImg from '@src/assets/img/tile-layout.png';
import { useStateSystemViewLayout } from 'pub-bbx-utils';
const { setSystemViewLayout } = useStateSystemViewLayout();

export default {
  name: 'BizLayoutModal',
  props: {
    bizLayoutType: {
      type: Number,
      default: 2
    },
    comType:{
      type: Number,
      default: 0
    },
    columns:{
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      visible: false,
      leftRightLayoutImg,
      tileLayoutImg,
      type: 2,
      localeColumns: 1
    };
  },
  methods: {
    open() {
      this.visible = true;
      this.type = this.bizLayoutType;
      this.localeColumns = this.columns;
    },
    close() {
      this.visible = false;
    },
    save() {
      this.$emit('changeLayout', this.type, this.localeColumns);
      const data = {
        baseLayout: this.type || 2,
			  formCellCount: this.localeColumns || 1,
      };
      setSystemViewLayout(data);

      this.close();
    },
    changeLayout(type) {
      this.type = type;
      if(type === 2){
        this.localeColumns = 1;
      }else{
        this.localeColumns = 2;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.biz-layout-modal__box{
  padding: 20px;
}
.biz-layout-modal-select {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  .is-active {
    border-color: $color-primary;
    .biz-layout-modal-select-layout-checkbox-sub {
      display: block;
    }
  }
  &-layout {
    position: relative;
    width: 120px;
    height: 100px;
    padding: 10px;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
    color: #595959;
    border: 1px solid #E4E7ED;
    border-radius: 8px;
    overflow: hidden;
    &-checkbox-sub {
      display: none;
      position: absolute;
      right: -22px;
      top: -22px;
      width: 44px;
      height: 44px;
      background: $color-primary;
      transform: rotate(45deg);
      i {
        position: absolute;
        left: 17px;
        right: 46px;
        color: #fff;
        top: 28px;
        transform: rotate(-45deg);
      }
    }
    img {
      width: 100px;
      height: 48px;
      margin-bottom: 10px;
    }
  }
  .margin-right-12 {
    margin-right: 12px;
  }
 }
</style>
