/* components */
import { ConnectorModuleAddCardDialog } from '@src/component/business/connector';
import { ConnectorModuleCreateConnectorDialog } from '@src/component/business/connector/components';
import { ConnectorModuleEditConnectorDialog } from '@src/component/business/connector/components';
/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
import { GrayFunctionEnum, GrayFunctionObj } from '@model/grayFunction';
import CardInfo, { CardInfoConnectorConfig } from '@model/entity/CardInfo';
/* vue */
import { ComponentInstance, defineComponent, Ref, ref } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
/* util */
import { getAllGrayInfo } from '@src/util/grayInfo';
import Log from '@src/util/log';
/* service */
import { getCardConfig } from '@service/CardService';
import { t } from '@src/locales';

export type ConnectorModuleCardSettingMixinProps = {
  visible: boolean;
}

export interface ConnectorModuleCardSettingMixinSetupState {
  
}

export enum ConnectorModuleCardSettingMixinEmitEventNameEnum {
  Input = 'input'
}

export type ConnectorModuleCardSettingMixinInstance = ComponentInstance & ConnectorModuleCardSettingMixinSetupState
export type ConnectorModuleCardSettingMixinVM = ComponentRenderProxy<ConnectorModuleCardSettingMixinProps> & CommonComponentInstance & ConnectorModuleCardSettingMixinInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleCardSettingMixin,
  components: {
    ConnectorModuleAddCardDialog,
    ConnectorModuleCreateConnectorDialog,
    ConnectorModuleEditConnectorDialog
  },
  data() {
    return {
      // 连接器新建弹窗标题
      connectorCreateModalTitle: t('common.base.createModule', {data1: t('common.connector.title.connector')}),
      // 连接器编辑弹窗标题
      connectorEditModalTitle: t('common.base.editModule', {module: t('common.connector.title.connector')}),
      // 是否开启连接器功能灰度
      isHasLinkCard: false,
      // 是否显示附件组件添加弹窗
      isShowAddCardModal: false,
      // 是否显示新版创建附加组件弹窗
      isShowNewCreateCardModal: false,
      // 是否显示新建连接器弹窗
      isShowCreateConnectorModal: false,
      // 是否显示编辑连接器弹窗
      isShowEditConnectorModal: false,
      // 连接器信息
      connectorInfo: {}
    };
  },
  mounted() {
    this.initializeConnectorCardGray();
  },
  methods: {
    /**
     * @description 获取灰度信息
    */
    fetchGrayModuleInfo() {
      return getAllGrayInfo();
    },
    /**
     * @description 从附件组件中获取连接器信息
     */
    getConnectorInfoByCard(card: CardInfo) {
      
      // 附件组件配置
      const config = getCardConfig<CardInfo, CardInfoConnectorConfig>(card);
      // 连接器信息
      const connectorInfo = config?.connectorInfo || {};
      
      return connectorInfo;
      
    },
    /** 
     * @description 初始化连接器卡片灰度
    */
    async initializeConnectorCardGray() {
      try {
        
        const grayInfo = (await this.fetchGrayModuleInfo()) || {};
        
        this.setIsHasLinkCardByGrayInfo(grayInfo);
        
      } catch (error) {
        Log.error(error, this.initializeConnectorCardGray.name);
      }
      
    },
    /** 
     * @description 显示新版创建附加组件弹窗
    */
    onOpenNewCreateCardModal() {
      this.isShowNewCreateCardModal = true;
    },
    /** 
     * @description 关闭新版创建附加组件弹窗
    */
    onCloseNewCreateCardModal() {
      this.isShowNewCreateCardModal = false;
    },
    /** 
     * @description 显示附件组件添加弹窗
    */
    onOpenShowAddCardModal() {
      this.isShowAddCardModal = true;
    },
    /** 
     * @description 关闭附件组件添加弹窗
    */
    onCloseShowAddCardModal() {
      this.isShowAddCardModal = false;
    },
    /** 
     * @description 显示新建连接器弹窗
    */
    onOpenCreateConnectorModal() {
      this.isShowCreateConnectorModal = true;
    },
    /** 
     * @description 关闭新建连接器弹窗
    */
    onCloseCreateConnectorModal() {
      this.isShowCreateConnectorModal = false;
    },
    /** 
     * @description 显示编辑连接器弹窗
    */
    onOpenEditConnectorModal() {
      this.isShowEditConnectorModal = true;
    },
    /** 
     * @description 关闭编辑连接器弹窗
    */
    onCloseEditConnectorModal() {
      this.isShowEditConnectorModal = false;
    },
    /** 
     * @description 附件组件 添加 按钮事件
    */
    onClickCreateHandler() {
      
      // 开启连接器灰度，则显示新版新建弹窗
      if (this.isHasLinkCard) {
        this.onOpenNewCreateCardModal();
        return;
      }
      
      // 否则显示旧版添加附件组件弹窗
      this.onOpenShowAddCardModal();
    },
    /** 
     * @description 普通附加组件新建事件
    */
    onCardCreateHandler() {
      this.onOpenShowAddCardModal();
      this.onCloseNewCreateCardModal();
    },
    /** 
     * @description 连接器新建事件
    */
    onConnectorCreateHandler() {
      this.onOpenCreateConnectorModal();
    },
    /** 
     * @description 连接器 编辑 按钮事件
    */
    onEditConnectorCardHandler(card: CardInfo) {
      
      const connectorInfo = this.getConnectorInfoByCard(card);
      this.setConnectorInfo(connectorInfo);
      
      this.onOpenEditConnectorModal();
    },
    /** 
     * @description 设置连接器配置数据
    */
    setConnectorInfo(connectorInfo: Record<string, any>) {
      this.connectorInfo = connectorInfo;
    },
    /** 
     * @description 根据灰度信息设置是否开启连接器功能
    */
    setIsHasLinkCardByGrayInfo(grayInfo: GrayFunctionObj) {
      this.isHasLinkCard = grayInfo[GrayFunctionEnum.LinkCard];
    }
  }
});
