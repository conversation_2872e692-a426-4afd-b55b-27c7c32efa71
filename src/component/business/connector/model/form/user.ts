
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueUserType,
  ConnectorServerValueUserType
} from '@src/component/business/connector/types';


class ConnectorFormValueUser extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueUserType): ConnectorServerValueUserType {
    // 通过选人组件获取的人员对象是 { userId, displayName, staffId }
    // 工单类型设置人员选择后对象已经转成 { id, name, staffId } 
    // @ts-ignore
    const id = formValue.userId || formValue.id;
    // @ts-ignore
    const name = formValue.displayName || formValue.name;
    const staffId = formValue.staffId;
    
    return {
      id,
      name,
      staffId
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueUserType): ConnectorFormValueUserType {
    
    const id = serverValue.id;
    const name = serverValue.name;
    const staffId = serverValue.staffId;
    
    return {
      id,
      name,
      staffId
    };
    
  }
  
}

export default ConnectorFormValueUser;