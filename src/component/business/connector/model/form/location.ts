/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
import { ConnectorAddressTypeEnum } from '@src/component/business/connector/model/enum';
/* types */
import { 
  ConnectorFormValueLocationType, 
  ConnectorServerValueLocationType 
} from '@src/component/business/connector/types';


class ConnectorFormValueLocation extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueLocationType): ConnectorServerValueLocationType {
    
    // @ts-ignore
    const country = formValue?.country || '';
    const province = formValue?.province || '';
    const city = formValue?.city || '';
    // @ts-ignore
    const district = formValue?.district || '';
    const latitude = null as unknown as number;
    const longitude = null as unknown as number;
    const time = null as unknown as number;
    // @ts-ignore
    const road = formValue?.road || '';
    // @ts-ignore
    const streeNumber = formValue?.streeNumber || '';
    // @ts-ignore
    const isHaveLocation = formValue?.isHaveLocation;
    const address = '';
    
    return {
      address,
      country,
      province,
      city,
      district,
      latitude,
      longitude,
      time,
      road,
      streeNumber,
      isHaveLocation
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueLocationType): ConnectorFormValueLocationType {
    const country = serverValue?.country || '';
    const province = serverValue?.province || '';
    const city = serverValue?.city || '';
    const dist = serverValue?.district || '';
    
    return {
      country,
      province,
      city,
      dist
    };
    
  }
  
}

export default ConnectorFormValueLocation;