
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueProductType,
  ConnectorServerValueProductType
} from '@src/component/business/connector/types';


class ConnectorFormValueProduct extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueProductType): ConnectorServerValueProductType {
    
    // 兼容 paas 表单编辑器
    
    const paasFormValue = (formValue || {}) as Record<string, any>;
    let paasFormProductName = '';
    let paasFormProductId = '';
    
    Object.keys(paasFormValue).forEach((key) => {
      
      if (key.includes('_name')) {
        paasFormProductName = paasFormValue[key];
      }
      
      if (key.includes('_productId')) {
        paasFormProductId = paasFormValue[key];
      }
      
    });
    
    const customerId = formValue?.customerId || '';
    const id = paasFormProductId || formValue?.id || '';
    const name = paasFormProductName || formValue?.name || '';
    
    return {
      id,
      name,
      customerId
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueProductType): ConnectorFormValueProductType[] {
    
    const id = serverValue?.id || '';
    const name = serverValue?.name || '';
    const customerId = serverValue?.customerId || '';
    const customerName = '';
    const serialNumber = '';
    const type = '';
    
    return [{
      customerName,
      id,
      name,
      serialNumber,
      type,
      customerId
    }];
    
  }
  
}

export default ConnectorFormValueProduct;
