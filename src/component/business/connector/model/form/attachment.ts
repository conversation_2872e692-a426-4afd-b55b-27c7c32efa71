
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueAttachmentType,
  ConnectorServerValueAttachmentType
} from '@src/component/business/connector/types';


class ConnectorFormValueAttachment extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueAttachmentType): ConnectorServerValueAttachmentType {
    throw new Error('Method not implemented.');
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueAttachmentType): ConnectorFormValueAttachmentType {
    throw new Error('Method not implemented.');
  }
  
}

export default ConnectorFormValueAttachment;