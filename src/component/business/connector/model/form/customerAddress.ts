
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueCustomerAddressType,
  ConnectorServerValueCustomerAddressType
} from '@src/component/business/connector/types';


class ConnectorFormValueCustomerAddress extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueCustomerAddressType): ConnectorServerValueCustomerAddressType {
    
    const id = formValue?.id || '';
    const name = formValue?.label || formValue?.address || '';
    
    return {
      id,
      name
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueCustomerAddressType): ConnectorFormValueCustomerAddressType[] {
    
    const id = serverValue?.id || '';
    const address = serverValue?.name || '';
    const label = serverValue?.name || '';
    
    return [{
      id,
      address,
      label
    }];
    
  }
  
}

export default ConnectorFormValueCustomerAddress;