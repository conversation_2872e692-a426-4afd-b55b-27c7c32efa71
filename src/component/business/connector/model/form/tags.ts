
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueTagsType,
  ConnectorServerValueTagsType
} from '@src/component/business/connector/types';


class ConnectorFormValueTags extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorServerValueTagsType): ConnectorServerValueTagsType {
    return formValue;
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueTagsType): ConnectorServerValueTagsType {
    return serverValue;
  }
  
}

export default ConnectorFormValueTags;