
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueRelatedCustomersType,
  ConnectorServerValueRelatedCustomersType
} from '@src/component/business/connector/types';


class ConnectorFormValueRelatedCustomers extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueRelatedCustomersType): ConnectorServerValueRelatedCustomersType {
    
    const serverValue: ConnectorServerValueRelatedCustomersType = formValue.map(item => {
      return {
        id: item.id,
        name: item.name
      };
    });
    
    return serverValue;
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueRelatedCustomersType): ConnectorFormValueRelatedCustomersType {
    
    const lmPhone = '';
    const serialNumber = '';
    
    const formValue = serverValue.map(item => {
      return {
        id: item.id,
        name: item.name,
        lmPhone,
        serialNumber
      };
    });
    
    return formValue;
    
  }
  
}

export default ConnectorFormValueRelatedCustomers;
