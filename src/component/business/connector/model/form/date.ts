
/* model */
import DateFormatEnum from '@model/enum/DateFormatEnum';
import { EndDateHM, EndDateHMS, StartDateHM, StartDateHMS } from '@src/model/const/Date';
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
import { ConnectorField } from '@src/component/business/connector/model/interface';
/* types */
import {
  ConnectorFormValueDateType,
  ConnectorServerValueDateType
} from '@src/component/business/connector/types';
/* util */
import { fmt_data_time } from '@src/util/lang';
import { safeNewDate } from '@src/util/date';
import { isNotArray } from '@src/util/type';


class ConnectorFormValueDate extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueDateType, field: ConnectorField): ConnectorServerValueDateType {
    
    if (isNotArray(formValue)) {
      return ['', ''];
    }
    
    const setting = field.setting || {};
    const dateType = setting?.dateType || DateFormatEnum.YMD;
    
    const startDate = formValue[0];
    const startTime = fmt_data_time(safeNewDate(startDate), dateType);
    
    const endDate = formValue[1];
    const endTime = (
      fmt_data_time(
        safeNewDate(endDate), 
        dateType
      )
        .replace(StartDateHMS, EndDateHMS)
        .replace(StartDateHM, EndDateHM)
    );
    
    return [startTime, endTime];
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueDateType, field: ConnectorField): ConnectorFormValueDateType {
    return serverValue;
  }
  
}

export default ConnectorFormValueDate;