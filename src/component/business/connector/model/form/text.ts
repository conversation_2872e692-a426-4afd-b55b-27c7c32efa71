
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
import { ConnectorFieldEnNameEnum } from '@src/component/business/connector/model/enum';
import { ConnectorField } from '@src/component/business/connector/model/interface';
/* types */
import {
  ConnectorFormValueTextType,
  ConnectorServerValueTextType,
  ConnectorServerValueLinkmanType,
  ConnectorFormValueLinkmanType,
} from '@src/component/business/connector/types';

class ConnectorFormValueText extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueTextType, field: ConnectorField): ConnectorServerValueTextType | ConnectorServerValueLinkmanType {
    
    // 如果 是 联系人姓名，需要转换为 联系人 对象
    if (field?.fieldName === ConnectorFieldEnNameEnum.LinkmanName) {
      return {
        name: formValue,
        id: ''
      };
    }
    
    return formValue;
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueTextType): ConnectorFormValueTextType {
    return serverValue;
  }
  
}

export default ConnectorFormValueText;
