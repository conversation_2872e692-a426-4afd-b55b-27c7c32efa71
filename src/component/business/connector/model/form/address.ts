/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
import { ConnectorAddressTypeEnum } from '@src/component/business/connector/model/enum';
/* types */
import { 
  ConnectorFormValueAddressType, 
  ConnectorServerValueAddressType 
} from '@src/component/business/connector/types';


class ConnectorFormValueAddress extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueAddressType): ConnectorServerValueAddressType {
    const country = formValue?.country || '';
    const province = formValue?.province || '';
    const city = formValue?.city || '';
    const dist = formValue?.dist || '';
    
    const address = formValue?.address || '';
    const all = '';
    const latitude = null as unknown as number;
    const longitude = null as unknown as number;
    const addressType = ConnectorAddressTypeEnum.NoLatitudeOrLongitude;
    
    return {
      country,
      province,
      city,
      dist,
      address,
      all,
      latitude,
      longitude,
      addressType
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueAddressType): ConnectorFormValueAddressType {
    const country = serverValue?.country || '';
    const province = serverValue?.province || '';
    const city = serverValue?.city || '';
    const dist = serverValue?.dist || '';
    const address = serverValue?.address || '';
    
    return {
      country,
      province,
      city,
      dist,
      address
    };
    
  }
  
}

export default ConnectorFormValueAddress;