
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueCustomerType,
  ConnectorServerValueCustomerType
} from '@src/component/business/connector/types';


class ConnectorFormValueCustomer extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueCustomerType): ConnectorServerValueCustomerType {
    
    const id = formValue?.id || '';
    const name = formValue?.name || '';
    
    return {
      id,
      name
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueCustomerType): ConnectorFormValueCustomerType[] {
    
    const id = serverValue?.id || '';
    const name = serverValue?.name || '';
    const lmPhone = '';
    const serialNumber = '';
    
    return [{
      id,
      name,
      lmPhone,
      serialNumber
    }];
    
  }
  
}

export default ConnectorFormValueCustomer;