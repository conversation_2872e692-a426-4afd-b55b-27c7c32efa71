
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueRelationTaskType,
  ConnectorServerValueRelationTaskType
} from '@src/component/business/connector/types';


class ConnectorFormValueRelationTask extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueRelationTaskType): ConnectorServerValueRelationTaskType {
    
    const taskId = formValue?.taskId || '';
    const taskNo = formValue?.taskNo || '';
    const templateId = formValue?.templateId || '';
    
    return {
      taskId,
      taskNo,
      templateId
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueRelationTaskType): ConnectorFormValueRelationTaskType[] {
    
    const taskId = serverValue?.taskId || '';
    const taskNo = serverValue?.taskNo || '';
    const templateId = serverValue?.templateId || '';
    
    return [{
      taskId,
      taskNo,
      templateId
    }];
    
  }
  
}

export default ConnectorFormValueRelationTask;