
/* model */
import { ConnectorFormValue } from '@src/component/business/connector/model/form/common';
/* types */
import {
  ConnectorFormValueLinkmanType,
  ConnectorServerValueLinkmanType
} from '@src/component/business/connector/types';


class ConnectorFormValueLinkman extends ConnectorFormValue {
  
  // 表单数据 -> 服务器数据
  formValueToServerValue(formValue: ConnectorFormValueLinkmanType): ConnectorServerValueLinkmanType {
    
    const id = formValue?.id || '';
    const name = formValue?.name || '';
    
    return {
      id,
      name
    };
    
  }
  
  // 服务器数据 -> 表单数据
  serverValueToFormValue(serverValue: ConnectorServerValueLinkmanType): ConnectorFormValueLinkmanType[] {
    
    const id = serverValue?.id || '';
    const name = serverValue?.name || '';
    const phone = '';
    
    return [{
      id,
      name,
      phone
    }];
    
  }
  
}

export default ConnectorFormValueLinkman;