/* model */
import Field from '@model/entity/Field';
import CardInfo, { CardInfoConfig, CardInfoConnectorConfig } from '@model/entity/CardInfo';
import { CardInputTypeEnum } from '@model/enum/CardEnum';
import {
  AppTypeEnum,
  ConnectorBizTypeEnum,
  ConnectorOptionsActionEnum,
  ConnectorOptionsFieldOptionValueTypeEnum
} from '@src/component/business/connector/model/enum';
/* types */
import { ConnectorRuleFormItemType } from '@src/component/business/connector/types';

type Merge<M, N> = Omit<M, Extract<keyof M, keyof N>> & N;

interface ConnectorDialogDetailData extends ConnectorOptions {
  additionalId?: string;
  bizTypeId: string;
  paasApp: ConnectorDialogDetailPaasData;
  actionsValue: ConnectorOptionsAction[];
  insertRuleForms: ConnectorRuleFormItemType[];
  selectRuleForms: ConnectorRuleFormItemType[];
  showFieldNameList: string[];
  inputType: CardInputTypeEnum;
  appType?: AppTypeEnum | null;
  conditionalLimit: string;
}

interface ConnectorDialogDetailPaasData {
  id: string;
  name: string;
  icon: string;
  type: string;
}

interface ConnectorCardInfoConfig extends CardInfoConfig {

}

interface ConnectorField extends Field {
  show?: boolean;
  label?: string;
  width?: number;
  minWidth?: number;
  field?: string;
  name?: string;
}

interface ConnectorModule {
  name: string;
  canSelect: boolean;
  children: ConnectorModuleForm[];
}

interface ConnectorModuleForm {
  appType: string,
  // 表单名称
  name: string;
  // 表单logo
  logo: string;
  // 表单类型
  bizType: ConnectorBizTypeEnum;
  // 表单id
  bizTypeId: string;
  // 唯一的key, 前端用的。bizTypeId + name 拼接而成
  uniqueId: string;

  canSelect: boolean;
}

type ConnectorAppModuleListItem = Merge<ConnectorModuleForm, {
  bizType: string,
  // 是否能选中
  canSelect?: boolean;
  // 类型
  appType?: AppTypeEnum | null;
  children?: ConnectorAppModuleListItem[];
  icon?: string;
  appId?: string;
}>

// 连接器配置 执行动作
interface ConnectorOptionsAction {
  // 动作apiId
  apiId?: number | string;
  // 动作名称
  name: string;
  // 动作类型
  action: ConnectorOptionsActionEnum;
  // 是否选中
  selected: boolean;
  toRequestFieldList: ConnectorToField[];
  toResponseFieldList: ConnectorToField[];
}

// 连接器配置 操作符
interface ConnectorOptionsOperate {
  // 中文名
  cnName: string;
  // 英文名
  enName: string;
}

interface ConnectorFromField {
  // 中文名
  cnName: string;
  // 英文名
  enName: string;
  // 字段类型
  fieldType: string;
  // 是否显示
  show?: boolean;
  children?: ConnectorFromField[];
}

interface ConnectorToField {
 // 父的enName
  parentEnName?: string;
  // 中文名
  cnName: string;
  // 英文名
  enName: string;
  // 字段类型
  fieldType: string;
  // 是否显示
  show: boolean;
  // 字段组，例如有 system 等
  filedGroup: string;
  // 是否支持查询
  supportSelect: boolean;
  // 是否支持新增
  supportInsert: boolean;
  // 是否支持更新
  supportUpdate?: boolean;
  // 是否支持删除
  supportDelete?: boolean;
  // 对于新增来说，是否必填
  required: boolean;
  // 该字段支持的操作符列表
  operateList: ConnectorOptionsOperate[];
  // 配置
  setting: Record<string, any>;

  children?: ConnectorToField[]
}

interface ConnectorOptions {
  // 连接器配置 执行动作列表
  actionList: ConnectorOptionsAction[];
  // from 表单的字段列表
  fromFieldList: ConnectorFromField[];
  // to表单的字段列表
  toFieldList: ConnectorToField[];

  insertFieldOptionsList?: ConnectorOptionsFieldOption[];
  selectFieldOptionsList?: ConnectorOptionsFieldOption[];
}

interface ConnectorEditOptions {
  additionalId: number;
  // from 表单业务类型
  fromBizType: ConnectorBizTypeEnum;
  // from 表单id
  fromBizTypeId: string;
  // to 表单业务类型
  toBizType: ConnectorBizTypeEnum;
  // to 表单id
  toBizTypeId: string;
  // to 表单logo
  toBizTypeLogo: string;
  // to 表单名称
  toBizTypeName: string;
  // 连接器配置 执行动作列表
  actionList: ConnectorOptionsAction[];
  // from 表单的字段列表
  fromFieldList: ConnectorFromField[];
  // to表单的字段列表
  toFieldList: ConnectorToField[];
  // 新建插入配置规则
  insertFieldOptionsList: ConnectorOptionsFieldOption[];
  // 查询配置规则
  selectFieldOptionsList: ConnectorOptionsFieldOption[];
  // 附加组件类型 单次 或 多次
  addTime: CardInputTypeEnum,
  // 表格显示的表单字段列表
  showFieldNameList: string[];
  appType?: AppTypeEnum | null;
  conditionalLimit: string;
}

interface ConnectorOptionsFieldOption {
  // 字段名
  toEnName: string;
  // 取值方式  1：固定值，2：对应字段
  valueType: ConnectorOptionsFieldOptionValueTypeEnum | null;
  // 操作符
  operate: string;
  // 如果取值方式为对应字段，则传：["$fromEnName$"]
  // 如果取值方式为固定值，操作符为 =，则传：["固定值"]
  // 如果取值方式为固定值，操作符为in，则传：["value1", "value2"]
  // 如果取值方式为固定值，操作符为between，则传：["start", "end"]，小的在前面，大的在后面
  // TODO: 类型
  value: any[];
  parentEnName?: string;
}

interface ConnectorCardInfo extends CardInfo  {
  config: CardInfoConnectorConfig;
}

interface ConnectorCardAdditionInfoHeader {
  // 中文名
  cnName: string;
  // 英文名
  enName: string;
  // 字段类型
  fieldType: string;
}

interface ConnectorCardAdditionInfoButton {
  // 按钮名称
  buttonName: string;
  // 按钮功能
  function: ConnectorOptionsActionEnum;
  // 前端跳转路径
  jumpPath: string;
}

interface ConnectorOpenBizNoConfig {
  // 编号的key
  fieldName: string;
  // 前端跳转路径的type
  openTabType: string
}
export {
  ConnectorDialogDetailData,
  ConnectorDialogDetailPaasData,
  ConnectorCardInfo,
  ConnectorField,
  ConnectorCardInfoConfig,
  ConnectorModule,
  ConnectorToField,
  ConnectorFromField,
  ConnectorOptionsOperate,
  ConnectorOptionsAction,
  ConnectorModuleForm,
  ConnectorOptions,
  ConnectorOptionsFieldOption,
  ConnectorCardAdditionInfoHeader,
  ConnectorCardAdditionInfoButton,
  ConnectorEditOptions,
  ConnectorAppModuleListItem,
  ConnectorOpenBizNoConfig,
};
