"use strict";
var _a, _b;
exports.__esModule = true;
exports.AppTypeEnum = exports.ConnectorOptionsActionMappingFieldKeyEnum = exports.ConnectorOptionsActionTextEnum = exports.ActionReqOrResTypeEnum = exports.ConnectorFieldEnNameEnum = exports.ConnectorBizTypeIdEnum = exports.ConnectorFieldNameEnum = exports.ConnectorSourceOperateEum = exports.ConnectorBizTypeCnNameEnum = exports.ConnectorFromBizTypeEnum = exports.ConnectorAddressTypeEnum = exports.ConnectorFieldTypeEnum = exports.ConnectorFieldOperateEnum = exports.ConnectorOptionsFieldOptionValueTypeEnum = exports.ConnectorConditionTypeEnum = exports.ConnectorOptionsActionEnum = exports.ConnectorBizTypeEnum = exports.ConnectorActionEnum = exports.ConnectorCardMultiFixedFieldNameEnum = exports.ConnectorCardSingleFixedFieldNameEnum = exports.CreateConnectorDialogFieldNameEnum = exports.ConnectorModuleErrorMessageEnum = exports.ConnectorModuleComponentNameEnum = void 0;
var locales_1 = require("@src/locales");
var ConnectorModuleComponentNameEnum;
(function (ConnectorModuleComponentNameEnum) {
    ConnectorModuleComponentNameEnum["ConnectorListView"] = "connector-list-view";
    ConnectorModuleComponentNameEnum["ConnectorListViewCreateDialog"] = "connector-list-view-create-dialog";
    ConnectorModuleComponentNameEnum["ConnectorListViewHeader"] = "connector-list-view-header";
    ConnectorModuleComponentNameEnum["ConnectorListViewCardList"] = "connector-list-view-card-list";
    ConnectorModuleComponentNameEnum["ConnectorListViewCardItem"] = "connector-list-view-card-item";
    ConnectorModuleComponentNameEnum["ConnectorSettingView"] = "connector-setting-view";
    ConnectorModuleComponentNameEnum["ConnectorSettingViewHeader"] = "connector-setting-view-header";
    ConnectorModuleComponentNameEnum["ConnectorSettingViewMain"] = "connector-setting-view-main";
    ConnectorModuleComponentNameEnum["ConnectorModuleCardItem"] = "connector-module-card-item";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetail"] = "connector-module-connector-dialog-detail";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailHeader"] = "connector-module-connector-dialog-detail-header";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailColumn"] = "connector-module-connector-dialog-detail-column";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailAction"] = "connector-module-connector-dialog-detail-action";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailActionQuery"] = "connector-module-connector-dialog-detail-action-query";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailActionCreate"] = "connector-module-connector-dialog-detail-action-create";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailSetting"] = "connector-module-connector-dialog-detail-setting";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorDialogDetailTypeRadio"] = "connector-module-connector-dialog-detail-setting-type-radio";
    ConnectorModuleComponentNameEnum["ConnectorModuleAddCardDialog"] = "connector-module-add-card-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleAddCardItem"] = "connector-module-add-card-item";
    ConnectorModuleComponentNameEnum["ConnectorModuleCreateConnectorDialog"] = "connector-module-create-connector-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleCreateConnectorNameDialog"] = "connector-module-create-connector-name-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleCreateConnectorDetailDialog"] = "connector-module-create-connector-detail-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleEditConnectorDialog"] = "connector-module-edit-connector-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCard"] = "connector-module-connector-card";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCardMulti"] = "connector-module-connector-card-multi";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCardMultiCardTable"] = "connector-module-connector-card-multi-card-table";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCardComponentTable"] = "connector-module-connector-component-table";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCardMultiButtonGroup"] = "connector-module-connector-card-multi-button-group";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCardMultiOutsideAppPagination"] = "connector-module-connector-card-multi-outside-app-pagination";
    ConnectorModuleComponentNameEnum["ConnectorModulePaasIframeDialog"] = "connector-module-paas-iframe-dialog";
    ConnectorModuleComponentNameEnum["ConnectorModuleRuleForm"] = "connector-module-rule-form";
    ConnectorModuleComponentNameEnum["ConnectorModuleRuleFormItem"] = "connector-module-rule-form-item";
    ConnectorModuleComponentNameEnum["ConnectorModuleCardSettingMixin"] = "connector-module-card-setting-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleCardSettingCustomerMixin"] = "connector-module-card-setting-customer-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleCardSettingTaskMixin"] = "connector-module-card-setting-task-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleCardSettingEventMixin"] = "connector-module-card-setting-event-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCreateMixin"] = "connector-module-connector-create-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCreateTaskMixin"] = "connector-module-connector-create-task-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCreateCustomerMixin"] = "connector-module-connector-create-customer-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCreateProductMixin"] = "connector-module-connector-create-product-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorCreateEventMixin"] = "connector-module-connector-create-event-mixin";
    ConnectorModuleComponentNameEnum["ConnectorModuleConnectorRecordMixin"] = "connector-module-connector-record-mixin";
})(ConnectorModuleComponentNameEnum || (ConnectorModuleComponentNameEnum = {}));
exports.ConnectorModuleComponentNameEnum = ConnectorModuleComponentNameEnum;
var ConnectorModuleErrorMessageEnum;
(function (ConnectorModuleErrorMessageEnum) {
    ConnectorModuleErrorMessageEnum["ConnectorList"] = "\u83B7\u53D6\u8FDE\u63A5\u5668\u5217\u8868\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["ConnectDataList"] = "\u83B7\u53D6\u8FDE\u63A5\u5668\u6570\u636E\u5217\u8868\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["ConnectorCardAdditionInfo"] = "\u83B7\u53D6\u8FDE\u63A5\u5668\u9644\u52A0\u4FE1\u606F\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["ConnectorAllowAddConnectorData"] = "\u83B7\u53D6\u8FDE\u63A5\u5668\u662F\u5426\u5141\u8BB8\u6DFB\u52A0\u6570\u636E\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["DeleteConnector"] = "\u5220\u9664\u8FDE\u63A5\u5668\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["DeleteConnectorData"] = "\u5220\u9664\u8FDE\u63A5\u5668\u6570\u636E\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["CardRelationPaasApplicationList"] = "\u83B7\u53D6\u5173\u8054\u7684\u5E94\u7528\u5217\u8868\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["ModuleList"] = "\u83B7\u53D6\u6A21\u5757\u5217\u8868\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["ConnectorOptions"] = "\u83B7\u53D6\u8FDE\u63A5\u5668\u914D\u7F6E\u5931\u8D25";
    ConnectorModuleErrorMessageEnum["SaveConnectorOptions"] = "\u4FDD\u5B58\u8FDE\u63A5\u5668\u914D\u7F6E\u5931\u8D25";
})(ConnectorModuleErrorMessageEnum || (ConnectorModuleErrorMessageEnum = {}));
exports.ConnectorModuleErrorMessageEnum = ConnectorModuleErrorMessageEnum;
var CreateConnectorDialogFieldNameEnum;
(function (CreateConnectorDialogFieldNameEnum) {
    // 关联应用表单
    CreateConnectorDialogFieldNameEnum["RelationAppForm"] = "relationAppForm";
    // 名称
    CreateConnectorDialogFieldNameEnum["Name"] = "name";
    // 描述
    CreateConnectorDialogFieldNameEnum["Description"] = "description";
    // 名称国际化
    CreateConnectorDialogFieldNameEnum["TitleLanguage"] = "titleLanguage";
    // 描述国际化
    CreateConnectorDialogFieldNameEnum["DescLanguage"] = "descLanguage";
})(CreateConnectorDialogFieldNameEnum || (CreateConnectorDialogFieldNameEnum = {}));
exports.CreateConnectorDialogFieldNameEnum = CreateConnectorDialogFieldNameEnum;
var ConnectorCardSingleFixedFieldNameEnum;
(function (ConnectorCardSingleFixedFieldNameEnum) {
    // 操作人
    ConnectorCardSingleFixedFieldNameEnum["UserName"] = "userName";
    // 更新时间
    ConnectorCardSingleFixedFieldNameEnum["UpdateTime"] = "updateTime";
})(ConnectorCardSingleFixedFieldNameEnum || (ConnectorCardSingleFixedFieldNameEnum = {}));
exports.ConnectorCardSingleFixedFieldNameEnum = ConnectorCardSingleFixedFieldNameEnum;
var ConnectorCardMultiFixedFieldNameEnum;
(function (ConnectorCardMultiFixedFieldNameEnum) {
    // 操作人
    ConnectorCardMultiFixedFieldNameEnum["UserName"] = "userName";
    // 更新时间
    ConnectorCardMultiFixedFieldNameEnum["UpdateTime"] = "updateTime";
    // 操作
    ConnectorCardMultiFixedFieldNameEnum["Operation"] = "Operation";
})(ConnectorCardMultiFixedFieldNameEnum || (ConnectorCardMultiFixedFieldNameEnum = {}));
exports.ConnectorCardMultiFixedFieldNameEnum = ConnectorCardMultiFixedFieldNameEnum;
var ConnectorActionEnum;
(function (ConnectorActionEnum) {
    // 查询
    ConnectorActionEnum["Select"] = "SELECT";
})(ConnectorActionEnum || (ConnectorActionEnum = {}));
exports.ConnectorActionEnum = ConnectorActionEnum;
var ConnectorBizTypeEnum;
(function (ConnectorBizTypeEnum) {
    // 工单
    ConnectorBizTypeEnum["Task"] = "TASK";
    // paas
    ConnectorBizTypeEnum["Paas"] = "PAAS";
    // 客户
    ConnectorBizTypeEnum["Customer"] = "CUSTOMER";
    // 事件
    ConnectorBizTypeEnum["Event"] = "EVENT";
    // 产品
    ConnectorBizTypeEnum["Product"] = "PRODUCT";
    //附加组件
    ConnectorBizTypeEnum["Addons"] = "ADDONS";
})(ConnectorBizTypeEnum || (ConnectorBizTypeEnum = {}));
exports.ConnectorBizTypeEnum = ConnectorBizTypeEnum;
var ConnectorBizTypeIdEnum;
(function (ConnectorBizTypeIdEnum) {
    // 客户
    ConnectorBizTypeIdEnum["Customer"] = "1";
    // 产品
    ConnectorBizTypeIdEnum["Product"] = "2";
})(ConnectorBizTypeIdEnum || (ConnectorBizTypeIdEnum = {}));
exports.ConnectorBizTypeIdEnum = ConnectorBizTypeIdEnum;
var ConnectorFromBizTypeEnum;
(function (ConnectorFromBizTypeEnum) {
    ConnectorFromBizTypeEnum["Customer"] = "1";
    ConnectorFromBizTypeEnum["Product"] = "2";
})(ConnectorFromBizTypeEnum || (ConnectorFromBizTypeEnum = {}));
exports.ConnectorFromBizTypeEnum = ConnectorFromBizTypeEnum;
var ConnectorOptionsActionEnum;
(function (ConnectorOptionsActionEnum) {
    // 查询
    ConnectorOptionsActionEnum["Select"] = "SELECT";
    // 更新
    ConnectorOptionsActionEnum["Update"] = "UPDATE";
    // 插入
    ConnectorOptionsActionEnum["Insert"] = "INSERT";
    // 删除
    ConnectorOptionsActionEnum["Delete"] = "DELETE";
    // 关联添加
    ConnectorOptionsActionEnum["Add"] = "ADD";
})(ConnectorOptionsActionEnum || (ConnectorOptionsActionEnum = {}));
exports.ConnectorOptionsActionEnum = ConnectorOptionsActionEnum;
var ConnectorOptionsActionMappingFieldKeyEnum;
(function (ConnectorOptionsActionMappingFieldKeyEnum) {
    // 查询
    ConnectorOptionsActionMappingFieldKeyEnum["SELECT"] = "supportSelect";
    // 更新
    ConnectorOptionsActionMappingFieldKeyEnum["UPDATE"] = "supportUpdate";
    // 插入
    ConnectorOptionsActionMappingFieldKeyEnum["INSERT"] = "supportInsert";
    // 删除
    ConnectorOptionsActionMappingFieldKeyEnum["DELETE"] = "supportDelete";
    // 插入
    ConnectorOptionsActionMappingFieldKeyEnum["ADD"] = "supportInsert";
})(ConnectorOptionsActionMappingFieldKeyEnum || (ConnectorOptionsActionMappingFieldKeyEnum = {}));
exports.ConnectorOptionsActionMappingFieldKeyEnum = ConnectorOptionsActionMappingFieldKeyEnum;
var ConnectorConditionTypeEnum;
(function (ConnectorConditionTypeEnum) {
    // 对应的字段
    ConnectorConditionTypeEnum["FromField"] = "fromField";
    // 固定值
    ConnectorConditionTypeEnum["FixedValue"] = "fixedValue";
    // 置空
    ConnectorConditionTypeEnum["Empty"] = "empty";
})(ConnectorConditionTypeEnum || (ConnectorConditionTypeEnum = {}));
exports.ConnectorConditionTypeEnum = ConnectorConditionTypeEnum;
var ConnectorOptionsFieldOptionValueTypeEnum;
(function (ConnectorOptionsFieldOptionValueTypeEnum) {
    // 固定值
    ConnectorOptionsFieldOptionValueTypeEnum[ConnectorOptionsFieldOptionValueTypeEnum["FixedValue"] = 1] = "FixedValue";
    // 对应字段
    ConnectorOptionsFieldOptionValueTypeEnum[ConnectorOptionsFieldOptionValueTypeEnum["FromField"] = 2] = "FromField";
})(ConnectorOptionsFieldOptionValueTypeEnum || (ConnectorOptionsFieldOptionValueTypeEnum = {}));
exports.ConnectorOptionsFieldOptionValueTypeEnum = ConnectorOptionsFieldOptionValueTypeEnum;
var ConnectorFieldOperateEnum;
(function (ConnectorFieldOperateEnum) {
    // 相等
    ConnectorFieldOperateEnum["Equal"] = "EQ";
    // 介于 在...之间
    ConnectorFieldOperateEnum["Between"] = "BETWEEN";
    // 包含
    ConnectorFieldOperateEnum["CONTAINS"] = "CONTAINS";
})(ConnectorFieldOperateEnum || (ConnectorFieldOperateEnum = {}));
exports.ConnectorFieldOperateEnum = ConnectorFieldOperateEnum;
var ConnectorFieldTypeEnum;
(function (ConnectorFieldTypeEnum) {
    ConnectorFieldTypeEnum["Address"] = "address";
    ConnectorFieldTypeEnum["Attachment"] = "attachment";
    ConnectorFieldTypeEnum["Customer"] = "customer";
    ConnectorFieldTypeEnum["Date"] = "date";
    ConnectorFieldTypeEnum["DateTime"] = "datetime";
    ConnectorFieldTypeEnum["SerialNumber"] = "serialNumber";
    ConnectorFieldTypeEnum["User"] = "user";
    ConnectorFieldTypeEnum["CustomerAddress"] = "customerAddress";
    ConnectorFieldTypeEnum["Linkman"] = "linkman";
    ConnectorFieldTypeEnum["Location"] = "location";
    ConnectorFieldTypeEnum["Product"] = "product";
    // 关联工单
    ConnectorFieldTypeEnum["RelationTask"] = "relationTask";
    ConnectorFieldTypeEnum["RelatedTask"] = "related_task";
    ConnectorFieldTypeEnum["RelatedCustomers"] = "related_customers";
    // 服务部门
    ConnectorFieldTypeEnum["Tags"] = "tags";
    // 单行文本
    ConnectorFieldTypeEnum["Text"] = "text";
    ConnectorFieldTypeEnum["JsonArray"] = "jsonArray";
    ConnectorFieldTypeEnum["JsonObject"] = "jsonObject";
})(ConnectorFieldTypeEnum || (ConnectorFieldTypeEnum = {}));
exports.ConnectorFieldTypeEnum = ConnectorFieldTypeEnum;
var ConnectorFieldEnNameEnum;
(function (ConnectorFieldEnNameEnum) {
    ConnectorFieldEnNameEnum["Linkman"] = "lmName";
    ConnectorFieldEnNameEnum["LinkmanName"] = "lmName";
    ConnectorFieldEnNameEnum["Tags"] = "tags";
    ConnectorFieldEnNameEnum["Customer"] = "customer";
    ConnectorFieldEnNameEnum["CustomerAddress"] = "customerAddress";
})(ConnectorFieldEnNameEnum || (ConnectorFieldEnNameEnum = {}));
exports.ConnectorFieldEnNameEnum = ConnectorFieldEnNameEnum;
var ConnectorFieldNameEnum;
(function (ConnectorFieldNameEnum) {
    ConnectorFieldNameEnum["Tags"] = "tags";
})(ConnectorFieldNameEnum || (ConnectorFieldNameEnum = {}));
exports.ConnectorFieldNameEnum = ConnectorFieldNameEnum;
var ConnectorAddressTypeEnum;
(function (ConnectorAddressTypeEnum) {
    // 有经纬度
    ConnectorAddressTypeEnum[ConnectorAddressTypeEnum["HasLatitudeOrLongitude"] = 1] = "HasLatitudeOrLongitude";
    // 无经纬度
    ConnectorAddressTypeEnum[ConnectorAddressTypeEnum["NoLatitudeOrLongitude"] = 0] = "NoLatitudeOrLongitude";
})(ConnectorAddressTypeEnum || (ConnectorAddressTypeEnum = {}));
exports.ConnectorAddressTypeEnum = ConnectorAddressTypeEnum;
var ConnectorSourceOperateEum = (_a = {},
    // TASK(1,"工单"), EVENT(2,"事件"), PRODUCT(3,"产品"), CUSTOMER(4,"客户"), PAAS(5,"PaaS")
    _a[ConnectorBizTypeEnum.Task] = 1,
    _a[ConnectorBizTypeEnum.Event] = 2,
    _a[ConnectorBizTypeEnum.Product] = 3,
    _a[ConnectorBizTypeEnum.Customer] = 4,
    _a[ConnectorBizTypeEnum.Paas] = 5,
    _a);
exports.ConnectorSourceOperateEum = ConnectorSourceOperateEum;
var ConnectorBizTypeCnNameEnum = (_b = {},
    // 工单
    _b[ConnectorBizTypeEnum.Task] = '工单',
    // paas
    _b[ConnectorBizTypeEnum.Paas] = 'PAAS',
    // 客户
    _b[ConnectorBizTypeEnum.Customer] = '客户',
    // 事件
    _b[ConnectorBizTypeEnum.Event] = '事件',
    // 产品
    _b[ConnectorBizTypeEnum.Product] = '产品',
    _b);
exports.ConnectorBizTypeCnNameEnum = ConnectorBizTypeCnNameEnum;
var ActionReqOrResTypeEnum;
(function (ActionReqOrResTypeEnum) {
    ActionReqOrResTypeEnum["ToRequestFieldList"] = "toRequestFieldList";
    ActionReqOrResTypeEnum["ToResponseFieldList"] = "toResponseFieldList";
})(ActionReqOrResTypeEnum || (ActionReqOrResTypeEnum = {}));
exports.ActionReqOrResTypeEnum = ActionReqOrResTypeEnum;
var ConnectorOptionsActionTextEnum = {
    'SELECT': locales_1.t('common.base.tableAction.query'),
    'INSERT': locales_1.t('common.base.tableAction.insert'),
    'DELETE': locales_1.t('common.base.tableAction.delete'),
    'UPDATE': locales_1.t('common.base.tableAction.update')
};
exports.ConnectorOptionsActionTextEnum = ConnectorOptionsActionTextEnum;
var AppTypeEnum;
(function (AppTypeEnum) {
    AppTypeEnum["OutSide"] = "outSide";
    AppTypeEnum["InSide"] = "inSide";
})(AppTypeEnum || (AppTypeEnum = {}));
exports.AppTypeEnum = AppTypeEnum;
