
import { t } from '@src/locales';

enum ConnectorModuleComponentNameEnum {

  ConnectorListView = 'connector-list-view',
  ConnectorListViewCreateDialog = 'connector-list-view-create-dialog',
  ConnectorListViewHeader = 'connector-list-view-header',
  ConnectorListViewCardList = 'connector-list-view-card-list',
  ConnectorListViewCardItem = 'connector-list-view-card-item',

  ConnectorSettingView = 'connector-setting-view',
  ConnectorSettingViewHeader = 'connector-setting-view-header',
  ConnectorSettingViewMain = 'connector-setting-view-main',

  ConnectorModuleCardItem = 'connector-module-card-item',

  ConnectorModuleConnectorDialogDetail = 'connector-module-connector-dialog-detail',
  ConnectorModuleConnectorDialogDetailHeader = 'connector-module-connector-dialog-detail-header',
  ConnectorModuleConnectorDialogDetailColumn = 'connector-module-connector-dialog-detail-column',
  ConnectorModuleConnectorDialogDetailAction = 'connector-module-connector-dialog-detail-action',
  ConnectorModuleConnectorDialogDetailActionQuery = 'connector-module-connector-dialog-detail-action-query',
  ConnectorModuleConnectorDialogDetailActionCreate = 'connector-module-connector-dialog-detail-action-create',
  ConnectorModuleConnectorDialogDetailSetting = 'connector-module-connector-dialog-detail-setting',
  ConnectorModuleConnectorDialogDetailTypeRadio = 'connector-module-connector-dialog-detail-setting-type-radio',

  ConnectorModuleAddCardDialog = 'connector-module-add-card-dialog',

  ConnectorModuleAddCardItem = 'connector-module-add-card-item',

  ConnectorModuleCreateConnectorDialog = 'connector-module-create-connector-dialog',
  ConnectorModuleCreateConnectorNameDialog = 'connector-module-create-connector-name-dialog',
  ConnectorModuleCreateConnectorDetailDialog = 'connector-module-create-connector-detail-dialog',

  ConnectorModuleEditConnectorDialog = 'connector-module-edit-connector-dialog',

  ConnectorModuleConnectorCard = 'connector-module-connector-card',
  ConnectorModuleConnectorCardMulti = 'connector-module-connector-card-multi',
  ConnectorModuleConnectorCardMultiCardTable = 'connector-module-connector-card-multi-card-table',
  ConnectorModuleConnectorCardComponentTable = 'connector-module-connector-component-table',
  ConnectorModuleConnectorCardMultiButtonGroup = 'connector-module-connector-card-multi-button-group',
  ConnectorModuleConnectorCardMultiOutsideAppPagination = 'connector-module-connector-card-multi-outside-app-pagination',

  ConnectorModulePaasIframeDialog = 'connector-module-paas-iframe-dialog',

  ConnectorModuleRuleForm = 'connector-module-rule-form',
  ConnectorModuleRuleFormItem = 'connector-module-rule-form-item',

  ConnectorModuleCardSettingMixin = 'connector-module-card-setting-mixin',
  ConnectorModuleCardSettingCustomerMixin = 'connector-module-card-setting-customer-mixin',
  ConnectorModuleCardSettingTaskMixin = 'connector-module-card-setting-task-mixin',
  ConnectorModuleCardSettingEventMixin = 'connector-module-card-setting-event-mixin',

  ConnectorModuleConnectorCreateMixin = 'connector-module-connector-create-mixin',
  ConnectorModuleConnectorCreateTaskMixin = 'connector-module-connector-create-task-mixin',
  ConnectorModuleConnectorCreateCustomerMixin = 'connector-module-connector-create-customer-mixin',
  ConnectorModuleConnectorCreateProductMixin = 'connector-module-connector-create-product-mixin',
  ConnectorModuleConnectorCreateEventMixin = 'connector-module-connector-create-event-mixin',

  ConnectorModuleConnectorRecordMixin = 'connector-module-connector-record-mixin',

}

enum ConnectorModuleErrorMessageEnum {
  ConnectorList = '获取连接器列表失败',
  ConnectDataList = '获取连接器数据列表失败',
  ConnectorCardAdditionInfo = '获取连接器附加信息失败',
  ConnectorAllowAddConnectorData = '获取连接器是否允许添加数据失败',
  DeleteConnector = '删除连接器失败',
  DeleteConnectorData = '删除连接器数据失败',
  CardRelationPaasApplicationList = '获取关联的应用列表失败',
  ModuleList = '获取模块列表失败',
  ConnectorOptions = '获取连接器配置失败',
  SaveConnectorOptions = '保存连接器配置失败'
}


enum CreateConnectorDialogFieldNameEnum {
  // 关联应用表单
  RelationAppForm = 'relationAppForm',
  // 名称
  Name = 'name',
  // 描述
  Description = 'description',
  // 名称国际化
  TitleLanguage = 'titleLanguage',
  // 描述国际化
  DescLanguage = 'descLanguage',
}

enum ConnectorCardSingleFixedFieldNameEnum {
  // 操作人
  UserName = 'userName',
  // 更新时间
  UpdateTime = 'updateTime',
}

enum ConnectorCardMultiFixedFieldNameEnum {
  // 操作人
  UserName = 'userName',
  // 更新时间
  UpdateTime = 'updateTime',
  // 操作
  Operation = 'Operation',
}

enum ConnectorActionEnum {
  // 查询
  Select = 'SELECT',
}

enum ConnectorBizTypeEnum {
  // 工单
  Task = 'TASK',
  // paas
  Paas = 'PAAS',
  // 客户
  Customer = 'CUSTOMER',
  // 事件
  Event = 'EVENT',
  // 产品
  Product = 'PRODUCT',
  //附加组件
  Addons = 'ADDONS'
}

enum ConnectorBizTypeIdEnum {
  // 客户
  Customer = '1',
  // 产品
  Product = '2',
}

enum ConnectorFromBizTypeEnum {
  Customer = '1',
  Product = '2'
}

enum ConnectorOptionsActionEnum {
  // 查询
  Select = 'SELECT',
  // 更新
  Update = 'UPDATE',
  // 插入
  Insert = 'INSERT',
  // 删除
  Delete = 'DELETE',
  // 关联添加
  Add = 'ADD',
}


enum ConnectorOptionsActionMappingFieldKeyEnum {
  // 查询
  SELECT = 'supportSelect',
  // 更新
  UPDATE = 'supportUpdate',
  // 插入
  INSERT = 'supportInsert',
  // 删除
  DELETE = 'supportDelete',
  // 插入
  ADD = 'supportInsert',
}


enum ConnectorConditionTypeEnum {
  // 对应的字段
  FromField = 'fromField',
  // 固定值
  FixedValue = 'fixedValue',
  // 置空
  Empty = 'empty',
}

enum ConnectorOptionsFieldOptionValueTypeEnum {
  // 固定值
  FixedValue = 1,
  // 对应字段
  FromField = 2,
}

enum ConnectorFieldOperateEnum {
  // 相等
  Equal = 'EQ',
  // 介于 在...之间
  Between = 'BETWEEN',
  // 包含
  CONTAINS = 'CONTAINS'
}

enum ConnectorFieldTypeEnum {
  Address = 'address',
  Attachment = 'attachment',
  Customer = 'customer',
  Date = 'date',
  DateTime = 'datetime',
  SerialNumber = 'serialNumber',
  User = 'user',
  CustomerAddress = 'customerAddress',
  Linkman = 'linkman',
  Location = 'location',
  Product = 'product',
  // 关联工单
  RelationTask = 'relationTask',
  RelatedTask = 'related_task',
  RelatedCustomers = 'related_customers',
  // 服务部门
  Tags = 'tags',
  // 单行文本
  Text = 'text',
  JsonArray = 'jsonArray',
  JsonObject = 'jsonObject',
  // 部门
  Tag = 'tag',
  aiLabel = 'aiLabel',
  // 仓位
  warehousePositionId = 'warehousePositionId',
  // 仓库
  warehouseId = 'warehouseId'
}

enum ConnectorFieldEnNameEnum {
  Linkman = 'lmName',
  LinkmanName = 'lmName',
  Tags = 'tags',
  Customer = 'customer',
  CustomerAddress = 'customerAddress',
}

enum ConnectorFieldNameEnum {
  Tags = 'tags',
}

enum ConnectorAddressTypeEnum {
  // 有经纬度
  HasLatitudeOrLongitude = 1,
  // 无经纬度
  NoLatitudeOrLongitude = 0,
}

const ConnectorSourceOperateEum = {
  // TASK(1,"工单"), EVENT(2,"事件"), PRODUCT(3,"产品"), CUSTOMER(4,"客户"), PAAS(5,"PaaS")
  [ConnectorBizTypeEnum.Task]: 1,
  [ConnectorBizTypeEnum.Event]: 2,
  [ConnectorBizTypeEnum.Product]: 3,
  [ConnectorBizTypeEnum.Customer]: 4,
  [ConnectorBizTypeEnum.Paas]: 5,
};

const ConnectorBizTypeCnNameEnum: Record<string, string> = {
  // 工单
  [ConnectorBizTypeEnum.Task]: '工单',
  // paas
  [ConnectorBizTypeEnum.Paas]: 'PAAS',
  // 客户
  [ConnectorBizTypeEnum.Customer]: '客户',
  // 事件
  [ConnectorBizTypeEnum.Event]: '事件',
  // 产品
  [ConnectorBizTypeEnum.Product]: '产品',
};


enum ActionReqOrResTypeEnum {
  ToRequestFieldList = 'toRequestFieldList',
  ToResponseFieldList = 'toResponseFieldList'
}

const ConnectorOptionsActionTextEnum: Record<string, string> = {
  'SELECT': t('common.base.tableAction.query'),
  'INSERT': t('common.base.tableAction.insert'),
  'DELETE': t('common.base.tableAction.delete'),
  'UPDATE': t('common.base.tableAction.update')
};

enum AppTypeEnum {
  OutSide = 'outSide',
  InSide = 'inSide'
}


export {
  ConnectorModuleComponentNameEnum,
  ConnectorModuleErrorMessageEnum,
  CreateConnectorDialogFieldNameEnum,
  ConnectorCardSingleFixedFieldNameEnum,
  ConnectorCardMultiFixedFieldNameEnum,
  ConnectorActionEnum,
  ConnectorBizTypeEnum,
  ConnectorOptionsActionEnum,
  ConnectorConditionTypeEnum,
  ConnectorOptionsFieldOptionValueTypeEnum,
  ConnectorFieldOperateEnum,
  ConnectorFieldTypeEnum,
  ConnectorAddressTypeEnum,
  ConnectorFromBizTypeEnum,
  ConnectorBizTypeCnNameEnum,
  ConnectorSourceOperateEum,
  ConnectorFieldNameEnum,
  ConnectorBizTypeIdEnum,
  ConnectorFieldEnNameEnum,
  ActionReqOrResTypeEnum,
  ConnectorOptionsActionTextEnum,
  ConnectorOptionsActionMappingFieldKeyEnum,
  AppTypeEnum
};
