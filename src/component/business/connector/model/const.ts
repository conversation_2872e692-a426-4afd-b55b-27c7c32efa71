/* model */
import Field from '@model/entity/Field';
import { CardInputTypeEnum } from '@model/enum/CardEnum';
/* enum */
import { FieldTypeMappingEnum } from '@model/enum/FieldMappingEnum';
import {
  ConnectorBizTypeEnum,
  ConnectorCardMultiFixedFieldNameEnum,
  ConnectorCardSingleFixedFieldNameEnum,
  ConnectorConditionTypeEnum,
  ConnectorFieldTypeEnum,
  CreateConnectorDialogFieldNameEnum
} from '@src/component/business/connector/model/enum';
/* model */
import {
  ConnectorDialogDetailData,
  ConnectorDialogDetailPaasData,
  ConnectorEditOptions,
  ConnectorField,
  ConnectorOptions,
  ConnectorOpenBizNoConfig
} from '@src/component/business/connector/model/interface';
/* util */
import { createConnectorRuleFormItem } from '@src/component/business/connector/util';
import { t } from '@src/locales';
import { PageRoutesTypeEnum } from "pub-bbx-global/pageType/dist/enum/PageRoutesEnum";

const CreateConnectorModalFields: Field[] = [
  {
    fieldName: CreateConnectorDialogFieldNameEnum.RelationAppForm,
    formType: FieldTypeMappingEnum.Cascader,
    displayName: t('common.connector.fields.relationAppForm.displayName'),
    placeHolder: t('common.placeholder.select'),
    isNull: 0,
    setting: {
      dataSource: []
    }
  },
  {
    fieldName: CreateConnectorDialogFieldNameEnum.Name,
    formType: FieldTypeMappingEnum.Text,
    displayName: t('common.base.name'),
    placeHolder: t('common.placeholder.inputName'),
    isNull: 0,
    // @ts-ignore
    maxlength: 20
  },
  {
    fieldName: CreateConnectorDialogFieldNameEnum.Description,
    formType: FieldTypeMappingEnum.Textarea,
    displayName: t('common.base.explain'),
    placeHolder: t('common.connector.fields.description.placeholder1'),
    isNull: 1,
    // @ts-ignore
    maxlength: 500
  }
];

const ConnectorCardMultiFixedFields: ConnectorField[] = [
  {
    displayName: t('common.base.operation'),
    // @ts-ignore
    minWidth: '140px',
    label: t('common.base.operation'),
    fieldName: ConnectorCardMultiFixedFieldNameEnum.Operation,
    show: true,
    // @ts-ignore
    fixed: 'right'
  }
];

const ConnectorDefaultOptions: ConnectorOptions = {
  actionList: [],
  fromFieldList: [],
  toFieldList: []
};

const ConnectorDefaultEditOptions: ConnectorEditOptions = {
  additionalId: null as unknown as number,
  // from 表单业务类型
  fromBizType: '' as unknown as ConnectorBizTypeEnum,
  // from 表单id
  fromBizTypeId: '',
  // to 表单业务类型
  toBizType: '' as unknown as ConnectorBizTypeEnum,
  // to 表单id
  toBizTypeId: '',
  // to 表单logo
  toBizTypeLogo: '',
  // to 表单名称
  toBizTypeName: '',
  // 连接器配置 执行动作列表
  actionList: [],
  // from 表单的字段列表
  fromFieldList: [],
  // to表单的字段列表
  toFieldList: [],
  // 新建插入配置规则
  insertFieldOptionsList: [],
  // 查询配置规则
  selectFieldOptionsList: [],
  // 附加组件类型 单次 或 多次
  addTime: '' as unknown as CardInputTypeEnum,
  // 表格显示的表单字段列表
  showFieldNameList: [],
  // 查询范围设置条件 1 忽略此条件 2 不展示数据
  conditionalLimit: '1'
};

const ConnectorDefaultPassAppData: ConnectorDialogDetailPaasData = {
  icon: '',
  name: '',
  id: '',
  type: ''
};

const ConnectorDialogDefaultDetailValue: ConnectorDialogDetailData = {
  appType: null,
  bizTypeId: '',
  paasApp: ConnectorDefaultPassAppData,
  actionsValue: [],
  actionList: [],
  fromFieldList: [],
  toFieldList: [],
  insertRuleForms: [
    // 默认插入一条空数据
    createConnectorRuleFormItem()
  ],
  selectRuleForms: [
    // 默认插入一条空数据
    createConnectorRuleFormItem()
  ],
  showFieldNameList: [],
  inputType: CardInputTypeEnum.Single,
  conditionalLimit: '1',
};


const ConnectorConditionSelectList = [
  {
    value: ConnectorConditionTypeEnum.FromField,
    label: t('common.connector.text.correspondingField')
  },
  {
    value: ConnectorConditionTypeEnum.FixedValue,
    label: t('common.connector.fixedValue')
  },
  {
    value: ConnectorConditionTypeEnum.Empty,
    label: t('common.connector.empty')
  }
];

const ConnectorConditionTagsSelectList = [
  {
    value: ConnectorConditionTypeEnum.FixedValue,
    label: t('common.connector.fixedValue')
  }
];

const ConnectorConditionFromFieldSelectList = [
  {
    value: ConnectorConditionTypeEnum.FromField,
    label: t('common.connector.text.correspondingField')
  }
];

const ConnectorConditionDateSelectList = [
  {
    value: ConnectorConditionTypeEnum.FixedValue,
    label: t('common.connector.fixedValue')
  },
  {
    value: ConnectorConditionTypeEnum.Empty,
    label: t('common.connector.empty')
  }
];

const ConnectorConditionInsertList = [
  {
    value: ConnectorConditionTypeEnum.FromField,
    label: t('common.connector.text.correspondingField')
  },
  {
    value: ConnectorConditionTypeEnum.FixedValue,
    label: t('common.connector.fixedValue')
  }
];

// 客户地址和位置没有固定值选择
const ConnectorConditionLocationList = [
  {
    value: ConnectorConditionTypeEnum.FromField,
    label: t('common.connector.text.correspondingField')
  },
  {
    value: ConnectorConditionTypeEnum.Empty,
    label: t('common.connector.empty')
  }
];

// const ConnectorConditionFromFieldSelectList = [
//   {
//     value: ConnectorConditionTypeEnum.FromField,
//     label: '对应字段'
//   },
// ];

const ConnectorPaasDataStorageKey = 'paas-connector-data';

const ConnectorCreateDataSessionKeyPrefix = 'connector_to_';
const ConnectorCreateDataSessionKeyInfix = '_create_data_';

const ConnectorFieldAddressChildren = [
  {
    "cnName": "省",
    "enName": "province",
    "fieldType": "text"
  },
  {
    "cnName": "市",
    "enName": "city",
    "fieldType": "text"
  },
  {
    "cnName": "区",
    "enName": "dist",
    "fieldType": "text"
  },
  {
    "cnName": "详细地址",
    "enName": "all",
    "fieldType": "text"
  }
];

const ConnectorFieldLocationChildren = [
  {
    "cnName": "省",
    "enName": "province",
    "fieldType": "text"
  },
  {
    "cnName": "市",
    "enName": "city",
    "fieldType": "text"
  },
  {
    "cnName": "区",
    "enName": "dist",
    "fieldType": "text"
  }
];

const ConnectorFieldCustomerAddressChildren = [
  {
    "cnName": "地址全路径",
    "enName": "name",
    "fieldType": "text"
  }
];

const ConnectorFieldCustomerChildren = [
  {
    "cnName": "客户名称",
    "enName": "name",
    "fieldType": "text"
  }
];

const ConnectorFieldLinkmanChildren = [
  {
    "cnName": "联系人名称",
    "enName": "name",
    "fieldType": "text"
  }
];

const ConnectorFieldProductChildren = [
  {
    "cnName": "产品名称",
    "enName": "name",
    "fieldType": "text"
  }
];

const ConnectorFieldUserChildren = [
  {
    "cnName": "用户名称",
    "enName": "name",
    "fieldType": "text"
  }
];

const ConnectorFieldRelatedTaskChildren = [
  {
    "cnName": "工单编号",
    "enName": "taskNo",
    "fieldType": "text"
  }
];

const ConnectorFieldTypeChildrenMap: Record<string, any> = {
  [ConnectorFieldTypeEnum.Address]: ConnectorFieldAddressChildren,
  [ConnectorFieldTypeEnum.Location]: ConnectorFieldLocationChildren,
  [ConnectorFieldTypeEnum.CustomerAddress]: ConnectorFieldCustomerAddressChildren,
  [ConnectorFieldTypeEnum.Customer]: ConnectorFieldCustomerChildren,
  [ConnectorFieldTypeEnum.Linkman]: ConnectorFieldLinkmanChildren,
  [ConnectorFieldTypeEnum.Product]: ConnectorFieldProductChildren,
  [ConnectorFieldTypeEnum.User]: ConnectorFieldUserChildren,
  [ConnectorFieldTypeEnum.RelatedTask]: ConnectorFieldRelatedTaskChildren,
};

const ConnectorFetchAppModulesStorageKey = 'connector_app_modules_list';

const ConnectorOpenBizNo: Record<string, ConnectorOpenBizNoConfig> = {
  // 工单详情
  TASK: {
    fieldName: 'taskNo',
    openTabType: PageRoutesTypeEnum.PageTaskView,
  },
  // 客户详情
  CUSTOMER: {
    fieldName: 'serialNumber',
    openTabType: PageRoutesTypeEnum.PageCustomerView,
  },
  // 事件详情
  EVENT: {
    fieldName: 'eventNo',
    openTabType: PageRoutesTypeEnum.PageEventView,
  },
  // 产品详情
  PRODUCT: {
    fieldName: 'serialNumber',
    openTabType: PageRoutesTypeEnum.PageProductView,
  },
  // 项目详情
  PROJECT_MANAGER: {
    fieldName: 'projectNo',
    openTabType: PageRoutesTypeEnum.PageProjectManageView,
  },
  // 活动调研详情
  ACTIVITY_RESEARCH: {
    fieldName: 'activitiesName',
    openTabType: PageRoutesTypeEnum.PageCustomerExperienceActivityResearchView,
  },
  // 合同详情
  CONTRACT: {
    fieldName: 'contractNo',
    openTabType: PageRoutesTypeEnum.PageContractView,
  },
  /*// 备件品类
  PART2_SPAREPART: {
    fieldName: 'serialNumber',
    openTabType: PageRoutesTypeEnum.PagePartCategoryDetail,
  },
 */
  // paas应用
  PAAS: {
    fieldName: 'serialNumber',
    openTabType: PageRoutesTypeEnum.PagePaasTemplateDetail,
  }
};
export {
  CreateConnectorModalFields,
  ConnectorCardMultiFixedFields,
  ConnectorDefaultOptions,
  ConnectorDefaultPassAppData,
  ConnectorConditionSelectList,
  ConnectorConditionDateSelectList,
  ConnectorConditionInsertList,
  ConnectorConditionLocationList,
  ConnectorDefaultEditOptions,
  ConnectorDialogDefaultDetailValue,
  ConnectorPaasDataStorageKey,
  ConnectorCreateDataSessionKeyPrefix,
  ConnectorCreateDataSessionKeyInfix,
  ConnectorConditionTagsSelectList,
  ConnectorFieldTypeChildrenMap,
  ConnectorConditionFromFieldSelectList,
  ConnectorFetchAppModulesStorageKey,
  ConnectorOpenBizNo,
};
