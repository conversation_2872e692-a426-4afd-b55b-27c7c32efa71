/* util */
import http from '@src/util/http';

const prefix = '/api/application';


/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37728
 * @description: 新增/修改触发器
 * @param {*} params
 * @return {*}
 */
export function updateTrigger(params) {
  return http.post(`${prefix}/outside/trigger/addOrUpdate`, params);
}


/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37746
 * @description: 触发器详情
 * @param {*} triggerId 触发器id
 * @return {*}
 */
export function getTriggerDetail(triggerId) {
  return http.get(`${prefix}/outside/trigger/getEditOptions?triggerId=${triggerId}`);
}

/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37758
 * @description: 获取触发应用条件可配置字段
 * @param {*} params
 * @return {*}
 */
export function getTriggerConditionConfig(params) {
  return http.post(`${prefix}/outside/trigger/getTriggerConditionConfig`, params);
}


/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37710
 * @description: 获取执行动作的表单信息
 * @param {*} params
 * @return {*}
 */
export function getCreateOptions(params) {
  return http.get(`${prefix}/outside/trigger/getCreateOptions`, params);
}

/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37716
 * @description: 触发动作
 * @param {*} params
 * @return {*}
 */
export function getTriggerActionList(params) {
  return http.post(`${prefix}/outside/trigger/getTriggerActionList`, params);
}


/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37722
 * @description: 获取触发器列表
 * @param {*} params
 * @return {*}
 */
export function getTriggerInfoList(params) {
  return http.post(`${prefix}/outside/trigger/getTriggerInfoList`, params);
}

/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37734
 * @description: 列表更新触发器
 * @param {*} params
 * @return {*}
 */
export function updateTriggerStat(params) {
  return http.post(`${prefix}/outside/trigger/updateTrigger`, params);
}

/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37740
 * @description: 删除触发器
 * @param {*} triggerId 触发器id
 * @return {*}
 */
export function deleteTrigger(triggerId) {
  return http.post(`${prefix}/outside/trigger/deleteTrigger`, {triggerId});
}


/**
 * @see https://yapi.shb.ltd/project/3088/interface/api/37922
 * @description: 获取应用信息
 * @param {*} params
 * @return {*}
 */
export function getTriggerFormInfo(params) {
  return http.post(`${prefix}/outside/trigger/getTriggerFormInfo`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/38526
 * @description 连接器获取知识库字段数据接口
 */
export function getWikiData(params) {
  return http.get(`${prefix}/outside/connector/getWikiData`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/55907
 * @description 获取所有应用列表
 */
export function getAppList(params = {}) {
  return http.get(`${prefix}/outside/trigger/getAppList`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/55914
 * @description 点击应用获取模块api列表
 */
export function getModuleListV2(params = {}) {
  return http.get(`${prefix}/outside/trigger/getModuleList/v2`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/56005
 * @description 获取执行的配置信息
 */
export function getExecuteConfig(params = {}) {
  return http.get(`${prefix}/outside/trigger/getExecuteConfig`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/55963
 * @description 获取条件配置信息字段
 */
export function getConditionConfig(params = {}) {
  return http.get(`${prefix}/outside/trigger/getConditionConfig`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/55816
 * @description 获取表单字段信息
 */
export function getApiFiledInfo(params = {}) {
  return http.get(`${prefix}/outside/trigger/getApiFiledInfo`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/56012
 * @description 创建触发器
 */
export function createTrigger(params = {}) {
  return http.post(`${prefix}/outside/trigger/create`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/56019
 * @description 获取触发器详情信息
 */
export function getTriggerDetailV2(params = {}) {
  return http.get(`${prefix}/outside/trigger/getDetail`, params);
}

/** 
 * @see https://yapi.shb.ltd/project/3088/interface/api/56026
 * @description 编辑触发器
 */
export function updateTriggerV2(params = {}) {
  return http.post(`${prefix}/outside/trigger/update`, params);
}

/** 
 * @description 手动触发触发器
 */
export function manualTrigger(params) {
  return http.post(`${prefix}/outside/trigger/manualTrigger`, params);
}

// 获取触发方式列表
export function getAllExecuteWayList(params){
  return http.get(`${prefix}/outside/trigger/v3/getAllExecuteWayList`, params);
}