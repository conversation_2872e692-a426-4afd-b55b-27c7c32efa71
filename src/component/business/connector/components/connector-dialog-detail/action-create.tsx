/* components */
import { ConnectorModuleRuleForm, ConnectorModuleConnectorDialogDetailTypeRadio } from '@src/component/business/connector/components';
/* model */
import { ConnectorModuleComponentNameEnum, ConnectorField } from '@src/component/business/connector/model';
import { CardInputTypeEnum } from '@model/enum/CardEnum';
/* scss */
import '@src/component/business/connector/components/card-item/index.scss';
import '@src/component/business/connector/components/connector-dialog-detail/action.scss';
import '@src/component/business/connector/components/connector-dialog-detail/action-create.scss';
/* vue */
import { ComponentInstance, computed, ComputedRef, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
/* types */
import { ConnectorRuleFormItemType } from '@src/component/business/connector/types';
/* util */
import { createConnectorRuleFormItem } from '@src/component/business/connector/util';
import { t } from '@src/locales';

export type ConnectorModuleConnectorDialogDetailActionCreateProps = {
  inputTypeValue: CardInputTypeEnum;
  fromBizTypeName: string;
  toBizTypeName: string;
  toBizTypeId: string;
}

export interface ConnectorModuleConnectorDialogDetailActionCreateSetupState {
  
}

export enum ConnectorModuleConnectorDialogDetailActionCreateEmitEventNameEnum {
  Input = 'input',
  InputTypeValue = 'inputTypeValueChange',
}

export type ConnectorModuleConnectorDialogDetailActionCreateInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailActionCreateSetupState
export type ConnectorModuleConnectorDialogDetailActionCreateVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailActionCreateProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailActionCreateInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailActionCreate,
  emits: [
    ConnectorModuleConnectorDialogDetailActionCreateEmitEventNameEnum.Input,
    ConnectorModuleConnectorDialogDetailActionCreateEmitEventNameEnum.InputTypeValue,
  ],
  props: {
    inputTypeValue: {
      type: String as PropType<CardInputTypeEnum>,
      default: ''
    },
    toFieldList: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    fromFieldList: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    value: {
      type: Array as PropType<ConnectorRuleFormItemType[]>,
      default: () => ([])
    },
    showDetailType: {
      type:Boolean,
      default: true
    },
    fromBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeId: {
      type: String as PropType<string>,
      default: ''
    },
  },
  setup(props) {
    
    const toFields: ComputedRef<ConnectorField[]> = computed(() => props.toFieldList);
    const fromFields: ComputedRef<ConnectorField[]> = computed(() => props.fromFieldList);
    
    return {
      toFields,
      fromFields
    };
  },
  methods: {
    /** 
     * @description 添加查询条件
    */
    addRuleFormItem() {
      
      const newValue = [...this.value, createConnectorRuleFormItem()];
      
      this.onValueInputHandler(newValue);
      
    },
    onValueInputHandler(value: ConnectorRuleFormItemType[]) {
      this.$emit(ConnectorModuleConnectorDialogDetailActionCreateEmitEventNameEnum.Input, value);
    },
    onInputTypeValueChangeHandler(value: CardInputTypeEnum) {
      this.$emit(ConnectorModuleConnectorDialogDetailActionCreateEmitEventNameEnum.InputTypeValue, value);
    }
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailActionCreate}>
        
        <div class="connector-module-connector-dialog-detail-column-header">
          {t('common.connector.title.setNewRule')}
        </div>
        
        { 
          this.showDetailType ? (
            <ConnectorModuleConnectorDialogDetailTypeRadio
              value={this.inputTypeValue}
              // @ts-ignore
              onInput={this.onInputTypeValueChangeHandler}
            />):(<br/>)
        }
        
        <ConnectorModuleRuleForm
          value={this.value}
          isSelect={false}
          // @ts-ignore
          toFieldList={this.toFields}
          // @ts-ignore
          fromFieldList={this.fromFields}
          fromBizTypeName={this.fromBizTypeName}
          toBizTypeName={this.toBizTypeName}
          toBizTypeId={this.toBizTypeId}
          isShowOperate={false}
          onInput={this.onValueInputHandler}
        />
        
        <div class="connector-module-connector-dialog-detail-action-query-add">
          <el-button 
            plain 
            type="primary"
            onClick={this.addRuleFormItem}
          >
            + {t('common.connector.buttons.addField')}
          </el-button>
        </div>
        
      </div>
    );
  }
});
