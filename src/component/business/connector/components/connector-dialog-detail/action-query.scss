
// 查询条件
.connector-module-connector-dialog-detail-action-query {
  
  background: #F5F5F5;
  padding: 16px;
  margin-top: 20px;
  margin-bottom: 12px;
  
  .connector-module-rule-form {
    margin-top: 16px;
  }
}

.connector-module-connector-dialog-detail-action-query-add {
  
  color: $color-primary;
  cursor: pointer;
  
  .el-button {
    
    border-color: $color-primary !important;
    background-color: $color-primary-light-1 !important;
    color: $color-primary !important;
    
    &:hover {
      cursor: pointer;
      background-color: $color-primary !important;
      color: #fff !important;
    }
    
  }

  .el-button--primary.is-plain{
    color: $color-primary !important;
    background-color: $color-primary-light-1 !important;
    &:hover{
      background-color: $color-primary !important;
      color: #fff!important;
    }
  }
  
}
.connector-module-connector-dialog-detail-action-config {
  display: flex;
  justify-content: flex-start;
  padding-top: 16px;
  border-top: 1px solid #d4d7de;
  margin-top: 16px;
  label {
    font-size: 14px;
    line-height: 32px;
    color: #595959;
    margin-right: 8px;
  }
  .el-tooltip {
    font-size: 12px;
    color: #8C8C8C;
    font-weight: normal;
    line-height: 32px;
    margin-left: 8px;
  }
}
.connector-tooltip {
  p {
    margin-bottom: 8px !important;
    line-height: 16px;
  }
}