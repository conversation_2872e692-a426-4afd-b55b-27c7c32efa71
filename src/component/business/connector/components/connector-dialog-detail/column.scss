
.connector-module-connector-dialog-detail-column {
  margin-top: 24px;
}

.connector-module-connector-dialog-detail-column-header {
  height: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 20px;
}

.connector-module-connector-dialog-detail-column-footer {
  height: 20px;
  display: flex;
  justify-content: space-between;
}

.connector-module-connector-dialog-detail-column-sub-title {
  height: 20px;
  font-size: 12px;
  font-weight: 400;
  color: #8C8C8C;
  line-height: 20px;
}

.connector-module-connector-dialog-detail-column-toggle-text {
  cursor: pointer;
  height: 20px;
  font-size: 12px;
  color: $color-primary;
  line-height: 20px;
}

.connector-module-connector-dialog-detail-column-table {
  
  width: 100%;
  margin-top: 8px;
  margin-bottom: 20px;
  
  .el-table {
    width: 100%;
    border: 1px solid #E8E8E8;
  }
  
  .el-table__empty-block {
    min-height: 20px;
    max-height: 20px;
  }
  
  .el-table__empty-text {
    display: none;
  }
  
}

.connector-module-connector-dialog-detail-select-column {
  .biz-select-column-main {
    
    .biz-select-column-tree-parent-header {
      display: none;
    }
    
    .biz-select-column-tree-child {
      margin-left: 0;
    }
    
  }
}