/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/connector-dialog-detail/header.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
import { t } from '@src/locales';

export type ConnectorModuleConnectorDialogDetailHeaderProps = {
  icon: string;
  name: string;
}

export interface ConnectorModuleConnectorDialogDetailHeaderSetupState {

}

export enum ConnectorModuleConnectorDialogDetailHeaderEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleConnectorDialogDetailHeaderInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailHeaderSetupState
export type ConnectorModuleConnectorDialogDetailHeaderVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailHeaderProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailHeaderInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailHeader,
  emits: [
    ConnectorModuleConnectorDialogDetailHeaderEmitEventNameEnum.Input,
  ],
  props: {
    icon: {
      type: String as PropType<string>,
      default: '',
    },
    name: {
      type: String as PropType<string>,
      default: '',
    }
  },
  methods: {
    isValidHttpUrl(string: string) {
      let url;

      try {
        url = new URL(string);
      } catch (_) {
        return false;
      }

      return url.protocol === 'http:' || url.protocol === 'https:';
    }
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailHeader}>

        <div class="connector-module-connector-dialog-detail-header-icon">
          {this.isValidHttpUrl(this.icon) ? <img src={this.icon}></img> : <i class={['iconfont', this.icon]}></i> }
        </div>

        <div class="connector-module-connector-dialog-detail-header-content">

          <span class="connector-module-connector-dialog-detail-header-content-top">
            {t('common.connector.fields.relationAppForm.displayName')}
          </span>

          <span class="connector-module-connector-dialog-detail-header-content-bottom">
            { this.name }
          </span>

        </div>

      </div>
    );
  }
});
