/* model */
import {
  ConnectorModuleComponentNameEnum,
  ConnectorOptionsAction
} from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/card-item/index.scss';
import '@src/component/business/connector/components/connector-dialog-detail/action.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
import { t } from '@src/locales';

export type ConnectorModuleConnectorDialogDetailActionProps = {
  actions: ConnectorOptionsAction[];
  value: ConnectorOptionsAction[];
}

export interface ConnectorModuleConnectorDialogDetailActionSetupState {

}

export enum ConnectorModuleConnectorDialogDetailActionEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleConnectorDialogDetailActionInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailActionSetupState
export type ConnectorModuleConnectorDialogDetailActionVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailActionProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailActionInstance

interface ElSelectActionOption {
  label: string;
  value: string;
}

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailAction,
  emits: [
    ConnectorModuleConnectorDialogDetailActionEmitEventNameEnum.Input,
  ],
  props: {
    actions: {
      type: Array as PropType<ConnectorOptionsAction[]>,
      default: () => []
    },
    value: {
      type: Array as PropType<ConnectorOptionsAction[]>,
      default: () => []
    }
  },
  setup(props: ConnectorModuleConnectorDialogDetailActionProps, {emit}) {

    const onActionChangeHandler = (value: ElSelectActionOption[]) => {
      emit(ConnectorModuleConnectorDialogDetailActionEmitEventNameEnum.Input, value);
    };

    return {
      onActionChangeHandler
    };
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailAction}>

        <div class="connector-module-connector-dialog-detail-column-header">
          {t('common.connector.tips.tip4')}
        </div>

        <div class="connector-module-connector-dialog-detail-column-footer">
          <div class="connector-module-connector-dialog-detail-column-sub-title">
            {t('common.connector.subTitle.title4')}
          </div>
        </div>

        <div class="connector-module-connector-dialog-detail-action-select">
          <el-select
            value={this.value}
            multiple
            value-key="apiId"
            onInput={this.onActionChangeHandler}>
            {
              this.actions.map((option: ConnectorOptionsAction) => {
                return (
                  <el-option
                    key={`${option.action }-${option.apiId}`}
                    label={option.name}
                    value={option}
                  >
                  </el-option>
                );
              })
            }
          </el-select>
        </div>

      </div>
    );
  }
});
