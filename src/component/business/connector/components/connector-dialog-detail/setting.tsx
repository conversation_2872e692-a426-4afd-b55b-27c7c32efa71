/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/card-item/index.scss';
/* vue */
import { ComponentInstance, defineComponent } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';

export type ConnectorModuleConnectorDialogDetailSettingProps = {
  
}

export interface ConnectorModuleConnectorDialogDetailSettingSetupState {
  
}

export enum ConnectorModuleConnectorDialogDetailSettingEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleConnectorDialogDetailSettingInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailSettingSetupState
export type ConnectorModuleConnectorDialogDetailSettingVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailSettingProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailSettingInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailSetting,
  emits: [
    ConnectorModuleConnectorDialogDetailSettingEmitEventNameEnum.Input,
  ],
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailSetting}>
        
      </div>
    );
  }
});
