/* model */
import { CardInputTypeEnum } from '@model/enum/CardEnum';
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/connector-dialog-detail/type-radio.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
import { t } from '@src/locales';
export type ConnectorModuleConnectorDialogDetailTypeRadioProps = {
  
}

export interface ConnectorModuleConnectorDialogDetailTypeRadioSetupState {
  
}

export enum ConnectorModuleConnectorDialogDetailTypeRadioEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleConnectorDialogDetailTypeRadioInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailTypeRadioSetupState
export type ConnectorModuleConnectorDialogDetailTypeRadioVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailTypeRadioProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailTypeRadioInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailTypeRadio,
  emits: [
    ConnectorModuleConnectorDialogDetailTypeRadioEmitEventNameEnum.Input,
  ],
  props: {
    value: {
      type: String as PropType<string>,
      default: '',
    }
  },
  setup(props: ConnectorModuleConnectorDialogDetailTypeRadioProps, { emit }) {
    
    const onRadioInputHandler = (value: string) => {
      emit(ConnectorModuleConnectorDialogDetailTypeRadioEmitEventNameEnum.Input, value);
    };
    
    return {
      onRadioInputHandler
    };
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailTypeRadio}>
        
        <div class="connector-module-connector-dialog-detail-setting-type-radio-text">
          {t('common.connector.title.addTimes')}：
        </div>
        
        <div>
          <el-radio-group 
            value={this.value}
            onInput={this.onRadioInputHandler}
          >
            
            <el-radio label={CardInputTypeEnum.Single}>
              {t('common.base.onceTime')}
            </el-radio>
            
            <el-radio label={CardInputTypeEnum.Multi}>
              {t('common.base.moreTime')}
            </el-radio>
            
          </el-radio-group>
        </div>
        
      </div>
    );
  }
});
