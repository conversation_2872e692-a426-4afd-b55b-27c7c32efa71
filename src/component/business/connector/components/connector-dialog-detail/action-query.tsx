/* components */
import { ConnectorModuleRuleForm } from '@src/component/business/connector/components';
/* model */
import { ConnectorModuleComponentNameEnum, ConnectorField } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/connector-dialog-detail/action-query.scss';
/* vue */
import { ComponentInstance, computed, ComputedRef, defineComponent, PropType, Ref, ref, watch } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
/* types */
import { ConnectorRuleFormItemType } from '@src/component/business/connector/types';
/* util */
import { createConnectorRuleFormItem } from '@src/component/business/connector/util';
import message from '@src/util/Message';
import { t } from '@src/locales';
export type ConnectorModuleConnectorDialogDetailActionQueryProps = {
  value: ConnectorRuleFormItemType[];
  fromBizTypeName: string;
  toBizTypeName: string;
}

export interface ConnectorModuleConnectorDialogDetailActionQuerySetupState {

}

export enum ConnectorModuleConnectorDialogDetailActionQueryEmitEventNameEnum {
  Input = 'input',
  ChangeCondition = 'changeCondition',
}

export type ConnectorModuleConnectorDialogDetailActionQueryInstance = ComponentInstance & ConnectorModuleConnectorDialogDetailActionQuerySetupState
export type ConnectorModuleConnectorDialogDetailActionQueryVM = ComponentRenderProxy<ConnectorModuleConnectorDialogDetailActionQueryProps> & CommonComponentInstance & ConnectorModuleConnectorDialogDetailActionQueryInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailActionQuery,
  emits: [
    ConnectorModuleConnectorDialogDetailActionQueryEmitEventNameEnum.Input,
    ConnectorModuleConnectorDialogDetailActionQueryEmitEventNameEnum.ChangeCondition
  ],
  props: {
    toFieldList: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    fromFieldList: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    value: {
      type: Array as PropType<ConnectorRuleFormItemType[]>,
      default: () => ([])
    },
    fromBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    max: {
      type: Number as PropType<number>,
      default: 0
    },
    conditionalLimit: {
      type: String as PropType<string>,
      default: '1'
    },
    isShowCondition:{
      type: Boolean,
      default: false
    },
  },
  setup(props) {

    const toFields: ComputedRef<ConnectorField[]> = computed(() => props.toFieldList);
    const fromFields: ComputedRef<ConnectorField[]> = computed(() => props.fromFieldList);

    return {
      toFields,
      fromFields
    };
  },
  methods: {
    /**
     * @description 添加查询条件
    */
    addRuleFormItem() {

      if (this.max > 0 && (this.value.length >= this.max)) {
        message.warning(t('common.connector.maxConditionLength', {length: this.max}));
        return;
      }

      const newValue = [...this.value, createConnectorRuleFormItem()];

      this.onValueInputHandler(newValue);

    },
    onValueInputHandler(value: ConnectorRuleFormItemType[]) {
      this.$emit(ConnectorModuleConnectorDialogDetailActionQueryEmitEventNameEnum.Input, value);
    },
    onChangeCondition(val: String) {
      // @ts-ignore
      this.$emit(ConnectorModuleConnectorDialogDetailActionQueryEmitEventNameEnum.ChangeCondition, val)
    },
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorDialogDetailActionQuery}>

        <div class="connector-module-connector-dialog-detail-column-header">
          {t('common.connector.title.setDataSearchConditions')}
        </div>

        <ConnectorModuleRuleForm
          value={this.value}
          isSelect={true}
          // @ts-ignore
          toFieldList={this.toFields}
          // @ts-ignore
          fromFieldList={this.fromFields}
          fromBizTypeName={this.fromBizTypeName}
          toBizTypeName={this.toBizTypeName}
          onInput={this.onValueInputHandler}
        />

        <div class="connector-module-connector-dialog-detail-action-query-add">
          <el-button
            plain
            type="primary"
            onClick={this.addRuleFormItem}
          >
            + {t('common.connector.buttons.addSearchConditions')}
          </el-button>
        </div>
        { this.isShowCondition &&
        <div class="connector-module-connector-dialog-detail-action-config">
          <label>{ t('common.connector.fieldSetting.conditionalLimitLabel') }</label>
          <el-select value={ this.conditionalLimit } onChange={ this.onChangeCondition }>
            <el-option label={t('common.connector.fieldSetting.conditionalLimitTitle1')} value="1"></el-option>
            <el-option label={t('common.connector.fieldSetting.conditionalLimitTitle2')} value="2"></el-option>
          </el-select>
          <el-tooltip popper-class="connector-tooltip" placement="top">
            <div slot="content">
              <div class="tips">
                <p>{t('common.connector.fieldSetting.conditionalLimitTitle1')}：<br/> {t('common.connector.fieldSetting.exampleOne')}</p>
                <p>{t('common.connector.fieldSetting.conditionalLimitTitle2')}：<br/>{t('common.connector.fieldSetting.exampleTwo')}</p>
              </div>
            </div>
            <i class="iconfont icon-question"></i>
          </el-tooltip>
        </div>
        }

      </div>
    );
  }
});
