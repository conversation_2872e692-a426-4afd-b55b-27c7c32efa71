import { defineComponent, PropType, toRefs } from 'vue';
/* model */
import { ConnectorModuleComponentNameEnum} from '@src/component/business/connector/model';
import Page from '@model/Page';

import { defaultTableData } from '@src/util/table';

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiOutsideAppPagination,
  props: {
    pageInfo: {
      type: Object as PropType<Page>,
      default: ()=> new Page()
    },
    pageNum: {
      type: Number as PropType<number>,
      default: ()=> 1
    },
    showSizes: {
      type: Boolean as PropType<boolean>,
      default: ()=> true
    }
  },
  emits: ['changePageNum', 'changePageSize'],
  setup(props, { emit }) {
    const { pageInfo, pageNum, showSizes } = toRefs(props);
    const handlePageChange = (type: string)=> {
      emit('changePageNum', type, pageInfo.value.pageNum);
    };

    const handleSizeChange = (v: number)=> {
      emit('changePageSize', v);
    };

    return ()=> (
      <div class={["connect-third-form__pagination", !pageInfo.value?.hasNextPage && pageNum.value === 1 ? 'mg-0': null]}>
        { pageNum.value > 1 ? <el-button type="primary" onClick={()=>handlePageChange('prev')}>上一页</el-button> : null }
        { pageInfo.value.hasNextPage ? <el-button type="primary" onClick={()=>handlePageChange('next')}>下一页</el-button> : null }
        { showSizes.value
          ? <el-pagination layout="sizes" page-sizes={defaultTableData.defaultPageSizes} {...{ on: { 'size-change': (v: number)=> handleSizeChange(v)}}}></el-pagination>
          : null }
      </div>
    );
  }
});
