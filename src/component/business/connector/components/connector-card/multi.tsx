/* components */
import {
  ConnectorModuleConnectorCardMultiButtonGroup,
  ConnectorModuleConnectorCardMultiCardTable,
} from '@src/component/business/connector/components';
import ConnectorModulePassIframeDialog from '@src/component/business/connector/components/paas-iframe-dialog';
import CardRelationDialog from '../card-relation-dialog/CardRelationDialog.vue';
import ConnectorModuleConnectorCardMultiOutsideAppPagination from '@src/component/business/connector/components/connector-card/multi-outside-pagination';
/* hooks */
import {
  useConnectorCardFetchAdditionInfo,
  useConnectorCardFetchConnectorAllowAddData,
  useConnectorCardFetchConnectorDataList,
  useConnectorCardFetchDeleteData,
  useConnectorCardFetchDisassociationData,
  useConnectorCardFetchCreateThirdData
} from '@src/component/business/connector//hooks';
/* model */
import {
  ConnectorBizTypeEnum,
  ConnectorCardAdditionInfoButton,
  ConnectorCardInfo,
  ConnectorModuleComponentNameEnum,
  ConnectorOptionsActionEnum,
  ConnectorPaasDataStorageKey,
  ConnectorSourceOperateEum,
  ConnectorToField
} from '@src/component/business/connector/model';
import { CardInfoConnectorConfig, CardInfoConnectorInfo } from '@model/entity/CardInfo';
import Page from '@model/Page';
/* scss */
import '@src/component/business/connector/components/connector-card/multi.scss';
/* vue */
import { ComponentInstance, computed, defineComponent, PropType, Ref, ref } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
/* types */
import { ConnectorModulePaasIframeDialogVM } from '@src/component/business/connector/components/paas-iframe-dialog';
import {
  ConnectorAllowAddDataParams,
  ConnectorCardAdditionInfoParams,
  ConnectorCardDataListParams,
  ConnectorDataDeleteParams,
  ConnectorDataDisassociationParams,
  ConnectorInsertSelectCallParams,
  ConnectorPaasDialogMessageData
} from '@src/component/business/connector/types';
/* util */
import { isFalse } from '@src/util/type';
import { defaultTableData } from '@src/util/table';
import {
  connectorToFields2FormFields,
  genConnectorCreateDataSessionKeyByBizType,
  getPaasUrlByBizId,
  getTabIdByConnectorJumpUrl,
  replacePaasUrlByProcessorInstanceId,
  replaceTaskUrlByTaskId,
  replaceTaskUrlByProjectId,
  replaceTaskUrlByToBizTypeId
} from '@src/component/business/connector/util';
import { stringify } from '@src/util/lang/object';
import platform from '@src/platform';
import message from '@src/util/Message';
/* service */
import { getCardConfig } from '@service/CardService';
import Log from '@src/util/log';
import { t } from '@src/locales';

export type ConnectorModuleConnectorCardMultiProps = {
  mainModuleValue: Record<string, any>;
  card: ConnectorCardInfo;
  task: Record<string, any>;
  bizId: string;
  fromBizNo: string;
  nodeTemplateId: string;
  nodeInstanceId: string;
  processId: string;
  showCreateButton: boolean;
  showRelationButton: boolean;
  showEditButton: boolean;
  showDeleteButton: boolean;
  showExportButton: boolean;
  isInIframe: boolean;
  showViewButton: boolean;
}

export interface ConnectorModuleConnectorCardMultiSetupState {

}

export enum ConnectorModuleConnectorCardMultiEmitEventNameEnum {
  Input = 'input',
  Add = 'add',
  Edit = 'edit',
  Delete = 'delete',
  Disassociation = 'disassociation',
  Detail = 'detail',
}

export type ConnectorModuleConnectorCardMultiInstance = ComponentInstance & ConnectorModuleConnectorCardMultiSetupState
export type ConnectorModuleConnectorCardMultiVM = ComponentRenderProxy<ConnectorModuleConnectorCardMultiProps> & CommonComponentInstance & ConnectorModuleConnectorCardMultiInstance

const ConnectorModulePassIframeDialogRef = 'ConnectorModulePassIframeDialogComponent';
const TabLoadingText = t('common.base.isLoading');

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMulti,
  inheritAttrs: false,
  components: {
    ConnectorModulePassIframeDialog,
    CardRelationDialog
  },
  emits: [
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Input,
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Add,
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Edit,
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Delete,
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Disassociation,
    ConnectorModuleConnectorCardMultiEmitEventNameEnum.Detail
  ],
  props: {
    bizId: {
      type: String as PropType<string>,
      default: ''
    },
    mainModuleValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    card: {
      type: Object as PropType<ConnectorCardInfo>,
      default: () => ({})
    },
    fromBizNo: {
      type: String as PropType<string>,
      default: ''
    },
    nodeTemplateId: {
      type: String as PropType<string>,
      default: ''
    },
    nodeInstanceId: {
      type: String as PropType<string>,
      default: ''
    },
    processId: {
      type: String as PropType<string>,
      default: ''
    },
    isInIframe: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    task: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    showCreateButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showRelationButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showEditButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showDeleteButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showExportButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showViewButton: {
      type: Boolean as PropType<boolean>,
      default: true
    }
  },
  setup(props: ConnectorModuleConnectorCardMultiProps, { emit }) {

    // 连接器数据
    const { loading: fetchConnectorDataListLoading, dataList, dataListPageInfo, fetchConnectorDataList, } = useConnectorCardFetchConnectorDataList();
    // 连接器信息 表单列表 按钮列表
    const { loading: fetchConnectorCardAdditionInfoLoading, additionInfo, fetchConnectorCardAdditionInfo } = useConnectorCardFetchAdditionInfo();
    // 连接器数据是否能继续添加
    const { isAllowAddData, fetchAllowAddConnectorData } = useConnectorCardFetchConnectorAllowAddData();

    // 创建第三方app的data
    const { loading: fetchThirdAppCreateLoading, fetchCreateThirdAppData } = useConnectorCardFetchCreateThirdData();

    // 连接器数据删除
    const { loading: fetchDeleteConnectorDataLoading, fetchDeleteConnectorData } = useConnectorCardFetchDeleteData();
    // 连接器数据取消关联
    const { loading: fetchDisassociationConnectorDataLoading, fetchDisassociationConnectorData } = useConnectorCardFetchDisassociationData();

    // 当前 paas 弹窗地址
    const currentPaasDialogUrl = ref('');

    const page = new Page();

    // 附加组件 表单字段列表
    const fields = computed(() => {
      return connectorToFields2FormFields(additionInfo.value.header as ConnectorToField[]);
    });

    // 附加组件数据
    const values = computed(() => {
      return dataList.value || [];
    });

    const isAddPaasForm = ref(false);

    // 是否为查看详情模式
    const isDetail = computed(() => isFalse(isAddPaasForm.value));

    // 连接器消息通讯数据
    const connectorMessageData: Ref<Record<string, any>> = ref({});
    // 是否为新建连接器数据
    const isCreateConnectorData = ref(false);
    // 是否为关联添加数据
    const isRelationData = ref(false);

    return {
      fields,
      values,

      isAddPaasForm,
      isAllowAddData,
      isDetail,

      additionInfo,
      dataList,
      dataListPageInfo,
      page,
      currentPaasDialogUrl,
      connectorMessageData,
      isCreateConnectorData,
      isRelationData,

      fetchConnectorDataListLoading,
      fetchConnectorCardAdditionInfoLoading,
      fetchDeleteConnectorDataLoading,
      fetchDisassociationConnectorDataLoading,
      fetchThirdAppCreateLoading,

      fetchAllowAddConnectorData,
      fetchConnectorDataList,
      fetchDeleteConnectorData,
      fetchDisassociationConnectorData,
      fetchConnectorCardAdditionInfo,
      fetchCreateThirdAppData
    };

  },
  computed: {
    /**
     * @description 连接器 按钮操作列表
    */
    buttonList(): ConnectorCardAdditionInfoButton[] {
      return this.additionInfo?.buttonList || [];
    },
    /**
     * @description 查询按钮
    */
    buttonSelect(): ConnectorCardAdditionInfoButton | undefined {
      return this.buttonList.find(item => item.function == ConnectorOptionsActionEnum.Select);
    },
    /**
     * @description 编辑按钮
    */
    buttonEdit(): ConnectorCardAdditionInfoButton | undefined {
      return this.buttonList.find(item => item.function == ConnectorOptionsActionEnum.Update);
    },
    /**
     * @description 新增按钮
    */
    buttonAdd(): ConnectorCardAdditionInfoButton | undefined {
      return this.buttonList.find(item => item.function == ConnectorOptionsActionEnum.Insert);
    },
    /**
     * @description 关联添加按钮
    */
    buttonRelation(): ConnectorCardAdditionInfoButton | undefined {
      return this.buttonList.find(item => item.function == ConnectorOptionsActionEnum.Add);
    },
    /**
     * @description 删除按钮
    */
    buttonDelete(): ConnectorCardAdditionInfoButton | undefined {
      return this.buttonList.find(item => item.function == ConnectorOptionsActionEnum.Delete);
    },
    /**
     * @description 附加组件配置
    */
    cardInfoConfig(): CardInfoConnectorConfig {
      return getCardConfig<ConnectorCardInfo, CardInfoConnectorConfig>(this.card);
    },
    /**
     * @description 连接器配置信息
    */
    connectorInfo(): CardInfoConnectorInfo {
      return this.cardInfoConfig?.connectorInfo || {};
    },
    /**
     * @description 连接器插入数据参数
    */
    connectorInsertSelectCallParams(): ConnectorPaasDialogMessageData {

      const params = {
        fromBizId: this.bizId,
        fromBizType: this.fromBizType,
        fromBizTypeId: this.fromBizTypeId,
        toBizType: this.toBizType,
        toBizTypeId: this.toBizTypeId,
      };

      if (this.isAddPaasForm) {
        return {
          ...params,
          isCreate: true
        };
      }

      return {
        ...params,
        isEdit: true,
      };

    },
    fromBizType(): ConnectorBizTypeEnum {
      return this.connectorInfo.fromBizType as ConnectorBizTypeEnum;
    },
    fromBizTypeId(): string {
      return this.connectorInfo.fromBizTypeId;
    },
    isThirdApp(): boolean {
      return this.connectorInfo.isToThird || false;
    },
    /**
     * @description 是否显示添加按钮
    */
    isShowAddButton(): boolean {
      return (
        this.isAllowAddData
        && this.additionInfo?.buttonList.some(button => button.function === ConnectorOptionsActionEnum.Insert)
        && this.showCreateButton
      );
    },
    /**
     * @description 是否显示关联添加按钮
    */
    isShowRelationButton(): boolean {
      return (
        this.additionInfo?.buttonList.some(button => button.function === ConnectorOptionsActionEnum.Add)
        && this.showRelationButton
      );
    },
    /**
     * @description 是否显示删除按钮
    */
    isShowDeleteButton(): boolean {
      return (
        this.additionInfo?.buttonList.some(button => button.function === ConnectorOptionsActionEnum.Delete)
        && this.showDeleteButton
      );
    },
    /**
     * @description 是否显示编辑按钮
    */
    isShowEditButton(): boolean {
      return (
        this.additionInfo?.buttonList.some(button => button.function === ConnectorOptionsActionEnum.Update)
        && this.showEditButton
      );
    },
    /**
     * @description 是否显示导出按钮
    */
    isShowExportButton(): boolean {
      return this.showExportButton;
    },
    /**
     * @description 是否显示详情按钮
     */
    isShowViewButton(): boolean {
      return this.buttonSelect?.jumpPath as unknown as boolean && this.showDeleteButton;
    },
    /**
     * @description 加载状态
    */
    loading(): boolean {
      return (
        this.fetchConnectorDataListLoading
        || this.fetchConnectorCardAdditionInfoLoading
        || this.fetchDeleteConnectorDataLoading
        || this.fetchDisassociationConnectorDataLoading
        || this.fetchThirdAppCreateLoading
      );
    },
    pageSize(): number {
      return this.page.pageSize;
    },
    pageNum(): number {
      return this.page.pageNum;
    },
    total(): number {
      return this.dataListPageInfo.total;
    },
    toBizType(): ConnectorBizTypeEnum {
      return this.connectorInfo.toBizType as ConnectorBizTypeEnum;
    },
    toBizTypeId(): string {
      return this.connectorInfo.toBizTypeId;
    },
    toAppType(): string {
      return this.connectorInfo?.toAppType || '';
    },
    toAppName(): string {
      if(this.toAppType === 'outSide' && this.connectorInfo?.toAppName) {
        return this.connectorInfo.toAppName;
      }
      return '';
    },
  },
  mounted() {
    this.initialize();
  },
  methods: {
    /**
     * @description 构建获取 连接器附加组件列表数据列表 参数
    */
    buildConnectorDataListParams(): ConnectorCardDataListParams {
      return {
        // 业务类型，工单还是paas
        fromBizType: this.fromBizType,
        fromBizNo: this.fromBizNo,
        // 业务类型id，比如工单类型id，paas表单类型id
        fromBizTypeId: this.fromBizTypeId,
        // 业务主键id
        fromBizId: this.bizId,
        // 业务类型，工单还是paas
        toBizType: this.toBizType,
        // 业务类型id，比如工单类型id，paas表单类型id
        toBizTypeId: this.toBizTypeId,
        // 页码
        pageNum: this.page.pageNum,
        // 每页数量
        pageSize: this.page.pageSize
      };
    },
    /**
     * @description 构建获取 连接器附加组件配置数据 参数
    */
    buildConnectorCardAdditionInfoParams(): ConnectorCardAdditionInfoParams {
      return {
        // 业务类型，工单还是paas
        fromBizType: this.fromBizType,
        // 业务类型id，比如工单类型id，paas表单类型id
        fromBizTypeId: this.fromBizTypeId,
        // 业务类型，工单还是paas
        toBizType: this.toBizType,
        // 业务类型id，比如工单类型id，paas表单类型id
        toBizTypeId: this.toBizTypeId,
      };
    },
    /**
     * @description 构建 删除连接器附加组件数据 参数
    */
    buildDeleteConnectorDataParams(bizIdList: string[]): ConnectorDataDeleteParams {
      return {
        bizIdList,
        toBizType: this.toBizType,
        toBizTypeId: this.toBizTypeId,
      };
    },
    /**
     * @description 构建 删除连接器附加组件数据 参数
    */
    buildDisassociationConnectorDataParams(bizIdList: string[]): ConnectorDataDisassociationParams {
      return {
        fromBizTypeId: this.fromBizTypeId,
        fromBizId: this.bizId,
        toBizObjs: bizIdList.map(toBizId => ({
          toBizId,
          toBizTypeId: this.toBizTypeId,
        }))
      };
    },
    /**
     * @description 构建获取 连接器附加组件是否能添加数据 参数
    */
    buildAllowAddConnectorDataParams(): ConnectorAllowAddDataParams {
      return {
        // 业务类型，工单还是paas
        toBizType: this.toBizType,
        // 业务类型id，比如工单类型id，paas表单类型id
        toBizTypeId: this.toBizTypeId,
        fromBizId: this.bizId,
        fromBizType: this.fromBizType,
        fromBizTypeId: this.fromBizTypeId,
      };
    },
    emitAddHandler() {

      const data = {
        ...this.connectorMessageData,
        currentPaasDialogUrl: this.currentPaasDialogUrl,
        currentPaasDialogTitle: t('common.pageTitle.pagePaasTemplateCreate')
      };

      this.$emit(ConnectorModuleConnectorCardMultiEmitEventNameEnum.Add, data);

    },
    emitEditHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardMultiEmitEventNameEnum.Edit, row);
    },
    emitDeleteHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardMultiEmitEventNameEnum.Delete, row);
    },
    emitDisassociationHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardMultiEmitEventNameEnum.Disassociation, row);
    },
    emitDetailHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardMultiEmitEventNameEnum.Detail, row);
    },
    /**
     * @description 获取属性列表
     * @return {Record<string, any>} 属性列表
     */
    getAttributes(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      };
    },
    /**
     * @description 初始化
    */
    initialize() {

      const dataListParams = this.buildConnectorDataListParams();
      const additionInfoParams = this.buildConnectorCardAdditionInfoParams();
      const allowAddConnectorDataParams  = this.buildAllowAddConnectorDataParams();

      this.fetchAllowAddConnectorData(allowAddConnectorDataParams);

      this.fetchConnectorDataList(dataListParams);

      this.fetchConnectorCardAdditionInfo(additionInfoParams);

    },
    /**
     * @description 页码数变化
    */
    onPaginationPageNumChangedHandler(pageNum: number) {

      this.page.pageNum = pageNum;

      const params = this.buildConnectorDataListParams();

      this.fetchConnectorDataList(params);

    },
    /**
     * @description 页大小变化
    */
    onPaginationPageSizeChangedHandler(pageSize: number) {

      this.page.pageSize = pageSize;

      const params = this.buildConnectorDataListParams();

      this.fetchConnectorDataList(params);

    },
    /**
     * @description 添加 某项附件组件卡片 事件
    */
    onAddCardItemHandler() {

      const toBizTypeEnabled = Boolean(this.additionInfo?.toBizTypeEnabled);

      if (!toBizTypeEnabled) {
        message.error(t('common.connector.createFormError'));
        return;
      }

      const jumpPath = this.buttonAdd?.jumpPath || '';
      const title = t('common.base.addModule', {module: this.card.cardName || this.card.name});
      const toBizTypeId = this.toBizTypeId || '';

      const isThirdApp = this.isThirdApp;


      if(isThirdApp) {
        const thirdAddParams = {
          ...this.connectorInsertSelectCallParams,
          fromBizNo: this.fromBizNo
        };

        // Reflect.deleteProperty(thirdAddParams, 'fromBizTypeId')
        Reflect.deleteProperty(thirdAddParams, 'isEdit');
        Reflect.deleteProperty(thirdAddParams, 'isCreate');

        this.fetchCreateThirdAppData(thirdAddParams);

        return this.initialize();
      }


      Log.info(jumpPath, 'jumpPath', this.onAddCardItemHandler.name);

      this.currentPaasDialogUrl = replaceTaskUrlByToBizTypeId(jumpPath, toBizTypeId);

      this.setIsAddPaasForm();

      this.saveConnectPaasDataToSessionStorage();

      if (this.isInIframe) {
        this.emitAddHandler();
        return;
      }

      this.isCreateConnectorData = true;

      this.showAddConnectorModulePassIframeDialog(title);
    },
    onRelationCardItemHandler() {
      this.isRelationData = true;
      this.$nextTick(() => {
        // @ts-ignore
        this.$refs.CardRelationDialog?.open?.();
      });
    },
    /**
     * @description 查看 某项附件组件卡片 事件
    */
    onViewCardItemHandler(row: Record<string, any>) {

      const jumpPath = this.buttonSelect?.jumpPath || '';
      const bizId = row?.bizId || '';
      const processorInstanceId = row?.processorInstanceId || '';

      Log.info(jumpPath, 'jumpPath', this.onViewCardItemHandler.name);

      this.currentPaasDialogUrl = replacePaasUrlByProcessorInstanceId(
        processorInstanceId,
        getPaasUrlByBizId(bizId, jumpPath)
      );

      this.currentPaasDialogUrl = replaceTaskUrlByTaskId(bizId, this.currentPaasDialogUrl);
      // 转换一下项目id
      this.currentPaasDialogUrl = replaceTaskUrlByProjectId(bizId, this.currentPaasDialogUrl);

      this.isCreateConnectorData = false;

      this.showDetailConnectorModulePassIframeDialog();

    },
    /**
     * @description 编辑 某项附件组件卡片 事件
    */
    onEditCardItemHandler(row: Record<string, any>) {

      const jumpPath = this.buttonEdit?.jumpPath || '';
      const bizId = row?.bizId || '';
      const title = t('common.base.editModule', {module: this.card.cardName || this.card.name});

      Log.info(jumpPath, 'jumpPath', this.onEditCardItemHandler.name);

      this.currentPaasDialogUrl = getPaasUrlByBizId(bizId, jumpPath);

      this.isCreateConnectorData = false;

      this.setIsAddPaasForm(false);

      this.saveConnectPaasDataToSessionStorage();

      this.showAddConnectorModulePassIframeDialog(title);

    },
    onStoreEditCardItemHandler(row: Record<string, any>) {

      const jumpPath = this.buttonAdd?.jumpPath || '';
      const bizId = row?.bizId || '';
      const processorInstanceId = row?.processorInstanceId || '';

      Log.info(jumpPath, 'jumpPath', this.onViewCardItemHandler.name);

      this.currentPaasDialogUrl = replacePaasUrlByProcessorInstanceId(
        processorInstanceId,
        getPaasUrlByBizId(bizId, jumpPath)
      ) + `&formContentId=${row.bizId}`;

      this.currentPaasDialogUrl = replaceTaskUrlByTaskId(bizId, this.currentPaasDialogUrl);

      this.isCreateConnectorData = false;

      this.setIsAddPaasForm(false);

      this.saveConnectorViewDataToSessionStorage(this.currentPaasDialogUrl);

      const contentWindow = this.isInIframe ? window?.parent : window;

      const fromId = contentWindow?.frameElement?.getAttribute('id');

      platform.openTab({
        id: getTabIdByConnectorJumpUrl(this.currentPaasDialogUrl),
        url: this.currentPaasDialogUrl,
        title: TabLoadingText,
        fromId
      } as any);

    },
    /**
     * @description 删除 某项附件组件卡片 事件
    */
    onDeleteCardItemHandler(row: Record<string, any>) {

      if (this.isInIframe) {
        this.emitDeleteHandler(row);
        return;
      }

      const bizIdList = [row.bizId];
      const params = this.buildDeleteConnectorDataParams(bizIdList);

      this.fetchDeleteConnectorData(params).then(() => {
        this.refresh();
      });

    },
    /**
     * @description 取消关联某项附件组件卡片 事件
    */
    onDisassociationCardItemHandler(row: Record<string, any>) {

      if (this.isInIframe) {
        this.emitDisassociationHandler(row);
        return;
      }

      const bizIdList = [row.bizId];
      const params = this.buildDisassociationConnectorDataParams(bizIdList);

      this.fetchDisassociationConnectorData(params).then(() => {
        this.refresh();
      });

    },
    /**
     * @description paas 数据更新成功事件
    */
    onPaasDataChangeSuccessHandler() {
      this.isCreateConnectorData = false;
      this.refresh();
    },
    /**
     * @description 刷新
    */
    refresh() {

      this.page = new Page();

      const dataListParams = this.buildConnectorDataListParams();
      this.fetchConnectorDataList(dataListParams);

      const allowAddConnectorDataParams  = this.buildAllowAddConnectorDataParams();
      this.fetchAllowAddConnectorData(allowAddConnectorDataParams);

    },
    /**
     * @description 保存连接数据到 sessionStorage
    */
    saveConnectPaasDataToSessionStorage() {
      this.saveConnectorDataToSessionStorage(ConnectorPaasDataStorageKey);
    },
    saveConnectorViewDataToSessionStorage(urlKey: string) {

      const key = `connector_to_${this.toBizType}_view_data_${urlKey}`;

      this.saveConnectorDataToSessionStorage(key);

    },
    saveConnectorCreateDataToSessionStorage(urlKey: string) {

      const key = genConnectorCreateDataSessionKeyByBizType(this.toBizType, urlKey);

      this.saveConnectorDataToSessionStorage(key);

    },
    saveConnectorDataToSessionStorage(key: string) {
      try {

        const data = {
          ...this.connectorInsertSelectCallParams,
          fromBizNo: this.fromBizNo,
          processId: this.processId,
          // sourceOperate: ConnectorSourceOperateEum[this.connectorInsertSelectCallParams.fromBizType],
          connectorSourceOperate: this.connectorInsertSelectCallParams.fromBizType
        };

        this.connectorMessageData = data;

        sessionStorage.setItem(key, stringify(data));

      } catch (error) {
        Log.error(error, this.saveConnectorViewDataToSessionStorage.name);
      }
    },
    setIsAddPaasForm(value: boolean = true) {

      this.isAddPaasForm = value;

    },
    /**
     * @description 显示 添加 附件组件卡片 弹窗
    */
    showAddConnectorModulePassIframeDialog(title: string) {

      this.saveConnectorCreateDataToSessionStorage(this.currentPaasDialogUrl);

      const contentWindow = this.isInIframe ? window?.parent : window;
      const fromId = contentWindow?.frameElement?.getAttribute('id');

      let titleText = TabLoadingText;

      if (this.toBizType == ConnectorBizTypeEnum.Paas) {
        titleText = t('common.pageTitle.pagePaasTemplateCreate');
      }

      if (this.isCreateConnectorData) {

        this.$nextTick(() => {
          // @ts-ignore
          this.$refs[ConnectorModulePassIframeDialogRef].open(t('common.pageTitle.pagePaasTemplateCreate'));
        });

        return;
      }

      platform.openTab({
        id: getTabIdByConnectorJumpUrl(this.currentPaasDialogUrl),
        url: this.currentPaasDialogUrl,
        title: titleText,
        fromId
      } as any);

    },
    /**
     * @description 显示 查看 附件组件卡片 弹窗
    */
    showDetailConnectorModulePassIframeDialog() {

      this.setIsAddPaasForm(false);

      this.saveConnectorViewDataToSessionStorage(this.currentPaasDialogUrl);

      const contentWindow = this.isInIframe ? window?.parent : window;

      const fromId = contentWindow?.frameElement?.getAttribute('id');

      platform.openTab({
        id: getTabIdByConnectorJumpUrl(this.currentPaasDialogUrl),
        url: this.currentPaasDialogUrl,
        title: TabLoadingText,
        fromId
      } as any);

    },
    handleThirdFormChangePage(type: string, pageNum: number) {
      if(type === 'next') {
        this.onPaginationPageNumChangedHandler(pageNum + 1);
      } else {
        this.onPaginationPageNumChangedHandler(pageNum - 1);
      }
    }
  },
  render(h: CreateElement) {

    const attrs = this.getAttributes();

    // @ts-ignore
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMulti} {...attrs}>

        <ConnectorModuleConnectorCardMultiButtonGroup
          card={this.card}
          isShowAddButton={this.isShowAddButton}
          isShowRelationButton={this.isShowRelationButton}
          mainModuleValue={this.mainModuleValue}
          // @ts-ignore
          onAdd={this.onAddCardItemHandler}
          onRelation={this.onRelationCardItemHandler}
        />

        <ConnectorModuleConnectorCardMultiCardTable
          card={this.card}
          task={this.task}
          fields={this.fields}
          loading={this.loading}
          // @ts-ignore
          values={this.values}
          toBizType={this.toBizType}
          is-third-app={this.isThirdApp}
          main-module-value={this.mainModuleValue}
          showEditButton={this.isShowEditButton}
          showDeleteButton={this.isShowDeleteButton}
          showRelationButton={this.isShowRelationButton}
          showViewButton={this.isShowViewButton}
          onView={this.onViewCardItemHandler}
          onEdit={this.onEditCardItemHandler}
          onDelete={this.onDeleteCardItemHandler}
          onEditStore={this.onStoreEditCardItemHandler}
          onDisassociation={this.onDisassociationCardItemHandler}
        />

        <div class='connector-card-multi-table__pagination'>

          {
            !this.isThirdApp ?
              <el-pagination
                background
                pageSizes={defaultTableData.defaultPageSizes}
                onCurrent-change={this.onPaginationPageNumChangedHandler}
                onSize-change={this.onPaginationPageSizeChangedHandler}
                pageSize={this.page.pageSize}
                currentPage={this.page.pageNum}
                total={this.total}
                layout={defaultTableData.defaultLayout}
              >
              </el-pagination>
              :

              <ConnectorModuleConnectorCardMultiOutsideAppPagination
                pageInfo={this.dataListPageInfo}
                pageNum={this.page.pageNum}
                //@ts-ignore
                onChangePageNum={this.handleThirdFormChangePage}
                onChangePageSize={this.onPaginationPageSizeChangedHandler}/>


          }
        </div>

        { this.values.length > 0 && this.toAppName ? <span class="connector-module-connector-card-multi__source-label">{t('common.connector.dataSource', { name: this.toAppName })}</span> : null }

        {this.isCreateConnectorData && (
          <ConnectorModulePassIframeDialog
            ref={ConnectorModulePassIframeDialogRef}
            url={this.currentPaasDialogUrl}
            isDetail={false}
            // @ts-ignore
            messageData={this.connectorMessageData}
            onSuccess={this.onPaasDataChangeSuccessHandler}
          />
        )}
        {this.isRelationData && (
          // @ts-ignore
          <CardRelationDialog
            ref="CardRelationDialog"
            fields={this.fields}
            card={this.card}
            paramFn={this.buildConnectorDataListParams}
            moduleName={this.card.name}
            onSuccess={this.refresh}
            onView={this.onViewCardItemHandler}
          />
        )}
      </div>
    );
  }
});
