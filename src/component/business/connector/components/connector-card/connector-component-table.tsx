// @ts-nocheck
/* components */
import BaseListForNoData from '@src/component/common/BaseListForNoData/index.vue';
import { FormBuilderTableColumnsView } from '@src/component/form/form-builder-table/components';
import FormAttachmentViewTableDialog from '@src/component/form/common/components/FormAttachmentViewTableDialog'
/* model */
import {
  ConnectorCardInfo,
  ConnectorCardMultiFixedFieldNameEnum,
  ConnectorCardMultiFixedFields,
  ConnectorField,
  ConnectorFieldTypeEnum,
  ConnectorModuleComponentNameEnum,
  ConnectorOpenBizNo,
  ConnectorOpenBizNoConfig
} from '@src/component/business/connector/model';
import LoginUser from '@model/entity/LoginUser/LoginUser';
import TaskAddress from '@model/entity/TaskAddress';
import Column from '@src/model/types/Column';
/* hooks */
import { useConnectorCard } from '@src/component/business/connector/hooks';
import { useFormBuilderTableEdit } from '@src/component/form/form-builder-table/hooks';
/* scss */
import '@src/component/form/form-builder-table/index.scss';
import '@src/component/business/connector/components/connector-card/multi-card-table.scss';
/* vue */
import { ComponentInstance, computed, ComputedRef, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement, VNode, getCurrentInstance } from 'vue';
/* service */
import { getFieldName, isSystemFiled } from '@service/FieldService';
/* util */
import { isArray, isElement, isNotArray, isObject } from '@src/util/type';
import { fmt_form_field_v2 } from '@src/filter/form-connector';
import { isOpenData, openAccurateTab } from '@src/platform';
import { fmt_datetime } from '@src/filter/fmt';
import {
  getFromId,
  openTabForCustomerView,
  openTabForProductView,
  openTabForTaskView
} from '@src/util/business/openTab';
/* types */
import {
  ConnectorServerValueCustomerType,
  ConnectorServerValueProductType,
  ConnectorServerValueRelatedCustomersType
} from '@src/component/business/connector/types';
/* enum */
import ComponentNameEnum from '@src/model/enum/ComponentNameEnum';
import { FieldTypeMappingEnum, TaskFieldNameMappingEnum } from '@model/enum/FieldMappingEnum';
import { t } from '@src/locales';
import {PageRoutesTypeEnum} from "pub-bbx-global/pageType/dist/enum/PageRoutesEnum";
import BizIntelligentTagsViewToConfig from "@src/component/business/BizIntelligentTags/BizIntelligentTagsViewToConfig";
const TableColumnDefaultWidth = '160px';

export type ConnectorModuleConnectorCardMultiCardTableProps = {
  fields: ConnectorField[];
  loading: boolean;
  values: Record<string, any>[];
  showDeleteButton: boolean;
  showIndex: boolean;
  showSelect: boolean;
  isMulti: boolean;
  formTableColumns: Column[];
}

export interface ConnectorModuleConnectorCardMultiCardTableSetupState {

}

export enum ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum {
  Delete = 'delete',
}

export type ConnectorModuleConnectorCardMultiCardTableInstance = ComponentInstance & ConnectorModuleConnectorCardMultiCardTableSetupState
export type ConnectorModuleConnectorCardMultiCardTableVM = ComponentRenderProxy<ConnectorModuleConnectorCardMultiCardTableProps> & CommonComponentInstance & ConnectorModuleConnectorCardMultiCardTableInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardComponentTable,
  emits: [
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Delete,
  ],
  props: {
    fields: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    mainModuleValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    values: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => ([])
    },
    showDeleteButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showIndex: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showSelect: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    isMulti: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    formTableColumns: {
      type: Array as PropType<Column[]>,
      default: () => [],
    },
    toBizType: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeId: {
      type: String as PropType<string>,
      default: ''
    },
    tableContainerHeight :{
      type: String as PropType<string>,
      default: '460px'
    }
  },
  computed: {
    tableValues(): Record<string, any>[] {
      if(!this.values) return [];
      if(isObject(this.values)) return [this.values];
      return this.values;
    },
    // 跳转编号的配置
    bizNoOpenConfig(): ConnectorOpenBizNoConfig {
      return ConnectorOpenBizNo?.[this.toBizType] ?? {};
    }
  },
  setup(props: ConnectorModuleConnectorCardMultiCardTableProps, { emit }) {
    const onConnectorCardItemDeleteHandler = (row: Record<string, any>) => {
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Delete, row);
    };

    const currentInstance = getCurrentInstance();
    const instance = currentInstance?.proxy;
    const columns = computed(() => props?.formTableColumns || []);

    const { onTableCellClickHandler, lastClickTableCellData } = useFormBuilderTableEdit(columns, instance);

    return {
      onConnectorCardItemDeleteHandler,
      onTableCellClickHandler,
      lastClickTableCellData,
    };
  },
  mounted() {
    // @ts-ignore
    this.$eventBus.$on('toggleRowSelection', this.toggleRowSelection);
  },
  methods: {
    randomStr() {
      return Math.random().toString(36).substr(2, 9);
    },
    /**
     * @description 渲染表格插入 用于无限加载显示
    */
    renderTableAppendSlot() {
      return (
        <div class={ this.loading ? 'block-hide' : 'block-show'}>
          <BaseListForNoData  notice-msg={t('common.base.tip.noData')}></BaseListForNoData>
        </div>
      );
    },
    // 跳转编号详情
    openTabForBizNoView(type = '', id = '', no = '', params = '') {
      let fromId = getFromId();

      openAccurateTab({
        type,
        key: id,
        titleKey: no,
        params,
        fromId
      });
    },
    /**
     * @description 渲染表格列
    */
    renderTableColumnField(h: CreateElement, scope: any, column: ConnectorField) {
      // 渲染业务列
      const renderColumnWithBusinessValue = this.renderColumnWithBusiness(column, scope.row);

      // 已经是元素 直接返回
      if (isElement(renderColumnWithBusinessValue)) {
        return renderColumnWithBusinessValue;
      }

      return (
        <div class='biz-table-cell no-base-tip'>
          { renderColumnWithBusinessValue }
        </div>
      );

    },
    // 编号跳转
    renderColumnWithOpenSerialNumber(column: ConnectorField, row: Record<string, any>) {
      const bizNo = row?.[column.fieldName || ''] ?? '';

      const bizId = row?.bizId ?? '';

      const classNames = bizId ? 'view-detail-btn' : 'no-view-show';

      let params = '';
      let no = bizNo;
      let openTabType = this.bizNoOpenConfig.openTabType;

      // 拼接参数&title
      if(this.toBizType === 'PAAS'){
        params = `formId=${this.toBizTypeId}&formContentId=${bizId}`;
        // 草稿跳转到编辑页面
        if(row.isStore && !row?.processorInstanceId ) {
          openTabType = PageRoutesTypeEnum.PagePaasTemplateEdit;
          bizId && (params = `${params}&onlyEdit=false`);
          no = '';
        }else {
          row.processorInstanceId && (params = `${params}&processId=${row.processorInstanceId}`);
        }
      } else if(['ACTIVITY_RESEARCH', 'PROJECT_MANAGER'].includes(this.toBizType)) {
        params = `id=${bizId}`;
        no = `-${no}`;
      }

      if (!bizId) {
        return (
          <div
            class={classNames}
          >
            { bizNo }
          </div>
        );
      }

      return (
        <BizIntelligentTagsViewToConfig
          type="table"
          value={bizNo}
          tags-list={row.labelList || []}
          config={{calcFontSize: '12px' }}
          canClick={Boolean(bizId)}
          onViewClick={() => this.openTabForBizNoView(openTabType ,bizId, no, params) }>
        </BizIntelligentTagsViewToConfig>
      );
    },
    renderColumnWithAiLabel(column: ConnectorField, row: Record<string, any>) {
      const value = row?.aiLabel || [];
      const labelNameList = value.map((item: any) => item?.name).join('，') || "";
      return (
        <div class='biz-table-cell no-base-tip'>
          { labelNameList }
        </div>
      );
    },
    // 仓位
    renderWarehousePosition(column: ConnectorField, row: Record<string, any>) {
      const value = row?.warehousePositionId || {};
      return (
        <div class='biz-table-cell no-base-tip'>
          { value?.name || '' }
        </div>
      )
    },
    // 仓库
    renderWarehouse(column: ConnectorField, row: Record<string, any>) {
      const value = row?.warehouseId || [];
      return (
        <div class='biz-table-cell no-base-tip'>
          { value?.name || '' }
        </div>
      )
    },
    handleAttachmentView(field: any, value: string) {
      // @ts-ignore
      this.$refs?.attachmentView.open(field, value);
    },
    // 附件显示逻辑
    renderFileHandler(column: ConnectorField, row: Record<string, any>) {
      const fileList = row[column.fieldName || ''] || []

      if (fileList.length == 0) {
        return (
          <span>暂无附件</span>
        )
      }
      return (
        <div class='biz-table-cell no-base-tip' style="gap: 2px;">
          <el-button type="text" size="small" onClick={() => this.handleAttachmentView(column, fileList)}>{ t('common.base.view') }</el-button>
        </div>
      )
    },
    /**
     * @description: 渲染业务字段
     * @param {ConnectorField} column 列
     * @param {any} row 行数据
     * @return {VNode} 元素
     */
    renderColumnWithBusiness(column: ConnectorField, row: any): VNode | JSX.Element | string | null {

      // @ts-ignore
      const formType = column.formType || column.fieldType;

      // 跳转编号
      if(this.bizNoOpenConfig?.fieldName === column.fieldName) {
        return this.renderColumnWithOpenSerialNumber(column, row);
      }
      // 用户名
      if (column.fieldName == ConnectorCardMultiFixedFieldNameEnum.UserName) {

        const value = row[column.fieldName];
        const staffId = row.staffId;

        return this.renderUserName(value, staffId);
      }

      // 仓库
      if (column.fieldName === ConnectorFieldTypeEnum.warehousePositionId) {
        return this.renderWarehousePosition(column, row);
      }

      // 仓库
      if (column.fieldName === ConnectorFieldTypeEnum.warehouseId) {
        return this.renderWarehouse(column, row);
      }

      // 标签
      if (formType == ConnectorFieldTypeEnum.aiLabel) {
        return this.renderColumnWithAiLabel(column, row);
      }
      // 编号
      if (formType == ConnectorFieldTypeEnum.SerialNumber || formType == TaskFieldNameMappingEnum.TaskNo) {
        return this.renderColumnWithSerialNumber(column, row);
      }

      // 客户
      if (formType == ConnectorFieldTypeEnum.Customer) {
        return this.renderColumnWithCustomer(column, row);
      }

      // 关联客户
      if (formType == ConnectorFieldTypeEnum.RelatedCustomers) {
        return this.renderColumnWithRelatedCustomers(column, row);
      }

      // 关联工单
      if (formType == ConnectorFieldTypeEnum.RelatedTask || formType == ConnectorFieldTypeEnum.RelationTask) {
        return this.renderColumnWithRelatedTasks(column, row);
      }

      // 产品
      if (formType == ConnectorFieldTypeEnum.Product) {
        return this.renderColumnWithProduct(column, row);
      }

      // 客户地址
      if (formType == ConnectorFieldTypeEnum.CustomerAddress) {
        return this.renderColumnWithCustomerAddress(column, row);
      }

      // 客户联系人
      if (formType == ConnectorFieldTypeEnum.Linkman) {
        return this.renderColumnWithCustomerLinkman(column, row);
      }

      // 服务部门
      if (column.fieldName == ConnectorFieldTypeEnum.Tags) {
        return this.renderTag(column, row);
      }
      // 关联的服务部门显示逻辑
      if (column && column.setting && column.setting.fieldName && column.setting.fieldName == ConnectorFieldTypeEnum.Tags) {
        return this.renderTag(column, row);
      }

      // 地址
      if (formType == ConnectorFieldTypeEnum.Address) {
        return this.renderColumnWithAddress(column, row);
      }

      // 日期
      if (formType == ConnectorFieldTypeEnum.Date) {
        return this.renderColumnWithDateTime(column, row);
      }

      // 部门
      if (formType == ConnectorFieldTypeEnum.Tag) {
        return this.renderPaaSTag(column, row);
      }

      // 操作
      if (column.fieldName == ConnectorCardMultiFixedFieldNameEnum.Operation) {
        return this.renderOperation(row);
      }

      if (formType == ConnectorFieldTypeEnum.Attachment) {
        return this.renderFileHandler(column, row);
      }
      
      return this.renderColumnWithCommon(column, row);
    },
    /**
     * @description: 渲染通用字段
     * @param {ConnectorField} column 列
     * @param {any} row 行数据
     * @return {VNode} 元素
     */
    renderColumnWithCommon(column: ConnectorField, row: any): VNode {

      // 是否是系统字段
      const isSystem = isSystemFiled(column);
      // 字段类型
      const formType = column.formType || '';
      // 字段名称
      const fieldName = getFieldName(column);
      // 值
      const value = row[fieldName];

      return (
        <div class="no-base-tip">
          { fmt_form_field_v2(value, formType) }
        </div>
      );

    },
    /**
     * @description 渲染 用户名
    */
    renderUserName(value: string, staffId: string) {

      const isShowOpenData = isOpenData && staffId;

      return (
        <div class="no-base-tip">
          {isShowOpenData
            ? (
              <open-data type="userName" openid={staffId}></open-data>
            )
            : (
              value
            )}
        </div>
      );
    },
    /* 人员类型 */
    renderColumnWithUser(column: ConnectorField, row: Record<string, any>): any {

      const user: LoginUser | any = row?.[column.fieldName || ''] || {};

      // 单选人员
      if(isOpenData && user.staffId) {
        return (<span><open-data type="userName" openid={user.staffId}></open-data></span>);
      }

      // 多选人员
      if (isOpenData && isArray(user)) {
        return (
          user.map((item: LoginUser) => {
            return <open-data type="userName" openid={item.staffId}></open-data>;
          })
        );
      }

      return user?.displayName || user?.name || '';
    },
    /* 位置类型 */
    renderColumnWithLocation(column: ConnectorField, row: Record<string, any>): string {
      const location: Record<string, any> = row?.[column.fieldName || ''] || {};
      return location?.address || '';
    },
    /* 日期类型 */
    renderColumnWithDateTime(column: ConnectorField, row: Record<string, any>): string {

      const fieldName: string = column.fieldName || '';

      return fmt_datetime(row?.[fieldName]);
    },
    /* 编号 */
    renderColumnWithSerialNumber(column: ConnectorField, row: Record<string, any>) {

      const serialNumber: number = row?.[column.fieldName || ''] || '';

      return (
        <div class="no-base-tip">
          { serialNumber }
        </div>
      );

    },
    /* 客户类型 */
    renderColumnWithCustomer(column: ConnectorField, row: Record<string, any>) {

      const customers = row?.[column.fieldName || ''] || [];
      const customer = (
        isArray(customers) ? customers[0] || {} : customers
      ) || {};

      const classNames = 'view-detail-btn no-base-tip';

      const customerId = customer?.id || '';
      const customerName = customer?.name || '';

      return (
        <BizIntelligentTagsViewToConfig
          type="table"
          value={customerName}
          tags-list={customers.labelList || []}
          config={{calcFontSize: '12px' }}
          onViewClick={()=> openTabForCustomerView(customerId)}>
        </BizIntelligentTagsViewToConfig>
      );
    },
    /* 产品类型 */
    renderColumnWithProduct(column: ConnectorField, row: Record<string, any>) {

      let products: ConnectorServerValueProductType[] = row?.[column.fieldName || ''] || [];

      if (isNotArray(products)) {
        products = [products as unknown as ConnectorServerValueProductType];
      }

      const classNames = 'view-detail-btn no-base-tip';

      const content = products.map((item: ConnectorServerValueProductType) => {
        return (
          <div class="connector-card-multi-table-product-row-item no-base-tip">
            <BizIntelligentTagsViewToConfig
              type="table"
              value={item.name}
              tags-list={item.labelList || []}
              config={{calcFontSize: '12px', tableShowType:'icon', tableMaxLength: 1}}
              showMoreIcon={false}
              onViewClick={() => openTabForProductView(item.id)}>
            </BizIntelligentTagsViewToConfig>
            <span class="connector-card-multi-table-product-row-text">
              ,
            </span>
          </div>
        );
      });

      return (
        <div class="connector-card-multi-table-product-row no-base-tip">
          { content }
        </div>
      );
    },
    /* 关联客户类型 */
    renderColumnWithRelatedCustomers(column: ConnectorField, row: Record<string, any>) {

      let customers: ConnectorServerValueRelatedCustomersType = row?.[column.fieldName || ''] || [];

      if (isNotArray(customers)) {
        customers = [customers as unknown as ConnectorServerValueCustomerType];
      }

      const classNames = 'view-detail-btn no-base-tip';

      const content = customers.map((item: ConnectorServerValueCustomerType) => {
        return (
          <div class="connector-card-multi-table-customer-row-item no-base-tip">
            <div
              class={classNames}
              onClick={() => openTabForCustomerView(item.id) }
            >
              { item.name }
            </div>
            <span class="connector-card-multi-table-customer-row-text">
              ,
            </span>
          </div>
        );
      });

      return (
        <div class="connector-card-multi-table-customer-row no-base-tip">
          { content }
        </div>
      );
    },
    /* 关联工单类型 */
    renderColumnWithRelatedTasks(column: ConnectorField, row: Record<string, any>) {

      let tasks: Record<string, any>[] = row?.[column.fieldName || ''] || [];

      if (isNotArray(tasks)) {
        tasks = [tasks as unknown as Record<string, any>];
      }

      const classNames = 'view-detail-btn no-base-tip';

      const content = tasks.map((item: Record<string, any>) => {
        return (
          <div class="connector-card-multi-table-task-row-item no-base-tip">
            <BizIntelligentTagsViewToConfig
              type="table"
              value={item.taskNo}
              tags-list={item.labelList || []}
              config={{calcFontSize: '12px', tableShowType: 'icon', tableMaxLength: 1}}
              showMoreIcon={false}
              onViewClick={() => openTabForTaskView(item.taskId)}>
            </BizIntelligentTagsViewToConfig>
            <span class="connector-card-multi-table-product-row-text">
              ,
            </span>
          </div>
        );
      });

      return (
        <div class="connector-card-multi-table-task-row no-base-tip">
          { content }
        </div>
      );
    },
    /* 客户地址 */
    renderColumnWithCustomerAddress(column: ConnectorField, row: Record<string, any>) {

      const addressList = row?.[column.fieldName || ''] || [];
      const address = (
        isArray(addressList) ? addressList[0] || {} : addressList
      ) || {};

      return new TaskAddress(address).toString() || address?.name || '';
    },
    /* 客户联系人 */
    renderColumnWithCustomerLinkman(column: ConnectorField, row: Record<string, any>) {

      const linkmanList = row?.[column.fieldName || ''] || [];
      const linkman = (
        isArray(linkmanList) ? linkmanList[0] : linkmanList
      ) || {};

      const linkmanName = linkman?.name || '';

      return (
        <div class="no-base-tip">
          { linkmanName }
        </div>
      );
    },
    /* 服务部门 */
    renderTag(column: ConnectorField, row: Record<string, any>) {

      const tags = row?.[column.fieldName || ''] || [];
      const tag = (
        isArray(tags) ? tags[0] : tags
      ) || {};

      const tagName = tag?.tagName || tag?.name;

      return (
        <div class="no-base-tip">
          { tagName }
        </div>
      );
    },
    /* 地址类型 */
    renderColumnWithAddress(column: ConnectorField, row: Record<string, any>): string {
      const address: any = row?.[column.fieldName || ''] || {};
      return new TaskAddress(address).toString();
    },
    // paas表单设计器部门控件 renderPaaSTag
    renderPaaSTag(column: ConnectorField, row: Record<string, any>) {

      const tags = row?.[column.fieldName || ''] || [];
      const tagName = isArray(tags)
        // @ts-ignore
        ? tags.map(tag => tag.name).join('，')
        : tags[0]?.name;

      return (
        <div class="no-base-tip">
          { tagName }
        </div>
      );
    },
    /**
     * @description 渲染 操作
     */
    renderOperation(row: Record<string, any>) {
      console.log('row', this.showDeleteButton );
      return (
        <div class="no-base-tip">
          { this.showDeleteButton && this.renderOperationDeleteButton(row) }
        </div>
      );
    },
    /**
     * @description 渲染 操作 删除按钮
     */
    renderOperationDeleteButton(row: Record<string, any>) {
      return (
        <el-button
          type="text"
          onClick={() => this.onConnectorCardItemDeleteHandler(row)}
        >
          {t('common.base.delete')}
        </el-button>
      );
    },
    handleSelectionChange(val: any) {
      // console.log("val", val, this.isMulti)
      // 判断是否支持多选
      if(this.isMulti) {
        // this.multipleSelection = val
        // @ts-ignore
        this.$emit("selectionChange", val);
        return;
      }
      const current = val[val.length -1];
      if(val.length > 1) {
        // @ts-ignore
        this.$refs.connectorMultipleTable.clearSelection();
        // @ts-ignore
        this.$refs.connectorMultipleTable.toggleRowSelection(current);
      }
      // this.multipleSelection = [current];
      // @ts-ignore
      this.$emit("selectionChange", [current]);
    },
    // @ts-ignore
    toggleRowSelection({ selections }) {
      // console.log("row", selections)
      // @ts-ignore
      this.$refs?.connectorMultipleTable?.clearSelection();
      // @ts-ignore
      selections && selections.forEach(row => {
        // @ts-ignore
        this.$refs?.connectorMultipleTable?.toggleRowSelection(row);
      });
    }
  },
  render(h: CreateElement) {

    const scopedSlots = this.loading
      ? {}
      : {
        empty: () => this.renderTableAppendSlot()
      };

    const classNames = {
      [ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiCardTable]: true,
      [ComponentNameEnum.FormBuilderTable]: this.formTableColumns.length,
    }

    return (
      <div class={classNames}>
        <el-table
          border
          class={ this.isMulti ? 'bbx-normal-list-box' : 'bbx-normal-list-box connector-table-select-single'}
          data={this.tableValues || []}
          header-row-class-name="common-list-table-header__v2"
          rowClassName='base-table-row-v3'
          row-key = {(row: { bizId: any }) => row.bizId}
          stripe
          scopedSlots={scopedSlots}
          onSelect={this.handleSelectionChange}
          on-select-all={this.handleSelectionChange}
          onCell-click={this.onTableCellClickHandler}
          ref='connectorMultipleTable'
          max-height={this.tableContainerHeight}
        >
          {
            this.showSelect ? (this.showIndex ? (<el-table-column label={t('common.fields.orderNo.displayName')} type="index" width="50" />) : (<el-table-column type="selection" width="55"/>)) : ''
          }
          {
            (this.fields as ConnectorField[]).map((column: ConnectorField) => {
              return (
                <el-table-column
                  // @ts-ignore
                  fixed={column.fixed}
                  label={column.label}
                  key={column.field || column.fieldName}
                  width={column.width}
                  minWidth={column.minWidth ? `${column.minWidth}px` : TableColumnDefaultWidth}
                  prop={column.field}
                  resizable
                  show-overflow-tooltip
                >
                  { (scope: any) => this.renderTableColumnField(h, scope, column) }
                </el-table-column>
              );
            })
          }

          {/* start 其他自定义列 */}
          {this.formTableColumns.length && (
            <FormBuilderTableColumnsView
              columns={this.formTableColumns}
              data={this.values}
            ></FormBuilderTableColumnsView>
          )}
          {/* end 其他自定义列 */}

          <div slot='append'></div>
          <div slot="empty">
            <div class={ this.loading ? 'block-hide' : 'block-show'}>
              <BaseListForNoData  notice-msg={t('common.base.tip.noData')}></BaseListForNoData>
            </div>
          </div>

        </el-table>
        <FormAttachmentViewTableDialog  ref="attachmentView" />
      </div>
    );
  }
});
