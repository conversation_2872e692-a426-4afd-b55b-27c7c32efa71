/* components */
import { ConnectorModuleConnectorCardMulti } from '@src/component/business/connector/components';
/* model */
import { ConnectorCardInfo, ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/connector-card/index.scss';
/* vue */
import { ComponentInstance, computed, defineComponent, nextTick, PropType, ref, watch } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
/* service */
/* util */
import { isFalse } from '@src/util/type';

export type ConnectorModuleConnectorCardProps = {
  mainModuleValue: Record<string, any>;
  task: Record<string, any>;
  card: ConnectorCardInfo;
  bizId: string;
  visible: boolean;
  fromBizNo: string;
  nodeTemplateId: string;
  nodeInstanceId: string;
  processId: string;
  showCreateButton: boolean;
  showRelationButton: boolean;
  showEditButton: boolean;
  showDeleteButton: boolean;
  showExportButton: boolean;
  isInIframe: boolean;
}

export interface ConnectorModuleConnectorCardSetupState {
  
}

export enum ConnectorModuleConnectorCardEmitEventNameEnum {
  Input = 'input',
  Add = 'add',
  Edit = 'edit',
  Delete = 'delete',
  Disassociation = 'disassociation',
  Detail = 'detail',
}

export type ConnectorModuleConnectorCardInstance = ComponentInstance & ConnectorModuleConnectorCardSetupState
export type ConnectorModuleConnectorCardVM = ComponentRenderProxy<ConnectorModuleConnectorCardProps> & CommonComponentInstance & ConnectorModuleConnectorCardInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCard,
  emits: [
    ConnectorModuleConnectorCardEmitEventNameEnum.Input,
    ConnectorModuleConnectorCardEmitEventNameEnum.Add,
    ConnectorModuleConnectorCardEmitEventNameEnum.Edit,
    ConnectorModuleConnectorCardEmitEventNameEnum.Delete,
    ConnectorModuleConnectorCardEmitEventNameEnum.Disassociation,
    ConnectorModuleConnectorCardEmitEventNameEnum.Detail
  ],
  props: {
    bizId: {
      type: String as PropType<string>,
      default: ''
    },
    mainModuleValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    card: {
      type: Object as PropType<ConnectorCardInfo>,
      default: () => ({})
    },
    fromBizNo: {
      type: String as PropType<string>,
      default: ''
    },
    nodeTemplateId: {
      type: String as PropType<string>,
      default: ''
    },
    nodeInstanceId: {
      type: String as PropType<string>,
      default: ''
    },
    processId: {
      type: String as PropType<string>,
      default: ''
    },
    task: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    isInIframe: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showCreateButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showRelationButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showEditButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showDeleteButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showExportButton: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  },
  setup(props: ConnectorModuleConnectorCardProps, { emit }) {
    
    const isLoaded = ref(false);
    const isShowConnectorCard = computed(() => {
      
      if (isLoaded.value) {
        return true;
      }
      
      return isLoaded.value && props.visible;
      
    });
    
    watch(() => props.visible, (newValue) => {
      
      if (isFalse(isLoaded.value) && newValue) {
        isLoaded.value = true;
      }
      
    }, { immediate: true });
    
    watch(() => props.card, (newValue) => {
      
      isLoaded.value = false;
      
      nextTick(() => {
        isLoaded.value = true;
      });
      
    });
    
    return {
      isShowConnectorCard,
    };
    
  },
  methods: {
    emitAddHandler(data: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardEmitEventNameEnum.Add, data);
    },
    emitEditHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardEmitEventNameEnum.Edit, row);
    },
    emitDeleteHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardEmitEventNameEnum.Delete, row);
    },
    emitDisassociationHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardEmitEventNameEnum.Disassociation, row);
    },
    emitDetailHandler(row: Record<string, any>) {
      this.$emit(ConnectorModuleConnectorCardEmitEventNameEnum.Detail, row);
    },
    refresh() {
      (this.$refs.ConnectorModuleConnectorCardMulti as any).refresh();
    }
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCard}>
        
        {this.isShowConnectorCard && (
          <ConnectorModuleConnectorCardMulti 
            ref="ConnectorModuleConnectorCardMulti"
            card={this.card}
            task={this.task}
            // @ts-ignore
            value={this.value}
            bizId={this.bizId}
            isInIframe={this.isInIframe}
            fromBizNo={this.fromBizNo}
            nodeTemplateId={this.nodeTemplateId}
            nodeInstanceId={this.nodeInstanceId}
            processId={this.processId}
            mainModuleValue={this.mainModuleValue}
            showCreateButton={this.showCreateButton}
            showRelationButton={this.showRelationButton}
            showEditButton={this.showEditButton}
            showDeleteButton={this.showDeleteButton}
            showExportButton={this.showExportButton}
            // @ts-ignore
            onAdd={this.emitAddHandler}
            onEdit={this.emitEditHandler}
            onDelete={this.emitDeleteHandler}
            onDisassociation={this.emitDisassociationHandler}
            onDetail={this.emitDetailHandler}
          />
        )}
        
      </div>
    );
  }
});
