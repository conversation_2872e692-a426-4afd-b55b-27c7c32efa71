
.connector-module-connector-card-multi-card-table {
  .biz-table-cell {
    .el-button--text {
      padding: 0;
      min-width: auto;
    }
    .biz-intelligent-tags__table-view-link{
      height: auto;
    }
    .biz-intelligent-tags__list-column {
      height: auto;
    }
  }
	.el-table__empty-block{
		width: 100% !important;
    position: sticky;
    left: 0;
    top: 0;
	}
}

.connector-module-connector-card-multi-card-table {

  .bbx-list-nodata-view {
    padding-bottom: 20px;
  }

}

// .connector-module-connector-card-multi-card-table {
//   .el-button {
//     font-size: 14px;
//   }
// }

.connector-card-multi-table-customer-row,
.connector-card-multi-table-task-row,
.connector-card-multi-table-product-row {

  display: flex;

  .connector-card-multi-table-customer-row-text,
  .connector-card-multi-table-task-row-text,
  .connector-card-multi-table-product-row-text {
    margin-right: 5px;
  }

  .connector-card-multi-table-customer-row-item:nth-last-of-type(1),
  .connector-card-multi-table-task-row-item:nth-last-of-type(1),
  .connector-card-multi-table-product-row-item:nth-last-of-type(1) {

    .connector-card-multi-table-customer-row-text,
    .connector-card-multi-table-task-row-text,
    .connector-card-multi-table-product-row-text {
      display: none;
    }

  }

}

.connector-card-multi-table-customer-row-item,
.connector-card-multi-table-task-row-item,
.connector-card-multi-table-product-row-item {
  display: flex;
}

.connector-module-connector-card-multi-card-table {
  .el-table .el-table__cell {
    font-size: $font-size-small !important;
  }
}
