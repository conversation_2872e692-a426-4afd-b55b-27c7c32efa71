.form-item-language {
    .form-item-control {
        display: flex;
        flex-wrap: wrap;

        >div:nth-child(1) {
            flex: 1;
        }

        .language {
            width: 32px;
        }

        .err-msg-wrap {
            width: 100%;
        }
    }
}



// 级联选择器样式
.connector-module-create-connector-name__cascader {
  .el-cascader-menu {
    min-width: 200px;
  }

  .el-cascader-menu__item {
    position: relative;
    z-index: 2;  // 提高选项的层级
  }

  .el-cascader-menu_hover-zone {
    z-index: 1;  // 降低 hover 区域的层级
  }

  svg {
    z-index: 0;  // 将 SVG 放到最底层
    pointer-events: none;  // 禁用 SVG 的鼠标事件
    height: 0px;
  }
}