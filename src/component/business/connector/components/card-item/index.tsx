/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/card-item/index.scss';
/* vue */
import { ComponentInstance, defineComponent } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';

export type ConnectorModuleCardItemProps = {
  
}

export interface ConnectorModuleCardItemSetupState {
  
}

export enum ConnectorModuleCardItemEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleCardItemInstance = ComponentInstance & ConnectorModuleCardItemSetupState
export type ConnectorModuleCardItemVM = ComponentRenderProxy<ConnectorModuleCardItemProps> & CommonComponentInstance & ConnectorModuleCardItemInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleCardItem,
  emits: [
    ConnectorModuleCardItemEmitEventNameEnum.Input,
  ],
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleCardItem}>
        
        <div class="connector-module-card-item-content">
          { this.$slots.default }
        </div>
        
        <div class="connector-module-item-footer">
          
          <div class="connector-module-item-footer-left">
            { this.$slots.footerLeft }
          </div>
          
          <div class="connector-module-item-footer-right">
            { this.$slots.footerRight }
          </div>
          
        </div>
        
      </div>
    );
  }
});
