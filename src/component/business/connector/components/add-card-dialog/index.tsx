/* components */
import { ConnectorModuleAddCardDialogItem } from '@src/component/business/connector/components';
/* hooks */
import { useDialog } from '@hooks/useDialog';
/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* image */
// @ts-ignore
import CardImage from '@src/assets/img/form/plain-form.png';
// @ts-ignore
import ConnectorImage from '@src/assets/img/form/connector.png';
/* scss */
import '@src/component/business/connector/components/add-card-dialog/index.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType, watch } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
import { t } from '@src/locales';

export type ConnectorModuleAddCardDialogProps = {
  visible: boolean;
}

export interface ConnectorModuleAddCardDialogSetupState {
  
}

export enum ConnectorModuleAddCardDialogEmitEventNameEnum {
  Input = 'input',
  CardCreate = 'cardCreate',
  ConnectorCreate = 'connectorCreate',
  ConnectorClose = 'close',
}

export type ConnectorModuleAddCardDialogInstance = ComponentInstance & ConnectorModuleAddCardDialogSetupState
export type ConnectorModuleAddCardDialogVM = ComponentRenderProxy<ConnectorModuleAddCardDialogProps> & CommonComponentInstance & ConnectorModuleAddCardDialogInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleAddCardDialog,
  emits: [
    ConnectorModuleAddCardDialogEmitEventNameEnum.Input,
    ConnectorModuleAddCardDialogEmitEventNameEnum.CardCreate,
    ConnectorModuleAddCardDialogEmitEventNameEnum.ConnectorCreate,
    ConnectorModuleAddCardDialogEmitEventNameEnum.ConnectorClose,
  ],
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    }
  },
  setup(props: ConnectorModuleAddCardDialogProps, { emit }) {
    
    const { visible: insideVisible, showDialog, hideDialog } = useDialog();
    
    const onCardCreateHandler = () => {
      emit(ConnectorModuleAddCardDialogEmitEventNameEnum.CardCreate);
    };
    
    const onConnectorCreateHandler = () => {
      emit(ConnectorModuleAddCardDialogEmitEventNameEnum.ConnectorCreate);
      // 第一步的新建弹框隐藏
      onConnectorCloseHandler();
    };
    
    const onConnectorCloseHandler = () => {
      emit(ConnectorModuleAddCardDialogEmitEventNameEnum.ConnectorClose);
    };
    
    watch(() => props.visible, (newValue) => {
      if (newValue) {
        showDialog();
      } else {
        hideDialog();
      }
    });
    
    return {
      insideVisible,
      
      showDialog,
      hideDialog,
      
      onCardCreateHandler,
      onConnectorCreateHandler,
      onConnectorCloseHandler
    };
    
  },
  methods: {
    /**
    * @description 获取属性列表
    * @return {Record<string, any>} 属性列表
    */  
    getAttributes(): Record<string, any> {
      return {
        class: ConnectorModuleComponentNameEnum.ConnectorModuleAddCardDialog,
        props: {
          title: t('common.base.addModule', {module: t('common.connector.title.additionalCard')}),
          show: this.insideVisible,
          appendToBody: true
        },
        on: {
          'update:show': () => {
            this.hideDialog();
          },
          'closed': () => {
            this.onConnectorCloseHandler();
          }
        }
      };
    }
  },
  render(h: CreateElement) {
    
    const attrs = this.getAttributes();
    
    return (
      <base-modal {...attrs}>
        
        <ConnectorModuleAddCardDialogItem 
          class="connector-module-add-card-item-card"
          // @ts-ignore
          title={t('common.connector.title.additionalCard')}
          subTitle={t('common.connector.subTitle.title2')}
          image={CardImage}
          onCreate={this.onCardCreateHandler}
        />
        
        <ConnectorModuleAddCardDialogItem 
          class="connector-module-add-card-item-connector"
          // @ts-ignore
          title={t('common.connector.title.connector')}
          subTitle={t('common.connector.subTitle.title3')}
          image={ConnectorImage}
          onCreate={this.onConnectorCreateHandler}
          onClose={this.onConnectorCloseHandler}
        />
        
      </base-modal>
    );
  }
});
