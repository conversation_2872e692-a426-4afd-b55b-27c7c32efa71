/* model */
import { ConnectorModuleComponentNameEnum } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/add-card-dialog/item.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
import { t } from '@src/locales';

export type ConnectorModuleAddCardItemProps = {
  image: string;
  title: string;
  desc: string;
}

export interface ConnectorModuleAddCardItemSetupState {
  
}

export enum ConnectorModuleAddCardItemEmitEventNameEnum {
  Input = 'input',
  Create = 'create',
}

export type ConnectorModuleAddCardItemInstance = ComponentInstance & ConnectorModuleAddCardItemSetupState
export type ConnectorModuleAddCardItemVM = ComponentRenderProxy<ConnectorModuleAddCardItemProps> & CommonComponentInstance & ConnectorModuleAddCardItemInstance

export default defineComponent({
  // @ts-ignore
  name: ConnectorModuleComponentNameEnum.ConnectorModuleAddCardItem,
  emits: [
    ConnectorModuleAddCardItemEmitEventNameEnum.Input,
    ConnectorModuleAddCardItemEmitEventNameEnum.Create,
  ],
  props: {
    image: {
      type: String as PropType<string>,
      default: '',
    },
    title: {
      type: String as PropType<string>,
      default: '',
    },
    subTitle: {
      type: String as PropType<string>,
      default: '',
    }
  },
  setup(props: ConnectorModuleAddCardItemProps, { emit }) {
    
    const onCreateHandler = () => {
      emit(ConnectorModuleAddCardItemEmitEventNameEnum.Create);
    };
    
    return {
      onCreateHandler,
    };
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleAddCardItem}>
        
        {/* @ts-ignore */}
        <img class="connector-module-add-card-item-image" src={this.image} />
        
        <div class="connector-module-add-card-item-operate">
            
          <div class="connector-module-add-card-item-title">
            <h3 class="connector-module-add-card-item-title-text">
              {/* @ts-ignore */}
              { this.title }
            </h3>
            <p class="connector-module-add-card-item-sub-title">
              {/* @ts-ignore */}
              { this.subTitle }
            </p>
          </div>
            
          <el-button
            class="connector-module-add-card-item-button"
            type="primary"
            // @ts-ignore
            onClick={this.onCreateHandler}
          >
            {t('common.base.create')}
          </el-button>
        </div>
        
      </div>
    );
  }
});
