
.connector-module-add-card-item {
  box-shadow: 0 4px 12px 0 rgb(0 0 0 / 10%);
  border-radius: 4px;
  
  position: relative;
  
  cursor: pointer;
  
  width: 210px;
  height: 260px;
  
  &:hover {
    border: 2px solid $color-primary;
    
    .connector-module-add-card-item-title {
      top: 16px;
    }
    
    .connector-module-add-card-item-button {
      opacity: 1;
    }
    
  }
  
}

.connector-module-add-card-item-image {
  width: 100%;
  height: 140px;
  padding: 8px 8px 0 8px;
  transform: translateY(-2px);
  
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.connector-module-add-card-item-operate {
  position: absolute;
  
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  width: 100%;
  height: 116px;
  padding: 16px;
  
  text-align: left;
  
  background-color: #fff;
}

.connector-module-add-card-item-title {
  position: absolute;
  top: 38px;
  
  transition: top .5s ease;
}

.connector-module-add-card-item-title-text {
  color: #262626;
  
  height: 22px;
  margin-bottom: 4px;
  
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
}

.connector-module-add-card-item-sub-title {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 18px;
}

.connector-module-add-card-item-button {
  opacity: 0;
  
  transition: top .5s ease;
  
  position: relative;
  top: 28px;
}