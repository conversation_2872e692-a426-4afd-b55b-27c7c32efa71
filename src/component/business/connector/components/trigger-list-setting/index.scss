// 卡片列表
@mixin dynamic-card-list($minCount: 1, $itemClass: 'dynamic-card-list__item') {
  --min-count: #{$minCount};
  width: 100%;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: space-between;

  &::after {
    content: '';
    flex: auto;
  }

  .#{$itemClass} {
    --cur-count: var(--min-count, 1);
    margin-bottom: 12px;
    margin-right: 12px;
    width: calc(
      (100% / (var(--cur-count))) -
        (12px * (var(--cur-count) - 1) / (var(--cur-count)))
    ) !important;
    box-sizing: border-box;

    @media screen and (max-width: 900px) {
      @if $minCount == 1 {
        margin-right: 0;
      }
      @else {
        &:nth-of-type(#{$minCount}n) {
          margin-right: 0;
        }
      }
    }

    @media screen and (min-width: 901px) and (max-width: 1200px) {
      --cur-count: calc(var(--min-count, 1) + 1);
      &:nth-of-type(#{$minCount + 1}n) {
        margin-right: 0;
      }
    }

    @media screen and (min-width: 1201px) and (max-width: 1500px) {
      --cur-count: calc(var(--min-count, 1) + 2);
      &:nth-of-type(#{$minCount + 2}n) {
        margin-right: 0;
      }
    }

    @media screen and (min-width: 1501px) and (max-width: 2400px) {
      --cur-count: calc(var(--min-count, 1) + 3);
      &:nth-of-type(#{$minCount + 3}n) {
        margin-right: 0;
      }
    }

    @media screen and (min-width: 2401px) {
      --cur-count: calc(var(--min-count, 1) + 4);
      &:nth-of-type(#{$minCount + 4}n) {
        margin-right: 0;
      }
    }
  }
}
.trigger-list-setting{
  margin-top: 36px;
  width: 100%;

  .trigger-list-setting-header-right {
    .el-button {
      i {
        margin-right: 6px;
      }
    }
  }
  .trigger-list{
    background: #F7F7F7;
    padding:12px;
    padding-bottom: 0;
    // display: flex;
    // width: 100%;
    .trigger-list-item{
      display: inline-block;
      max-width: 431px;
      // min-width: 431px;
      width: 100%;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px 20px;
      margin:0 12px 12px 0;
      .trigger-list-item {
        background-color: #fff;
        border-radius: 4px;
        padding: 15px 20px;
      }
      .trigger-list-item-header {
        height: 22px;
        margin-bottom: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        i{
          cursor: pointer;
          margin-left: 8px;
          color: #ccc;
        }
      }
      .trigger-list-item-name {
        height: 22px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 22px;
        @include text-ellipsis();
        width: calc(100% - 80px);
      }
      .trigger-list-item-part {
        i {
          display: inline-block;
          transform: rotate(180deg);
          margin: 0 10px;
          font-size: 12px;
        }
        .app-logo{
          font-size: 16px;
          color: $color-primary;
          margin: 0 4px 0 0;
        }
      }
      .trigger-list-item-part,
      .trigger-list-item-type,
      .trigger-list-item-action,
      .trigger-list-item-description,
      .trigger-list-item-update {
        height: 17px;
        margin-bottom: 8px;
        font-size: 12px;
        font-weight: 400;
        color: #595959;
        line-height: 17px;
        @include text-ellipsis();
      }
      .trigger-list-item-update,.trigger-list-item-type{
        color: #8c8c8c;
      }
      .trigger-list-item-update-time {
        margin-left: 4px;
      }
    }
  }
}

.trigger-list-setting-header {
  // height: 46px;
  margin-bottom: 16px;
}

.trigger-list-setting-header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  // padding-left: 10px;
}

.trigger-list-setting-header-sub-title {
  font-size: 12px;
  font-weight: 400;
  color: #8C8C8C;
  line-height: 28px;
  // width: 180px;
  font-family: PingFangSC-Regular, PingFang SC;
}

.other-setting-panel {
  .trigger-list-setting {
    
    .trigger-list {
      
      @include dynamic-card-list(1, 'trigger-list-item');
      
      .trigger-list-item {
        max-width: inherit;
      }
      
    }
    
  }
}

.setting-flow-main-create,
.setting-flow-main-finish,
.setting-flow-main-review {
  
  .trigger-list-setting {
    
    .trigger-list-item {
      max-width: 100% !important;
    }
    
  }
  
}