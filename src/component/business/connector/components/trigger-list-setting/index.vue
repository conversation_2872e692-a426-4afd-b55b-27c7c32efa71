<template>
  <div class="trigger-list-setting" v-if="triggerGray">
    <div class="trigger-list-setting-header">
      <div class="trigger-list-setting-header-title">
        {{ title }}
        <div class="trigger-list-setting-header-right">
          <el-button
            type="primary"
            size="small"
            @click="addTrigger"
          >
            <i class="iconfont icon-add2">
            </i>
            {{ $t('common.base.create') }}
          </el-button>
        </div>
      </div>
      <div class="trigger-list-setting-header-sub-title">
        {{ subTitle }}
      </div>
    </div>
    <div class="trigger-list" v-if="list && list.length">
      <TriggerListItem
        v-for="item in list"
        :key="item.id"
        :value.sync="item"
        :auth-type="authType"
        @edit="toEdit"
        @delete="toDelete"
        @update="toUpdate"
        @updateList="getList"
      >
      </TriggerListItem>
    </div>

    <ConnectorListViewCreateDialog
      :title="formValue.id ? $t('common.connector.trigger.edit') : $t('common.connector.trigger.create')"
      :value="formValue"
      :visible="visible"
      :trigger-id="triggerId"
      :fields="CreateConnectorFields"
      @close="onDialogCloseHandler"
      @confirm="onDialogConfirmHandler"
      @input="onDialogUpdateHandler"
    >
    </ConnectorListViewCreateDialog>
    <CreateTriggerDialog
			ref='CreateTriggerDialog'
			@submit="onDialogSubmitHandler"
		>
		</CreateTriggerDialog>
  </div>
</template>
<script>
/* components */
import ConnectorListViewCreateDialog from './components/ConnectorListViewCreateDialog.vue';
import TriggerListItem from './components/item.vue';
import CreateTriggerDialog from './components/CreateTriggerDialog/index.vue'
/* api */
import * as TriggerApi from '@src/component/business/connector/api/trigger';

/* util */
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';
import { isFalsy } from '@src/util/type';
import { getRootWindow } from '@src/util/dom';
const rootWindow = getRootWindow(window);

/* model */
import { t } from '@src/locales';

const CreateConnectorFields = [
  {
    fieldName: 'name',
    formType: 'text',
    displayName: t('common.connector.fields.triggerName.displayName'),
    isNull: 0,
    maxlength: 20
  },
  {
    fieldName: 'description',
    formType: 'textarea',
    displayName: t('common.connector.fields.triggerDescription.displayName'),
    isNull: 1,
    maxlength: 500
  }
];
export default {
  name:'trigger-list-setting',
  props:{
    title:{
      type:String,
      default: t('common.connector.title.trigger')
    },
    subTitle:{
      type:String,
      default: t('common.connector.trigger.create')
    },
    bizType:{
      type:String,
      default:''
    },
    bizTypeId:{
      type:String,
      default:''
    },
    paasAppId:{
      type:String,
      default:''
    },
    paasNodeId:{
      type:String,
      default:''
    },
    bizActionCode:{
      type:String,
      default:''
    },
    bizActionName:{
      type:String,
      default:''
    }
  },
  computed:{
    listQuery(){
      return {
        bizType:this.bizType,
        bizTypeId:this.bizTypeId,
        bizActionCode:this.actionCode,
        paasNodeId: this.paasNodeId,
        pageNum:1,
        pageSize:9999
      };
    },
    triggerGray(){
      return rootWindow.grayAuth?.TRIGGER || false;
    },
    newTriggerGray() {
      return rootWindow.grayAuth?.NEW_TRIGGER || false;
    },
    currentLoginUserId() {
      return rootWindow?.loginUser?.userId || localStorage.getItem('loginUserId');
    },
  },
  watch:{
    bizActionCode(newVal){
      this.init();
    }
  },
  data(){
    return{
      list:[],
      triggerId: '',
      visible:false,
      CreateConnectorFields,
      formValue:{
        name:'',
        description:'',
        sync:'',
      },
      actionCode:'',
      authType: {
        isCreate: false,
        isDelete: false,
        isEdit: false
      },
      auth: []
    };
  },
  mounted() {
    this.init();
    this.getAuthByRootInitData();
  },
  methods: {
    getAuthByRootInitData() {
      const _initData = JSON.parse(rootWindow?._init || '{}');
      const _initData2 = _initData?.user?.auth || {};
      this.auth = Object.keys(_initData2);
      this.authType.isCreate = this.auth.includes('TRIGGER_CREATE');
      this.authType.isDelete = this.auth.includes('TRIGGER_DELETE');
      this.authType.isEdit = this.auth.includes('TRIGGER_EDIT');
    },
    addTrigger(){
      if (this.newTriggerGray) {
				this.$refs.CreateTriggerDialog.showDialog();
			} else {
				this.visible = true
			}
    },
    init(){

      if (!this.triggerGray && !this.newTriggerGray) {
        return;
      }
      this.list = [];
      if(this.bizActionCode){
        TriggerApi.getTriggerFormInfo({
          "bizType": this.bizType,
          "bizTypeId":this.bizTypeId,
          "oldActionCode": this.bizActionCode,
          "paasNodeId": this.paasNodeId
        }).then(res=>{
          if(res.status == 0){
            this.actionCode = res.data?.currActionCode?.bizActionCode || '';
            this.getList();
          }
        });
      }else{
        this.getList();
      }
    },
    getList(){
      TriggerApi.getTriggerInfoList(this.listQuery).then(res=>{
        if(res.status == 0){
          this.list = res.data?.list || [];
          // 获取当前列表是否有同步触发器
          this.triggerId = this.getSyncTriggerId();
          this.$emit('update');
        }else{
          console.error('error', `触发器列表获取失败：${res.message}`);
        }
      });
    },
    resetFormValue(){
      this.formValue = {
        name:'',
        description:'',
        sync:false
      };
    },
    onDialogCloseHandler(){
      this.visible = false;
      this.resetFormValue();
    },
    onDialogConfirmHandler(formValue){
      if(formValue?.name) {
        this.formValue.name = formValue.name;
        this.formValue.description = formValue.description;
        this.formValue.sync = formValue.sync;
      }
      if(this.formValue.id){
        TriggerApi.updateTriggerStat({triggerId: this.formValue.id,triggerName:this.formValue.name,triggerDesc:this.formValue.description, sync:this.formValue.sync}).then(res=>{
          if(res.status == 0){
            this.$message.success(t('common.base.tip.modifySuccess'));
            this.getList();
            this.onDialogCloseHandler();
          }else{
            this.$message.error(res.message);
          }
        });
      }else{
        let fromId = window.frameElement?.getAttribute('id');
        this.$platform.openAccurateTab({
          type: PageRoutesTypeEnum.PageConnectorSetting,
          params: `name=${this.formValue.name}&description=${this.formValue.description}&sync=${this.formValue.sync}&bizTypeId=${this.bizTypeId}&bizType=${this.bizType}&bizActionCode=${this.bizActionCode}&paasAppId=${this.paasAppId}&from=paas`,
          fromId,
          reload:true
        });
        this.onDialogCloseHandler();
      }
    },
    getSyncTriggerId() {
      const triggerItem = this.list.find(item=>item.sync);
      return triggerItem?.id || '';
    },
    onDialogUpdateHandler(info) {
      let { fieldName,newValue } = info;
      if( !fieldName )return;
      this.formValue[fieldName] = newValue;
    },
    toEdit(value){
      let fromId = window.frameElement?.getAttribute('id');
      if (this.newTriggerGray) {
				this.$platform.openAccurateTab({
					type: PageRoutesTypeEnum.PageTriggerDesign,
					params: `triggerId=${value.id}`,
					fromId
				})
			} else {
				this.$platform.openAccurateTab({
					type: PageRoutesTypeEnum.PageConnectorSetting,
          titleKey: value.triggerNo,
          key:value.id,
          params: `id=${value.id}&sync=${value.sync}&from=paas`,
          fromId
				})
			}
    },
    async toDelete(value){
      const confirmed = await this.$confirm(t('common.connector.tips.tip1'), this.$t('common.base.deleteTip'), {
        confirmButtonText: this.$t('common.base.makeSure'),
        cancelButtonText: this.$t('common.base.cancel'),
        type: 'warning'
      });
      if (isFalsy(confirmed)) {
        return;
      }
      TriggerApi.deleteTrigger(value.id).then(res=>{
        if(res.status===0){
          this.getList();
        }else{
          this.$message.error(res.message);
        }
      });
    },
    toUpdate(value){
      this.formValue = {
        id:value.id,
        name:value.triggerName,
        description:value.triggerDesc,
        sync: value.sync,
      };
      this.visible = true;
    },
    onDialogSubmitHandler(formValue) {
			const fromId = window.frameElement.getAttribute('id') || ''
			this.$platform.openAccurateTab({
				type: PageRoutesTypeEnum.PageTriggerDesign,
				params: `name=${formValue?.name}&triggerWay=${formValue?.triggerWay}&desc=${formValue?.dec}`,
				fromId
			})
		},
  },
  components:{
    ConnectorListViewCreateDialog,
    TriggerListItem,
    CreateTriggerDialog,
  }
};
</script>
<style lang="scss">
@import './index.scss';
.trigger-list-item-part {
  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}
</style>
