<template>
    <div class="trigger-dialog">
        <el-dialog
            title="新建触发器"
            :visible.sync="dialogVisible"
            :modal="false"
        >
            <el-form
                :model="formValue"
                :rules="rules"
                ref="formRef"
            >
                <el-form-item label="触发器名称" prop="name">
                    <el-input v-model="formValue.name" required="true" placeholder="[必填]最多20字" maxlength="20"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input type="textarea" placeholder="最多500字" maxlength="500" :autosize="{minRows:2,maxRows:6}" v-model="formValue.dec" rows="2"></el-input>
                </el-form-item>
                <el-form-item label="触发方式" prop="triggerWay">
                    <div class="trigger-way-des">
                        <span>选择一个触发器，通过触发事件或定时任务来触发自动化流程的运行 </span>
                        <el-button @click="openDetails" type="text">了解触发器</el-button>
                    </div>
                    <div class="trigger-way-list" v-loading="loading">
                        <div v-for="i in TriggerWayList" :key="i.way" class="trigger-way-item" @click="handleWayClick(i)"
                            :class="{'trigger-way-item-active': formValue.triggerWay === i.way}"
                        >
                            <div class="trigger-way-logo">
                                <i class="iconfont" :class="i.logo"></i>
                            </div>
                            <div class="trigger-way-info">
                                <div class="trigger-way-name">{{ i.name }}</div>
                                <div class="trigger-way-desc">{{ i.desc }}</div>
                            </div>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { getAllExecuteWayList } from '@src/component/business/connector/api/trigger';

export default {
    name: 'TriggerDialog',
    data() {
        return {
            dialogVisible: false,
            loading: false,
            TriggerWayList: [],
            formValue: {
                name: '',
                dec: '',
                triggerWay: '',
            },
            rules: {
                name: [
                    { required: true, message: '请输入触发器名称' },
                ],
                triggerWay: [
                    { required: true, message: '请选择触发方式' },
                ],
            },
        };
    },
    methods: {
        fetchTriggerWays() {
            this.loading = true;
            getAllExecuteWayList()
                .then(res => {
                    this.TriggerWayList = res.data;
                    this.formValue.triggerWay = res.data?.[0]?.way;
                    this.loading = false;
                })
                .catch(error => {
                    console.error(error);
                    this.loading = false;
                });
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        showDialog() {
            this.dialogVisible = true;
        },
        openDetails() {
            window.open('https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/eka8tf9wv8x43arx/dklg7gkm64cya8l4.html', '_blank');
        },
        handleWayClick(item) {
            this.formValue.triggerWay = item.way;
        },
        submit() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    this.closeDialog();
                    this.$emit('submit', this.formValue);
                } else {
                    console.error('Validation error.');
                }
            });
        },
    },
    watch: {
        dialogVisible(val) {
            if (!val) {
                this.formValue = {
                    name: '',
                    dec: '',
                    triggerWay: '',
                };
                if (this.$refs.formRef) {
                    this.$refs.formRef.clearValidate();
                }
            } else {
                this.fetchTriggerWays();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    width: 800px;
}

:deep(.el-input) {
    width: 100%;
}

:deep(.el-form) {
    .el-form-item {
        flex-direction: column;
        align-items: flex-start;
        .el-form-item__content {
            display: block;
            width: 100%;
            .trigger-way-des {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                font-size: 12px;
                .el-button {
                    margin-left: 4px;
                    font-size: 12px;
                }
            }
            .trigger-way-list {
                display: grid;
                gap: 16px;
                grid-template-columns: 1fr 1fr 1fr;
                margin: 6px 0 0;
                min-height: 176px;
                padding: 0;
                .trigger-way-item {
                    border: 1px solid transparent;
                    position: relative;
                    display: flex;
                    align-items: center;
                    max-width: 240px;
                    height: 80px;
                    overflow: hidden;
                    border-radius: 8px;
                    padding: 0 16px;
                    cursor: pointer;
                    .trigger-way-logo {
                        margin-right: 10px;
                        border: 1px solid #ebeef5;
                        border-radius: 4px;
                        height: 40px;
                        width: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .iconfont {
                            font-size: 24px;
                        }
                    }
                    .trigger-way-info {
                        .trigger-way-name {
                            line-height: 22px;
                            font-size: 14px;
                        }
                        .trigger-way-desc {
                            line-height: 16px;
                            font-size: 12px;
                            color: #8c8c8c;
                        }
                    }
                }
                .trigger-way-item:hover {
                    background-color: #f5f8fa;
                }
                .trigger-way-item-active {
                    background-color: $color-primary-light-1 !important;
                    border: 1px solid $color-primary;
                    &::after {
                        position: absolute;
                        content: " ";
                        width: 7px;
                        height: 12px;
                        transform: rotate(45deg);
                        border-right: 2px solid #fff;
                        border-bottom: 2px solid #fff;
                        top: -1px;
                        right: 2px;
                    }
                
                    &::before {
                        position: absolute;
                        content: '';
                        right: 0;
                        top: 0;
                        width: 0;
                        height: 0;
                        border-left: 24px solid transparent;
                        border-right: 24px solid $color-primary;
                        border-bottom: 24px solid transparent;
                    }
                }
            }
        }
    }
}
</style>
