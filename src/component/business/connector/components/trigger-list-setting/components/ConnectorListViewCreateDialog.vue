<template>
  <base-modal :show.sync="dialogVisible" :title="title" @cancel="cancel" width="400px" class="pass-node-create-trigger-dialog">
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="top">
      <el-switch v-model="ruleForm.sync" :active-text="$t('common.base.sync')"></el-switch>
      <el-form-item
        :label="$t('common.connector.fields.triggerName.displayName')"
        prop="name"
        :rules="{ required: true, validator: validateName, trigger: ['blur', 'change'] }">
        <el-input
          v-model="ruleForm.name"
          :placeholder="$t('common.connector.fields.triggerName.placeholder')"
          maxlength="20" />
      </el-form-item>

      <el-form-item
        :label="$t('common.connector.fields.triggerDescription.displayName')"
        prop="description">
        <el-input
          v-model="ruleForm.description"
          :placeholder="$t('common.connector.fields.triggerDescription.placeholder')"
          type="textarea"
          maxlength="500" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button plain @click="cancel">{{ $t('common.base.cancel') }}</el-button>
        <el-button type="primary" @click="confirm">{{ value?.id ? $t('common.base.makeSure') : $t('common.base.steps.next') }}</el-button>
      </div>
    </template>
  </base-modal>
</template>

<script>
import { t } from '@src/locales';

export default {
  name: 'ConnectorListViewCreateDialog',
  props: {
    visible: {
      type: Boolean,
      required: false,
    },
    title: {
      type: String,
      default: ''
    },
    triggerId: {
      type: String,
      default: ''
    },
    value: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      dialogVisible: this.visible,
      ruleForm: {
        id: 0,
        name: '',
        description: '',
        sync: false,
      },
    };
  },

  watch: {
    value(data) {
      this.ruleForm.id = data.id || 0;
      this.ruleForm.name = data.name || '';
      this.ruleForm.description = data.description || '';
      this.ruleForm.sync = data.sync || false;
    },
    visible(data) {
      this.dialogVisible = data;
      if (data) {
        this.$nextTick(() => {
          this.ruleFormRef && this.ruleFormRef.clearValidate();
        });
      }
    },
  },

  methods: {
    validateName(rule, value, callback) {
      if (value === '') {
        callback(new Error(t('common.placeholder.inputName')));
      } else {
        callback();
      }
    },

    cancel() {
      this.dialogVisible = false;
      this.$emit('close');
    },

    confirm() {
      const formEl = this.$refs.ruleFormRef;
      if (!formEl) return;
      formEl.validate(async (valid) => {
        if (!valid) {
          return;
        }
        // 判断当前触发器列表是否存在同步触发器
        if(this.triggerId && this.ruleForm.sync) {
          this.$message.warning(this.$t('view.designer.workFlow.onlyOneSyncTrigger'));
          return;
        }
        // 新建触发器提示先保存
        try {
          const rs = await this.$confirm(this.$t('view.designer.tip.tip7'), this.$t('common.base.toast'), {
            confirmButtonText: this.$t('common.base.makeSure'),
            cancelButtonText: this.$t('common.base.cancel'),
            type: 'warning'
          }).catch(()=> {});
          if(!rs) return;
          this.$eventBus.$emit('saveTriggerProcess');
          setTimeout(() => {
            this.$emit('confirm', this.ruleForm);
            this.dialogVisible = false;
            this.$emit('update:visible', false);
          }, 1000);
        } catch (error) {
          console.warn(error);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.pass-node-create-trigger-dialog {
  display: flex;
  justify-content: center;
  align-items: center; 
  .base-modal-body {
    padding: 20px;
    .el-form {
      position: relative;
      .el-switch {
        position: absolute;
        top: 4px;
        right: 0;
        .el-switch__label--left {
          display: none;
        }
      }
    }
  }
}
</style>
