<template>
  <div class="trigger-list-item">
    <template v-if="value.id">
      <div class="trigger-list-item-content">
        <div class="trigger-list-item-header">
          <div class="trigger-list-item-name">
            {{ value.triggerName }}
          </div>
          <div class="trigger-list-item-switch">
            <template v-if="authType.isEdit">
              <el-switch
                :active-value="1"
                :inactive-value="0"
                :value="value.enable"
                @change="updateState"
                :disabled="!authType.isEdit"
              >
              </el-switch>
            </template>
            <template v-else>
              <el-tooltip class="item" effect="dark" :content="$t('view.authMessage')" placement="top-start">
                <el-switch
                  :active-value="1"
                  :inactive-value="0"
                  :value="value.enable"
                  @change="updateState"
                  :disabled="authType.isEdit"
                >
                </el-switch>
              </el-tooltip>
            </template>
            <el-dropdown placement="top">
              <i class="el-icon-more" v-if="authType.isEdit || authType.isDelete"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <div @click="onUpdateHandler" v-if="authType.isEdit">
                    {{ $t('common.base.rename') }}
                  </div>
                </el-dropdown-item>
                <el-dropdown-item>
                  <div @click="onEditHandler" v-if="authType.isEdit">
                    {{ $t('common.base.edit') }}
                  </div>
                </el-dropdown-item>
                <el-dropdown-item>
                  <div @click="onDeleteHandler" v-if="authType.isDelete">
                    {{ $t('common.base.delete') }}
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <div class="trigger-list-item-description">
          {{ value.triggerDesc }}
        </div>

        <div class="trigger-list-item-part" v-if="triggerProcessVOList&&triggerProcessVOList[0]">
          <span>
            <template v-if="fromAppLogo.startsWith('http')">
              <img class="module-logo" :src="fromAppLogo"/>
            </template>
            <i
              v-else 
              :class="[
                'iconfont',
                'app-logo',
                fromAppLogo,
              ]"
            />
            {{ triggerProcessVOList[0].from.bizTypeName }}
          </span>

          <i class="iconfont icon-arrow-left"> </i>

          <span>
            <template v-if="toAppLogo.startsWith('http')">
              <img class="module-logo" :src="toAppLogo"/>
            </template>
            <i
              v-else 
              :class="[
                'iconfont',
                'app-logo',
                toAppLogo,
              ]"
            />
            {{ triggerProcessVOList[0].to.bizTypeName }}
          </span>
        </div>
        
        <el-tooltip :content="tooltipText" placement="top">
          <div class="trigger-list-item-update">
            {{ $t('common.connector.trigger.no') }}: {{ value.triggerNo }}&nbsp;
            {{ $t('common.base.lastUpdate') }}:
            <open-data
              type="userName"
              v-if="isOpenData"
              :openid="value.operatorStaffId"
            >
            </open-data>
            <span v-else>
              {{ value.operatorName }}
            </span>
            <span class="trigger-list-item-update-time">
              {{ fmt_datetime(value.updateTime) }}
            </span>
          </div>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>
<script>
/* util */
import { fmt_datetime } from '@src/filter/fmt';
/* api */
import * as TriggerApi from '@src/component/business/connector/api/trigger';
export default {
  name: 'TriggerListItem',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    authType: {
      type: Object,
      default:() => ({})
    }
  },
  data() {
    return {
      fmt_datetime,
    };
  },
  computed: {
    isOpenData() {
      return this.$platform.isOpenData;
    },
    triggerProcessVOList(){
      return this.value?.triggerProcessVOList || null;
    },
    triggerProcessVO() {
      return this.triggerProcessVOList?.[0] || null;
    },
    fromAppLogo() {
      return this.triggerProcessVO?.from?.icon || '';
    },
    toAppLogo() {
      return this.triggerProcessVO?.to?.icon || '';
    },
    tooltipText() {
      return `${this.$t('common.connector.trigger.no')}: ${ this.value?.triggerNo || '' }  ${this.$t('common.base.lastUpdate')}: ${ this.value?.operatorName || '' } ${ this.fmt_datetime(this.value.updateTime) }`;
    }
  },
  methods: {
    updateState(state) {
      if(this.authType.isEdit === false) return;
      TriggerApi.updateTriggerStat({triggerId:this.value.id, enable:state}).then((res)=>{
        if(res.status == 0){
          this.value.enable = state;
          this.$emit('update:value', this.value);
          this.$emit('updateList');
        }else{
          this.$message.error(res.message);
        }
      });
    },
    onUpdateHandler(){
      this.$emit('update', this.value);
    },
    onEditHandler(){
      this.$emit('edit', this.value);
    },
    onDeleteHandler(){
      this.$emit('delete', this.value);
    }
  },
};
</script>
<style lang="scss"></style>
