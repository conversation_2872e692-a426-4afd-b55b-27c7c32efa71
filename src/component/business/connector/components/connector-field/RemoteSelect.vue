<template>
  <div class="select-search">
    <el-select
      :id="`form_${field.fieldName}`"
      popper-class="advanced-search-remote-select-search-popper"
      :value="selectValue"
      :placeholder="placeholder"
      :multiple="multiple"
      :remote-method="keywordSearch"
      remote
      clearable
      filterable
      collapse-tags
      v-el-select-loadmore="loadMoreOptions"
      @focus="focus"
      @input="changeSelect"
    >
      <el-option
        v-for="item in options"
        :key="item.key || item.value"
        :label="item.label"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { defineComponent, computed, ref } from 'vue';
import { debounce } from 'lodash';
import i18n from '@src/locales';

export default defineComponent({
  name: 'remote-select-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.inputKeyWordToSearch'),
    },
    value: {
      type: [Number, String, Array, Object],
      default: '',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    remoteMethod: {
      type: Function,
      default: null,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    // 是否多选
    const selectValue = computed(() => {
      return Array.isArray(props.value) ? [...props.value] : props.value;
    });
    const options = ref(
      props.value
        ? Array.isArray(props.value)
          ? [...props.value]
          : [props.value]
        : []
    );

    const searchParams = ref({
      keyword: '',
      pageNum: 1,
    });
    const loading = ref(false);

    const hasNextPage = ref(true);

    // 改变值
    function changeSelect(value) {
      emit('update', value);
    }

    function keywordSearch(keyword = '') {
      searchParams.value.keyword = keyword;
      searchParams.value.pageNum = 1;
      search();
    }

    const search = debounce(async () => {
      if (loading.value) return;
      if (!props.remoteMethod) return console.warn('请提供查询方法');
      try {
        loading.value = true;
        const res = await props.remoteMethod(searchParams.value);
        if (searchParams.value.pageNum === 1) {
          options.value = [];
        }

        options.value.push(...res.list);
        hasNextPage.value = res.hasNextPage;
      } catch (error) {
        console.error(error);
      } finally {
        loading.value = false;
      }
    }, 500);

    function focus() {
      if (options.value.length) return;
      keywordSearch();
    }

    const loadMore = debounce(() => {
      if (!hasNextPage.value || loading.value) return;
      searchParams.value.pageNum++;
      search();
    });

    const loadMoreOptions = computed(() => ({
      disabled: false,
      callback: loadMore,
      distance: 10,
    }));

    return {
      selectValue,
      loadMoreOptions,
      options,

      changeSelect,
      focus,
      keywordSearch,
      loadMore,
    };
  },
});
</script>

<style lang="scss">
.advanced-search-remote-select-search-popper {
  .el-select-dropdown__item {
    width: 275px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
