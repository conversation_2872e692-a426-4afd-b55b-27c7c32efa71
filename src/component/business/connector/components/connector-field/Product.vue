<template>
  <div class="advanced-search__product-search">
    <el-select
      :value="selectValue"
      value-key="id"
      filterable
      remote
      clearable
      :placeholder="placeholder"
      :multiple="multiple"
      :remote-method="keywordSearch"
      @focus="focus"
      @input="updateSelectValue"
      v-el-select-loadmore="loadMoreOptions"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        class="advanced-search__product-search-select-option"
        :label="item.name"
        :origin="item"
        :value="item"
      >
        <div class="service-template-option">
          <h3>{{ item.name }}</h3>
          <p class="service-template-option-content">
            <span class="service-template-option-content-text">
              <label>{{ $t('common.fields.serialNumber.displayName') }}：</label>
              <span>{{ item.serialNumber }}</span>
            </span>
            <span class="service-template-option-content-text">
              <label>{{ $t('common.fields.type.displayName') }}:</label>
              <span>{{ item.type }}</span>
            </span>
            <span class="service-template-option-content-text">
              <label>{{ $t('common.fields.customer.displayName') }}：</label>
              <span>{{ item.customerName || '' }}</span>
            </span>
          </p>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import {
  defineComponent,
  ref,
  computed,
} from 'vue';
import i18n from '@src/locales';

import { debounce } from 'lodash';
import { getSystemNormalSearchInputForLength } from '@src/util/getSystemConfig';

export default defineComponent({
  name: 'product-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.selectProduct'),
    },
    remoteMethod: {
      type: Function,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    value: {
      type: [Array, Object, String], // 产品暂时只有等于 传入的 是个对象
      default: null,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const selectValue = computed(() => {
      return Array.isArray(props.value) ? [...props.value] : props.value;
    });
    const options = ref(
      props.value
        ? Array.isArray(props.value)
          ? [...props.value]
          : [props.value]
        : []
    );
    const searchParam = ref({
      pageNum: 1,
      keyword: '',
    });
    const hasNextPage = ref(true);
    const loadMoreOptions = ref({
      disabled: false,
      callback: loadMore,
      distance: 10,
    });
    const loading = ref(false);

    // 搜索
    async function searchFn() {
      try {
        if(loading.value) return;
        if (!props.remoteMethod) return console.warn('请提供产品查询方法');
        loading.value = true;
        const res = await props.remoteMethod(searchParam.value);
        if (!res || !res.list) return (options.value = []);

        if (searchParam.value.pageNum === 1) {
          options.value = [];
        }

        hasNextPage.value = !res.isLastPage;
        loadMoreOptions.value.disabled = !hasNextPage.value;
        options.value.push(...res.list);
      } catch (error) {
        options.value = [];
      }finally {
        loading.value = false;
      }
    }

    const search = debounce(searchFn, 500);

    // 关键字搜索
    function keywordSearch(keyword) {
      // 产品相关搜索受系统搜索长度控制
      searchParam.value.keyword = getSystemNormalSearchInputForLength(keyword);
      searchParam.value.pageNum = 1;
      search();
    }

    function focus() {
      if (options.value.length) return;
      keywordSearch();
    }

    // 加载更多
    function loadMore() {
      if(!hasNextPage.value || loading.value) return;
      searchParam.value.pageNum++;
      search();
    }

    // 选中更新值
    function updateSelectValue(selected) {
      emit('update', selected);
    }

    return {
      selectValue,
      options,
      loadMoreOptions,
      focus,
      keywordSearch,
      updateSelectValue,
    };
  },
});
</script>

<style lang="scss">
.advanced-search__product-search {
}
.advanced-search__product-search-select-option {
  
  height: auto !important;
  
  .service-template-option {
  * {
    margin: 0;
  }
  padding: 8px 0;
  h3 {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 8px;
  }
  p {
    display: flex;
    justify-content: space-between;
    flex-flow: column;
    line-height: 22px;
    
    & > span {
      
      width: 100%;
      display: flex;
      justify-content: flex-start;
      font-size: 12px;
      color: #666666;
      padding-right: 10px;
      
      & > label {
        padding: 0;
        width: auto !important;
      }
      
      & > span {
        @include text-ellipsis();
      }
      
    }
  }
}
  
}
.service-template-option-content {
  &-text {
    display: flex;

    label {
      padding-top: 0;
    }

    span {
      line-height: 24px;
    }
  }
}
</style>
