<template>
  <div class="cascader-search">
    <el-cascader
      :props="productTypeProps"
      :value="value"
      :options="options"
      :placeholder="placeholder"
      clearable
      @change="changeValue"
    />
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'product-type-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    remoteMethod: {
      type: Function,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.select'),
    },
    value: {
      type: [String, Array],
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {

    const productTypeProps = ref({
      value:'id',
      label:'name',
      children:'tasks',
      expandTrigger:'hover',
    });
    
    const options = ref([]);

    // 获取数据
    async function getOptions() {
      try { 
        if (!props.remoteMethod) return console.warn('请提供产品类型查询方法');
        const res = await props.remoteMethod();
        if (!res) return (options.value = []);
        options.value.push(...res);
      } catch (error) {
        console.error(error);
        options.value = [];
      }
    }
    getOptions();

    // 改变值
    function changeValue(value) {
      emit('update', value);
    }

    return {
      options,

      changeValue,

      productTypeProps
    };
  },
});
</script>

<style></style>
