<template>
  <div>
    <biz-team-select
      :value="value"
      :multiple="multiple"
      @input="update"
    />
  </div>
</template>
<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'tags-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    value: {
      type: Array,
      default: () => null,
    },
    multiple: {
      type: Boolean,
      default: true,
    }
  },
  emits: ['update'],
  setup(props, { emit }) {
    function update(value){
      emit('update', value);
    }
    return {
      update,
    };
  },
});
</script>
