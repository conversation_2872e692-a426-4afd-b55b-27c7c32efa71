<template>
  <div class="address-search">
    <base-dist-picker-international
      :value="distValue"
      :placeholder="placeholder"
      @input="handleCitySelectorChange"
      :showOneLine="true"
    />
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import i18n from '@src/locales';
export default defineComponent({
  name: 'address-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.selectAddress'),
    },
    value: {
      type: Object,
      default() {
        let value = {
          /**
           * value值必须包含以下值:
           * province: String,
           * city: String,
           * dist: String,
           * address: String
           *
           * 以下值可选：
           * latitude： [String,Number],
           * longitude: [String,Number],
           * addressType: Number,
           * adCode: String (区级别的行政区划代码)
           */
        };
        
        return {};
      },
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const distValue = computed(() => {
      if(!props.value) return [];
      let { country, province, city, dist } = props.value;
      // 兼容老数据，country默认值为中国
      let arr = [country || '中国']
      if (province) {
        arr.push(province)
        if (city) {
          arr.push(city);
          if (dist) arr.push(dist);
        }
      }

      return arr;
    });

    function handleCitySelectorChange(val) {
      let newAddress = {
        country: '',
        province: '',
        city: '',
        dist: '',
        address: '',
        adCode: '', // 添加adCode字段
      };

      // 处理新的数据格式（包含selectedValues和adCode）
      let selectedValues, adCode = '';
      if (val && typeof val === 'object' && val.selectedValues) {
        // 新格式：{ selectedValues: [...], adCode: '...' }
        selectedValues = val.selectedValues;
        adCode = val.adCode || '';
      } else if (Array.isArray(val)) {
        // 兼容旧格式：直接是数组
        selectedValues = val;
      } else {
        // 无效数据
        return updateValue(newAddress);
      }

      if (!Array.isArray(selectedValues) || !selectedValues.length) {
        return updateValue(newAddress);
      }

      newAddress = {
        ...props.value,
        country: selectedValues[0] || '',
        province: selectedValues[1] || '',
        city: selectedValues[2] || '',
        dist: selectedValues[3] || '',
        adCode, // 保存区级别的adCode
      };

      updateValue(newAddress);
    }

    function updateValue(newValue) {
      // 工单客户关联字段清除位置信息时候不调用update
      emit('update', newValue);
    }

    return {
      distValue,

      handleCitySelectorChange,
      updateValue,
    };
  },
});
</script>
