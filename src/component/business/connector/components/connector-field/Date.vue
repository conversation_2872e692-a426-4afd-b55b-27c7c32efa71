<template>
  <el-date-picker
    :value="value"
    @input="choose"
    :type="datePickerType"
    unlink-panels
    range-separator="-"
    :start-placeholder="$t('common.base.startDate')"
    :end-placeholder="$t('common.base.endDate')"
    :placeholder="placeholder"
  ></el-date-picker>
</template>

<script>
import { safeNewDate } from '@src/util/date';
import { formatDate } from '@src/util/lang';
import i18n from '@src/locales';

export default {
  name: 'date-search',
  props: {
    field: {
      type: Object,
      default: () => ({}),
    },
    placeholder: {
      type: String,
      default() {
        return i18n.t('common.placeholder.selectTime');
      },
    },
    type: {
      type: String,
      default: 'date',
    },
    value: {
      type: [Array, String],
      default: null,
    },
    pickerOptions: {
      type: Object,
      default: () => ({}),
    },
    isRange: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update'],
  data() {
    return {
      datePickerOptions: {
        shortcuts: [
          {
            text: i18n.t('common.time.lastWeek'),
            onClick(picker) {
              const end = safeNewDate();
              const start = safeNewDate();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: i18n.t('common.time.lastMonth'),
            onClick(picker) {
              const end = safeNewDate();
              const start = safeNewDate();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: i18n.t('common.time.lastThreeMonth'),
            onClick(picker) {
              const end = safeNewDate();
              const start = safeNewDate();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    datePickerType() {
      return this.isRange ? this.type + 'range' : this.type;
    },
    format() {
      return this.type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss';
    },
  },
  methods: {
    choose(newValue) {
      if (Array.isArray(newValue)) {
        if (newValue[0]) {
          newValue = [
            `${formatDate(newValue[0], 'YYYY-MM-DD')} 00:00:00`,
            `${formatDate(newValue[1], 'YYYY-MM-DD')} 00:00:00`,
          ];
        }
      } else {
        newValue = formatDate(newValue, this.format);
      }
      
      this.$emit('update', newValue);
    },
  },
};
</script>

<style lang="scss">
.el-date-editor {
  width: 100% !important;
}
</style>
