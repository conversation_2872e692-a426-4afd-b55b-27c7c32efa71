<template>
    <biz-form-remote-select :placeholder="placeholder" :remote-method="searchPosition" :value="selectValue"
        :inputDisabled="disabled" cleared @input="updateSelectValue">
        <div class="position-item" slot="option" slot-scope="{ option }">
            {{ option.name }}
        </div>
    </biz-form-remote-select>
</template>

<script>
import { t } from '@src/locales'
import http from '@src/util/http';
export default {
    name: 'search-position-select',
    props: {
        placeholder: {
            type: String,
            default: t('common.form.placeHolder.materialReturn.pla3')
        },
        remoteMethod: {
            type: Function
        },
        value: {
            type: Array,
            default: () => []
        },
        disabled: {
            type: Boolean,
            default: false
        },
        warehouseId: {
            type: String | Number,
            default: () => ''
        }
    },
    data() {
        return {
            selectValue: this.value.slice()
        };
    },
    watch: {
        value(newValue) {
            this.selectValue = newValue.slice();
        }
    },
    methods: {
        updateSelectValue(value) {
            console.log(value, 'position')
            let _wareHouseId = {
                id: value[0]?.id,
                warehouseId: value[0]?.warehouseId,
                name: value[0]?.name
            }
            this.$emit('input', [_wareHouseId]);
        },
        searchPosition(params) {
            const pms = params || {};

            let para = { ...pms };
            para.name = para.keyword;
            if (this.warehouseId) {
                // 只取第一个
                para.warehouseId = this.warehouseId || '';
            }
            delete para.keyword;

            // pms.enabled = true
            return  http.post(`/api/warehouse/outside/warehousePosition/list/search`, para)
                .then(res => {
                    if (!res.data || !res.data.list) return;
                    res.data.list = res.data.list.map(position =>
                        Object.freeze({
                            label: position.name,
                            value: position.id,
                            ...position
                        })
                    );
                    res = res.data;
                    return res;
                })
                .catch(e => console.error(e));
        },
    }
};
</script>

<style lang="scss">
.position-item {
    h3 {
        font-size: 14px;
        margin: 0;
    }

    p {
        display: flex;
        margin: 0;

        span {
            label {
                display: inline-block;
                width: auto;
            }

            span {
                margin-right: 10px;
            }
        }
    }
}

::v-deep .el-input--small .el-input__inner {
    line-height: 0 !important;
}
</style>