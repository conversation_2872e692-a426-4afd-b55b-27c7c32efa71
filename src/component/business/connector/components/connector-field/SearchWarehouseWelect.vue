<template>
    <biz-form-remote-select :placeholder="placeholder" :remote-method="searchWarehouse" :value="selectValue" cleared
        @input="updateSelectValue">
        <div class="warehouse-item" slot="option" slot-scope="{ option }">
            {{ option.name }}
        </div>
    </biz-form-remote-select>
</template>

<script>
import { t } from '@src/locales'
import http from '@src/util/http';

export default {
    name: 'search-warehouse-select',
    props: {
        placeholder: {
            type: String,
            default: t('common.form.placeHolder.materialReturn.pla2')
        },
        remoteMethod: {
            type: Function
        },
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            selectValue: this.value.slice()
        };
    },
    watch: {
        value(newValue) {
            this.selectValue = newValue.slice();
        }
    },
    methods: {
        updateSelectValue(value) {
            console.log(value, 'warehouseWelect')
            let _warehouse = {
                id: value[0]?.id || '',
                name: value[0]?.name || ''
            }
            this.$emit('input', [_warehouse]);
        },
        searchWarehouse(params) {
            const pms = params || {};

            let para = { ...pms };
            para.keyword = para.keyword;

            // pms.enabled = true
            return http.get(`/api/warehouse/outside/warehouse/search`, para)
                .then(res => {
                    if (!res.data) return;
                    res.data.list = res.data.map(warehouse =>
                        Object.freeze({
                            label: warehouse.name,
                            value: warehouse.id,
                            ...warehouse
                        })
                    );
                    res = res.data;
                    return res;
                })
                .catch(e => console.error(e));
        }
    }
};
</script>

<style lang="scss">
.warehouse-item {
    h3 {
        font-size: 14px;
        margin: 0;
    }

    p {
        display: flex;
        margin: 0;

        span {
            label {
                display: inline-block;
                width: auto;
            }

            span {
                margin-right: 10px;
            }
        }
    }
}

::v-deep .el-input--small .el-input__inner {
    line-height: 0 !important;
}
</style>