<template>
  <div class="advanced-search__linkman-search">
    <el-select
      :value="selectValue"
      value-key="id"
      filterable
      remote
      clearable
      :placeholder="placeholder"
      :multiple="multiple"
      :remote-method="keywordSearch"
      v-el-select-loadmore="loadMoreOptions"
      @focus="focus"
      @input="updateSelectValue"
    >
      <el-option
        v-for="(item, index) in options"
        :key="`${item.id}_${index}`"
        class="advanced-search__linkman-search-select-option"
        :label="item.name"
        :origin="item"
        :value="item"
      >
        <div class="linkman-search-option">
          <span>{{ item.name || '' }}</span>
          <span>{{ item.phone || '' }}</span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { debounce } from 'lodash';
import i18n from '@src/locales';

export default defineComponent({
  name: 'linkman-search',
  props: {
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.selectContact'),
    },
    remoteMethod: {
      type: Function,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    value: {
      type: [Array, Object],
      default: null,
    },
  },
  setup(props, { emit }) {
    const selectValue = computed(() => {
      return Array.isArray(props.value) ? [...props.value] : props.value;
    });
    const options = ref(
      props.value
        ? Array.isArray(props.value)
          ? [...props.value]
          : [props.value]
        : []
    );
    const searchParam = ref({
      pageNum: 1,
      keyword: '',
    });
    const hasNextPage = ref(true);
    const loadMoreOptions = ref({
      disabled: false,
      callback: loadMore,
      distance: 10,
    });
    const loading = ref(false);

    // 搜索
    const search = debounce(async function search() {
      try {
        if (loading.value) return;
        if (!props.remoteMethod) return console.warn('请提供联系人查询方法');
        loading.value = true;
        const res = await props.remoteMethod(searchParam.value);
        if (!res || !res.list) return (options.value = []);

        if (searchParam.value.pageNum === 1) {
          options.value = [];
        }

        hasNextPage.value = !res.isLastPage;
        loadMoreOptions.value.disabled = !hasNextPage.value;
        options.value.push(...res.list);
      } catch (error) {
        options.value = [];
      } finally {
        loading.value = false;
      }
    });

    // 关键字搜索
    function keywordSearch(keyword) {
      searchParam.value.keyword = keyword;
      searchParam.value.pageNum = 1;
      search();
    }

    // 聚焦输入框第一次 默认搜索一下
    function focus() {
      if (options.value.length) return;
      keywordSearch('');
    }

    // 加载更多
    function loadMore() {
      if (!hasNextPage.value || loading.value) return;
      searchParam.value.pageNum++;
      search();
    }

    // 选中更新值
    function updateSelectValue(value) {
      emit('update', value);
    }

    return {
      selectValue,
      options,
      loadMoreOptions,
      focus,
      keywordSearch,
      updateSelectValue,
    };
  },
});
</script>

<style lang="scss">
.advanced-search__linkman-search-select-option {
  height: auto;
  .linkman-search-option {
    display: flex;
    justify-content: space-between;
    & > span {
      max-width: 49%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
