<template>
  <div class="text-search">
    <el-input
      :value="value"
      maxlength="1000"
      :placeholder="placeholder"
      @input="changeText"
    ></el-input>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import i18n from '@src/locales';

export default defineComponent({
  name: 'text-search',
  props: {
    field: {
      type: Object,
      default: null,
    },
    placeholder: {
      type: String,
      default: i18n.t('common.placeholder.input'),
    },
    value: {
      type: [String, Array, Number],
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    function changeText(value) {
      emit('update', value.trim());
    }

    return {
      changeText,
    };
  },
});
</script>
