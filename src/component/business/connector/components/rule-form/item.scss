
.connector-module-rule-form-item {

  display: flex;
  align-items: center;
  position: relative;

  .el-select,
  .el-input {
    background-color: #fff;
  }

}

.connector-module-rule-form-item {

  .el-select {
    margin-right: 12px;
  }

  .el-select:nth-last-of-type(1) {
    margin-right: 0;
  }

}

.connector-module-rule-form-item-operate-select {
  width: 100px;
}

.connector-module-rule-form-item-condition-select {
  width: 120px;
}

.connector-module-rule-form-item-delete-button {

  margin-left: 12px;

  .connector-module-rule-form-item-delete-button-icon {

    color: #8C8C8C;
    cursor: pointer;

    &:hover {
      color: $color-danger;
    }

  }

}

.connector-module-rule-form-item-left-condition-tip {
  width: 40px;
}

.connector-module-rule-form-item-left-to-field-select {
  margin-right: 12px;
  width: 182px;
  &.indent {
    .el-input {
      width: calc(100% - 16px);
      margin-left: 16px;
    }
  }
}

.connector-module-rule-form-item-operate-select {
  width: 120px;
}

.connector-module-rule-form-item-condition-select {
  width: 120px;
}

.connector-module-rule-form-item-required {
  position: absolute;
  left: -8px;
  color: red;
}
