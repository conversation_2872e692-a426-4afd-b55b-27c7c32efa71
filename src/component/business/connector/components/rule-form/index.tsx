/* components */
import { ConnectorModuleRuleFormItem } from '@src/component/business/connector/components';
/* model */
import { ConnectorFromField, ConnectorModuleComponentNameEnum, ConnectorToField } from '@src/component/business/connector/model';
/* scss */
import '@src/component/business/connector/components/rule-form/index.scss';
/* vue */
import { ComponentInstance, defineComponent, PropType } from 'vue';
import { CommonComponentInstance, ComponentRenderProxy } from '@model/VC';
import { CreateElement } from 'vue';
/* types */
import { ConnectorRuleFormItemType } from '@src/component/business/connector/types';
/* util */
import { isEmpty } from '@src/util/type';
import { createConnectorRuleFormItem } from '@src/component/business/connector/util';
import { t } from '@src/locales';
const FieldSuffixText = `- ${t('common.connector.field')}`;

export type ConnectorModuleRuleFormProps = {
  fromFieldList: ConnectorFromField[];
  isShowOperate: boolean;
  toFieldList: ConnectorToField[];
  value: ConnectorRuleFormItemType[];
  fromBizTypeName: string;
  toBizTypeName: string;
  toBizTypeId: string;
}

export interface ConnectorModuleRuleFormSetupState {

}

export enum ConnectorModuleRuleFormEmitEventNameEnum {
  Input = 'input',
}

export type ConnectorModuleRuleFormInstance = ComponentInstance & ConnectorModuleRuleFormSetupState
export type ConnectorModuleRuleFormVM = ComponentRenderProxy<ConnectorModuleRuleFormProps> & CommonComponentInstance & ConnectorModuleRuleFormInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleRuleForm,
  emits: [
    ConnectorModuleRuleFormEmitEventNameEnum.Input,
  ],
  props: {
    fromFieldList: {
      type: Array as PropType<ConnectorFromField[]>,
      default: () => ([])
    },
    isShowOperate: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    isSelect: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    toFieldList: {
      type: Array as PropType<ConnectorToField[]>,
      default: () => ([])
    },
    value: {
      type: Array as PropType<ConnectorRuleFormItemType[]>,
      default: () => ([])
    },
    fromBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeName: {
      type: String as PropType<string>,
      default: ''
    },
    toBizTypeId: {
      type: String as PropType<string>,
      default: ''
    },
    isUpdate:{
      type: Boolean as PropType<boolean>,
      default: false
    },
  },
  setup(props: ConnectorModuleRuleFormProps, { emit }) {

  },
  computed: {
    toFieldListMap(): Record<string, ConnectorToField> {
      return (
        this.toFieldList.reduce((prev, current) => {
          prev[current.enName] = current;
          return prev;
        }, {} as Record<string, ConnectorToField>)
      );
    }
  },
  mounted() {
    this.initiateRuleForm();
  },
  methods: {
    /**
     * @description 初始化规则表单
    */
    initiateRuleForm() {

      // 没有数据则默认添加一个
      if (isEmpty(this.value)) {

        const newRuleFormItem = createConnectorRuleFormItem();

        this.onValueInputHandler([newRuleFormItem]);
      }

    },
    /**
     * @description 数据变化事件
    */
    onValueInputHandler(value: ConnectorRuleFormItemType[]) {
      this.$emit(ConnectorModuleRuleFormEmitEventNameEnum.Input, value);
    },
    /**
     * @description 删除某一个项事件
    */
    onDeleteRuleFormItemHandler(index: number) {

      // 浅拷贝一份数据
      const value = [...this.value];

      // 根据索引删除
      value.splice(index, 1);

      // 如果删除后没有数据则添加一个
      if (isEmpty(value)) {
        value.push(createConnectorRuleFormItem());
      }

      this.onValueInputHandler(value);

    },
  },
  render(h: CreateElement) {
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleRuleForm}>

        <div class="connector-module-rule-form-header">

          <div class="connector-module-rule-form-header-to">

            <div class="connector-module-rule-form-header-to-name">
              { this.toBizTypeName }
            </div>

            <div class="connector-module-rule-form-header-to-suffix">
              &nbsp;{ FieldSuffixText }
            </div>

          </div>

          {
            this.isSelect && (<div class="connector-module-rule-form-header-operate">
              {t('common.connector.discriminator')}
            </div>)
          }

          <div class="connector-module-rule-form-header-condition">
            {t('common.connector.takeValueMethod')}
          </div>

          <div class="connector-module-rule-form-header-from">

            <div class="connector-module-rule-form-header-from-name">
              { this.fromBizTypeName }
            </div>

            <div class="connector-module-rule-form-header-from-suffix">
              &nbsp;{ FieldSuffixText }
            </div>

          </div>

        </div>

        {this.value.map((item, index) => {

          const isFirst = index === 0;

          return (
            <ConnectorModuleRuleFormItem
              key={item.id}
              value={item}
              isFirst={isFirst}
              isSelect={this.isSelect}
              existFieldList={this.value}
              toFieldList={this.toFieldList}
              toFieldListMap={this.toFieldListMap}
              toBizTypeId={this.toBizTypeId}
              fromFieldList={this.fromFieldList}
              isShowOperate={this.isShowOperate}
              isUpdate={this.isUpdate}
              // @ts-ignore
              onInput={this.onValueInputHandler}
              // @ts-ignore
              onDelete={() => this.onDeleteRuleFormItemHandler(index)}
            />
          );

        })}

      </div>
    );
  }
});
