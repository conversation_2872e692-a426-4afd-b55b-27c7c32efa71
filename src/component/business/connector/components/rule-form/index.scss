

.connector-module-rule-form {
  .connector-module-rule-form-item {
    margin-bottom: 12px;
    &-right-from-field-form-field-select {
      width: 220px;
      margin-right: 0!important;
    }
    .el-date-editor{
      width: 220px!important;
    }
    &-condition-select+div {
      width: 220px!important;
      .el-select, .el-cascader {
        width: 220px;
      }
    }
    .biz-form-remote-select {
      flex: inherit;
    }
  }
}

.connector-module-rule-form-header {
  
  display: flex;
  margin-bottom: 8px;
  
  & > div {
    height: 22px;
    font-size: 14px;
    font-weight: 600;
    color: #595959;
    line-height: 22px;
  }
  
  .connector-module-rule-form-header-to {
    margin-left: 40px;
    width: 182px;
    margin-right: 18px;
  }
  
  .connector-module-rule-form-header-operate {
    width: 110px;
    margin-right: 22px;
  }
  
  .connector-module-rule-form-header-condition {
    width: 120px;
    margin-right: 14px;
    @include text-ellipsis();
  }
  
  .connector-module-rule-form-header-from {
    width: 220px;
  }
  
}

.connector-module-rule-form-header-to,
.connector-module-rule-form-header-from {
  
  display: flex;
  
  &-name {
    @include text-ellipsis();
  }
  
  &-suffix {
    width: 48px;
    min-width: 48px;
  }
  
}