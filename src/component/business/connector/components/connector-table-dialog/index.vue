<template>
  <base-modal
    :show.sync="visible"
    :title="$t('common.connector.connectorDetail')"
    width="740px"
  >
    <div class="connector-content">
      <div class="table-box">
        <ConnectorModuleConnectorComponentTable
          ref="multipleTable"
          :fields="columns"
          :values="tableData"
          :show-index="true"
        />
      </div>
    </div>

    <div slot="footer">
      <el-button type="primary" @click="visible = false">{{ $t('common.base.close') }}</el-button>
    </div>
  </base-modal>
</template>

<script>
import ConnectorModuleConnectorComponentTable from "@src/component/business/connector/components/connector-card/connector-component-table.tsx";
import {
  getSortConnectorToFieldsByShowFieldNameList,
} from "@src/component/business/connector/util";

export default {
  name: 'connector-table-dialog',
  computed: {
    // 表单表头
    columns() {
      let columns = [];
      if(this.selectColumns?.length > 0){
        columns = [...this.selectColumns];
        columns.map(field => {
          field.label = field.cnName;
          field.field = field.enName;
          field.fieldName = field.enName;
          field.formType = field.fieldType;
        });
      }
      return columns;
    },
    connector() {
      return this.field?.setting?.connector;
    },
    selectColumns() {
      if(this.connector) {
        let { showFieldNameList, toFieldList } = this.connector;
        if(toFieldList && showFieldNameList) {
          return getSortConnectorToFieldsByShowFieldNameList(toFieldList, showFieldNameList);
        }
      }
      return [];
    },
  },
  components: {
    ConnectorModuleConnectorComponentTable,
  },
  data() {
    return {
      visible: false,
      field: {},
      tableData: []
    };
  },
  methods: {
    open(field, value) {
      this.visible = true;
      this.field = field;
      this.tableData = value;

    },
  },
};
</script>
<style lang="scss" scoped>
.connector-content {
  padding: 20px;
  .table-box{
    margin:10px 0;
    ::v-deep .el-table{
      border: 1px solid #ebeef5;
    }
  }
}

</style>
