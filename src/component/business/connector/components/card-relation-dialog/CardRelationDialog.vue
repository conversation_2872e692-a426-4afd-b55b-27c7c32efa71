<template>
  <base-modal
    class="card-create-dialog"
    :title="$t('view.template.detail.title1', {moduleName: card.name || ''})"
    :show.sync="visible"
    width="800px"
  >
    <div v-loading.lock="loading">
      <div class="search-panel">
        <el-input v-model="baseParams.keyword" :placeholder="inputPlaceHolder" @keyup.enter.native="jump(1)"></el-input>
        <el-button type="primary" @click="jump(1)">{{ $t('common.base.search') }}</el-button>
      </div>

      <connector-module-connector-card-multi-card-table
        ref="cardTable"
        :card="card"
        :fields="fields"
        :loading="loading"
        :values="data"
        @view="data => $emit('view', data)"
        selectable
      />

      <el-pagination
        background
        :total="totalItems"
        :page-sizes="defaultTableData.defaultPageSizes"
        :page-size="baseParams.pageSize"
        :current-page="baseParams.pageNum"
        :layout="defaultTableData.defaultLayout"
        @current-change="jump"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button :disabled="pending" @click="visible = false">{{ $t('common.base.close') }}</el-button>
      <el-button type="primary" :disabled="pending" @click="submit">{{ $t('common.base.save') }}</el-button>
    </div>
  </base-modal>
</template>

<script>
/* api */
import { getConnectorCardDataList, addRelationshipRecord } from '../../api/index.ts';
/* component */
import ConnectorModuleConnectorCardMultiCardTable from '@src/component/business/connector/components/connector-card/multi-card-table';
import { defaultTableData } from '@src/util/table';

export default {
  name: 'card-create-dialog',
  props: {
    card: {
      type: Object,
      default: () => ({})
    },
    fields: {
      type: Array,
      default: () => ([])
    },
    value: {
      type: Array,
      default: () => []
    },
    paramFn: {
      type: Function,
    }
  },
  data() {
    return {
      defaultTableData,
      loading: false,
      pending: false,
      visible: false,
      data: [],
      totalItems: 0,
      baseParams: this.buildParams(),
    };
  },
  computed: {
    connectorInfo() {
      const { config } = this.card;
      let connectorInfo = {};
      if(typeof config === 'string' && config.length > 0) {
        connectorInfo = JSON.parse(config)?.connectorInfo || {};
      }
      return connectorInfo;
    },
    inputPlaceHolder() {
      if(this.connectorInfo.toBizType === 'WIKI') {
        return this.$t('view.template.detail.placeholder3', {moduleName: this.card.name});
      }
      return this.$t('view.template.detail.placeholder2', {data: this.card.name || ''});
    }
  },
  methods: {
    buildParams() {
      return {
        keyword: '',
        pageSize: 10,
        pageNum: 1,
        showAllData: true
      };
    },
    /* 打开弹窗 */
    open() {
      this.data = [];
      this.totalItems = 0;

      this.baseParams = this.buildParams();
      this.onSearch();

      this.pending = false;
      this.visible = true;
    },
    /* 搜索 */
    onSearch() {
      this.loading = true;

      const param = {
        ...this.paramFn(),
        ...this.baseParams,
      };

      getConnectorCardDataList(param)
        .then(res => {
          let { success, message, data } = res;
          if (!success) return this.$message.error(message);

          if (!data || !data.list) {
            this.data = [];
            this.totalItems = 0;
            this.baseParams.pageNum = 1;
          } else {
            const { total, pageNum, list = [] } = data;

            this.data = list;
            this.totalItems = total;
            this.baseParams.pageNum = pageNum;

            this.$nextTick(() => {
              this.$refs.cardTable.matchSelected();
            });
          }
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(err => console.error(err));
    },
    /* 提交 */
    submit() {
      const { multipleSelection = [] } = this.$refs.cardTable;
      const parentParams = this.paramFn();

      const params = {
        fromBizType: parentParams.fromBizType,
        fromBizTypeId: parentParams.fromBizTypeId,
        fromBizId: parentParams.fromBizId,
        toBizObjs: multipleSelection.map(item => {
          return {
            toBizTypeId: parentParams.toBizTypeId,
            // 后端要求不为空，取type~
            toBizNo: parentParams.toBizType,
            toBizId: item.bizId,
            toBizType: parentParams.toBizType,
          };
        })
      };

      this.pending = true;

      addRelationshipRecord(params)
        .then(res => {
          if (res.success) {
            this.$message.success(this.$t('common.base.saveSuccess'));
            this.$emit('success');
            this.visible = false;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.pending = false;
        })
        .catch(err => console.error(err));

    },
    /* 跳转至n页 */
    jump(pageNum) {
      this.baseParams.pageNum = pageNum;
      this.onSearch();
    },
    /* 切换一页n条 */
    handleSizeChange(pageSize) {
      this.baseParams.pageNum = 1;
      this.baseParams.pageSize = pageSize;
      this.onSearch();
    },
  },
  components: {
    [ConnectorModuleConnectorCardMultiCardTable.name]: ConnectorModuleConnectorCardMultiCardTable
  }
};
</script>

<style lang="scss" scoped>
.card-create-dialog {
  :deep(.base-modal-body) {
    padding: 16px 24px;
  }

  .search-panel {
    margin-bottom: 16px;
    display: flex;
    
    .el-input {
      width: 300px;
      margin-right: 12px;
    }
  }

  .el-pagination {
    margin-top: 16px;

    :deep(.el-pagination__jump) {
      margin-left: 0;
    }
  }
}
</style>