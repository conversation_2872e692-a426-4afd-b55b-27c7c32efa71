
type ConnectorSettingCardItem = {
  id: string;
  // 连接器名称
  name: string;
  // 连接器描述
  description: string;
  // 连接器是否开启
  enabled: boolean;
  // 连接器类型
  type: string;
  // 连接器动作
  action: string;
  // 连接器最新更新时间
  lastUpdateTime: string;
  // 连接器最新更新人
  lastUpdateUser: string;
  staffId: string;
  // 连接器链接的模块
  leftModule: string;
  // 连接器链接的模块
  rightModule: string;
}

export type {
  ConnectorSettingCardItem
};
