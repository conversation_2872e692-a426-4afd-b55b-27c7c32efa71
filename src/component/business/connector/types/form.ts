import Tag from "@model/entity/Tag/Tag";
import { AppTypeEnum } from '@src/component/business/connector/model';

type ConnectorFormValueAddressType = {
  country?: string;
  // 省
  province: string;
  // 市
  city: string;
  // 区
  dist: string;
  address: string;
}

type ConnectorServerValueAddressType = {
  country?: string;
  // 省
  province: string;
  // 市
  city: string;
  // 区
  dist: string;
  // 地址
  address: string;
  // 纬度
  latitude: number;
  // 经度
  longitude: number;
  // 地址类型
  addressType: number;
  // 全地址
  all: string;
}

type ConnectorFormValueAttachmentType = {

}

type ConnectorServerValueAttachmentType = {
  // 附件 id
  id: string;
  // 附件名称
  fileName: string;
  // 文件大小
  size: number;
  // 文件大小
  fileSize: string;
  // 纬度
  latitude: number;
  // 附件 id
  fileId: string;
  // 附件地址
  url: string;
}

type ConnectorFormValueTextType = string;
type ConnectorServerValueTextType = string;

type ConnectorFormValueUserType = {
  // 用户 id
  id: string;
  // 用户名
  name: string;
  // 用于企微端显示人员名称
  staffId: string;
}

type ConnectorServerValueUserType = {
  // 用户 id
  id: string;
  // 用户名
  name: string;
  // 用于企微端显示人员名称
  staffId: string;
}

type ConnectorFormValueCustomerType = {
  // 用户 id
  id: string;
  // 用户名
  name: string;
  // 联系人
  lmPhone: string;
  // 客户编号
  serialNumber: string;
}

type ConnectorServerValueCustomerType = {
  // 客户 id
  id: string;
  // 客户名称
  name: string;
}

type ConnectorFormValueRelatedCustomersType = ConnectorFormValueCustomerType[]

type ConnectorServerValueRelatedCustomersType = ConnectorServerValueCustomerType[]

type ConnectorFormValueDateType = string[];

type ConnectorServerValueDateType = string[];

type ConnectorFormValueCustomerAddressType = {
  // 地址id
  id: string;
  // 地址全路径，用于回显
  address: string;
  // 地址全路径，用于回显
  label: string;
}

type ConnectorServerValueCustomerAddressType = {
  // 地址id
  id: string;
  // 地址全路径，用于回显
  name: string;
}

type ConnectorFormValueLinkmanType = {
  // 用户 id
  id: string;
  // 用户名
  name: string;
  // 联系人
  phone: string;
}

type ConnectorServerValueLinkmanType = {
  // 客户联系人 id
  id: string;
  // 客户联系人名称
  name: string;
}

type ConnectorFormValueProductType = {
  // 客户名称
  customerName: string;
  // 产品 id
  id: string;
  // 产品名称
  name: string;
  // 产品序列号
  serialNumber: string;
  // 产品类型
  type: string;
  // 客户id
  customerId: string;
}

type ConnectorServerValueProductType = {
  // 产品 id
  id: string;
  // 产品 名称
  name: string;
  // 客户id
  customerId: string;
}

type ConnectorFormValueLocationType = {
  country?: string,
  // 省
  province: string;
  // 市
  city: string;
  // 区
  dist: string;
}

type ConnectorServerValueLocationType = {
  // 地址
  address: string;
  // 市
  city: string;
  // 国家
  country: string;
  // 区
  district: string;
  // 纬度
  latitude: number;
  // 经度
  longitude: number;
  // 省
  province: string;
  // 路
  road: string;
  // 街道编号
  streeNumber: string;
  // 时间
  time: number;
  // 是否有定位
  isHaveLocation: boolean;
}

type ConnectorFormValueRelationTaskType = {
  // 工单id
  taskId: string;
  // 工单编号
  taskNo: string;
  // 工单类型id
  templateId: string;
}

type ConnectorServerValueRelationTaskType = {
  // 工单id
  taskId: string;
  // 工单编号
  taskNo: string;
  // 工单类型id
  templateId: string;
}

type ConnectorFormValueTagsType = Tag[]

type ConnectorServerValueTagsType = Tag[]

// TODO: 类型
type ConnectorFormValueType = any
type ConnectorServerValueType = any


type ConnectorFormCascadeDataSourceType = {
  value: string;
  label: string;
  canSelect: boolean,
  children?: ConnectorFormCascadeDataSourceType[];
  // 表单类型
  bizType?: string;
  // 表单id
  bizTypeId?: string;
  icon?: string;
  appId?: string;
  leaf?: boolean;
  appType?: AppTypeEnum | null;
}

export {
  ConnectorFormValueAddressType,
  ConnectorServerValueAddressType,
  ConnectorFormValueAttachmentType,
  ConnectorServerValueAttachmentType,
  ConnectorFormValueUserType,
  ConnectorServerValueUserType,
  ConnectorFormValueType,
  ConnectorServerValueType,
  ConnectorFormValueCustomerType,
  ConnectorServerValueCustomerType,
  ConnectorFormValueCustomerAddressType,
  ConnectorServerValueCustomerAddressType,
  ConnectorFormValueLinkmanType,
  ConnectorServerValueLinkmanType,
  ConnectorFormValueProductType,
  ConnectorServerValueProductType,
  ConnectorFormValueLocationType,
  ConnectorServerValueLocationType,
  ConnectorFormValueRelationTaskType,
  ConnectorServerValueRelationTaskType,
  ConnectorFormValueRelatedCustomersType,
  ConnectorServerValueRelatedCustomersType,
  ConnectorFormValueDateType,
  ConnectorServerValueDateType,
  ConnectorFormValueTagsType,
  ConnectorServerValueTagsType,
  ConnectorFormValueTextType,
  ConnectorServerValueTextType,
  ConnectorFormCascadeDataSourceType
};
