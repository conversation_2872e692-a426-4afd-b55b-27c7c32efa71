/* model */
import { ConnectorConditionTypeEnum, ConnectorField } from '@src/component/business/connector/model';

// 远程搜索方法类型
type RemoteMethodType = (...args: any[]) => Promise<any>

// 组件绑定值类型
export type CompBindType = {
  // 占位符
  placeholder?: string;
  // 远程搜索方法
  remoteMethod?: RemoteMethodType;
  // 是否多选
  multiple?: boolean;
  // 是否折叠
  collapsed?: boolean;
  // 监听事件
  on?: Record<string, Function>;
};

type ConnectorRuleFormItemType = {
  parentEnName?: string;
  // id 唯一值
  id: string;
  // 字段
  field?: ConnectorField | null;
  // 值
  value: any;
  // 组件名称
  componentName: string;
  // 组件绑定数据
  bind: CompBindType;
  // 操作符
  operator: string;
  operators: string[];
  // 条件
  condition: ConnectorConditionTypeEnum;
  leftFieldValue: string;
  rightFieldValue: string;
  formType?:string;
  rightField?: Record<string, any>;
  required?: boolean
}

export {
  ConnectorRuleFormItemType
};
