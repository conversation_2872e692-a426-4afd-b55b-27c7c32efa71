
/* model */
import Page from '@model/Page';
import {
  ConnectorAppModuleListItem,
  ConnectorCardAdditionInfoButton,
  ConnectorCardAdditionInfoHeader,
  ConnectorEditOptions,
  ConnectorModule,
  ConnectorOptions
} from '@src/component/business/connector/model';

/// @ts-ignore
type ConnectorCardDataListResult = Page<Record<string, any>[]>;

type ConnectorDataDeleteResult = null;

type ConnectorGetModuleListResult = ConnectorModule[];

type ConnectorGetAppModuleListResult = ConnectorAppModuleListItem[];

type ConnectorGetAppModuleListItemResult = ConnectorAppModuleListItem[];

type ConnectorGetOptionsResult = ConnectorOptions;

type ConnectorGetEditOptionsResult = ConnectorEditOptions;

type ConnectorSaveOptionsResult = null;

type ConnectorAllowAddDataResult = boolean;

type ConnectorCardAdditionInfoResult = {
  header: ConnectorCardAdditionInfoHeader[];
  buttonList: ConnectorCardAdditionInfoButton[];
  // to 表单是否开启
  toBizTypeEnabled: boolean;
}

type ConnectorInsertSelectCallResult = Record<string, any>;

export {
  ConnectorCardDataListResult,
  ConnectorDataDeleteResult,
  ConnectorGetModuleListResult,
  ConnectorGetOptionsResult,
  ConnectorSaveOptionsResult,
  ConnectorAllowAddDataResult,
  ConnectorCardAdditionInfoResult,
  ConnectorGetEditOptionsResult,
  ConnectorInsertSelectCallResult,
  ConnectorGetAppModuleListResult,
  ConnectorGetAppModuleListItemResult
}
