/* model */
import { CardInputTypeEnum } from '@model/enum/CardEnum';
import {
  ConnectorBizTypeEnum,
  ConnectorOptionsAction,
  ConnectorOptionsFieldOption
} from '@src/component/business/connector/model';

type ConnectorSaveOptionsParamsActionListItem = Omit<ConnectorOptionsAction, 'toRequestFieldList' | 'toResponseFieldList' | 'selected'>

type ConnectorCommonParams = {
  // from 表单的id
  fromBizTypeId: string;
  // from 表单业务类型
  fromBizType: ConnectorBizTypeEnum;
  // to 表单的id
  toBizTypeId: string;
  // to 表单业务类型
  toBizType: ConnectorBizTypeEnum;
}

type ConnectorFromCommonParams = {
  // 业务类型，TASK-工单，PAAS-paas
  fromBizType: ConnectorBizTypeEnum;
  // 业务类型id，比如工单类型id，paas表单类型id
  fromBizTypeId: string;
  // 业务主键id
  fromBizId: string;
  // 终点业务类型 TASK-工单，PAAS-paas
  toBizType: ConnectorBizTypeEnum;
  // 终点业务类型id
  toBizTypeId: string;
}

type ConnectorCardDataListParams = {
  // 业务主键id
  fromBizId: string;
  fromBizNo: string;
  // 页码
  pageNum: number;
  // 每页数量
  pageSize: number;

} & ConnectorCommonParams;

type ConnectorDataDeleteParams = {
  // 删除记录id集合
  bizIdList: string[];
  // 业务类型id
  toBizTypeId: string;
  // 业务类型
  toBizType: ConnectorBizTypeEnum;
}
type ConnectorDataRelationParams = {
  fromBizType: string;
  fromBizTypeId: string;
  fromBizId: string;
  toBizObjs: {
    toBizTypeId: string;
    toBizNo: string;
    toBizId: string;
    toBizType: string;
  }[]
}
type ConnectorDataDisassociationParams = {
  fromBizTypeId: string;
  fromBizId: string;
  toBizObjs: {
    toBizId: string;
    toBizTypeId: string;
  }[]
}

type ConnectorGetModuleListParams = {
  // 如果是to, 则需要过滤from的bizTypeId
  fromBizTypeId?: string;
  toBizType?: string;
}

type ConnectorGetOptionsParams = ConnectorCommonParams;

type ConnectorGetEditOptionsParams = {
  fromBizTypeId: string;
  toBizTypeId: string;
  fromBizType: ConnectorBizTypeEnum;
  toBizType: ConnectorBizTypeEnum;
  isToThird?: boolean
}

// 表单设计器连接器组件查询toFieldList
type ConnectorGetFormOptionsParams = {
  bizType: string;
  bizTypeId: string;
}

type ConnectorGetModuleListItemParams = {
  // 如果是to, 则需要过滤from的bizTypeId
  appId?: string;
}

type ConnectorSaveOptionsParams = {
  // 用于区分编辑还是新增
  additionalId: number | null;
  // 连接器名称
  connectorName?: string;
  // 连接器说明
  connectorRemarks?: string;
  // 连接器名称国际化语言
  titleLanguage?: any,
  // 连接器说明国际化语言
  descLanguage?: any,
  // from 表单业务类型
  fromBizType: ConnectorBizTypeEnum;
  // from 表单id
  fromBizTypeId: string;
  // to 表单id
  toBizTypeId: string;
  // to 表单业务类型
  toBizType: ConnectorBizTypeEnum;
  // to 表单logo
  toBizTypeLogo: string;
  // to 表单名称
  toBizTypeName: string;
  // 执行动作列表
  actionList: ConnectorSaveOptionsParamsActionListItem[];
  // 新建插入配置规则
  insertFieldOptionsList?: ConnectorOptionsFieldOption[];
  // 查询配置规则
  selectFieldOptionsList?: ConnectorOptionsFieldOption[];
  // 附加组件类型 单次 或 多次
  addTime: CardInputTypeEnum,
  // 表格显示的表单字段列表
  showFieldNameList: string[];
  // 查询范围设置条件 1 忽略此条件 2 不展示数据
  conditionalLimit: string;
}

type ConnectorAllowAddDataParams = ConnectorFromCommonParams;

type ConnectorCardAdditionInfoParams = ConnectorCommonParams;

type ConnectorInsertSelectCallParams = ConnectorFromCommonParams;

type ConnectorPaasDialogMessageData = ConnectorFromCommonParams & {
  isCreate?: boolean;
  isEdit?: boolean;
};

export {
  ConnectorCardDataListParams,
  ConnectorDataDeleteParams,
  ConnectorDataRelationParams,
  ConnectorDataDisassociationParams,
  ConnectorGetModuleListParams,
  ConnectorGetOptionsParams,
  ConnectorSaveOptionsParams,
  ConnectorAllowAddDataParams,
  ConnectorCardAdditionInfoParams,
  ConnectorGetEditOptionsParams,
  ConnectorInsertSelectCallParams,
  ConnectorPaasDialogMessageData,
  ConnectorGetFormOptionsParams,
  ConnectorGetModuleListItemParams,
  ConnectorSaveOptionsParamsActionListItem
};
