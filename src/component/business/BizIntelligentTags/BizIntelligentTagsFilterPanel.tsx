import { defineComponent, computed, toRefs, ref, watch, unref, PropType, onBeforeUnmount } from "vue";
import { processConcatGroupList } from "./BizIntelligentTaggingButton";
import { TagsItem } from './BizIntelligentTagsView';
import BizIntelligentTagsGroupItem from './components/BizIntelligentTagsGroupItem';
import { getAssetsForOssUrl} from "pub-bbx-utils";
import locales, { t } from "@src/locales";
import './style/BizIntelligentTagsFilterPanel.scss';
/* utils */
import { getTagsGroupToTagsList } from '@src/business/intelligentTags/utils/utils'

const searchEmptyImg = getAssetsForOssUrl('search_icon.png', locales.locale);

export interface CustomOperatorListItem {
    icon: string,
    text: string,
    id: string
}

export interface TagsGroupListItem {
    groupName: string,
    labelList: TagsItem[],
    type: number,
    personalLabel: number | string
}

export interface TagFilterSelectValueItem {
  value: string | number
}

const defaultCustomOperatorList: CustomOperatorListItem[] = [{
  icon: 'icon-fdn-cascader',
  text: t('common.base.intelligentTag.all'),
  id: 'all-tags'
},
// {
//     icon: 'icon-jinzhi',
//     text: '无标签',
//     id: 'no-tags'
// }
];

export default defineComponent({
  name: 'BizIntelligentTagsFilterPanel',
  props: {
    // 是否显示面板
    show: {
      type: Boolean,
      required: true,
      default: ()=> true
    },
    // 是否需要对应的关联标签switch
    showLinkSwitch: {
      type: Boolean,
      default: ()=> true
    },
    // 当前面板选过滤条件选中的值（有可能是标签项也有可能是写死的比如全部或者无标签）
    value: {
      type: Array as PropType<TagFilterSelectValueItem []>,
      default: ()=> ([{value: 'all-tags'}])
    },
    // 标签的分组列表
    tagsGroupList: {
      type: Array as PropType<TagsGroupListItem []>,
      default: ()=> []
    },
    // 标签请求的远程方法 （remoteFetchFun优先级大于tagsGroupList）
    remoteFetchFun: {
      type: Function
    },
    // 标签请求远程方法的ids
    remoteFetchIdsFun: {
      type: Function
    },
    // 自定义操作列表
    customerOperatorList: {
      type: Array as PropType<CustomOperatorListItem []>,
      default: ()=> defaultCustomOperatorList
    },
    showLinkTagsSwitchValue: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示标签设置按钮
    showLabelSettingSwitch: {
      type: Boolean,
      default: ()=> true
    },
    // 显示标签名称默认的值
    showLabelSettingSwitchValue: {
      type: Boolean,
      default: ()=> true
    }
  },
  setup(props, { emit, expose }) {
    const { tagsGroupList, show, value, remoteFetchFun, remoteFetchIdsFun, customerOperatorList, showLinkSwitch, showLinkTagsSwitchValue, showLabelSettingSwitch, showLabelSettingSwitchValue } = toRefs(props);
    // 相关请求的分页以及关键字搜索的默认参数
    const remoteFetchFunBaseParams = ref({ pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false });
    // 相关请求的分页以及关键字搜索的默认参数 ids 用于数据变更
    const remoteFetchFunBaseIdsParams = ref({ pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false });
    // 是否显示对应的请求 loading
    const showFetchLoading = ref(false);
    // 标签分组列表
    const fetchTagsGroupDataList = ref<TagsGroupListItem[]>([]);

    // 标签分组列表ids数据
    const fetchTagsGroupDataIdsList = ref<TagsGroupListItem[]>([]);

    // 请求次数
    const fetchCount = ref<number>(10);
    // 定时器实例
    const timer =  ref<ReturnType<typeof setInterval> | null>(null);

    // 是否有远程请求方法
    const hasRemoteFetchFun = computed(()=> {
      return typeof remoteFetchFun.value === 'function';
    });

    // 是否存在远程请求方法 ids
    const hasRemoteFetchFunIds = computed(() => {
      return typeof remoteFetchIdsFun.value === 'function';
    });

    // 当前标签分组
    const currentTagsGroupList = computed(()=> {
      if(hasRemoteFetchFun.value) {
        return fetchTagsGroupDataList.value;
      }
      return tagsGroupList.value;
    });


    /**
     * @des 标签设置显示点击事件
     */
    const handleLabelSettingSwitchChange = (v: boolean)=> {
      emit('labelSettingSwitchChange', v);
    };

    /**
     * @des 远程请求方法
     * @param {any} merge=false
     * @param {any} loading=true
     * @returns {any}
     */
    const remoteFetchData = async(merge = false, loading = true)=> {
      const showFetchLoadingFun = (loading: boolean = true)=> showFetchLoading.value = loading;
      const needLoading = !merge && loading;
      try {
        needLoading && showFetchLoadingFun();
        const params = { ...remoteFetchFunBaseParams.value };
        if(Reflect.has(params, 'hasNextPage')) {
          Reflect.deleteProperty(params, 'hasNextPage');
        }
        if(remoteFetchFun.value) {
          const { list, hasNextPage, pageNum, pageSize } = await remoteFetchFun.value(params);

          merge ? fetchTagsGroupDataList.value = processConcatGroupList(fetchTagsGroupDataList.value, list.slice(0)) : fetchTagsGroupDataList.value = list;
          needLoading && showFetchLoadingFun(false);
          remoteFetchFunBaseParams.value = { ...remoteFetchFunBaseParams.value, hasNextPage, pageNum, pageSize };
        }
      } catch (error) {
        console.error('[remoteFetchFun error]', error);
      } finally {
        showFetchLoadingFun(false);
      }
    };

    /**
     * @des 远程请求方法ids请求方法
     * @param {any} merge=false
     * @param {any} loading=true
     * @returns {any}
     */
    const remoteFetchDataIds = async(merge = false, loading = true, cb: Function)=> {
      try {
        const { hasNextPage: _, ...params } = { ...remoteFetchFunBaseIdsParams.value}
        if(remoteFetchIdsFun.value) {
          const res = await remoteFetchIdsFun.value(params);

          fetchTagsGroupDataIdsList.value = res;

          typeof cb === 'function' && cb(res);
        }
      } catch (error) {
        console.error('[remoteFetchFun error]', error);
      }
    };

      /**
     * @desc 删除数据后的ids请求
     * @param isPolling Boolean 是否轮询
     */
      const deleteTagFetch = (isPolling = false) => {
        if (!show.value) return
        if (!hasRemoteFetchFunIds.value) return
        if (isPolling) {
            restFetch()
        } else {
            plainRestFetch()
        }
    }

    /**
     * @des 重置对应的基础请求参数
     * @returns {any}
     */
    const resetFetchParamsData = ()=> {
      remoteFetchFunBaseParams.value = { pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false  };
    };

    /**
     * @des 加载更多
     * @returns {any}
     */
    const loadMore = async ()=> {
      if(remoteFetchFunBaseParams.value.hasNextPage) {
        remoteFetchFunBaseParams.value.pageNum += 1;
        if(hasRemoteFetchFun.value) {
          await remoteFetchData(true);
        }
        emit('loadMore');
      }
    };


    /**
     * @des 关闭点击事件
     * @returns {any}
     */
    const handleCloseIconClick =()=> {
      emit('close', show.value);
    };

    /**
     * @des 标签点击事件
     * @param {any} tagItem:TagsItem
     * @param {any} index:number
     * @returns {any}
     */
    const handleTagItemClick = (tagItem: TagsItem, index: number)=> {
      // @ts-ignore
      const noExistLabel = filterGoSessionLoaclLabelOrExist(value.value)
      emit('filterNoExistItemOfClick', noExistLabel)
      emit('handleTagItemClick', tagItem, index);
      emit('filterItemClick', tagItem);
    };

    /**
     * @description 过滤掉不存在标签 不存在标签来自使用统计情况跳转过来的数据，会存在一些不存在的标签
     * 如果用户从使用统计情况跳转过来，第一次是不需要过滤标签的，但是用户再一次点击就要清空不存在标签的情况
     * @param tagItemIdList [{value: 1}] 点击的标签id列表
     * @return id 列表 返回需要过滤的标签id列表
     */
    const filterGoSessionLoaclLabelOrExist = (tagItemIdList: Record<'value', number>[]) => {
      if (!tagItemIdList && !Array.isArray(tagItemIdList)) return 
      const tagSet = new Set<number>();
      const tagsGroupToTagsList = getTagsGroupToTagsList(currentTagsGroupList.value);
  
      for (const item of tagItemIdList) {
          if (!tagsGroupToTagsList.some(tag => tag.id === item.value)) {
              tagSet.add(item.value);
          }
      }
  
      return tagSet;
    }

    /**
     * @des 操作栏点击事件
     * @param {any} operatorItem:any
     * @returns {any}
     */
    const handleOperatorItemClick = (operatorItem: any)=> {
      emit('filterItemClick', operatorItem);
    };

    /**
     * @des 关联标签显示点击事件
     * @param {any} v:boolean
     * @returns {any}
     */
    const handleLinkSwitchChange = (v: boolean)=> {
      emit('linkTagsSwitchChange', v);
      // showLinkTagsSwitchValue.value = v;
    };

    /**
     * @des 相关空数据的时候点击事件
     */
    const handleEmptyLinkClick = ()=> {
      emit('emptyLinkClick');
    };

    /**
     * @des 初始化
     * @returns {any}
     */
    const init = async () => {
      if(!hasRemoteFetchFun.value) return;
      // 相关远程搜索
      resetFetchParamsData();
      await remoteFetchData();
    };

    /**
     * @des 不用调用十次的逻辑函数
     */
    const plainRestFetch = async () => {
      if (fetchCount.value <= 0) {
          fetchCount.value = 10
      }
      if(!hasRemoteFetchFunIds.value) return

      try {
          await remoteFetchData(false, false);
      } catch (error) {
          console.error('plainRestFetch error:', error)
      }
    }

    /**
     * @des 打标之后数据更新
     */
    const restFetch = async () => {
      if (fetchCount.value <= 0) {
        fetchCount.value = 10;
      }

      if(!hasRemoteFetchFunIds.value) return;

      clearFetchTimer();

      await remoteFetchData(false, false);

      timer.value = setInterval(async () => {
        if (fetchCount.value > 0) {
          await remoteFetchDataIds(false, false, cbIds);
          fetchCount.value--;
        } else {
          // 清除定时器
          clearFetchTimer();
        }
      }, 1000);
    };

    // 更改count回调执行函数
    const cbIds = (value: Record<number, number>) => {
      if (!value) return;
      if (!fetchTagsGroupDataList.value) return;
      fetchTagsGroupDataList.value.forEach((item: any) => {
        if (!item?.labelList && !Array.isArray(item?.labelList)) return;
        item?.labelList.forEach((label: any) => {
          if (label.hasOwnProperty('id')) {
            label.count = value?.[label?.id] || 0;
          }
        });
      });
    };

    const clearFetchTimer = () => {
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
      restFetchCount();
    };

    // 重置请求参数次数
    const restFetchCount = () => {
      fetchCount.value = 10;
    };

    onBeforeUnmount(()=> {
      clearFetchTimer();
    });

    expose({
      restFetch,
      plainRestFetch,
      deleteTagFetch,
      clearFetchTimer
    });

    watch(()=> show.value, (n)=> n && init());

    return ()=> (
      <transition name='transform-left-fade'>
        { show.value
          ? <div class="biz-intelligent-tags__filter-panel">
            <div class="biz-intelligent-tags__filter-panel-top">
              <div class="biz-intelligent-tags__filter-panel-title">
                <h1 class="title">{t('common.label.tag')}</h1>
                <i class="iconfont icon-xiangzuo" onClick={handleCloseIconClick}></i>
              </div>
              {
                customerOperatorList.value.map(item=> (
                  <div
                    class={["biz-intelligent-tags__filter-panel-operator", unref(value).some(v=> v.value == item.id) ? 'active': null]}
                    onClick={()=> handleOperatorItemClick(item)}>
                    <i class={["iconfont", item.icon]}></i>
                    <span class="text">{item.text}</span>
                  </div>
                ))
              }
            </div>
            <div class={['biz-intelligent-tags__filter-panel-content',! showLinkSwitch.value ? 'biz-intelligent-tags__filter-panel-padding' : null]}
              {...{
                directives: [{
                  name: 'infinite-scroll', value: ()=> loadMore()
                }]
              }}
              infinite-scroll-distance={10}
              infinite-scroll-delay={400}>
              {showFetchLoading.value ?
                <el-skeleton class="biz-intelligent-tags__filter-panel-skeleton"/>
                :
                currentTagsGroupList.value.length
                  ? currentTagsGroupList.value.map((item, index)=> (
                    <BizIntelligentTagsGroupItem
                      tagGroup={item}
                      value={unref(value)}
                      intelligentType={false}
                      onTagItemClick={(tagItem: TagsItem)=> handleTagItemClick(tagItem, index)}
                      showTagNum
                      showGroupCollapse
                    />
                  ))
                  :
                  <div class="biz-intelligent-tags__filter-panel-empty">
                    <img class="biz-intelligent-tags__empty-img" src={searchEmptyImg} alt="" />
                    <span class="biz-intelligent-tags__empty-text">{t('common.base.intelligentTag.unSetting')}</span>
                    <a class="biz-intelligent-tags__empty-link" href="javascript:void(0)" onClick={handleEmptyLinkClick}>{t('common.base.intelligentTag.goSetting')}</a>
                  </div>
              }
            </div>
            <div class="biz-intelligent-tags__filter-panel-bottom">
              {
                showLabelSettingSwitch.value
                  ? (
                    <div class="biz-intelligent-tags__filter-panel-bottom-item">
                      <div>
                        <span class="text">{t('common.base.intelligentTag.showTagName')}</span>
                        <el-tooltip content={t('common.base.intelligentTag.listIsShowLinkTagName')} placement='top'>
                          <i class="iconfont icon-question-circle"></i>
                        </el-tooltip>
                      </div>
                      <div>
                        <el-switch
                          value={showLabelSettingSwitchValue.value}
                          onChange={handleLabelSettingSwitchChange}
                        >
                        </el-switch>
                      </div>
                    </div>
                  )
                  : null
              }
              {
                showLinkSwitch.value
                  ? (
                    <div class="biz-intelligent-tags__filter-panel-bottom-item">
                      <div>
                        <span class="text">{t('common.base.intelligentTag.showLinkTag')}</span>
                        <el-tooltip content={t('common.base.intelligentTag.listIsShowLinkTag')} placement='top'>
                          <i class="iconfont icon-question-circle"></i>
                        </el-tooltip>
                      </div>
                      <el-switch
                        value={showLinkTagsSwitchValue.value}
                        onChange={handleLinkSwitchChange}
                      >
                      </el-switch>
                    </div>
                  )
                  : null
              }
            </div>

          </div>
          : null
        }
      </transition>
    );
  }
});
