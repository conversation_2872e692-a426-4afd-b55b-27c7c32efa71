import { defineComponent } from "vue";
import { t } from "@src/locales";
import './style/BizIntelligentTagsFilterPanelOperatorButton.scss';


export default defineComponent({
  name: 'BizIntelligentTagsFilterPanelOperatorButton',
  props: {
    // 是否是选中也就是高亮
    active: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示右上角的红点
    showDot: {
      type: Boolean,
      default: ()=> false
    }
  },
  setup(props, { emit }) {
    const handleClick = ()=> {
      emit('click', !props.active);
    };
    return ()=> (
      <el-tooltip className="item" effect="dark" content={t('common.base.intelligentTag.tagFilter')} placement="top">
        <div class={["biz-intelligent-tags__operator-button", props.active ? 'active' : null]} onClick={handleClick}>
          <i class="iconfont icon-biaoqian-xian"></i>
          {props.showDot ? <div class="biz-intelligent-tags__operator-button-dot"></div> : null}
        </div>
      </el-tooltip>
    );
  }
});
