// 该组件只是对BizIntelligentTagsView组件的扩张，对config数据的侦听
import { defineComponent, ref, onBeforeMount, onMounted, onBeforeUnmount, getCurrentInstance, toRef } from 'vue'
import BizIntelligentTagsView from './BizIntelligentTagsView'
/* utils */
import { getRootWindow } from '@src/util/dom';
import {isFunction} from "lodash";

export default defineComponent({
  name: 'BizIntelligentTagsViewToConfig',
  props: {
    config: {
        type: Object,
        default: () => ({})
    },
    type:{
        type: String,
        default: 'table'
    }
  },
  setup(props: any) {
    const configData = ref(props.config)
    const type = toRef(props, 'type')

    const getLabelInfo = getRootWindow(window)?.getLabelInfo

    const showTable = (value: number) => {
        const changeType = value ? 'text' : 'icon'
        if(type.value === 'detail') {
            configData.value = { ...configData.value, normalShowType:  changeType }
        } else {
            configData.value = { ...configData.value, tableShowType:  changeType }
        }
    }

    const init = () => {
        try {
            const labelInfo = isFunction(getLabelInfo) ? getLabelInfo() : { labelTableShow: false }
            if (!labelInfo) return
            configData.value = {
                ...configData.value
            }
            showTable(labelInfo?.labelTableShow)
        } catch (error) {
            console.warn(error)
        }
    }

    const listenIframeHandler = (event: MessageEvent) => {
        const { action, data } = event.data
        const labelTableShow = data?.labelTableShow || 0
        if (action == 'changeAllLabelView') {
            showTable(labelTableShow)
        }
    }

    onBeforeMount(() => {
        init()
    })

    onMounted(() => {
        window.addEventListener('message', listenIframeHandler)
    })

    onBeforeUnmount(() => {
        window.removeEventListener('message', listenIframeHandler)
    })

    return {
        configData
    }
  },
  render() {
      return (
        <BizIntelligentTagsView
            {
                ...{
                    props: this.$attrs,
                    on: this.$listeners
                }

            }
            config={this.configData}
            type={this.type}
        />
      )
  }
})
