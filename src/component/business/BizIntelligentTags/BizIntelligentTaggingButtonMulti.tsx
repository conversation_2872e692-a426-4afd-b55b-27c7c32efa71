import { computed, defineComponent, onMounted, PropType, ref, toRefs, unref, watchEffect, getCurrentInstance, nextTick } from "vue";
import { Fragment } from "vue-frag";
/* utils */
//@ts-ignore
import clickOutside from 'element-ui/lib/utils/clickoutside';
import { cloneDeep, debounce } from 'lodash';
import { getAssetsForOssUrl, } from 'pub-bbx-utils';
import { t } from "@src/locales";
/* model */
import { TagsItem } from "./BizIntelligentTagsView";
import { TagsGroupListItem } from './BizIntelligentTagsFilterPanel';
/* style */
import './style/BizIntelligentTaggingButtonMulti.scss';
import { processConcatGroupList } from "./BizIntelligentTaggingButton";
import locales from "@src/locales";

const searchEmptyImg = getAssetsForOssUrl('search_icon.png', locales.locale);
const noDataImg =  getAssetsForOssUrl('/no_data.png',  locales.locale);

export default defineComponent({
  name: 'BizIntelligentTaggingButtonMulti',
  props: {
    // 标签的分组列表
    tagsGroupList: {
      type: Array as PropType<TagsItem[]>,
      default: ()=> []
    },
    // 标签请求的远程方法 （remoteFetchFun优先级大于tagsGroupList）
    remoteFetchFun: {
      type: Function
    },
    // 默认勾选的 tagArray 数据数组
    value: {
      type: Array as PropType<Partial<TagsItem>[]>,
      default: ()=> []
    },
    // popover显示的位置
    placement: {
      type: String,
      default: ()=> 'left-start'
    },
    // 打开对应的 popover 前置方法 如果是方法 返回 true 才能显示
    openPopoverHook: {
      type: Function,
    },
    // 按钮是否是disabled
    disabled: {
      type: Boolean,
      default: ()=> false
    },
    // 对应接口操作类型 setMethod  设置标签方式：1，增量；2，覆盖；3，删除
    operatorType: {
      type: Number,
      default: ()=> 0
    },
    // 是否显示添加标签的文字
    showText: {
      type: Boolean,
      default: ()=> true
    },
    // 按钮文案
    buttonText: {
      type: String,
      default: ()=> t('common.label.tag')
    },
    // 是否远程搜索
    remoteSearch: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示
    show: {
      type: Boolean,
      default: ()=> true
    }
  },
  directives:{
    clickOutside
  },
  setup(props, { emit, expose }) {
    const { remoteFetchFun, placement, tagsGroupList: propTagsGroupList, openPopoverHook, disabled, operatorType, value, showText, remoteSearch, buttonText, show } = toRefs(props);
    const { proxy: ctx } = getCurrentInstance() as unknown as any;
    const remoteFetchFunBaseParams = ref({ pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false  });
    const selectRef = ref(null);
    const tagsGroupList = ref<TagsGroupListItem[]>([]);
    const showFetchLoading = ref(false);
    const showPopover = ref(false);
    const fetchTagsGroupDataList = ref<TagsGroupListItem[]>([]);
    const searchGroupTagValue = ref<TagsGroupListItem[]>([]);

    const currentSelectTagsValue = ref<TagsItem[]>([]);
    // 判断当前的remoteFetchFun是否是一个方法
    const hasRemoteFetchFun = computed(()=> {
      return typeof remoteFetchFun.value === 'function';
    });

    const searchValue = computed(()=> remoteFetchFunBaseParams.value.keyword);

    // 获取当前的 groupTagList
    const currentTagsGroupList = computed({
      set(v: any[]) {
        hasRemoteFetchFun.value ?
          fetchTagsGroupDataList.value = v
          :
          tagsGroupList.value = v;
      },
      get() {
        // 如果有搜索的话直接返回搜索的值
        if(searchValue.value && !remoteSearch.value) return searchGroupTagValue.value;
        // 如果有有远程方法直接返回远程方法返回的值
        if(hasRemoteFetchFun.value) {
          return fetchTagsGroupDataList.value;
        }
        return tagsGroupList.value;
      }
    });

    // 当前的 input的事件
    const handleSearchInput = (val: string)=> {
      remoteFetchFunBaseParams.value.keyword = val;
      handleForSearch();
    };

    /**
         * @des 相关搜搜方法
         * @param {any} (
         * @returns {any}
         */
    const handleForSearch = debounce(async ()=> {
      if(remoteSearch.value && hasRemoteFetchFun.value) {
        remoteFetchFunBaseParams.value.pageNum = 1;
        return await remoteFetchData(false ,false);
      }
      const dataList = cloneDeep(fetchTagsGroupDataList.value.length ? fetchTagsGroupDataList.value : tagsGroupList.value);
      const result = dataList.reduce((acc: TagsGroupListItem[], item: any)=> {
        if(Array.isArray(item.labelList)) {
          item.labelList = item.labelList.filter((childItem: any) => searchValue.value ? childItem.name.includes(searchValue.value) : true);
        }
        if(item.labelList.length) {
          acc.push(item);
        }
        return acc;
      }, []);
      searchGroupTagValue.value = result;
    }, 400);

    /**
         * @des 设置对应的标签是否勾选中方法
         * @param {any} tagGroupList:any[]
         * @returns {any}
         */
    const setDefaultDataForGroupTagsList = (tagGroupList: any[], )=> {
      const defaultValue = unref(value);
      return tagGroupList.map(groupItem=> {
        if(Array.isArray(groupItem.labelList)) {
          groupItem.labelList.map((tagItem: any)=> {
            tagItem.checked = defaultValue.some((item: any)=> tagItem.id === item.id);
            return tagItem;
          });
        }
        groupItem.groupName = `${groupItem.groupName}（${groupItem.labelList ? groupItem.labelList.length : 0}）`
        return groupItem;
      });
    };

    /**
         * @des 远程搜索方法
         * @param {any} merge=false
         * @param {any} loading=true
         * @returns {any}
         */
    const remoteFetchData = async(merge = false, loading = true)=> {
      const showFetchLoadingFun = (loading: boolean = true)=> showFetchLoading.value = loading;
      const needLoading = !merge && loading;
      try {
        needLoading && showFetchLoadingFun();
        const params = { ...remoteFetchFunBaseParams.value, count: false };
        if(Reflect.has(params, 'hasNextPage')) {
          Reflect.deleteProperty(params, 'hasNextPage');
        }
        if(remoteFetchFun.value) {
          const { list, hasNextPage, pageNum, pageSize } = await remoteFetchFun.value(params);
          merge ? fetchTagsGroupDataList.value = setDefaultDataForGroupTagsList(processConcatGroupList(fetchTagsGroupDataList.value, list.slice(0))) : fetchTagsGroupDataList.value = setDefaultDataForGroupTagsList(list);
          needLoading && showFetchLoadingFun(false);
          remoteFetchFunBaseParams.value = { ...remoteFetchFunBaseParams.value, hasNextPage, pageNum, pageSize };
        }
      } catch (error) {
        console.error('[remoteFetchFun error]', error);
      } finally {
        showFetchLoadingFun(false);
      }
    };


    /**
         * @des 显示 popover 方法
         * @returns {any}
         */
    const handleShowPopover = async () => {
      if(openPopoverHook.value && typeof openPopoverHook.value === 'function') {
        if(!(await openPopoverHook.value())) return;
      }

      // 判断是是已经显示
      if(showPopover.value) return showPopover.value = false;
      // 判断是否有hasRemoteFetchFun没有的话直接显示
      if(!hasRemoteFetchFun.value) return showPopover.value = true;
      // 判断remoteFetchFun可能没有值的情况
      resetFetchParamsData();
      await remoteFetchData();
      showPopover.value = true;

    };


    /**
         * @des 重置对应的基础远程请求参数
         * @returns {any}
         */
    const resetFetchParamsData = ()=> {
      remoteFetchFunBaseParams.value = { pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false  };
      currentSelectTagsValue.value = [];
    };

    /**
         * @des 加载更多请求方法
         * @returns {any}
         */
    const loadMore = async ()=> {
      if(remoteFetchFunBaseParams.value.hasNextPage) {
        remoteFetchFunBaseParams.value.pageNum += 1;
        if(hasRemoteFetchFun.value) {
          await remoteFetchData(true);
        }
        emit('loadMore');
      }
    };

    const getModalAttributes = () => {
      return {
        class: [
          'biz-intelligent-tagging__modal'
        ],
        props: {
          title: t('common.base.intelligentTag.editTag'),
          show: showPopover.value,
          width: '500px',
          appendToBody: true
        },
        on: {
          'update:show': () => {
            hidePopover();
          }
        }
      };
    };

    const handleSelectChange = (v: TagsItem[])=> {
      currentSelectTagsValue.value = v;
      emit('change', true, v);
    };


    watchEffect(()=> {
      tagsGroupList.value = setDefaultDataForGroupTagsList(cloneDeep(propTagsGroupList.value));
      searchGroupTagValue.value = tagsGroupList.value.slice(0);
    });

    const hidePopover = ()=> {
      emit('hidePopover');
      showPopover.value = false;
    };


    const handleManageTagsClick = ()=> {
      emit('manageTagsClick');
      hidePopover();
    };

    const handleCheck = (...args: any[])=> {
      emit('change', ...args);
    };

    const handleSave = ()=> {
      emit('save', operatorType.value);
    };

    const handleClear = (item: Partial<TagsItem>)=> {
      emit('clear', item);
    };

    expose({
      hidePopover
    });

    return ()=> (
      show.value ?
        //@ts-ignore
        <Fragment>
          <div class="biz-intelligent-tagging__box">
            <div class="biz-intelligent-tagging__button multi-button" onClick={handleShowPopover}>
              <i class="iconfont icon-tianjiabiaoqian"></i>
              {showText.value ? buttonText.value : null}
            </div>
          </div>
          <base-modal { ...getModalAttributes()}>
            <div class="biz-intelligent-tagging__box-modal">
              {
                value.value.length ?
                  <div class="biz-intelligent-tagging__box-item">
                    <div class="title">{t('common.base.intelligentTag.existTags')}</div>
                    <ul class="biz-intelligent-tagging__tag-list">
                      {
                        value.value.map((item) => (
                          <li class="biz-intelligent-tagging__tag-list-item">
                            <span class="txt">{item.name}</span>
                            <i class="iconfont icon-fe-close" onClick={() => handleClear(item)}></i>
                          </li>
                        ))
                      }
                    </ul>
                  </div>
                  :
                  null
              }

              <div class="biz-intelligent-tagging__box-item">
                <div class="title">{t('common.base.intelligentTag.multiAddTag')}</div>
                <el-select
                  ref={selectRef}
                  class="biz-intelligent-tag__select"
                  popper-class="biz-intelligent-tag__select-popper"
                  value-key="id"
                  placeholder={t('common.base.add') + t('common.label.tag')}
                  value={currentSelectTagsValue.value}
                  onChange={handleSelectChange}
                  remote-method={handleSearchInput}
                  remote
                  multiple
                  clearable
                  filterable
                  {...{
                    directives: [{
                      name: 'el-select-loadmore',
                      value: {
                        disabled: false,
                        callback: () => loadMore,
                        distance: 50,
                      }
                    }]
                  }}>
                  {showFetchLoading.value ?
                    <el-skeleton/>
                    :
                    currentTagsGroupList.value.map(groupItem => (
                      <el-option-group
                        label={groupItem.groupName}
                        key={groupItem.groupName}>
                        {groupItem.labelList.map((tagItem: TagsItem) => {
                          return (
                            <el-option label={tagItem.name} value={tagItem} key={tagItem.id}>
                              <div class="biz-intelligent-tags__option-item">
                                {
                                    tagItem?.personalLabel == '1' ? (
                                        <i class={["iconfont",  'icon-qizhi-mian']} style={{ color: tagItem.logoColor }}></i>
                                    ) : (
                                        <i class={["iconfont",  tagItem.labelType === 0 ? 'icon-zhinengbiao-mian': 'icon-biaoqian-mian']} style={{ color: tagItem.logoColor }}></i>
                                    )
                                }
                                <span>{tagItem.name}</span>
                              </div>
                            </el-option>
                          );
                        })}

                      </el-option-group>
                    ))
                  }
                </el-select>
              </div>
            </div>
            <div slot="footer" class="biz-intelligent-tagging__footer">
              <div class="biz-intelligent-tagging__modal-manage" onClick={handleManageTagsClick}>
                <i class="iconfont icon-setting"></i>
                <div class="biz-intelligent-tagging__modal-manage-left">
                  {/* <i class="iconfont icon-biaoqian-xian"></i> */}
                  <span class="text">{t('common.base.intelligentTag.manageTag')}</span>
                </div>
              </div>
              <div class="biz-intelligent-tagging__modal-button">
                <el-button type="ghost" onClick={hidePopover}>{t('common.base.cancel')}</el-button>
                <el-button type="primary" onClick={handleSave}>{t('common.base.save')}</el-button>
              </div>
            </div>
          </base-modal>
        </Fragment>
        :
        null
    );
  }
});
