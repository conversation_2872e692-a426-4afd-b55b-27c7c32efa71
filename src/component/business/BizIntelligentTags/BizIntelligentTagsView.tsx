import { computed, defineComponent, PropType, ref, toRefs, watch } from "vue";
import { t } from "@src/locales";
import { Fragment } from "vue-frag";
import './style/BizIntelligentTagsView.scss';


export interface TagsItem {
    id: number | string,
    name: string,
    logoColor: string,
    description?: string,
    updateTime?: string,
    enabled?: boolean | number,
    labelId?: number | string,
    labelType?: number,
    appList?: TagsModulesItem[],
    checked?: boolean,
    count?: number,
    personalLabel?: 0 | 1 | number | string
}
/**
 * @des 本地标签修改传递类型 定义
 */
export interface TagLabel {
  [key: string]: any
  id?: number | string,
  name: string,
  logoColor: string,
  status: number,
}

export interface TagsModulesItem {
    appId: string
    bizType: string
    name: string
    logo: string
    appName?: string
}

enum ShowType {
    TEXT = 'text',
    ICON = 'icon'
}

interface TagsViewConfig {
    normalMaxLength: number,
    normalShowType: ShowType,
    tableMaxLength: number,
    tableShowType: ShowType,
    calcFontSize: string,
    calcDistance: number
}

const defaultConfig = {
  normalMaxLength: 3,
  normalShowType: 'text',
  tableMaxLength: 3,
  tableShowType: ShowType.TEXT,
  calcFontSize: '14px',
  calcDistance: 10
};

export default defineComponent({
  name: 'BizIntelligentTagsView',
  props: {
    // 当前显示的值（在table 模式下使用）
    value: {
      type: String,
      default: ()=> ''
    },
    // 标签列表
    tagsList: {
      type: Array as PropType<TagsItem []>,
      default: ()=> []
    },
    // 类型
    type: {
      type: String,
      default: ()=> 'table'
    },
    // 对应显示配置项
    config: {
      type: Object as PropType<Partial<TagsViewConfig>>,
      default: ()=> (defaultConfig)
    },
    // table 模式下是否可以点击
    canClick: {
      type: Boolean,
      default: ()=> true
    },
    showMoreIcon: {
      type: Boolean,
      default: ()=> true
    }
  },
  setup(props, { emit, slots }) {
    const { value, tagsList, type, config, canClick, showMoreIcon: showMoreIconProp} = toRefs(props);
    const maxWidth = ref('auto');
    const currentConfig = computed(()=> ({...defaultConfig, ...config.value}));

    const normalOnlyShowIcon = computed(()=> currentConfig.value.normalShowType === 'icon');

    const normalMaxLength = computed(()=> currentConfig.value.normalMaxLength);


    const tableOnlyShowIcon = computed(()=> currentConfig.value.tableShowType === 'icon');

    const tableMaxLength = computed(()=> currentConfig.value.tableMaxLength);

    const calcFontSize = computed(()=> currentConfig.value.calcFontSize);

    const calcDistance = computed(()=> currentConfig.value.calcDistance);

    const onlyShowIcon = computed(()=> {
      if(type.value === 'table') return tableOnlyShowIcon.value;
      return normalOnlyShowIcon.value;
    });

    const handleLinkViewClick = (item?: TagsItem, index?: number)=> {
      if(!canClick.value) return;
      if(item) {
        return emit('viewClick', item, index, value.value, type.value);
      }
      emit('viewClick', item, index, value.value, type.value);
    };

    const getPageFont = ()=> {
      const htmlStyles = window.getComputedStyle(document.body);
      const fontFamily = htmlStyles.getPropertyValue('font-family');
      return fontFamily;
    };

    const getTextWidth = (text: string, font: string)=> {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d') as CanvasRenderingContext2D;
      context.font = font;
      return context.measureText(text).width;
    };

    const comparePxStringsToNumber = (nextValue: string, prevValue: string = '80px')=> {
      const num1 = parseFloat(nextValue.replace(/\p\x/g, ''));
      const num2 = parseFloat(prevValue.replace(/\p\x/g, ''));
      return [num2, num1];
    };

    const showMoreIcon = computed(()=> {
      return tagsList.value.length > (type.value === 'table'? tableMaxLength.value : normalMaxLength.value) && showMoreIconProp.value;
    });

    const templateColumnsPrev = computed(()=> {
      const [firstValue, nextValue] = comparePxStringsToNumber(maxWidth.value);
      return  nextValue > firstValue ? '80px' : `${ nextValue - 10}px`;
    });


    watch(()=> value.value, ()=> {
      if(type.value === 'table') {
        setTimeout(()=>maxWidth.value = getTextWidth(value.value, `${calcFontSize.value} ${getPageFont()}`) + calcDistance.value + 'px', 0);
      }
    }, {
      immediate: true
    });

    const renderTagView = (tagItem: TagsItem) => {
      return onlyShowIcon.value
        ?
        <el-tooltip class="item" popper-class="biz-intelligent-tags__tooltip" effect="dark" content={tagItem.name} placement="top">
          {
              tagItem.personalLabel == '1' ? (
                  <i class={["iconfont",  'icon-qizhi-mian']} style={{ color: tagItem.logoColor }}></i>
              ) : (
                  <i class={["iconfont",  tagItem.labelType === 0 ? 'icon-zhinengbiao-mian': 'icon-biaoqian-mian']} style={{ color: tagItem.logoColor }}></i>
              )
          }
        </el-tooltip>
        :
      // @ts-ignore
        <Fragment>
          {
            tagItem.personalLabel == '1' ? (
                <i class={["iconfont", 'icon-qizhi-mian']} style={{ color: tagItem.logoColor }}></i>
            ) : (
                <i class={["iconfont", tagItem.labelType === 0 ? 'icon-zhinengbiao-mian': 'icon-biaoqian-mian']} style={{ color: tagItem.logoColor }}></i>
            )
          }
          <span class="tag-text">{tagItem.name}</span>
        </Fragment>;

    };

    const renderShowMoreIcon = ()=> {
      return (
        showMoreIcon.value ?
          <li>
            <el-popover
              placement="top"
              trigger="click"
              popper-class="biz-intelligent-tags__view-more-popover"
            >
              <div slot="reference" class="biz-intelligent-tags__view-more-btn">
                <i class="iconfont icon-MoreOutlined" style="transform: rotate(90deg)"></i>
              </div>
              <div class="biz-intelligent-tags__view-more-box">
                <div class="biz-intelligent-tags__view-title">{ t('common.base.label') }</div>
                <ul class="biz-intelligent-tags__view-list">
                  {tagsList.value.map(tagItem=> {
                    return (
                      <li class="biz-intelligent-tags__view-list-item">
                        {
                            tagItem.personalLabel == '1' ? (
                                <i class={["iconfont", 'icon-qizhi-mian']} style={{ color: tagItem.logoColor }}></i>
                            ) : (
                                <i class={["iconfont", tagItem.labelType === 0 ? 'icon-zhinengbiao-mian': 'icon-biaoqian-mian']} style={{ color: tagItem.logoColor }}></i>
                            )
                        }
                        <span class="tag-text">{tagItem.name}</span>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </el-popover>
          </li>
          : null
      );
    };


    const TableColumnView = ()=> {
      return (
        <div class="biz-intelligent-tags__table-view table-blacklist" style={{gridTemplateColumns: `minmax(${templateColumnsPrev.value}, ${maxWidth.value}) auto`}}>
          { Array.isArray(value.value)
            ?
            <div class="biz-intelligent-tags__table-view-list">
              {
                value.value.map((item, index)=> {
                  return (
                    <a
                      href="javascript:void(0)"
                      class={[canClick.value ? 'biz-intelligent-tags__table-view-link' : 'biz-intelligent-tags__table-view-text']}
                      onClick={()=> handleLinkViewClick(item, index)}
                    >
                      {`${item} ${value.value.length - 1 !== index ? ',' : ''}`}
                    </a>
                  );
                })
              }
            </div>
            :
            <a
              href="javascript:void(0)"
              class={[canClick.value ? 'biz-intelligent-tags__table-view-link' : 'biz-intelligent-tags__table-view-text']}
              onClick={()=> handleLinkViewClick()}
            >
              {value.value}
            </a>
          }
          { tagsList.value.length ?
            <div class="biz-intelligent-tags__list-column-box">
              <ul class="biz-intelligent-tags__list-column">
                {tagsList.value.slice(0, tableMaxLength.value).map(tagItem=> {
                  return (
                    <li class={["biz-intelligent-tags__list-column-item", !tableOnlyShowIcon.value ?  "text-item" : null]}>
                      { renderTagView(tagItem) }
                    </li>
                  );
                })}

                {renderShowMoreIcon()}

              </ul>
            </div>
            :
            null
          }
        </div>
      );
    };

    const NormListView = ()=> {
      return (
        <div class="biz-intelligent-tags__view">
          <ul class="biz-intelligent-tags__view-list ">
            {tagsList.value.slice(0, normalMaxLength.value).map((tagItem)=> {
              return (
                <li class={["biz-intelligent-tags__view-list-item", onlyShowIcon.value ? 'icon-item': null]}>
                  { renderTagView(tagItem) }
                </li>
              );
            })}
            {renderShowMoreIcon()}
          </ul>

        </div>
      );
    };

    const EditColumnView = ()=> {
      return (
            <div class="biz-intelligent-tags__view">
                <ul class="biz-intelligent-tags__view-list edit"  onClick={(e: Event)=> e.stopPropagation()}>
                    {tagsList.value.slice(0, normalMaxLength.value).map((tagItem)=> {
                            return (
                                <li class={["biz-intelligent-tags__view-list-item omit", onlyShowIcon.value ? 'icon-item': null]}>
                                    { renderTagView(tagItem) }
                                </li>
                            )
                    })}
                    {
                        slots.default ? slots.default() : null
                    }
                </ul>
            </div>
        )
    } 

    if (type.value === 'edit') {
      return () => (
          <EditColumnView />
      )
    }

    return ()=> (
      type.value === 'table' ? <TableColumnView /> : <NormListView />
    );
  }
});
