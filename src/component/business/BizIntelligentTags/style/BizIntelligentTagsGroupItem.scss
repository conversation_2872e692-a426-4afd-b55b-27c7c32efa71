.biz-intelligent-tags__group-box{
    // padding: 0 8px;
    .biz-intelligent-tags__group-title{
        min-height: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 8px;
        font-size: 14px;
        color: $text-color-primary;
        line-height: 22px;
        &-lf{
            display: flex;
            align-items: center;
            margin-right: 6px;
            span {
                @include text-ellipsis();
            }
            .collapse-icon{
                margin-left: 8px;
                cursor: pointer;
            }
            .int-icon {
                color: #595959;
                font-weight: normal;
                margin-right: 8px;
                width: 16px;height: 16px;line-height: 1;
            }
        }
        &-rf{
            .caret-wrapper{
                display: flex;
                flex-direction: column;
                .caret-icon{
                    display: block;
                    font-size: 12px;
                    color: #BFBFBF;
                    cursor: pointer;
                    &.descending{
                        transform: translateY(-3px);
                        position: relative;
                        z-index: 2;
                    }
                    &.ascending{
                        transform: translateY(3px);
                    }
                }
                &.descending{
                    .descending{
                        color: $text-color-regular;
                    }
                }
                &.ascending{
                    .ascending{
                        color: $text-color-regular;
                    }
                }
            }
        }
    }
    .biz-intelligent-tags__group-item{
        padding: 0;
        // margin: 8px 0 0 0;
        margin: 0;
        .biz-intelligent-tags__item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 22px;
            line-height: 22px;
            padding: 4px 8px;
            box-sizing: content-box;
            cursor: default;
            border-radius: 4px;
            &:hover{
                background: #F5F8FA;
                .biz-intelligent-tags__item-right {
                    visibility: visible;
                }
            }
            &-left{
                display: flex;
                align-items: center;
            }
            &-right {
                visibility: hidden;
                width: 16px;height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            &-content{
                display: flex;
                align-items: center;
                .iconfont{
                    margin-right: 10px;
                    font-size: 14px;
                    flex-shrink: 0;
                }
                span {
                    color: $text-color-primary;
                    font-size: 14px;
                    line-height: 22px;
                    @include text-ellipsis();
                }
            }
            &-checkbox{
                margin-right: 10px;
                margin-top: -2px;
            }
            &-num {
                font-size: 14px;
                color: $text-color-secondary;
            }
            &.active{
                background: $color-primary-light-1;
                .biz-intelligent-tags__item-content{
                    span {
                        color: $color-primary-light-6;
                    }
                }
            }
        }
    }
}