@mixin flexCenter {
    display: flex;
    align-items: center;
    justify-content: center;
}
// 显示标签布局mixin
@mixin showLabelLayout {
    display: flex;
    flex-direction: column;
    max-height: 450px;
    min-height: 0;
    overflow: hidden;
}
.biz-intelligent-tagging{
    &__button{
        display: flex;
        align-items: center;
        font-size: 14px;
        color: $text-color-regular;
        cursor: pointer;
        line-height: 24px;
        .icon-t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>{
            margin-right: 4px;
            line-height: 24px;
        }
        &.formEdit {
            border: 1px dashed #CBD6E2;
            padding: 2px 8px;
            border-radius: 4px;
            color: #13c2c2;
            line-height: 1;
            height: 26px;
        }
    }
    &__popover {
        padding: 12px 0 !important;
        &-content{
            max-height: 450px;
            min-height: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        &-input{
            display: flex;
            align-items: center;
            background: #F5F8FA;
            margin: 0px 12px;
            input {
                width: 100px;
                border: none;
                background: #F5F8FA;
                border: none;
                flex: 1;
                height: 28px;
                padding: 0;
                padding-left: 10px;
            }
            &__list {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                width: 100%;
                overflow-y: auto;
                overflow-x: hidden;
                max-height: 64px;
                padding: 5px 10px;
                gap: 4px;
                .biz-list-item {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    white-space:nowrap;
                    background-color: #e6e8eb;
                    color: #595959;
                    border-radius: 4px;
                    padding: 2px 10px;
                    border-radius: 4px;
                    .iconfont {
                        font-size: 10px;
                        margin-left: 6px;
                        cursor: pointer;
                    }
                }
            }
        }
        &-manage {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            background: #F5F8FA;
            border-radius: 4px;
            // margin-top: 8px;
            height: 32px;
            flex-shrink: 0;
            margin: 8px 12px 0 12px;
            &-left{
                flex: 1;
                display: flex;
                align-items: center;
                line-height: 22px;
                .icon-biaoqian-xian{
                    color: $text-color-regular;
                    font-size: 14px;
                }
                .text{
                    font-size: 14px;
                    color: $text-color-primary;
                    margin-left: 6px;
                }
            }
            .icon-setting{
                font-size: 16px;
                color: $text-color-regular;
                cursor: pointer;
            }
        }
        &-tags-group{
            flex: 1;
            overflow: auto;
            min-height: 160px;
            margin: 8px 0;
            padding: 0 12px;
            // biz-intelligent-tags__group-box
            .biz-intelligent-tags__group-title{
                min-height: 30px !important;
            }
            .biz-intelligent-tags__group-box{
                &:not(:last-child){
                    margin-bottom: 6px;
                    .biz-intelligent-tags__group-item{
                        border-bottom: 1px solid #e0e3ea;
                        padding-bottom: 4px;
                        .biz-intelligent-tags__item-content span{
                            max-width: 204px;
                        }
                    }
                }
            }
            &::-webkit-scrollbar-thumb  {
                background-color: #fff;
            }
            &::-webkit-scrollbar-track {
                background-color: #fff;
            }
            &:hover{
                &::-webkit-scrollbar-thumb  {
                    background-color: #BFBFBF;
                }
                &::-webkit-scrollbar-track {
                    background-color: #fff;
                }
            }
        }
        &-bottom{
            padding: 10px 12px 0 12px;
            flex-shrink: 0;
            display: flex;
            justify-content: flex-end;
            box-sizing: border-box;
            position: relative;
            &::before{
                content: '';
                position: absolute;
                top: 0;
                left: 12px;
                width: calc(100% - 24px);
                height: 1px;
                background: #e0e3ea;
            }
        }
    }
}

.biz-intelligent-tags__group-empty{
    min-height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 24px;
    .text{
        font-size: 14px;
        color: #BFBFBF;
    }
    .img{
        width: 160px;
    }
}
.biz-intelligent-tagging__popover-tags-label {
    @include showLabelLayout;
}
// 个人标签展示样式
.biz-intelligent-tagging__popover-tags-personal {
    @include showLabelLayout;
    .ipt-box {
        gap: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        flex-shrink: 0;
        padding: 0 12px;
        .biz-intelligent-tagging__popover-input {
            flex: 1;
            margin: 0;
        }
        input{
            border: none;
            background: #F5F8FA;
            border-radius: 4px;
        }
    }
    .btn-add {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        @include flexCenter;
        border-radius: 4px;
        border: 1px solid #CBD6E2;
        cursor: pointer;
    }
    
}
.btn-grp-con {
    width: 100%;
    padding: 0px 12px 8px 12px;
    cursor: pointer;
    .el-radio-group {
        width: 100%;
        display: inline-flex;
    }
    .el-radio-button {
        flex: 1;
    }
    .el-radio-button__inner {
        width: 100%;
    }
}