@mixin flexCenter {
    display: flex;
    align-items: center;
}
.int-personal {
    width: 100%;
    padding: 0px 16px 0px 16px;
    background-color: #fff;
    &-head {
        @include flexCenter;
        .back {
            width: 16px;
            height: 16px;
            @include flexCenter;
            cursor: pointer;
            color: initial;
        }
        .title {
            text-align: center;
            margin: 0 auto;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            color: #262626;
        }
    }

    &-main {
        margin-top: 24px;
        .create {
            .el-button {
                width: 100%;
            }
        }
        .color-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .color-item {
                width: 24px;
                height: 24px;
                cursor: pointer;
                position: relative;
                .iconfont {
                    display: none
                }
                &.active {
                    .iconfont {
                        display: block;
                        position: absolute;
                        top: 50%;left: 50%;
                        translate: -50% -50%;
                        line-height: 1;
                        color: #fff;
                        width: 16px;height: 16px;
                    }
                }
            }
        }
        .edit-group {
            @include flexCenter;
            align-items: center;
            .el-button {
                flex: 1;
            }
        }
    }
}