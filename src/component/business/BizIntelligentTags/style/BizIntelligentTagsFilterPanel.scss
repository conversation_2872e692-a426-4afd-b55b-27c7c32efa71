.biz-intelligent-tags__filter-panel{
    width: 200px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding-left: 8px;
    background: #fff;
    border-radius: 4px;
    transition: all linear 0.25s;
    overflow: hidden;
    &-top{
        padding-right: 8px;
    }
    &-title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 12px;
        .title{
            font-size: 16px;
            color: $text-color-primary;
            line-height: 24px;
            margin-bottom: 0;
        }
        .iconfont{
            font-size: 12px;
            color: $text-color-regular;
            cursor: pointer;
        }
    }
    &-operator{
        height: 38px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 8px 12px;
        position: relative;
        border-radius: 4px;
        cursor: pointer;
        &:hover{
            background: #F5F8FA;
        }
        &:last-child{
            margin-bottom: 8px;
            &::before{
                content: '';
                display: block;
                width: calc(100% - 4px);
                height: 1px;
                background: #E4E7ED;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -8px;

            }
        }
        .iconfont{
            color: $text-color-regular;
            font-size: 16px;
        }
        .text{
            color: $text-color-primary;
            // line-height: 22px;
            margin-left:  10px;
            font-size: 14px;
        }
        &.active{
            background-color: var( --color-primary-light-2);
        }
    }
    &-content{
        flex: 1;
        box-sizing: border-box;
        padding-right: 8px;
        // padding: 8px 8px 8px 0;
        overflow-y: auto;
        .biz-intelligent-tags__group-title{
            cursor: default;
            padding: 0 8px 0 12px;
            min-height: 36px;
            &-lf{
                span {
                    max-width: 116px;
                    font-weight: 600;
                }
            }
        }
        .biz-intelligent-tags__item{
            padding: 7px 8px !important;
            &-content{
                .iconfont{
                    margin-left: 3px;
                }
                span {
                    max-width: 116px;
                }
            }
            margin-top: 2px;
        }
        .tags-item-active{

        }
        &::-webkit-scrollbar-thumb  {
            background-color: #fff;
        }
        &::-webkit-scrollbar-track {
            background-color: #fff;
        }
        &:hover{
            &::-webkit-scrollbar-thumb  {
                background-color: #BFBFBF;
            }
            &::-webkit-scrollbar-track {
                background-color: #fff;
            }
        }
    }
    &-bottom{
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        border-top: 1px solid #E4E7ED;
        padding: 0px 8px;
        .text{
            margin: 0 4px;
            font-size: 14px;
            line-height: 22px;
            color: $text-color-regular;
        }
        .icon-question-circle{
            color: $text-color-regular;
            font-size: 14px;
        }
        &-item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 0 4px;
            &:last-child {
                padding-bottom: 6px;
            }
        }
    }
    &-padding{
        padding-bottom: 10px;
    }
}
.transform-left-fade-enter-active, .transform-left-fade-leave-active {
    transition: transform 0.28s ease-in-out;
}

.transform-left-fade-enter, .transform-left-fade-leave-to{
    transform: translateX(-225px);
}

.biz-intelligent-tags__filter-panel-skeleton{
    margin-top: 20px;
}

.biz-intelligent-tags__filter-panel-empty{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .biz-intelligent-tags__empty-img{
        width: 100px;
    }
    .biz-intelligent-tags__empty-text{
        color: $text-color-secondary;
        line-height: 22px;
        margin-top: 8px;
        cursor: default;
    }
    .biz-intelligent-tags__empty-link{
        margin-top: 8px;
        line-height: 22px;
        color: $color-primary-light-6;
        text-decoration: none;
    }
}
