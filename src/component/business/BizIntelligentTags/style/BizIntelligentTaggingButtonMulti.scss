.biz-intelligent-tagging{
    &__modal{
        .biz-intelligent-tagging__box-modal{
            padding: 24px;
            .biz-intelligent-tagging__box-item{
                &:not(:last-child){
                    margin-bottom: 16px;
                }
                .title{
                    font-size: 12px;
                    font-weight: 500px;
                    line-height: 20px;
                    color: #8C8C8C;
                }
                .biz-intelligent-tagging__tag-list{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                    list-style: none;
                    padding: 0;
                    margin: 8px 0 0 0;
                    &-item{
                        color: #595959;
                        background: #F4F4F5;
                        display: flex;
                        align-items: center;
                        padding: 2px 10px;
                        border-radius: 4px;
                        .txt{
                            font-size: 12px;
                            line-height: 20px;
                            cursor: default;
                        }
                        .iconfont{
                            font-size: 10px;
                            margin-left: 6px;
                            cursor: pointer;
                        }
                    }
                }
                .biz-intelligent-tag__select{
                    width: 100%;
                    margin-top: 8px;
                }
            }
        }

        .biz-intelligent-tagging__footer{
            width: 100%;
            display: flex;
            justify-content: space-between;

            .biz-intelligent-tagging__modal-manage {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 32px;
                flex-shrink: 0;
                cursor: pointer;
                &-left{
                    flex: 1;
                    display: flex;
                    align-items: center;
                    line-height: 22px;
                    .icon-biaoqian-xian{
                        color: $text-color-regular;
                        font-size: 14px;
                    }
                    .text{
                        font-size: 14px;
                        color: $text-color-primary;
                        margin-left: 4px;
                    }
                }
                .icon-setting{
                    font-size: 16px;
                    color: $text-color-regular;
                    cursor: pointer;
                }
            }
        }
    }
}

.biz-intelligent-tag__select-popper{
    .biz-intelligent-tags__option-item{
        display: flex;
        align-items: center;
        .iconfont{
            margin-right: 10px;
            font-size: 14px;
            flex-shrink: 0;
            font-weight: normal !important;
        }
        span {
            color: $text-color-primary;
            font-size: 14px;
            line-height: 22px;
            font-weight: normal !important;
            @include text-ellipsis();
        }
    }
}
