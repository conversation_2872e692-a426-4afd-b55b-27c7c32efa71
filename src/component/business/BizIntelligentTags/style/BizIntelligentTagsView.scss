.biz-intelligent-tags__table-view{
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: minmax(auto, auto) auto;
    &-detail {
        .biz-intelligent-tags__table-view-link{
            height: auto !important;
        }
        .biz-intelligent-tags__list-column{
            height: auto !important;
        }
    }
    &-list{
        width: 100%;
        height: 40px;
        color: var(--color-primary-light-6);
        cursor: pointer;
        @include text-ellipsis;
    }
    &-link{
        width: 100%;
        height: 40px;
        color: var(--color-primary-light-6);
        cursor: pointer;
        @include text-ellipsis;
    }
    &-text{
        color: $text-color-primary;
        text-decoration: none;
        cursor: default;
        &:hover{
            text-decoration: none;
        }
    }
    .biz-intelligent-tags__list-column{
        display: flex;
        align-items: center;
        margin: 0 0 0 4px;
        padding: 0;
        height: 40px;
        overflow: hidden;
        &-box {
            display: flex;
            align-items: center;
            .biz-intelligent-tags__view-more-btn{
                width: 22px;
                background: #F0F2F5;
                margin-left:  4px;
                line-height: 24px;
                text-align: center;
                border-radius: 2px;
                .iconfont{
                    display: block;
                    color: $text-color-primary !important;
                }
            }
        }
        &-item{
            .iconfont{
                font-size: 14px;
            }
            &:not(:last-child) {
                margin-right: 4px;
            }
            &.text-item{
                max-width: 120px;
                // font-size: 14px;
                padding: 2px 8px;
                background: #F0F2F5;
                border-radius: 2px;
                flex-shrink: 0;
                cursor: default;
                display: flex;
                line-height: 20px;
                align-items: center;
                .tag-text{
                    flex: 1;
                    color: $text-color-primary;
                    font-size: 12px;
                    margin-left: 4px;
                    @include text-ellipsis;
                }
            }
        }
    }
}

.biz-intelligent-tags__view{
    display: flex;
    align-items: center;
    &-list{
        display: flex;
        align-items: center;
        margin: 0;
        padding: 0;
        &-item{
            max-width: 150px;
            font-size: 14px;
            padding: 2px 8px;
            background: #F0F2F5;
            border-radius: 2px;
            flex-shrink: 0;
            cursor: default;
            display: flex;
            align-items: center;
            &.icon-item{
                padding: 1px 0;
                background: transparent;
                &:not(:last-child){
                    margin-right: 4px;
                }
            }
            &:not(:last-child) {
                margin-right: 8px;
            }
            &.omit {
                max-width: none;
                flex-shrink: 1;
                overflow: hidden;
            }
            .tag-text{
                flex: 1;
                color: $text-color-primary;
                font-size: 12px;
                margin-left: 4px;
                line-height: 20px;
                @include text-ellipsis;
            }
            .iconfont{
                font-size: 14px;
                line-height: 20px;
            }
        }
        &.edit {
            flex-wrap: wrap;
            row-gap: 8px;
        }
    }
    .biz-intelligent-tags__view-more-btn{
        width: 28px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #F0F2F5;
        border-radius: 4px;
        // margin-left: 8px;
        cursor: pointer;
        .iconfont{
            color: $text-color-primary !important;
        }
    }
}


.biz-intelligent-tags__view-more-popover{
    max-width: 500px;
    padding: 12px 16px;
    .biz-intelligent-tags__view-more-box{
        .biz-intelligent-tags__view-list{
            margin-top: 4px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px 8px;
            &-item{
                margin-right: 0;
                max-width: none;
                flex-shrink: 1;
                overflow: hidden;
            }
        }
    }
    .biz-intelligent-tags__view-title{
        color: $text-color-primary;
        font-size: 14px;
        font-weight: bolder;
        line-height: 22px;
    }
}
.biz-intelligent-tags__tooltip {
    z-index: 99999 !important;
}
