import { computed, defineComponent, ref, toRefs, unref, PropType } from "vue";
import { TagFilterSelectValueItem } from "../BizIntelligentTagsFilterPanel";
import { TagsItem } from '../BizIntelligentTagsView';
import '../style/BizIntelligentTagsGroupItem.scss';

export default defineComponent({
  name: 'BizIntelligentTagsGroupItem',
  props: {
    onTagItemClick: {
      type: Function,
    },
    // 当前标签的分组
    tagGroup: {
      type: Object,
      default: ()=> ({})
    },
    // 当前选中的值（在面板的时候使用到上层prop传入）
    value: {
      type: Array as PropType<TagFilterSelectValueItem []>,
      default: ()=> ([])
    },
    // 自定义相关 class
    customerClass: {
      type: String,
      default: ()=> ''
    },
    // 是否显示 checkbox
    showCheckBox: {
      type: Boolean,
      default: ()=> false
    },
    // 是否是智能标签
    intelligentType: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示排序
    showGroupSort: {
      type: Boolean,
      default: ()=> false
    },
    // 是否需要分组折叠
    showGroupCollapse: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示标签的数量
    showTagNum: {
      type: Boolean,
      default: ()=> false
    },
    // 是否显示个人标签显示逻辑
    showTagEdit: {
      type: Boolean,
      default: ()=> false
    },
    showGroupIcon: {
      type: Boolean,
      default: ()=> true
    }
  },
  emits: ['tagItemClick', 'checkboxChange', 'sortChange', 'personalLabelEdit'],
  setup(props, { emit }) {
    const { customerClass, intelligentType, showCheckBox, tagGroup, value, showGroupSort, showGroupCollapse, showTagNum, showTagEdit, showGroupIcon } = toRefs(props);
    // 当前的排序（降序）
    const sortType = ref('descending');
    // 组件更新的 key（相关折叠面板更新是否显示时候更新组件的 ey）
    const updateKey = ref('1111');

    // 当前标签分组的的 data
    const tagGroupData = computed(()=> {
      return Object.assign({ show: true }, tagGroup.value);
    });

    /**
     * @des 相关 tagItem 点击事件
     * @param {any} tagItem:any
     * @returns {any}
     */
    const handleTagsItemClick = (e: Event, tagItem: TagsItem)=> {
      e.preventDefault();
      if(showCheckBox.value) {
        handleCheckboxChange(!tagItem.checked, tagItem);
      }

      emit('tagItemClick', tagItem);
    };

    const handleEditTag = (e: Event, tagItem: TagsItem) => {
      e.stopPropagation()
      emit('personalLabelEdit', tagItem)
    }

    const handleTagGroupItemNameClick = ()=>{

    };


    /**
     * @des 相关折叠分组的 icon 点击事件
     * @returns {any}
     */
    const handleCollapse = ()=> {
      tagGroupData.value.show = !tagGroupData.value.show;
      updateKey.value = updateKey.value += 1;
    };

    /**
     * @des 相关 checkbox 的change 事件
     * @param {any} v:boolean
     * @param {any} tagItem:any
     * @returns {any}
     */
    const handleCheckboxChange = (v: boolean, tagItem: TagsItem)=> {
      tagItem.checked = v;
      emit('checkboxChange', v, tagItem, tagGroup.value);
    };

    /**
     * @des 相关排序的 icon 的点击事件
     * @param {any} e:MouseEvent
     * @returns {any}
     */
    const handleSortingClick = (e: MouseEvent)=> {
      sortType.value = sortType.value  === 'ascending' ? 'descending' : 'ascending';
      emit('sortChange', sortType.value);
    };

    return ()=> (
      <div class={["biz-intelligent-tags__group-box", customerClass.value ? customerClass.value : null]} key={tagGroupData.value.id}>
        <div class="biz-intelligent-tags__group-title" onClick={handleTagGroupItemNameClick}>
          <div class="biz-intelligent-tags__group-title-lf">
            {
              showGroupIcon.value ? (
                  <span class="int-icon">
                      {
                          tagGroupData.value?.personalLabel == '1' ? (
                              <i class="iconfont icon-qizhi-xian"></i>
                          ) : (
                              <i class="iconfont icon-biaoqian-xian"></i>
                          )
                      }
                  </span>
              ) : null
            }
            <span>{tagGroupData.value.groupName}</span>
            { showGroupCollapse.value ?
              <slot name="group-title-collapse">
                <i class="collapse-icon el-icon-arrow-down" style={{transform: tagGroupData.value.show ? 'rotate(180deg)' : 'rotate(0)'}} onClick={handleCollapse}></i>
              </slot>
              :
              null
            }
          </div>
          {showGroupSort.value ?
            <div class="biz-intelligent-tags__group-title-rf">
              <slot name="group-title-suffix">
                <div class={["caret-wrapper", sortType.value]} onClick={handleSortingClick}>
                  <i class="caret-icon el-icon-caret-top ascending"></i>
                  <i class="caret-icon el-icon-caret-bottom descending"></i>
                </div>
              </slot>
            </div>
            :
            null}
        </div>
        <el-collapse-transition key={updateKey.value}>
          { tagGroupData.value.show ?
            <ul class="biz-intelligent-tags__group-item">
              {tagGroupData.value?.labelList.map((tagItem: TagsItem)=> (
                <li
                  class={["biz-intelligent-tags__item", unref(value).some(item=> item.value == tagItem.id) ? 'active' : null ]}
                  onClick={(e)=> handleTagsItemClick(e, tagItem)}>
                  <div class="biz-intelligent-tags__item-left">
                    { showCheckBox.value ? <el-checkbox class="biz-intelligent-tags__item-checkbox" value={tagItem.checked}></el-checkbox> : null }
                    <div class="biz-intelligent-tags__item-content">
                    {
                          tagItem?.personalLabel ? (
                              <i 
                                  class={["iconfont", "icon-qizhi-mian"]}
                                  style={{ color: tagItem.logoColor }}
                              ></i>
                          ) : (
                              <i 
                                  class={["iconfont", intelligentType.value ? 'icon-zhinengbiao-mian':'icon-biaoqian-mian']}
                                  style={{ color: tagItem.logoColor }}
                              ></i>
                          )
                      }
                      <span>{tagItem.name}</span>
                    </div>
                  </div>
                  {
                    showTagEdit.value && tagItem?.personalLabel ? (
                        <div class="biz-intelligent-tags__item-right" onClick={(e) => {
                            handleEditTag(e,tagItem)
                        }}>
                            <i class="iconfont icon-edit-square"></i>
                        </div>
                    ) : null
                  }
                  { showTagNum.value && tagItem?.count != null ?  <div class="biz-intelligent-tags__item-num">{tagItem?.count || 0}</div> : null }
                </li>
              ))}
            </ul>
            :
            null
          }
        </el-collapse-transition>
      </div>
    );
  }
});
