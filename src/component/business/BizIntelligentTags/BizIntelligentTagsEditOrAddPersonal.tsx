import { defineComponent, ref, PropType, computed, toRefs, watch, onBeforeUnmount } from 'vue'
import { t } from "@src/locales";
import './style/BizIntelligentTagsEditOrAddPersonal.scss';
import { Form } from 'element-ui'
/* utils */
import { isEmpty } from 'lodash'

export default defineComponent({
    name: 'BizIntelligentTagsEditOrAddPersonal',
    props: {
        title: {
            type: String,
            default: t('common.base.intelligentTag.createPersonal')
        },
        status: {
            type: String as PropType<'add' | 'edit'>, // 修正了 type 的拼写错误
            default: 'add'
        },
        tagItem: {
            type: Object as PropType<any>,
            default: ()=> ({})
        },
        // 按钮是否禁用
        disabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['back', 'add', 'edit', 'delete'],
    setup(props, { emit }) {
        const formRef = ref<typeof Form>()
        const { status, tagItem, disabled: personalDisabled } = toRefs(props)
        const titleText = computed(() => {
            return status.value === 'add' ? t('common.base.intelligentTag.createPersonal') : t('common.base.intelligentTag.editPersonal')
        })

        // 表单数据
        const formData = ref({
            labelName: '',
            color: '#F5222D',
        })

        // 表单校验规则
        const rules = {
            labelName: [
                { required: true, message: t('common.base.intelligentTag.pleaseInputLabelName'), trigger: 'blur' },
                { min: 1, max: 10, message: `${t('common.base.intelligentTag.length')} 1 ${t('common.base.intelligentTag.dao')} 10 ${t('common.base.intelligentTag.character')}`, trigger: 'blur' }
            ],
            color: [
                { required: true, message: `${t('common.base.intelligentTag.selectLabelColor')}`, trigger: 'change' }
            ]
        }

        const colorList = ref([
            '#F5222D','#fa541c','#faad14', '#a0d911', '#13c2c2', '#2f54eb','#eb2f96', '#7b8383'
        ])

        const handleBack = () => {
            emit('back')
            restFormInfo()
        }

        // 创建
        const handleCreate = () => {
            // @ts-ignore
            formRef.value?.validate((valid: boolean) => {
                if (valid) {
                    emit('add', formData.value)
                }
            })
        }
        
        // 保存
        const handleSave = () => {
            // @ts-ignore
            formRef.value?.validate((valid: boolean) => {
                if (!valid) return
                emit('edit', Object.assign({}, formData.value, { id: tagItem.value.id }))
            })
        }
        // 删除
        const handleDelete = () => {
            emit('delete', tagItem.value.id)
        }
        
        // 重置表单
        const resetForm = () => {
            // @ts-ignore
            formRef.value?.resetFields()
        }
        const restFormInfo = () => {
            formData.value = {
                labelName: '',
                color: '#F5222D',
            }
        }
        // 选择颜色
        const handleColorSelect = (color: string) => {
            formData.value.color = color
        }

        const stop = [
            watch(() => props.tagItem, (val: any) =>{
                if (!val) return
                if (isEmpty(val)) return
                formData.value = {
                    labelName: val.name,
                    color: val.logoColor
                }
            }, { immediate: true })
        ]

        onBeforeUnmount(() => {
            stop.forEach((item) => item())
        })

        return {
            formRef,
            formData,
            rules,
            titleText,
            colorList,
            personalDisabled,
            handleBack,
            resetForm,
            handleCreate,
            handleSave,
            handleDelete,
            handleColorSelect
        }
    },
    render() {
        return (
            <div class="int-personal">
                <header class="int-personal-head">
                    <span class="back" onClick={this.handleBack}>
                        <i class="iconfont icon-left"></i>
                    </span>
                    <span class="title">{ this.titleText }</span>
                </header>
                <div class="int-personal-main">
                    <el-form 
                        ref="formRef"
                        {
                            ...{
                                props: {
                                    model: this.formData,
                                    rules: this.rules,
                                }

                            }
                        }
                    >
                        <el-form-item prop="labelName">
                            <el-input 
                                value={this.formData.labelName}
                                onInput={(e: string) => this.formData.labelName = e}
                                maxlength="10" 
                                show-word-limit 
                                placeholder={t('common.base.intelligentTag.labelName2')}
                            />
                        </el-form-item>

                        <el-form-item prop="color">
                            <div class="color-list">
                                {this.colorList.map(color => (
                                    <div 
                                        class={['color-item', { "active": this.formData.color === color }]}
                                        style={{ backgroundColor: color }}
                                        onClick={() => this.handleColorSelect(color)}
                                    >
                                        <i class="iconfont icon-check"></i>
                                    </div>
                                ))}
                            </div>
                        </el-form-item>
                        {
                            this.status === 'add' ? (
                            <el-form-item>
                                <div class="create">
                                    <el-button type="primary" onClick={this.handleCreate} disabled={this.personalDisabled}>
                                        {t('common.base.create3')}
                                    </el-button>
                                </div>
                            </el-form-item>
                            ) : (
                            <el-form-item>
                                <div class="edit-group">
                                    <el-button type="danger" onClick={this.handleDelete} disabled={this.personalDisabled}>
                                        {t('common.base.delete')}
                                    </el-button>
                                    <el-button type="primary" onClick={this.handleSave} disabled={this.personalDisabled}>
                                        {t('common.base.save')}
                                    </el-button>
                                </div>
                            </el-form-item>
                            )
                        }
                    </el-form>
                </div>
            </div>
        )
    }
})
