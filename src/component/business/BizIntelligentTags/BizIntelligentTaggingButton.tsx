import { computed, defineComponent, onMounted, PropType, ref, toRefs, unref, watchEffect, getCurrentInstance, onBeforeUnmount, nextTick, watch, reactive } from "vue";
import BizIntelligentTagsGroupItem from './components/BizIntelligentTagsGroupItem';
import BizIntelligentTagsEditOrAddPersonal from './BizIntelligentTagsEditOrAddPersonal'
/* utils */
//@ts-ignore
import clickOutside from 'element-ui/lib/utils/clickoutside';
import { cloneDeep, debounce } from 'lodash';
import { getAssetsForOssUrl, uuid} from 'pub-bbx-utils';
import { getTagsGroupToTagsList } from '@src/business/intelligentTags/utils/utils'
import locales, { t } from '@src/locales';
/* model */
import { TagsItem } from "./BizIntelligentTagsView";
import { TagsGroupListItem } from './BizIntelligentTagsFilterPanel';
/* style */
import './style/BizIntelligentTaggingButton.scss';

const searchEmptyImg = getAssetsForOssUrl('search_icon.png', locales.locale);
const noDataImg =  getAssetsForOssUrl('/no_data.png', locales.locale);

/**
 * @des 处理相关标签分组分页时候合并的情况
 * @returns {any}
 * @param defaultList
 * @param currentList
 */
export const processConcatGroupList = (defaultList: TagsGroupListItem[], currentList: TagsGroupListItem[])=> {
  if(defaultList.length === 0) return defaultList;
  if(currentList.length === 0) return currentList;
  const defaultLastGroup = defaultList[defaultList.length - 1];
  const currentFirstGroup = currentList[0];

  if(defaultLastGroup.groupName === currentFirstGroup.groupName) {
    defaultLastGroup.labelList.concat(currentFirstGroup.labelList);
    currentList.unshift();
  }
  defaultList[defaultList.length - 1] = defaultLastGroup;
  return defaultList.concat(currentList);
};


export default defineComponent({
  name: 'BizIntelligentTaggingButton',
  props: {
    // 标签的分组列表
    tagsGroupList: {
      type: Array as PropType<TagsItem []>,
      default: ()=> []
    },
     // 前端新建个人&编辑标签
     localGroupLabelList: {
      type: Object as PropType<TagsItem & { status: 'add' | 'edit' | 'delete'}>,
      default: ()=> {}
    },
    // 标签请求的远程方法 （remoteFetchFun优先级大于tagsGroupList）
    remoteFetchFun: {
      type: Function
    },
    // 默认勾选的 tagArray 数据数组
    value: {
      type: Array,
      default: ()=> []
    },
    // popover显示的位置
    placement: {
      type: String,
      default: ()=> 'left-start'
    },
    // 打开对应的 popover 前置方法 如果是方法 返回 true 才能显示
    openPopoverHook: {
      type: Function,
    },
    // 按钮是否是disabled
    disabled: {
      type: Boolean,
      default: ()=> false
    },
    // 对应接口操作类型 setMethod  设置标签方式：1，增量；2，覆盖；3，删除
    operatorType: {
      type: Number,
      default: ()=> 0
    },
    // 是否显示添加标签的文字
    showText: {
      type: Boolean,
      default: ()=> true
    },
    // 按钮文案
    buttonText: {
      type: String,
      default: ()=> t('common.base.add') + t('common.label.tag')
    },
    // 是否远程搜索
    remoteSearch: {
      type: Boolean,
      default: ()=> false
    },
    // 来自表单设计器里面的参数
    formProps: {
      type: Object,
      default: ()=> ({
          showButton: true
      })
    },
    // 标签编辑时传入的标签数组，用于匹配当前标签组是否存在标签
    tagsList: { 
      type: Array as PropType<TagsItem[]>,
      default: ()=> []
    },
    // 显示分组图片
    showGroupIcon: {
      type: Boolean,
      default: ()=> true
    },
    // 表单编辑器的编辑模式 正常模式 编辑模式：来自表单
    mode: {
        type: String as PropType<'normal' | 'edit'>,
        default: ()=> 'normal'
    }
  },
  directives:{
    clickOutside
  },
  setup(props, { emit, expose }) {
    const { remoteFetchFun, placement, tagsGroupList: propTagsGroupList, localGroupLabelList, openPopoverHook, disabled, operatorType, value, showText, remoteSearch, buttonText, formProps, showGroupIcon,
      mode, tagsList
     } = toRefs(props);
    const { proxy: ctx } = getCurrentInstance() as unknown as any;
    const remoteFetchFunBaseParams = ref({ pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false  });
    const tagsGroupList = ref<TagsGroupListItem[]>([]);
    const showFetchLoading = ref(false);
    const showPopover = ref(false);
    const fetchTagsGroupDataList = ref<TagsGroupListItem[]>([]);
    const searchGroupTagValue = ref<TagsGroupListItem[]>([]);
    const isPersonalShow = ref<Boolean>(false) // 个人标签弹窗显示逻辑
    const personalStatus = ref<'add' | 'edit'>('add') // 个人标签弹窗新建&编辑逻辑
    const currentRadioValue = ref(1) // 1表示智能标签 2表示个人标签
    const currentPersonEditTagItem = ref<any>({}) // 当前编辑的个人标签
    const personalKey = ref('1234') // 个人标签弹窗key
    /* 是否在输入中的判断 */
    const isOnComposition = ref(false)
    const backspaceDeleteTagNum = ref(0)
    // 搜索框中上一个搜索的值
    const prvSearchValue = ref('')
    let currentSelectLabels = ref<any[]>([])
    const inputSearchListRef = ref<HTMLElement | HTMLInputElement | null>(null)

    // 判断当前的remoteFetchFun是否是一个方法
    const hasRemoteFetchFun = computed(()=> {
      return typeof remoteFetchFun.value === 'function';
    });

    // 当前状态
    const currentMode = computed(()=> {
      return mode.value || 'normal'
    })

    const searchValue = computed(()=> remoteFetchFunBaseParams.value.keyword);

    // 获取当前的 groupTagList
    const currentTagsGroupList = computed({
      set(v: any[]) {
        hasRemoteFetchFun.value ?
          fetchTagsGroupDataList.value = v
          :
          tagsGroupList.value = v;
      },
      get() {
        // 如果有搜索的话直接返回搜索的值
        if(searchValue.value && !remoteSearch.value) return searchGroupTagValue.value;
        // 如果有有远程方法直接返回远程方法返回的值
        if(hasRemoteFetchFun.value) {
          return fetchTagsGroupDataList.value;
        }
        return tagsGroupList.value;
      }
    });

    //  当标签为空且没有远程方法加载时
    const isBtnDisabled = computed(() => {
      return currentTagsGroupList.value && currentTagsGroupList.value.length === 0 && !showFetchLoading.value
    })

    //  智能标签
    const isIntelligentLabels = computed(()=> {
      return currentTagsGroupList.value.filter(item=> {
          return Reflect.has(item, 'personalLabel') && item?.personalLabel == '0'
      })
    })
    // 个人标签
    const isPersonalLabels = computed(()=> {
        return currentTagsGroupList.value.filter(item=> {
            return Reflect.has(item, 'personalLabel') && item?.personalLabel == '1'
        })
    })

    // 当前的 input的事件
    const handleSearchInput = (val: string)=> {
      remoteFetchFunBaseParams.value.keyword = val;
      handleForSearch();
    };

    const handleSearchKeyUp = async (e: KeyboardEvent) => {
      if (isOnComposition.value) return;
      
      // @ts-ignore
      const text = e.target.value;
      //@ts-ignore 
      const selectionStart = e.target.selectionStart;
      
      // 处理删除键的情况
      if((e.key === 'Backspace' || e.key === 'Delete')) {
        // 光标在最前且按下两次删除,删除标签
        if(selectionStart === 0 && backspaceDeleteTagNum.value > 1) {
          const lastTag = cloneDeep(currentSelectLabels.value[currentSelectLabels.value.length - 1]);
          if(lastTag) {
            handleClear(lastTag);
          }
          backspaceDeleteTagNum.value = 1;
          return;
        }
    
        // 更新删除计数
        if(selectionStart === 0) {
          backspaceDeleteTagNum.value += 1;
        } else {
          backspaceDeleteTagNum.value = 0; 
        }
    
        // 判断是否需要调用搜索
        const needSearch = Boolean(prvSearchValue.value) && (!text || text !== prvSearchValue.value);
        if(needSearch) {
          handleSearchInput(text);
        }
        return;
      }
    
      // 非删除键重置计数
      backspaceDeleteTagNum.value = 0;
      
      // 处理其他输入
      await nextTick();
      handleSearchInput(text);
    };

    // 鼠标按下处理函数
    const handleSearchKeyDown = (e: KeyboardEvent) => {
      //@ts-ignore
      prvSearchValue.value = e.target.value
    }

    const handleFocus = ()=> {
      remoteFetchFunBaseParams.value.keyword.length === 0 ?
          backspaceDeleteTagNum.value = 1
          :
          backspaceDeleteTagNum.value = 0
    }  

    const isKorean = (text: string)=> {
      const reg = /([(\uAC00-\uD7AF)|(\u3130-\u318F)])+/gi;
      return reg.test(text);
    }

    const handleComposition = (event: CompositionEvent) => {
      // @ts-ignore
      const text = event.target.value;
      if (event.type === 'compositionend') {
          isOnComposition.value = false;
          nextTick(()=> handleSearchInput(text))
      } else {
          const lastCharacter = text[text.length - 1] || '';
          isOnComposition.value = !isKorean(lastCharacter);
      }
    }

    /**
     * @des 相关搜搜方法
     * @param {any} (
     * @returns {any}
     */
    const handleForSearch = debounce(async ()=> {
      if(remoteSearch.value && hasRemoteFetchFun.value) {
        remoteFetchFunBaseParams.value.pageNum = 1;
        return await remoteFetchData(false ,false);
      }
      const dataList = cloneDeep(fetchTagsGroupDataList.value.length ? fetchTagsGroupDataList.value : tagsGroupList.value);
      const result = dataList.reduce((acc: TagsGroupListItem[], item: any)=> {
        if(Array.isArray(item.labelList)) {
          item.labelList = item.labelList.filter((childItem: any) => searchValue.value ? childItem.name.includes(searchValue.value) : true);
        }
        if(item.labelList.length) {
          acc.push(item);
        }
        return acc;
      }, []);
      searchGroupTagValue.value = result;
    }, 400);

    /**
     * @des 设置对应的标签是否勾选中方法
     * @param {any} tagGroupList:any[]
     * @param {any} difValue:any 需要对比的值
     * @returns {any}
     */
    const setDefaultDataForGroupTagsList = (tagGroupList: any[], difValue?: any)=> {
      const defaultValue = difValue ? difValue : unref(value);
      return tagGroupList.map(groupItem=> {
        const originalGroupName = groupItem.groupName.replace(/（\d+）$/, '')
        if(Array.isArray(groupItem.labelList)) {
          groupItem.labelList.map((tagItem: any)=> {
            tagItem.checked = defaultValue.some((item: any)=> tagItem.id === item.id);
            return tagItem;
          });
        }
        groupItem.groupName = `${originalGroupName}（${groupItem.labelList ? groupItem.labelList.length : 0}）`
        return groupItem;
      });
    };

    /**
     * @des 远程搜索方法
     * @param {any} merge=false
     * @param {any} loading=true
     * @returns {any}
    */
    const remoteFetchData = async(merge = false, loading = true)=> {
      const showFetchLoadingFun = (loading: boolean = true)=> showFetchLoading.value = loading;
      const needLoading = !merge && loading;
      try {
        needLoading && showFetchLoadingFun();
        const { hasNextPage: _, ...params } = { 
          ...remoteFetchFunBaseParams.value, 
          count: false 
        }
        if(remoteFetchFun.value) {
          const { list, hasNextPage, pageNum, pageSize } = await remoteFetchFun.value(params);
          // 处理标签组数据
          let processedList = list;
          const tagsListValue = unref(tagsList)
          
          try {
              // 处理不存在于标签组的标签
              if(tagsListValue?.length && !Boolean(searchValue.value)) {
                // 使用Set优化查找性能
                const flatTagsSet = new Set(
                    getTagsGroupToTagsList(list).map(item => item.id)
                )
                const noExistTags = tagsListValue.filter(item => !flatTagsSet.has(item.id))
                if(noExistTags.length) {
                    const _customGroup = reactive({
                        groupName: t('common.base.intelligentTag.stopLabel'),
                        labelList: noExistTags.map(tag => ({
                            ...tag,
                            checked: false  // 明确设置初始选中状态
                        })),
                        personalLabel: 0,
                        type: 0
                    })
                    processedList = [
                        _customGroup,
                        ...list,
                    ]
                }
            }
          } catch (error) {
            console.error('[remoteFetchFun error]', error)
          }
          merge ? fetchTagsGroupDataList.value = setDefaultDataForGroupTagsList(processConcatGroupList(fetchTagsGroupDataList.value, processedList.slice(0))) : fetchTagsGroupDataList.value = setDefaultDataForGroupTagsList(processedList);
          needLoading && showFetchLoadingFun(false);
          remoteFetchFunBaseParams.value = { ...remoteFetchFunBaseParams.value, hasNextPage, pageNum, pageSize };
        }
      } catch (error) {
        console.error('[remoteFetchFun error]', error);
      } finally {
        showFetchLoadingFun(false);
      }
    };

    /**
     * @des 显示 popover 方法
     * @returns {any}
     */
    const handleShowPopover = async () => {
      if(openPopoverHook.value && typeof openPopoverHook.value === 'function') {
        if(!(await openPopoverHook.value())) return;
      }

      // 判断是是已经显示
      if(showPopover.value) return showPopover.value = false;
      // 判断是否有hasRemoteFetchFun没有的话直接显示
      if(!hasRemoteFetchFun.value) return showPopover.value = true;
      // 判断remoteFetchFun可能没有值的情况
      resetFetchParamsData();
      await remoteFetchData();
      showPopover.value = true;

      scrollInputStart();
    };

    /**
     * @des 重置对应的基础远程请求参数
     * @returns {any}
     */
    const resetFetchParamsData = ()=> {
      remoteFetchFunBaseParams.value = { pageSize: 1000, pageNum: 1, keyword: '', hasNextPage: false  };
    };

    /**
     * @des 加载更多请求方法
     * @returns {any}
     */
    const loadMore = async ()=> {
      if(remoteFetchFunBaseParams.value.hasNextPage) {
        remoteFetchFunBaseParams.value.pageNum += 1;
        if(hasRemoteFetchFun.value) {
          await remoteFetchData(true);
        }
        emit('loadMore');
      }
    };

    const stop = [watchEffect(()=> {
      tagsGroupList.value = setDefaultDataForGroupTagsList(cloneDeep(propTagsGroupList.value));
      searchGroupTagValue.value = tagsGroupList.value.slice(0);
    }), 
    watch(() => localGroupLabelList.value, (newVal)=> {
      console.log('[localGroupLabelList newVal]', newVal)
      if (!Object.keys(newVal).length) return
      let findItem = currentTagsGroupList.value.find(item=> Reflect.has(item, 'personalLabel') && item?.personalLabel == '1')
      if (!findItem) return
      if (newVal.status == 'add') {
          // @ts-ignore
          findItem.labelList = [Object.assign({personalLabel: 1}, newVal), ...findItem.labelList]
      }
      if (newVal.status == 'edit') {
          findItem.labelList = findItem.labelList.map((item: any)=> {
              if (item.id == newVal.id) {
                item.logoColor = newVal.logoColor ?? ''
                item.name = newVal.name ?? ''
                return item
              }
              return item
          })
          handleCurrentSelectLabelLocal(newVal)
          emit('detailViewLabel', newVal, 'edit')
      }
      if (newVal.status == 'delete') {
          findItem.labelList = findItem.labelList.filter((item: any)=> item.id != newVal.id)
          let exist = currentSelectLabels.value.findIndex(i => i.id == newVal.id);
          if (exist > -1) {
              handlerCurrentSelectLabel(false, newVal)
          }
          emit('detailViewLabel', newVal, 'delete')
      }
      if (['add', 'delete'].includes(newVal.status)) {
        const checkedTags = findItem.labelList.filter((tag: any) => tag.checked) || []
        currentTagsGroupList.value = setDefaultDataForGroupTagsList(cloneDeep(currentTagsGroupList.value), checkedTags)
      }
    }),
    watch(() => showPopover.value,(newVal) => {
      if (!newVal) return
      const checkedTags = currentTagsGroupList.value.reduce((acc: any[], group) => {
        const checkedInGroup = group.labelList?.filter((tag: any) => tag.checked) || []
        return [...acc, ...checkedInGroup]
      }, [])
  
      // 更新当前选中标签列表
      currentSelectLabels.value = checkedTags.map(item => ({
        ...item,
        checked: true
      }))
    }),
  ];


    const hidePopover = ()=> {
      emit('hidePopover');
      showPopover.value = false;
    };


    const handleManageTagsClick = ()=> {
      emit('manageTagsClick');
      hidePopover();
    };

    const handleCheck = (...args: any[])=> {
      emit('change', ...args);
      handlerCurrentSelectLabel(...args)
    };

    // 智能标签保存逻辑
    const handleSave = ()=> {
      if (isBtnDisabled.value || disabled.value) return
      emit('save', operatorType.value);
    };

    // 个人标签保存逻辑
    const handleSaveOfPersonal = () => {
      if (isBtnDisabled.value || disabled.value) return
      emit('personalSave', operatorType.value)
    }
    
    // 切换按钮功能逻辑
    const handlerRadioChange = (val: number) => {
        currentRadioValue.value = val
        emit('radioChange', val)
    }
    
    // 新增个人标签
    const handleAddPersonalLabel = () => {
        currentPersonEditTagItem.value = {}
        isPersonalShow.value = true
        personalStatus.value = 'add'
    }
    // 编辑个人标签
    const handleEditPersonalLabel = (val: any) => {
        isPersonalShow.value = true
        personalStatus.value = 'edit'
        nextTick(() => {
            currentPersonEditTagItem.value = val
        })
    }
    // 回退
    const handleBack = () => {
        isPersonalShow.value = false
        personalStatus.value = 'add'
        nextTick(() => {
            personalKey.value = uuid()
        })
    }
    // 个人标签编辑
    const handlePersonEdit = (val: any) => {
        emit('personalLabelEdit', val)
    }
    // 个人标签新增
    const handlePersonAdd = (val: any) => {
        emit('personalLabelAdd', val)
    }
    // 个人标签删除
    const handlePersonDelete = (val: any) => {
        emit('personalLabelDelete', val)
    }

    /**
     * @description 滚动到输入框开头
     */
    const scrollInputStart = () => {
      nextTick(() => {
        inputSearchListRef.value?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      })
    }

    const handleClear = (item: any) => {
      handleCheck(false, item);

      try {
        const tag = getTagsGroupToTagsList(currentTagsGroupList.value).find((i) => i.id == item.id);
        if (tag) {
        // @ts-ignore
          tag.checked = false;
        }
      } catch (error) {
        console.log(error);
      }
    }

    /**
     * @description 更改当前选中的标签
     * @param args 
     */
    const handlerCurrentSelectLabel = (...args: any) => {
      let exist =  currentSelectLabels.value.findIndex(i => i.id == args[1].id);

      if (exist > -1) {
        currentSelectLabels.value.splice(exist, 1);
      } else {
        currentSelectLabels.value.push(args[1]);
      }

      scrollInputStart()
    }

  /**
   * @description 更改当前选中的标签信息 仅用于个人标签编辑修改
   */
    const handleCurrentSelectLabelLocal = (updateLabelInfo: any) => {
      let exist = currentSelectLabels.value.findIndex(i => i.id == updateLabelInfo.id);
      if (exist > -1) {
          currentSelectLabels.value.splice(exist, 1, updateLabelInfo);
      }
    }

    /**
     * @description 添加键盘炒作
     * @param e 事件对象
     */
    const handleKeyboardNavigation = (e: KeyboardEvent) => {
      switch(e.key) {
        case 'Escape':
          hidePopover();
          break;
      }
    }
    
    // 添加键盘事件监听
    onMounted(() => {
      document.addEventListener('keydown', handleKeyboardNavigation)
    })
    
    onBeforeUnmount(()=>{
      stop.forEach(item=> item());
      document.removeEventListener('keydown', handleKeyboardNavigation)
    })

    expose({
      hidePopover,
      handleBack
    });


    const renderInputSearch = () => {
      return (
        <div class="biz-intelligent-tagging__popover-input" role="search">
          <div class="biz-intelligent-tagging__popover-input__list">
              { currentSelectLabels.value.map((item: any) => {
                  return (
                    <div class="biz-list-item"
                      role="listitem"
                    >
                      <span>{item.name}</span>
                      <i class="iconfont icon-fe-close" role="button" onClick={() => handleClear(item)}></i>
                    </div>
                  )
                })
              }
            <input 
              ref={el => inputSearchListRef.value = el as unknown as HTMLInputElement}
              placeholder={ t('common.base.search') + '...'}
              onFocus={handleFocus}
              onCompositionstart={handleComposition}
              onCompositionupdate={handleComposition}
              onCompositionend={handleComposition}
              onKeyup={handleSearchKeyUp} 
              onKeydown={handleSearchKeyDown}
              role="searchbox"
              aria-label={t('common.base.search') + '...'}
            />
          </div>
        </div>
      )
    }
    // 渲染智能标签函数
    const renderIntelligentTagsLabel = () => {
      return (
        <div class="biz-intelligent-tagging__popover-tags-label">
          {renderInputSearch()}
          <div class="biz-intelligent-tagging__popover-manage" onClick={handleManageTagsClick}>
            <div class="biz-intelligent-tagging__popover-manage-left">
              <i class="iconfont icon-biaoqian-xian"></i>
              <span class="text">{t('common.base.intelligentTag.manageTag')}</span>
            </div>
            <i class="iconfont icon-setting"></i>
          </div>
          <div
            class="biz-intelligent-tagging__popover-tags-group"
            {...{
              directives: [
                {
                  name: 'infinite-scroll',
                  value: () => loadMore(),
                },
              ],
            }}
            infinite-scroll-distance={10}
            infinite-scroll-delay={400}
          >
            {showFetchLoading.value ? (
              <el-skeleton />
            ) : isIntelligentLabels.value.length ? (
              // @ts-ignore
                isIntelligentLabels.value.map(item => <BizIntelligentTagsGroupItem tagGroup={item} showGroupIcon={showGroupIcon.value} intelligentType={false} onCheckboxChange={handleCheck} showCheckBox />)
            ) : (
              <div class="biz-intelligent-tags__group-empty">
                <img class="img" src={searchValue.value ? searchEmptyImg : noDataImg}></img>
                <span class="text">{searchValue.value ? t('common.base.intelligentTag.notFindTag') : t('common.base.intelligentTag.noData') }</span>
              </div>
            )}
          </div>
          {formProps.value.showButton ? (
            <div class="biz-intelligent-tagging__popover-bottom">
              <el-button type="plain-third" disabled={disabled.value} onClick={hidePopover}>
                {t('common.base.cancel')}
              </el-button>
              <el-button type="primary" disabled={disabled.value || isBtnDisabled.value} onClick={handleSave}>
                {t('common.base.save')}
              </el-button>
            </div>
          ) : null}
        </div>
      );
    };

    // 渲染个人标签显示函数
    const renderPersonalTagsLabel = () => {
      return (
        <div class="biz-intelligent-tagging__popover-tags-personal" key={personalKey.value}>
          <div class="ipt-box">
            {renderInputSearch()}
            <div role="button" class="btn-add" onClick={handleAddPersonalLabel}>
              <i class="iconfont icon-add2"></i>
            </div>
          </div>
          <div
            class="biz-intelligent-tagging__popover-tags-group"
            {...{
              directives: [
                {
                  name: 'infinite-scroll',
                  value: () => loadMore(),
                },
              ],
            }}
            infinite-scroll-distance={10}
            infinite-scroll-delay={400}
          >
            {showFetchLoading.value ? (
              <el-skeleton />
            ) : isPersonalLabels.value.length ? (
              // @ts-ignore
                isPersonalLabels.value.map(item => <BizIntelligentTagsGroupItem tagGroup={item} onPersonalLabelEdit={handleEditPersonalLabel} showGroupIcon={showGroupIcon.value} intelligentType={false} showTagEdit={true} onCheckboxChange={handleCheck} showCheckBox />)
            ) : (
              <div class="biz-intelligent-tags__group-empty">
                <img class="img" src={searchValue.value ? searchEmptyImg : noDataImg}></img>
                <span class="text">{searchValue.value ? t('common.base.intelligentTag.notFindTag') : t('common.base.intelligentTag.noData') }</span>
              </div>
            )}
          </div>
          {formProps.value.showButton ? (
            <div class="biz-intelligent-tagging__popover-bottom">
              <el-button type="plain-third" disabled={disabled.value} onClick={hidePopover}>
                {t('common.base.cancel')}
              </el-button>
              <el-button type="primary" disabled={disabled.value || isBtnDisabled.value} onClick={handleSaveOfPersonal}>
                {t('common.base.save')}
              </el-button>
            </div>
          ) : null}
        </div>
      );
    };

    return ()=> (
      <div class="biz-intelligent-tagging__box">
        <el-popover
          placement={placement.value}
          width="300"
          trigger="manual"
          value={showPopover.value}
          popper-class="biz-intelligent-tagging__popover"
        >
          <div slot="reference" class={['biz-intelligent-tagging__button', currentMode.value === 'edit' ? 'formEdit' : '']} onClick={handleShowPopover}>
            <i class="iconfont icon-tianjiabiaoqian"></i>
            {showText.value ? buttonText.value : null}
          </div>
          { showPopover.value ?
              isPersonalShow.value ? (
                // @ts-ignore
                <div 
                  {...{
                    directives: [
                        {
                        name: 'clickOutside',
                        value: () => hidePopover()
                    }]
                  }}
                >
                  {/* @ts-ignore */}
                  <BizIntelligentTagsEditOrAddPersonal status={personalStatus.value} onBack={handleBack} onEdit={handlePersonEdit} onAdd={handlePersonAdd} onDelete={handlePersonDelete} tagItem={currentPersonEditTagItem.value} />
                </div>
              ) : (
                <div class="biz-intelligent-tagging__popover-content"
                  {...{
                    directives: [
                        {
                        name: 'clickOutside',
                        value: () => hidePopover()
                    }]
                }}
                >
                  <div class="btn-grp-con">
                    <el-radio-group value={currentRadioValue.value} onInput={handlerRadioChange}>
                      <el-radio-button label="1">{ t('common.base.intelligentTag.label1') }</el-radio-button>
                      <el-radio-button label="2">{ t('common.base.intelligentTag.label2') }</el-radio-button>
                    </el-radio-group>
                  </div>
                  
                  {
                      currentRadioValue.value == 1 ? (
                          renderIntelligentTagsLabel()
                      ) : currentRadioValue.value == 2 ? (
                          renderPersonalTagsLabel()
                      ) : null
                  }
                </div>
              )
            : null}
        </el-popover>
      </div>
    );
  }
});
