<template>
  <div class="task-detail-container product-detail-container" v-loading="loading">
    <no-auth v-if="!loading && !hasViewProductAuth"></no-auth>

    <div v-if="hasViewProductAuth" id="product-product-detail"></div>
    <div v-if="hasViewProductAuth" class="task-detail-header common-detail-header">
      <div class="header-btns product-btn-group">
        <div class="product-base-info font-16">
          <span class="product-name">{{ product.name }}</span>
          <IntelligentTagsTaggingView v-bind="tagsSingleComponentAttrs" showType="text" />
        </div>
        <div class="box-14-t-b button-group">
          <div class="header-left-buttons">
            <el-button
              v-if="hasEditProductAuth"
              @click="editProduct"
              v-track="getBtnsTrackData('TO_EDIT')"
              type="plain-third">
              {{ $t('common.base.edit') }}
            </el-button>
            <el-dropdown trigger="click">
              <el-button type="plain-third" v-if="allowCreateTask" v-track="getBtnsTrackData('CREATE_TASK')">{{`${$t('common.base.create')}${$t('common.base.task')}`}}</el-button>
              <el-dropdown-menu slot="dropdown">
                <div class="task-type-dropdown-group">
                  <el-dropdown-item v-for="(type, index) in taskTypes" :key="type.id">
                  <a class="link-of-dropdown" href="javascript:;" @click.prevent="createTask(type.id)">{{type.name}}</a>
                </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown trigger="click">
              <el-button type="plain-third" v-if="allowCreateEvent" v-track="getBtnsTrackData('CREATE_EVENT')">{{`${$t('common.base.create')}${$t('common.base.event')}`}}</el-button>
              <el-dropdown-menu slot="dropdown">
                <div class="task-type-dropdown-group">
                  <el-dropdown-item v-for="event in eventTypes" :key="event.id">
                    <a class="link-of-dropdown" href="javascript:;" @click.prevent="createEvent(event.id)">{{event.name}}</a>
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown trigger="click">
              <el-button type="plain-third" v-if="allowCreatePlanTask && isShowPlanTask" v-track="getBtnsTrackData('CREATE_PLAN_TASK')">{{ `${$t('common.base.create')}${$t('common.base.scheduledTasks')}` }}</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="task in planTypeInfo" :key="task.id">
                  <a class="link-of-dropdown" href="javascript:;" @click.prevent="createPlanTask(task.id)">
                    {{ task.name }}
                  </a>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- 更多 -->
            <template v-if="showMorePopover">
              <el-dropdown
                trigger="click"
                placement="top-end"
              >
                <el-button class="el-dropdown-link" type="plain-third">
                  {{$t('common.base.more')}}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" class="more-btns">
                  <el-dropdown-item
                    v-if="!dataInfo.qrcodeId && isQrcodeEnabled && canProductCodeProduct"
                    v-track="getBtnsTrackData('RELATE_QRCODE')"
                    @click.native="openPublicDialog('linkQrcode')"
                  >
                    {{$t('product.detail.btns.btn2')}}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="dataInfo.qrcodeId && isQrcodeEnabled && canProductCodeProduct"
                    @click.native="leftActiveTab='qrcode-view'"
                    v-track="getBtnsTrackData('CHECK_QRCODE')">
                    {{$t('product.detail.btns.btn1')}}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="isShowCustomerRemind"
                    @click.native="openRemindDialog('remind')"
                    v-track="getBtnsTrackData('ADD_REMIND')">
                    {{ $t('common.base.addRemind') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    @click.native="openBindDialog()"
                    v-if="showBindCustomer">
                    {{$t('product.btns.relateCustomer')}}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="isShowCreateSmartPlan"
                    @click.native="handleMoreOperationCommand('smartPlan')">
                    {{ $t('smartPlan.title') }}
                  </el-dropdown-item>
                  <template v-if="pageButtonSetGray">
                    <template v-for="(item, index) in pageButtonList">
                      <el-dropdown-item ><div @click="handlePageButtonClick(item)">{{ item.name }}</div></el-dropdown-item>
                    </template>
                  </template>
                  <el-dropdown-item
                    v-if="allowDeleteProduct"
                    @click.native="deleteProduct"
                    v-track="getBtnsTrackData('DELETE')">
                    {{ $t('common.base.delete') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <!-- <template v-if="pageButtonSetGray">
              <template v-for="(item, index) in pageButtonList">
                <el-button
                  :type="item.viewType"
                  @click="pageButtonClick(item, [product.id], {}, ()=>{pageButtonLoading = true}, null, ()=>{
                    pageButtonLoading = false
                  })"
                  :loading="pageButtonLoading"
                  >
                  {{item.name}}
                </el-button>
              </template>
            </template> -->
          </div>
          <!-- <div class="fle-x mar-l-20">
            <el-tooltip v-if="dataInfo.qrcodeId"
                        :popper-options="popperOptions"
                        content="查看二维码"
                        placement="top">
              <i class="iconfont icon-qrcode cur-point font-18"
                  @click="leftActiveTab='qrcode-view'"></i>
            </el-tooltip>
            <el-tooltip :popper-options="popperOptions"
                        content="加提醒"
                        placement="top">
              <i class="iconfont icon-bell cur-point font-18"
                  @click="openRemindDialog('remind')"></i>
            </el-tooltip>

            <el-tooltip
              v-if="allowDeleteProduct"
              :popper-options="popperOptions"
              content="删除产品"
              placement="top"
            >
              <i class="iconfont icon-delete cur-point font-18"
                  @click="deleteProduct"></i>
            </el-tooltip>
            <el-tooltip :popper-options="popperOptions"
                        content="编辑产品"
                        placement="top">
              <i class="iconfont icon-edit-square cur-point font-18"
                  @click="editProduct"></i>
            </el-tooltip>
          </div> -->
        </div>
        <!-- <div class="btn-box flex-x flex-1 box-12-t-b">
          <div class="btn-item">
            <el-button
              icon="iconfont icon-notification"
              size="medium"
              @click="openRemindDialog('remind')"
              v-if="isShowCustomerRemind"
            >加提醒</el-button
            >
          </div>
          <div class="btn-item">
            <el-dropdown
              trigger="click"
              v-if="allowCreatePlanTask && isShowPlanTask"
            >
              <el-button size="medium"
              ><i class="iconfont icon-add1"></i>计划任务</el-button
              >

              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="task in taskTypes" :key="task.id">
                  <a
                    class="link-of-dropdown"
                    href="javascript:;"
                    @click.prevent="createPlanTask(task.id)"
                  >
                    {{ task.name }}
                  </a>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="btn-item" size="medium">
            <el-button>关联二维码</el-button>
          </div>
          <div class="btn-item" size="medium">
            <el-button>查看二维码</el-button>
          </div>
        </div> -->
      </div>
      <div class="header-data"></div>
    </div>
    <!-- end 顶部操作区 -->

    <!-- start 工单详情折叠面板 -->
      <BaseTileLayoutTabBar
        :bar-list="taskLayoutTabBarList"
        :now-item="leftActiveTab"
        @changeItem="tabBarChangeItem"
        @openLayoutModal="openBaseLayoutModal"
        v-if="taskLayout == 1"
      ></BaseTileLayoutTabBar>
    <base-collapse
      class="task-detail-main-content detail-main-content"
      v-if="hasViewProductAuth"
      :show-collapse="showCollapse"
      :direction.sync="collapseDirection"
      :hidePartCollapse="hidePartCollapse"
      >
      <!-- start 工单详情 -->
      <template slot="left">
        <div class="task-detail-main-content-left detail-main-content-left"
             v-show="collapseDirection != 'left'">
              <BaseTileLayoutTabBar
                :bar-list="leftTabBarList"
                :now-item="leftActiveTab"
                @changeItem="tabBarChangeItem"
                @openLayoutModal="openBaseLayoutModal"
                v-if="taskLayout == 2"
              ></BaseTileLayoutTabBar>
          <div class="base-detail-layout-left-content" :class="{'bbx-normal-form-view-cell': formCellCount > 1, 'height-100': leftActiveTab == 'qrcode-view'}" id="product-product-detail-1">
            <div v-if="leftActiveTab == 'product-view'" :class="{ 'height-100': leftActiveTab == 'qrcode-view' }">
                            <!-- <div class="detail-main-content-left-head">
                <div class="head-label label-delete"
                     v-if="dataInfo.isDelete">{{$t('common.base.deleted')}}</div>
                <div class="main-name-tags">
                  <span class="tagText">{{ product.name }}</span>
                  <IntelligentTagsTaggingView v-bind="tagsSingleComponentAttrs" showType="icon"  v-if="tagsMainDataList.length" />
                </div>
              </div> -->
              <!-- <div
                class="flex-x product-type form-view-row"
                v-if="dataInfo && dataInfo.catalogId">
                <label>产品类型</label>
                <div class="flex-1"  v-if="isOpenSuperCodePro">
                  <el-tooltip
                    effect="dark"
                    :content="dataInfo.catalogPathName"
                    placement="bottom-start">
                    <div class="color-primary overHideCon-1"  v-if="productMenuAuth">
                      <span
                        @click="openProductMenuTab(dataInfo.catalogId)"
                        class="cur-point ">
                        {{dataInfo.catalogPathName}}
                      </span>
                    </div>
                    <div class="overHideCon-1"  v-else>
                      <span>
                        {{dataInfo.catalogPathName}}
                      </span>
                    </div>
                  </el-tooltip>
                </div>
                 <div class="flex-1"  v-else>
                  <el-tooltip
                    effect="dark"
                    :content="dataInfo.type"
                    placement="bottom-start">
                    <div class="overHideCon-1">
                      <span>
                        {{dataInfo.type}}
                      </span>
                    </div>
                  </el-tooltip>
                </div>
              </div> -->
              <form-view :fields="fields"
                         :value="product"
                         :form-cell-count="formCellCount"
                         class="task-tab-container task-view-containner tab-container bbx-cell-form-view">
                <!-- <div slot="name"></div> -->
                <template slot="createUser" slot-scope="{ value }">
                  <div v-if="value" class="form-view-row bbx-form-cell-item">
                    <label>{{$t('common.base.createUser')}}</label>
                    <div class="form-view-row-content">
                      <span v-user="value.userId" class="user-card-triggle">
                        <template v-if="isOpenData">
                          <open-data type="userName" :openid="product.createUserStaffId"></open-data>
                        </template>
                        <template v-else>
                          <span>{{ value.displayName}}</span>
                        </template>
                      </span>
                    </div>
                  </div>
                </template>
                <template slot="isRepeatRepair" slot-scope="{ field, value }">
                  <div class="form-view-row bbx-form-cell-item">
                    <label>{{ $t('product.detail.type.isRepeatRepair') }}</label>
                    <div class="form-view-row-content" :style="{color: field.setting.color || '#F4882f'}">
                      {{ value || $t('common.base.no') }}
                    </div>
                  </div>
                </template>
                <template slot="customer"
                          slot-scope="{ value }">
                  <div class="form-view-row bbx-form-cell-item">
                    <label>{{ $t('common.base.customer') }}</label>
                    <div class="form-view-row-content form-view-row-customer flex">
                      <span class="link" @click="openCustomer" v-if="product.customerName">
                        {{ product.customerName }}
                      </span>
                      <span class="unbind" v-else>
                        {{ $t('common.base.notContact') }}
                      </span>
                      <BizIntelligentTagsView type="detail" :tags-list="product['customer'] ? product['customer']['labelList'] || [] : []" :config="{ normalShowType:'text', normalMaxLength: 3 }"></BizIntelligentTagsView>
                    </div>
                  </div>
                  <div class="form-view-row bbx-form-cell-item"
                       v-if="value && hasLinkman">
                    <label>{{ $t('common.base.contact') }}</label>
                    <div class="form-view-row-content">
                      {{
                        product.linkman &&
                          product.linkman.name + ' ' + product.linkman.phone
                      }}
                    </div>
                  </div>
                  <form-address-view
                    class="bbx-form-cell-item"
                    v-if="value && hasAddress"
                    :field="{ displayName: $t('product.type.customerAddress') }"
                    :value="product.address">
                  </form-address-view>
                  <!-- 客户负责人 -->
                  <div class="form-view-row bbx-form-cell-item" v-if="value && isShowCustomerManager">
                    <label>客户负责人</label>
                    <div class="form-view-row-content">
                      {{ customer.customerManager }}
                    </div>
                  </div>
                  <!-- 联系方式 -->
                  <div class="form-view-row bbx-form-cell-item" v-if="value && isShowCustomerContact">
                    <label>联系方式</label>
                    <div class="form-view-row-content">
                      {{ product.linkman.phone }}
                    </div>
                  </div>
                  <!-- 客户等级 -->
                  <div class="form-view-row bbx-form-cell-item" v-if="value && isShowCustomerLevel">
                    <label>客户等级</label>
                    <div class="form-view-row-content">
                      {{ customer.customerLevel }}
                    </div>
                  </div>
                  <!-- 客户来源 -->
                  <div class="form-view-row bbx-form-cell-item" v-if="value && isShowCustomerSource">
                    <label>客户来源</label>
                    <div class="form-view-row-content">
                      {{ customer.customerSource }}
                    </div>
                  </div>
                </template>

                <div slot="qrcodeId"
                     slot-scope="{ value }">
                     <!-- <div class="form-view-row">
                    <label>产品二维码</label>
                    <div class="form-view-row-content" v-show="value">
                      <span>{{ value }}</span>
                      <div ref="qrcode" style="margin: 10px 0;"></div>
                      <a
                        href="javascript:;"
                        class="link"
                        @click="openDownloadCodeDialog"
                      >下载</a
                      >
                      <a
                        href="javascript:;"
                        class="link"
                        @click="unbindQrcodeFromProduct"
                      >删除</a
                      >
                    </div>
                    <div class="form-view-row-content" v-show="!value">
                      <a
                        href="javascript:;"
                        class="link"
                        @click="openPublicDialog('linkQrcode')"
                      >关联二维码</a
                      >
                    </div>
                  </div> -->
                </div>
                <template slot="catalogId" slot-scope="{ value }">
                  <div v-if="dataInfo && dataInfo.catalogId" class="form-view-row product-type bbx-form-cell-item">
                    <label>{{$t('common.base.productType')}}</label>
                    <div class="flex-1" v-if="isOpenSuperCodePro">
                      <el-tooltip
                        effect="dark"
                        :content="dataInfo.catalogPathName"
                        placement="bottom-start">
                        <div class="color-primary overHideCon-1" v-if="productMenuAuth">
                          <span
                            @click="openProductMenuTab(dataInfo.catalogId)"
                            class="cur-point ">
                            {{dataInfo.catalogPathName}}
                          </span>
                        </div>
                        <div class="overHideCon-1" v-else>
                          <span>
                            {{dataInfo.catalogPathName}}
                          </span>
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="flex-1" v-else>
                      <el-tooltip
                        effect="dark"
                        :content="dataInfo.type"
                        placement="bottom-start">
                        <div class="overHideCon-1">
                          <span>
                            {{dataInfo.type}}
                          </span>
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                </template>

                <template slot="createTime"
                          slot-scope="{ value }">
                  <div class="form-view-row bbx-form-cell-item"
                       v-if="value">
                    <label>{{ $t('common.base.createTime') }}</label>
                    <div class="form-view-row-content">
                      <span>{{ value | fmt_datetime }}</span>
                    </div>
                  </div>
                </template>

                <!-- 产品目录 -->
                <template slot="productMenu" slot-scope="{ field }">
                  <div class="form-view-row bbx-form-cell-item">
                    <label>{{ field.displayName }}</label>
                    <div class="form-view-row-content">
                      {{ dataInfo.productMenu }}
                    </div>
                  </div>
                </template>

                <!-- start 产品负责人 -->
                <template slot="productManager" slot-scope="{ field }">
                  <div class="form-view-row bbx-form-cell-item">
                    <label>{{ field.displayName }}</label>
                    <div class="form-view-row-content">
                      <span v-user="dataInfo.productManager" class="user-card-triggle">
                        <template v-if="isOpenData">
                          <open-data type="userName" :openid="dataInfo.productManagerStaffId"></open-data>
                        </template>
                        <template v-else>
                          {{ dataInfo.productManagerName }}
                        </template>
                      </span>
                    </div>
                  </div>
                </template>
                <!-- end 产品负责人 -->
              </form-view>
            </div>
            <div v-if="leftActiveTab == 'catalog-view'">
              <div class="flex-x jus-center"
                   v-if="dataInfo && !dataInfo.catalogId">
                <div class="flex-y al-center">
                  <img :src="noneImage1"
                       class="size-160 mar-t-50 mar-b-8"
                       alt="">
                  <div class="mar-b-12">{{ $t('product.detail.tips.tip1') }}</div>
                  <el-button @click="openPublicDialog('linkCatalog')">{{ $t('product.detail.btns.btn3') }}</el-button>
                </div>
              </div>

              <template v-if="dataInfo && dataInfo.catalogId">
                <catalog-view ref="catalogView"
                              @unRelationSuc='unRelationSuc'
                              :prop-data="dataInfo" />
              </template>
            </div>
            <div  v-show="leftActiveTab == 'qrcode-view'">
              <div class="flex-x jus-center"
                   v-if="!dataInfo.qrcodeId">
                <div class="flex-y al-center">
                  <img :src="noneImage2"
                       class="size-160 mar-t-50 mar-b-8"
                       alt="">
                  <div class="mar-b-12">
                    {{ productQrcodeNoRelationTip }}
                  </div>
                  <el-button v-if="canProductCodeProduct" @click="openPublicDialog('linkQrcode')">
                    {{ productQrcodeNoRelationButtonText }}
                  </el-button>
                </div>
              </div>

              <template v-else>
                <div class="box-12">
                  <div class="form-view-row">
                    <div class="form-view-row-content form-view-row-code-box" v-show="dataInfo && dataInfo.qrcodeId">
                      <!-- 新增小程序二维码 -->
                      <div class="code-wrap">
                        <div class="code-item">
                          <div ref="qrcode"></div>
                          <span class="code-type">{{ $t('product.detail.h5WebpageAccess') }}</span>
                          <div class="hover-box" v-if="canProductCodeProduct || canProductCodeDownload">
                            <i class="iconfont icon-delete" v-if="canProductCodeProduct" @click="unbindQrcodeFromProduct"></i>
                            <i class="iconfont icon-download" v-if="canProductCodeDownload" @click="openDownloadCodeDialog('qrcode')"></i>
                          </div>
                        </div>
                        <div class="code-item" v-if="miniProgramCodeImg || (isDoorAppletGenericGray && miniProgramCodeImgCommon && dataInfo && dataInfo.qrcodeId)">
                          <img v-if="miniProgramCodeImg" :src="miniProgramCodeImg" alt="">
                          <img v-if="miniProgramCodeImgCommon" :src="miniProgramCodeImgCommon" alt="">
                          <span class="code-type">{{ $t('product.detail.wechatMiniProgramAccess') }}</span>
                          <div class="hover-box">
                            <i class="iconfont icon-delete" v-if="!miniProgramCodeImgCommon" @click="unbindQrcodeFromProduct"></i>
                            <i class="iconfont icon-download" @click="openDownloadCodeDialog('miniProgramCode')"></i>
                          </div>
                        </div>
                      </div>
                      <span class="code-id">{{dataInfo.qrcodeId}}</span>
                      <div v-if="isReportAuth" class="analysis-btn">
                        <el-button type="primary" @click="goQrcode(dataInfo.qrcodeId)">{{ $t('product.detail.qrCodeAccessAnalysis') }}</el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <div class="collapse-left"
             v-show="collapseDirection == 'left'">
          {{ $t('product.detail.tabs.tab1') }}
        </div>
      </template>
      <!-- end 工单详情 -->

      <template slot="rightExtend">
        <button class="right-extend-btn" @click="rightActiveTab = 'info-record'">
          <i class="iconfont icon-message1"></i>{{ $t('common.base.addRemark') }}
        </button>
      </template>

      <!-- start 附加组件 -->
      <template slot="right">
        <div class="task-detail-main-content-right detail-main-content-right" v-show="collapseDirection != 'right'" v-loading="rightTabBarLoading">
          <BaseBarV3
            :bar-list="rightTabBarList"
            :now-item="rightActiveTab"
            @changeItem="tabBarChangeItem"
            module="PRODUCT"
            :track-config="{ifTrack: true}"
            @upDateBarList="tabBarUpdateList"
            v-if="taskLayout == 2"
            >
          </BaseBarV3>
          <div class="right-content-box" ref="rightContent">
            <info-record v-if="rightActiveTab == 'info-record'" ref="producInfoRecord" :share-data="propsForSubComponents" />
            <task-table v-else-if="rightActiveTab == 'task-table'" :share-data="propsForSubComponents" :auth="permission" :extra-attrs="tableExtraAttrs"></task-table>
            <BaseEventTable
              v-else-if="rightActiveTab == 'event-table'"
              module="project"
              :share-data="propsForSubComponents"
              :otherParams="{productId: product.id}"
            />
            <customer-contract-table v-else-if="rightActiveTab == 'product-contract'" :share-data="propsForSubComponents" type="productContract"></customer-contract-table>
            <smart-plan-table v-else-if="rightActiveTab == 'smart-plan-table'" :product-ids="[product.id]"></smart-plan-table>
            <plan-table v-else-if="rightActiveTab == 'plan-table'" :share-data="propsForSubComponents" :extra-attrs="tableExtraAttrs"></plan-table>
            <remind-table v-else-if="rightActiveTab == 'remind-table'" :share-data="propsForSubComponents" :extra-attrs="tableExtraAttrs"></remind-table>
            <product-quality-info v-else-if="rightActiveTab == 'product-quality-info'" :share-data="propsForSubComponents" :permission="permission"></product-quality-info>
            <product-faultLibrary-table v-else-if="rightActiveTab == 'fault-library-table'" :share-data="propsForSubComponents"></product-faultLibrary-table>
            <WarrantyCard v-else-if="rightActiveTab == 'warranty-card'" :share-data="propsForSubComponents" :extra-attrs="tableExtraAttrs"/>
            <task-detail-card
              ref="taskDetailCard"
              :show-name-list="false"
              :product='product'
              :form-cell-count="formCellCount"
              v-if="initData.openSuperCodePro"
              v-show="isAddOn(rightActiveTab)"
              :share-data="propsForSubComponents"
              :init-data="initData">
            </task-detail-card>
            
          </div>
        </div>
      </template>
      <!-- end 附加组件 -->
    </base-collapse>
    <!-- end 工单详情折叠面板 -->

    <remind-dialog ref="addRemindDialog"
                   :product="product"></remind-dialog>


    <public-dialog :product-id="product.id"
                   :dialog-type="dialogType"
                   @dialogBind="dialogBind"
                   ref="publicDialog" />
    <download-code :code-data="downloadCodeData"
                   ref="downloadCodeDialog"></download-code>

    <customer-bind ref="customerBind" :fields="fields" @refresh="unRelationSuc"></customer-bind>
    <!-- 新建智能计划弹窗 -->
    <createSmartPlanDialog :products="[product]" ref="createSmartPlanDialog" />
    <biz-layout-modal
      ref="bizLayoutModal"
      :biz-layout-type="taskLayout"
      :columns="formCellCount"
      @changeLayout="changeTaskDetailLayout">
    </biz-layout-modal>
  </div>
</template>

<script>
import ProductDetailView from './ProductDetailView';
export default ProductDetailView;
</script>

<style lang="scss">
@import "./ProductDetailView.scss";
</style>
<style lang="scss" scoped>
.task-detail-header {
  padding: 12px;
  margin-bottom: 12px;
  .header-btns {
    // padding: 0 16px;
  }
  .header-data {
    // padding: 0 16px;
  }
}
.task-detail-btn-group {
  position: absolute;
  right: 12px;
  top: 8px;

  font-size: 0;
  z-index: 995;

  .iconfont {
    margin-left: 16px;
    color: $text-color-secondary;
    cursor: pointer;

    &.icon-bianji1 {
      @include fontColor();
    }

    &.icon-shanchu-copy {
      margin-left: 13px;
    }
  }
}

.task-detail-btn-group-point {
  z-index: 997;
  background: #fff;
}

.product-type {
  padding-left: 16px;
  margin-top: 0px;
  margin-bottom: 4px;
}

.link {
  color: $color-primary-light-6;
  cursor: pointer;
  display: inline-block;
}
.unbind{
  color:#8C8C8C;
  // cursor: pointer;
}

// ::v-deep .el-table--border{
//   border:1px solid #ebeef5 !important;
// }
.bbx-base-tab-bar-box{
  background-color: $bg-color-l2;
}
.product-btn-group {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  .product-base-info {
    flex: 1;
    display: inline-flex;
    gap: 4px;
    align-items: center;
    overflow: hidden;
    .product-name{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // color: $color-primary-light-6;
    }
    ::v-deep .biz-intelligent-tags__tagging-view {
      flex: 0 1 auto;
      flex-shrink: 0;
      margin-right: 12px;
    }
  }
  .button-group{
    display: flex;
    align-items: center;
  }
}
.task-type-dropdown-group {
  display: flex;
  flex-flow: column;
  max-height: 70vh;
  overflow: auto;
}
</style>
<style lang="scss">
  @import "@src/assets/scss/common-detail.scss";
</style>

<style lang="scss">
.task-detail-container {
  .v-step[data-v-7c9c03f0] {
    background: #fff !important;
    color: #333 !important;
    -webkit-filter: drop-shadow(
      0px 9px 28px 8px rgba(0, 0, 0, 0.05)
    ) !important;
    filter: drop-shadow(0px 9px 28px 8px rgba(0, 0, 0, 0.05)) !important;
    padding: 0 !important;
    min-width: 240px !important;
    max-width: 350px !important;
  }

  .v-step .v-step__arrow[data-v-7c9c03f0] {
    border-color: #fff !important;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
  }

  .guide-model-box {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 996;
  }

  .bg-w {
    background: #fff;
  }

  .task-detail-step-2-box {
    position: absolute;
    top: 0;
    left: 208px;
    z-index: 997;

    .task-detail-step-2 {
      width: 104px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      top: 0;
      left: 0;
      position: absolute;
      background: transparent;
    }
  }
}
.header-btns {
  .iconfont {
    font-size: 14px;
    // margin-right: 8px;
  }
  .product-img {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }
  // .product-name {
  //   font-size: 16px;
  //   font-weight: 600;
  // }
  .btn-box {
    .btn-item {
      &:not(:first-child) {
        margin-left: 12px;
      }
    }
  }
}

.qrcodeBtn {
  width: 250px;
}
.detail-more-dropdown-menu .el-dropdown-menu__item {
  width: initial;
  max-width: 180px;
  @include text-ellipsis();
}

.base-detail-layout-left-content {
  overflow-y: auto;
}
.product-detail-container {
  position: fixed;
  left: 0;
  right: 0;
  margin: 12px;
  top: 0;
}

.more-btns{
  .el-dropdown-menu__item{
    min-width: 116px !important;
    width: auto !important;
    height: 36px !important;
    line-height: 22px !important;
    padding: 7px 16px !important;
  }
}
</style>
