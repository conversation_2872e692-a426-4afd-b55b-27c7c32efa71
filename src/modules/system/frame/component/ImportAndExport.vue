<template>
  <div class="frame-export-panel">
    <!-- start 标题 -->
    <div class="frame-export-title">
      <h3>{{ title }}（{{total || 0}}）</h3>
      <div class="frame-export-operation">
        <el-popover trigger="hover" popper-class="el-dropdown-menu" ref="dropdown" @command="setSortMark">
          <span class="el-dropdown-link just-cur-point" slot="reference">
            {{sortMark == 0 ? $t('frame.backgroundTask.text1') : $t('frame.backgroundTask.text2')}}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <div class="el-dropdown-menu__item" @click="setSortMark(0)">{{$t('frame.backgroundTask.text1')}}</div>
          <div class="el-dropdown-menu__item" @click="setSortMark(1)">{{$t('frame.backgroundTask.text2')}}</div>
          <!-- <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="0">按时间倒序</el-dropdown-item>
            <el-dropdown-item command="1">按时间正序</el-dropdown-item>
          </el-dropdown-menu> -->
        </el-popover>
        <span class="delete" @click="clearRecord">{{$t('frame.backgroundTask.text3')}}<i class="iconfont icon-qingkongshanchu el-icon--right"></i></span>
      </div>
    </div>
    <!-- end 标题 -->
    <template v-if="sourceList.length > 0">
      <div v-for="item in sourceList" :key="item.id" :class="getSourceItemClassNames(item)">
        <div class="export-row-left">
          <img :src="importAndExportImage1" v-if="item.name.substring(item.name.lastIndexOf('.')+1)==='zip'">
          <img :src="xiaoBaoIcon" v-else-if="item.module == 'AI_TASK_SUMMARY_EXPORT'">
          <img :src="importAndExportImage2" v-else>
          
          <div class="export-row-info">
            <h4>{{item.name}}</h4>
            <p><i :class="`${item.action=='export'?'el-icon-download':'el-icon-upload2'}`"></i> {{item.createTime | fmt_datetime}}<span>{{item.fileSize}}KB</span></p>
          </div>
        </div>

        <div class="export-row-right">
          <div>
            <!-- start 导出状态 -->
            <div 
              class="export-row-badge" 
              :class="{
                'export-row-badge-doing': item.isFinished == 0,
                'export-row-badge-finished': item.isFinished == 1,
                'export-row-badge-error': item.isFinished == 2,
                'export-row-badge-cancel': item.isFinished == 3,
                'export-row-badge-wait': item.isFinished == 4,
              }"
            >
              {{ getStatusText(item) }}
            </div>
            <!-- end 导出状态 -->

            <!-- start 未下载 -->
            <template>
              <span class="export-not-download">
                <template v-if="item.isFinished == 0 && item.exportProgressBar">{{ item.exportProgressBar }}</template>
                <template v-else>
                  {{showOperateButton(item) && item.isFinished==1 && item.isDownload==0 ? $t('common.base.notDownloaded') : ''}}
                </template>
              </span>
            </template>
            <!-- end 未下载 -->
          </div>

          <div>
            <template v-if="operationList.some(o => o.id == item.id && item.isDownloaDongoing == 1)">
              <span class="export-operate-btn">{{$t('common.base.downloading')}}</span>
            </template>

            <!-- start 操作按钮显示 -->
            <template v-else>

              <!-- start 导出 -->
              <div v-if="showOperateButton(item)">
                <div @click="operateExport(item)" class="btn btn-text export-operate-btn">
                  <span v-if="item.isFinished == 0">{{$t('common.base.cancel')}}</span>
                  <template v-else-if="item.isFinished == 1">
                    <span v-if="isReadFile(item)">
                      {{ $t('common.base.view') }}
                    </span>
                    <span>
                      {{item.isDownload == 0 ? $t('common.base.download') : $t('common.base.reDownload')}}
                    </span>
                  </template>
                  
                </div>
              </div>
              <!-- end 导出 -->

              <!-- start 导入 或 批量更新 -->
              <div v-if="item.action == 'import' || item.action == 'update' " >
                <div @click="operateImportAndUpdate(item)" class="btn btn-text export-operate-btn">
                  <span v-if="item.isFinished == 0 && isImportDelete(item.createTime, 30)">
                    {{$t('common.base.cancel')}}
                  </span>
                  <span class="go_see" v-if="item.isFinished == 1 && isHaveReasons(item)">
                    {{$t('common.base.view')}}
                  </span>
                  <span class="go_see" v-if="item.isFinished == 2">
                    {{$t('frame.backgroundTask.text4')}}
                  </span>
                </div>
              </div>
              <!-- end 导入 或 批量更新 -->

              <!-- start 绩效 -->
              <div v-if="item.action == 'calculation'" >
                <div @click="operateCalculation(item)" class="btn btn-text export-operate-btn">
                  <span v-if="item.isFinished == 0 && isImportDelete(item.createTime, 10)">
                    {{$t('common.base.cancel')}}
                  </span>
                  <span class="go_see" v-if="item.isFinished == 1">
                    {{$t('common.base.checkDetail')}}
                  </span>
                  <span class="go_see" v-if="item.isFinished == 2">
                    {{$t('frame.backgroundTask.text4')}}
                  </span>
                </div>
              </div>
              <!-- end 绩效 -->
            </template>
            <!-- start 删除 -->
            <button type="button" class="btn btn-text export-operate-btn export-delete-btn" @click="onDeleteRecord(item)">
              <span v-if="item.isFinished !== 0 ">
                {{$t('common.base.delete')}}
              </span>
            </button>
            <!-- end 删除 -->

            <!-- end 操作按钮显示 -->
          </div>
        </div>

      </div>
    </template>
    <p class="export-empty" v-else>
      {{$t('frame.backgroundTask.tip1')}}
    </p>

    <!-- start 导出更新失败 原因弹窗 -->
    <base-modal 
      :title="errorDialogTitle" 
      :show.sync="errorDialog" 
      width="500px"
      class="import-update-error-dialog"
    >
      <div class="import-update-error-dialog-body">
        <p v-for="(reason, index) in reasons" :key="`reson_import${index}`">
          {{ reason }}
        </p>
      </div>
      <div slot="footer" class="import-update-error-dialog-footer">
        <!-- <el-button type="danger" @click="deleteRecord(item)" :loading="pending" :disabled="pending">
          {{ pending ? '删除中' : '删除记录' }}
        </el-button> -->
        <el-button @click="errorDialog = false" :disabled="pending">
          {{$t('common.base.cancel')}}
        </el-button>
        <el-button type="primary" @click="errorDialog = false" :disabled="pending">
          <a :href="`/excels/exportErrorData/${item.id}`" v-if="errorIndex.length>0">{{$t('frame.backgroundTask.text6')}} </a>
          <span v-else>{{$t('common.base.makeSure')}}</span>

        </el-button>
        <el-button type="primary" @click="onSkipOverData " :disabled="pending" v-if="reasons.length>0&& errorIndex.length>0 && wrongDataMark == 0">
          {{$t('frame.backgroundTask.text7')}}
        </el-button>
        <!-- <el-button type="primary" @click="errorDialog = false" :disabled="pending">
          确定
        </el-button> -->
      </div>
    </base-modal>
    <!-- end 导出更新失败 原因弹窗 -->

    <!-- start 查看绩效报告 统计信息弹窗 -->
    <base-modal :title="$t('frame.backgroundTask.text13')" :show.sync="performanceDialogVisible" width="500px" class="performance-report-modal">
      <div class="stage-success">
        <p>{{$t('frame.backgroundTask.text8')}}：{{createReportResult.name || createReportResult.reportName}}</p>
        <p>{{$t('frame.backgroundTask.text9')}}：{{createReportResult.totalNumber}}</p>
        <p>{{$t('frame.backgroundTask.text10')}}：{{createReportResult.hitNumber}}</p>
        <p>{{$t('frame.backgroundTask.text11')}}：{{createReportResult.time}}</p>
        <div class="dialog-footer" style="margin-top: 15px;">
          <el-button type="primary" @click="performanceViewDetail">{{$t('frame.backgroundTask.text12')}}</el-button>
          <el-button type="primary" @click="performanceDialogVisible = false" :disabled="pending">
            {{$t('common.base.makeSure')}}
          </el-button>
        </div>
      </div>
    </base-modal>
    <!-- end 查看绩效报告 统计信息弹窗 -->
    
    <!-- start 工单AI摘要 -->
    <AiSummariesDialog
      class="task-ai-summaries-dialog"
      ref="AiSummariesDialog"
    >
    </AiSummariesDialog>
    <!-- end 工单AI摘要 -->
    
  </div>
</template>

<script>
/* components */
import AiSummariesDialog from '@src/modules/task/list/components/AiSummariesDialog'
/* eslint-disable indent */
import platform from '@src/platform'
import http, { downloadByhref } from '@src/util/http';
import { safeNewDate } from '@src/util/time';
/** constants */
/* api */
import * as ImportApi from '@src/api/ImportApi'
import { excelsRead } from '@src/api/ExcelsApi'
/* model */
import MsgModel from '@model/MsgModel';

import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
// 支持下载的actions  导出/批量生成服务报告/批量打印服务报告
const SupportDownloadActions = ['export', 'taskServiceReportBatch', 'taskServicePrintBatch', 'taskExceptionChartExport', 'paasBackExport', 'paasBackImport', 'paasFlowLogExport', 'taskAttachmentDownloadBatch', 'eventAttachmentDownloadBatch', 'batchPrintExport']
import i18n from '@src/locales';
const $t = i18n.t.bind(i18n);

import { getOssUrl, getLocalesOssUrl } from '@src/util/assets'

const xiaoBaoIcon = getLocalesOssUrl('/xiao-bao-icon.png')

const importAndExportImage1 = getOssUrl('/zip.png')
const importAndExportImage2 = getOssUrl('/excel.png')

// 导出的action
const EXPORT_ACTION = [
  'cloudWarehouseBackorderExport',
  'cloudWarehouseDiscrepancyRecordExport',
  'cloudWarehouseInventoryExport',
  'cloudWarehouseInWarehouseExport',
  'cloudWarehouseMaterialExport',
  'cloudWarehouseOutWarehouseExport',
  'cloudWarehouseRelocateExport',
  'cloudWarehouseReplacementExport',
  'cloudWarehouseTransferExport',
  'cloudWarehouseWarehouseExport',
  'eventAttachmentDownloadBatch',
  'paasBackExport',
  'paasFlowLogExport',
  'taskAttachmentDownloadBatch',
  'taskExceptionChartExport',
  'taskExceptionChartExportAll',
  'taskExceptionHistogramExport',
  'taskServicePrintBatch',
  'taskServiceReportBatch'
]
// 导入的action
const IMPART_ACTION = ['cloudWarehouseMaterialImport','cloudWarehouseWarehouseImport']
export default {
  name: 'import-and-export-view',
  components: {
    AiSummariesDialog
  },
  props: {
    sourceList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: $t('frame.backgroundTask.text14')
    },
    total: {
      type: Number
    },
    exportPopperVisible:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      importAndExportImage1,
      importAndExportImage2,
      operationList: [],
      errorDialog: false, // 错误原因弹窗
      errorDialogTitle: '', // 错误原因 弹窗 标题
      pending: false,
      reasons: [], // 错误原因
      errorIndex:[], // 错误数据下标
      item: {},
      performanceDialogVisible: false,
      createReportResult: {},
      sortMark: 0,
      xiaoBaoIcon,
      wrongDataMark: 0 // 0显示跳过错误数据 1不显示
    }
  },
  // TODO dropdown 没有hide这个方法 先注释了
  //  watch: {
  //   'exportPopperVisible'(newValue) {
  //     this.$refs.dropdown.hide() // 隐藏按时间排序
  //   }
  // },
  computed: {
  },
  methods: {
    isReadFile(item) {
      return this.isAIExport(item)
    },
    isAIExport(item) {
      return item.module == 'AI_TASK_SUMMARY_EXPORT'
    },
    /** 判断是否有错误  */
    isHaveReasons(item){
      return item.importInfo?.reasons.length > 0
    },
    /**
     * @description: 跳过错误数据
     * @param {*}
     */
    onSkipOverData() {
      ImportApi.skipErrorImport({id:this.item.id}).then((res)=>{
        this.$message.success(this.$t('common.base.tip.operationSuccess'))
        this.errorDialog = false;
        this.$emit('change', this.operationList);
      }).catch(error=>{
        console.log(error)
      })
    },
    /**
     * @description: 清除记录
     * @param {*}
     */
    async clearRecord() {
      this.$confirm(this.$t('frame.backgroundTask.tip2'), this.$t('common.base.toast'), {
        confirmButtonText: this.$t('common.base.makeSure'),
        cancelButtonText: this.$t('common.base.cancel'),
        type: 'warning',
      }).then(() => {
        ImportApi.emptyExportList().then((res)=>{
          if(res.status == 0) {
            this.$message.success(this.$t('common.base.tip.operationSuccess'))
            this.$emit('change', this.operationList);
          }else{
            this.$message.warning(res.message)
          }

        }).catch(error=>{
          console.log(error)
        })
        this.$emit('onChangeTips', 1)
      }).catch((error) => {
        this.$emit('onChangeTips', 0)
       });
    },
    /**
     * @description:删除单条记录
     * @param {*}
     */
    async onDeleteRecord(item) {
      this.$confirm(this.$t('frame.backgroundTask.tip3', {name: item.name}), this.$t('common.base.toast'), {
        confirmButtonText: this.$t('common.base.makeSure'),
        cancelButtonText: this.$t('common.base.cancel'),
        type: 'warning',
      }).then(() => {
        this.deleteRecord(item);
        this.$emit('onChangeTips', 1)
      }).catch((error) => {
        console.log(error)
       this.$emit('onChangeTips', 0)
       });
    },
    // 选择排序方式
    setSortMark(command) {
      this.sortMark = command;
      this.$emit('onSortMark', this.sortMark)
    },
    /** 取消操作  */
    async cancelOperation(item, action) {
      let url = '';
      let itemAction = item.action;
      let subTitle = item.action == 'calculation' ? this.$t('common.base.create2') : this.$t('common.base.file');
      
      switch (itemAction) {
        case 'export': {
          url = '/excels/offExcel';
          break;
        }
        case 'import': {
          url = '/excels/delete/manual';
          break;
        }  
        case 'update': {
          url = '/excels/delete/manual';
          break;
        }
        case 'calculation': {
          url = '/excels/performance/cancel';
          break;
        }
        default: {
          url = '/excels/cancel';
          break;
        }
      }
      
      if(await platform.confirm(this.$t('frame.backgroundTask.tip4', {subTitle, name: item.name, action}))){
          
        this.operationList.push({id: item.id, operate: 'cancel'})
        try {
          let result = await http.get(url, {id: item.id});
          
          this.operationList = this.operationList.filter(i => i.id != item.id)
          
          let isSuccess = result.status == 0
          if(!isSuccess) {
            platform.alert(result.message);
          }
          
          this.$emit('change', this.operationList);
          
          return
          
        } catch (error) {
          console.error('error: ', error);
        }
      }
    },
    /** @deprecated */
    async execExportFile(item){
      let action = item.action == 'import' ? this.$t('common.base.import') : this.$t('common.base.update');
      const Finished = item.isFinished == 1

      // 导出 取消下载文件
      if(this.showOperateButton(item) && !Finished) {
        if(await platform.confirm(this.$t('frame.backgroundTask.tip5', {name: item.name}))){
          
          this.operationList.push({id: item.id, operate: 'cancel'})
          
          try {
            let result = await http.post('excels/cancel', {id: item.id}, false);
            
            if(result.status == 0) {
              this.operationList = this.operationList.filter(i => i.id != item.id)
            } else {
              platform.alert(result.message);
            }
            this.$emit('change', this.operationList);
            
            return
            
          } catch (error) {
            console.error(error);
          }
          
        }
      }
      // 导出 下载文件
      if((item.action == 'export' || !item.action) && Finished){
        // let frame = document.createElement('iframe');
        // frame.style.display = 'none';
        // frame.src = `/excels/download?id=${item.id}`;
        // document.body.appendChild(frame);

        const a = document.createElement('a')
        a.download = item.name
        a.href = `${isLocalDev() ? '/serve' : ''}/excels/download?id=${item.id}`;
        a.click()
        
        this.operationList.push({id: item.id, operate: 'download'});
        this.$emit('change', this.operationList);
        return
      }
      // 导入或批量更新 取消
      if((item.action == 'import' || item.action == 'update') && !Finished) {
        if(await platform.confirm(this.$t('frame.backgroundTask.tip6', {name: item.name, action}))){
          
          this.operationList.push({id: item.id, operate: 'cancel'})
          try {
            let result = await http.get('/excels/delete/manual', {id: item.id});
            
            this.operationList = this.operationList.filter(i => i.id != item.id)
            
            if(result.status == 0) {
              // 
            } else {
              platform.alert(result.message);
            }
            this.$emit('change', this.operationList);
            
            return
            
          } catch (error) {
            console.error(error);
          }
        }
      }
      // 导入或更新完成
      if(item.action == 'import' || item.action == 'update'){
        try {

          if(item.isFinished == 1) {
            platform.notification({
              title: this.$t('frame.backgroundTask.record1', {action, total: item.importInfo.total}),
              type: 'successs',
            });
          } else if (item.isFinished == 2) {
            this.errorDialog = true;
            this.item = item;
            this.errorDialogTitle = this.$t('frame.backgroundTask.record2', {action});
            this.reasons = item.importInfo.reasons;
            this.errorIndex = item.importInfo.errorIndex || [];

            return
          }

          this.operationList.push({id: item.id, operate: 'cancel'});

          await http.get('/excels/cancel', { id: item.id});

          this.operationList = this.operationList.filter(i => i.id != item.id);
          this.$emit('change', this.operationList);

          return
          
        } catch (error) {
          console.error(error);
        }
      }
    },
    /** 导出下载 */
    exportDownload(item) {
      downloadByhref({href:`${isLocalDev() ? '/serve' : ''}/excels/download?id=${item.id}`, name:item.name})

      item.isDownloaDongoing = 1;
      // let frame = document.createElement('iframe');

      // frame.style.display = 'none';
      // frame.src = `/excels/download?id=${item.id}`;
      // document.body.appendChild(frame);

      // const a = document.createElement('a')
      // a.download = item.name
      // a.href = `${location.port !== '' ? '/serve' : ''}/excels/download?id=${item.id}`;
      // a.click()

      // this.operationList.push({id: item.id, operate: 'download'});
      setTimeout(() => {
        this.$emit('change', this.operationList);
      }, 1000);
    },
    
    /**
     * @description: 删除记录
     * @param {Object} item
     * @param {Boolean} isShowMessage 是否展示删除成功提示消息
     */    
    async deleteRecord(item, isShowMessage = true) {
      this.pending = true;
      try {
        await http.get('/excels/cancel', { id: item.id});

        this.errorDialog = false;
        this.pending = false;

        this.operationList = this.operationList.filter(i => i.id != item.id);
        if(isShowMessage) this.$message.success(this.$t('common.base.deleteSuccess'))
       this.$emit('change', this.operationList);
      } catch (error) {
        console.error('deleteRecord -> error', error)
      }
    },
    /** 当前状态显示的文字  */
    getStatusText(item) {
      let { action, isFinished } = item;
      let text = '';
      
      const isAIExport = this.isAIExport(item)

      switch (action) {
        case 'export': {
          switch (isFinished) {
            case 0: {
              text = isAIExport ? '摘要中' : this.$t('common.base.exporting2');
              break;
            }
            case 1: {
              text = this.$t('common.base.exportSuccess');
              text = isAIExport ? '摘要完成' : text;
              break;
            }
            case 2: {
              text = this.$t('common.base.exportFail');
              text = isAIExport ? '摘要失败' : text;
              break;
            }
            case 3: {
              text = this.$t('common.event.stateProcess.offed');
              text = isAIExport ? '摘要取消' : text;
              break;
            }
            case 4: {
              text = this.$t('frame.backgroundTask.text25');
              break;
            }
            default: {
              break;
            }
          }
          break;
        }
        case 'import': {
          switch (isFinished) {
            case 0: {
              text = this.$t('common.base.importing2');
              break;
            }
            case 1: {
              text = this.$t('common.base.importSuccess');
              break;
            }
            case 2: {
              text = this.$t('common.base.importFail');
              break;
            }
             case 3: {
              text = this.$t('common.event.stateProcess.offed');
              break;
            }
            default: {
              break;
            }
          }
          break;
        }
        case 'update': {
          switch (isFinished) {
            case 0: {
              text = this.$t('common.base.tip.updating');
              break;
            }
            case 1: {
              text = this.$t('common.base.usualStatus.finish');
              break;
            }
            case 2: {
              text = this.$t('common.base.tip.updateError');
              break;
            }
            default: {
              break;
            }
          }
          break;
        }
        case 'calculation': {
          switch (isFinished) {
            case 0: {
              text = this.$t('common.base.tip.generating');
              break;
            }
            case 1: {
              text = this.$t('common.base.usualStatus.finish');
              break;
            }
            case 2: {
              text = this.$t('common.base.tip.generateFail');
              break;
            }
            default: {
              break;
            }
          }
          break;
        }
        default: {
          switch (isFinished) {
            case 0: { // 正在
              text = EXPORT_ACTION.includes(action) ? this.$t('common.base.exporting2') : (IMPART_ACTION.includes(action) ? this.$t('common.base.importing2') : this.$t('common.base.processing'));
              break;
            }
            case 1: { // 完成
              text = EXPORT_ACTION.includes(action) ? this.$t('common.base.exportSuccess') : (IMPART_ACTION.includes(action) ? this.$t('common.base.importSuccess') : this.$t('common.base.usualStatus.finish'));
              break;
            }
            case 2: { // 失败
              text = EXPORT_ACTION.includes(action) ? this.$t('common.base.exportFail') : (IMPART_ACTION.includes(action) ? this.$t('common.base.importFail') : this.$t('common.base.messageStatus.fail'));
              break;
            }
            case 3: { // 已取消
              text = this.$t('common.event.stateProcess.offed');
              break;
            }
            case 4: { // 排队中
              text = this.$t('frame.backgroundTask.text25');
              break;
            }
            default: { // 默认加下
              text = this.$t('common.base.usualStatus.finish');
              break;
            }
          }
          break;
        }
      }

      return text;
    },
    isImportDelete(createTime, minutes) {
      let timeOut = minutes * 60 * 1000;
      let now = safeNewDate().getTime();

      if((createTime + timeOut) < now) {
        return true;
      }
      return false
    },
    /** 导入或更新 完成  */
    importAndUpdateDone(item, action) {
      platform.notification({
        title: this.$t('frame.backgroundTask.record1', {action, total: item.importInfo.total}),
        type: 'successs',
      });
      this.deleteRecord(item);
    },
    /** 打开  失败的弹窗   */
    openErrorDialog(item, action) {
      let reasons = item?.importInfo?.reasons;
      let errorIndex = item?.importInfo?.errorIndex || [];
      let wrongDataMark = item?.importInfo?.wrongDataMark

      this.errorDialog = true;
      this.item = item;
      this.errorDialogTitle = this.$t('frame.backgroundTask.record2', {action});
      this.reasons = Array.isArray(reasons) ? reasons : [reasons];
      this.errorIndex = errorIndex;
      this.wrongDataMark = wrongDataMark
    },
    /** 导出 按钮操作  */
    operateExport(item) {
      
      if(item.isFinished == 0) {
        return this.cancelOperation(item, this.$t('common.base.export'));
      }
      
      if (item.isFinished == 1 && this.isAIExport(item)) {
        return this.readAIExport(item)
      }
      
      this.exportDownload(item);
    },
    readAIExport(item) {
      this.readAiSummaries(item)
    },
    /** 导入，批量更新 按钮操作  */
    operateImportAndUpdate(item) {
      let action = item.action == 'import' ? this.$t('common.base.import') : this.$t('common.base.update');
      let { isFinished } = item;
      let fn = () => ({ });

      switch (isFinished) {
      case 0: {
        fn = this.cancelOperation;
        break;
      }
      case 1: {
        fn = this.openErrorDialog;
        // fn = this.importAndUpdateDone;
        break;
      }
      case 2: {
        fn = this.openErrorDialog;
        break;
      }
      default: {
        break;
      }
      }
      
      fn(item, action);
    },
    /** 绩效 按钮操作  */
    operateCalculation(item) {
      let action = this.$t('common.pageTitle.pagePerformanceList');
      let { isFinished } = item;
      let fn = () => ({ });
      
      this.item = item;
      
      switch (isFinished) {
      case 0: {
        fn = this.cancelOperation;
        break;
      }
      case 1: {
        fn = this.performanceDone;
        break;
      }
      case 2: {
        fn = this.openErrorDialog;
        break;
      }
      default: {
        break;
      }
      }

      fn(item, action);
    },
    /** 绩效报告 完成  */
    performanceDone(item, action) {
      this.performanceDialogVisible = true;
      this.createReportResult = item.importInfo;
    },
    performanceViewDetail() {
      const id = this.createReportResult.reportId || this.createReportResult.id;
      this.performanceDialogVisible = false;

      openAccurateTab({
        type: PageRoutesTypeEnum.PagePerformanceReport,
        titleKey: this.$t('frame.backgroundTask.text16'),
        key: id
      })

      this.deleteRecord(this.item, false);
    },
    /** 
     * @description 是否显示操作按钮 下载/取消
    */
    showOperateButton(listItem = {}) {
      return SupportDownloadActions.indexOf(listItem.action) > -1 || !listItem.action
    },
    openAiSummariesDialog() {
      
      const AiSummariesDialogComponent = this.$refs.AiSummariesDialog
      
      AiSummariesDialogComponent.open()
      AiSummariesDialogComponent.status = 3
      
    },
    readAiSummaries(item) {
      
      const params = {
        id: item.id
      }
      
      excelsRead(params).then(res => {
        
        const isSuccess = MsgModel.isSuccess(res)
        
        if (isSuccess) {
          
          const AiSummariesDialogComponent = this.$refs.AiSummariesDialog
          AiSummariesDialogComponent.showText = res.data
          
          this.openAiSummariesDialog()
          
        } else {
          this.$message.error(res.message)
        }
        
      }).catch(err => {
        
        console.error(err)
        
      })
      
    },
    getSourceItemClassNames(item) {
      
      const baseClass = 'export-row'
      const module = item.module
      const moduleClass = module ? `${baseClass}-${module}` : ''
      
      return {
        [baseClass]: true,
        [moduleClass]: true
      }
    }
  },
}
</script>

<style lang="scss">
  .import-update-error-dialog-body {
    padding: 20px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: scroll;
    p {
      margin-bottom: 5px;
    }
  }
  .import-update-error-dialog-footer {
    display: flex;
    justify-content: flex-end;
    a {
      color: #fff;
      text-decoration: none
    }
  }
  .performance-report-modal {

    .approve-process-container {
      width: 300px;
      margin: 0;
    }

    .base-modal-body {
      padding: 15px;
      padding-right: 25px;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
    }

  }
  .task-ai-summaries-dialog{
    .ai-summaries-result-dialog__reopen-btn {
      display: none;
    }
  }
</style>
