<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">  
  <title>#{title}</title>
  <link rel="stylesheet" href="/resource/plugins/swiper/swiper.min.css">
  <script type="text/javascript">if(navigator.userAgent.toLowerCase().indexOf("msie")>-1)document.write('<script src="/resource/pc-fe-static/js/ie-tip.js"><\/script>')</script>
  <script type="text/javascript">
    // Frame模块专用：设置根窗口标识，解决A项目嵌入B项目时的跨域问题
    // 只有A项目的frame主页面才设置根窗口标识
    // 判断条件：在iframe中 且 父窗口不是A项目自己的窗口
    if (window !== window.top) {
      try {
        // 尝试检查父窗口是否已经有__root_window_标识
        // 如果父窗口有标识，说明当前是A项目的子页面，不应该设置
        if (!window.parent.__root_window_) {
          // 父窗口没有标识，说明当前是A项目的frame主页面被嵌入到B项目
          window.__root_window_ = 'root';
          console.log('[Frame HTML] A项目frame主页面：已设置根窗口标识');
        } else {
          console.log('[Frame HTML] A项目子页面：父窗口已有根标识，不重复设置');
        }
      } catch (e) {
        // 跨域访问父窗口失败，说明被嵌入到外部项目，设置根标识
        window.__root_window_ = 'root';
        console.log('[Frame HTML] 跨域环境检测：已设置根窗口标识');
      }
    } else {
      // 非iframe环境，直接设置根标识
      window.__root_window_ = 'root';
      console.log('[Frame HTML] 非iframe环境：已设置根窗口标识');
    }
    
    if(/DingTalk/.test(navigator.userAgent)){document.write('<script src="//g.alicdn.com/dingding/dingtalk-pc-api/2.8.0/index.js"><\/script>')}
  </script>
  <script data-init="js">window._init = '#{initJson}';</script>
  <script src="/resource/plugins/jQuery/jquery-3.6.0.min.js"></script>
  <script src="/resource/plugins/swiper/swiper.min.js"></script>
  <script src="/resource/plugins/toastr/toastr.min.js"></script>
  <script src="/resource/dingtalk/dingtalkUtil-v2.js?v=20210506.2052"></script>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
  <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
  <link rel="stylesheet" href="/resource/plugins/toastr/toastr.min.css">
  
</head>
<body>
  <div id="app"></div>
  
</body>
  <!-- 下面脚本动态插入如果有问题，可以打开下面注释 -->
  <script src="https://www.soboten.com/paas/component/common/jquery.json-2.4.min.js"></script>
  <script src="https://www.soboten.com/paas/component/common/verto-min-1.1.js"></script>
  <script src="https://www.soboten.com/paas/component/plugin/call_1.8.js"></script>

</html>