/* components */
import EditAddressDialog from '@src/modules/customer/view/operationDialog/EditAddressDialog.vue'
import EditContactDialog from '@src/modules/customer/view/operationDialog/EditContactDialog.vue'
/* util */
import _ from 'lodash'
import * as FormUtil from '@src/component/form/util'
import { getTimestamp, parseDateTime } from 'pub-bbx-utils'
import { findComponentDownward } from '@src/util/assist'
import { getFieldValue2string } from '@service/TaskService.ts'
import ObjectUtil from '@src/util/lang/object.ts';
import Filter from '@src/filter/filter.js';
import { 
  customerAddressSelectConversion,
  linkmanSelectConversion
} from '@src/util/conversionFunctionUtil.ts'
/* Vue */
import props from './props'
import data from './data'
import computed from './computed'
import methods from './methods'
import { safeNewDate } from '@src/util/time';
/* enum */
import { TaskFieldNameMappingEnum } from '@model/enum/FieldMappingEnum.ts'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { openAccurateTab } from '@src/util/platform'
import * as HSConst from '@src/hsjg/const'
/* label */
import { useLabelRelate } from '@hooks/intlabel/useLabelRelate.ts'
/* constant */
import { 
  TASK_PRODUCT_LINKMAN_AND_ADDRESS_NOT_EQUAL_MESSAGE,
  TASK_PRODUCT_LINKMAN_NOT_EQUAL_MESSAGE,
  TASK_PRODUCT_ADDRESS_NOT_EQUAL_MESSAGE,
  REQUIRES_PRODUCT_MESSAGE,
  PLAN_TIME_NOT_LESS_THEN_NOW_MEESSAGE,
  PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE,
  PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE
} from '@src/model/const/Alert.ts'
import MathUtil from '@src/util/math';
// 关联项类型
const RELATION_TYPE_MAP = {
  customer: 'relationCustomer',
  product: 'relationProduct'
}
import { getProductFields } from '@src/api/ProductApi'

const { fieldIntLabel } = useLabelRelate()

export default {
  name: 'event-edit-form',
  // inject: ['initData'],
  props,
  data() {
    data.validation = this.buildValidation();
    data.planTimeDatePickerOptions = {
      disabledDate(time) {
        return time.getTime() < safeNewDate(safeNewDate().toLocaleDateString()).getTime()
      }
    };
    data.isInFirst = 0
    // 客户关联字段值
    data.customerAssociationValue = {
      customerManager: '',  // 客户负责人
      customerLevel: '',  // 客户等级
      customerSource: '',  // 客户来源
      customerContact: '',  // 联系方式
    }
    return data
  },
  computed,
  created () {
    
  },
  watch: {
    'value.customer': {
      handler(newVal) {
        const customer = newVal?.[0] ?? {}
        this.updateCustomerAssociationValue({
          ...this.customerAssociationValue,
          customerManager: customer?.customerManagerName ?? '',
          customerLevel: customer?.attribute?.field_hN95I4gcuSkf270D ?? '',
          customerSource: customer?.attribute?.field_nyJBPzV3bqbKVQVX ?? ''
        })
      },
      immediate: true
    },
    'value.linkman': {
      handler(newVal) {
        const linkman = newVal?.[0] ?? {}
        this.updateCustomerAssociationValue({
          ...this.customerAssociationValue,
          customerContact: linkman?.phone ?? ''
        })
      },
      immediate: true
    }
  },
  mounted () {
    this.eventFields = this.fields
    this.handlePlantimeFields()
    this.eventValue = this.value;
    
    if(this.agendaEvent) {
      this.eventValue.planStartTime = this.planStartTime 
      this.eventValue.planEndTime = this.planEndTime 
    }
    if(this.eventValue.customer && !this.eventValue.isEdit) {
      // 外部联系人手动添加的name没有id。可以不查询详情
      const { id, value } = this.eventValue?.customer?.[0] || {};

      this.updateCustomer(this.eventValue.customer, { 
        isForceUpdateCustomer: true,
        isUpdateCustomerProduct: true,
        isGetDefaultInfo: id || value
      })
    }

    this.initSelectedEventType()

    this.getProductFields()

    this.$eventBus.$on('event_create_or_edit.update_linkman', this.updateLinkman);
    this.$eventBus.$on('event_create_or_edit.update_address', this.addAddressCallback);
  },
  beforeDestroy() {
    this.$eventBus.$off('event_create_or_edit.update_linkman', this.updateLinkman);
    this.$eventBus.$off('event_create_or_edit.update_address', this.addAddressCallback);
  },
  methods: {
    ...methods.fetch,
    ...methods.render,
    ...methods.search,
    ...methods.update,
    fieldIntLabel,
    planTimeChange({ field, newValue, oldValue }) {
      let { fieldName, formType } = field;
      // console.log(fieldName, displayName, newValue, oldValue );
      // if(this.agendaEvent) {
      let isDateTimeType = field?.setting?.dateType == 'date';
      if(isDateTimeType) {
        newValue = getTimestamp(newValue)
      }
      /*let planTime = parseDateTime(newValue).getTime();
      let nowTime = safeNewDate().getTime();
      let tip = formType == 'planStartTime' ? PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE : PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE
      if(planTime < nowTime) {
        this.eventValue[fieldName] = oldValue
        this.$emit('update:value', this.eventValue);
        return this.$platform.notification({
          title: tip,
          type: 'error'
        });
      }*/

      if(this.eventValue.planEndTime && this.eventValue.planStartTime) {
        let sTime = safeNewDate(this.eventValue.planStartTime).getTime();
        let eTime = safeNewDate(this.eventValue.planEndTime).getTime();
        if((sTime >= eTime)){
          //this.$emit('update:value', this.eventValue);
          this.eventValue[fieldName] = oldValue;
          return this.$platform.notification({
            title: this.$t('task.tip.planTimeTip6'),
            type: 'error'
          });
        }

        if(fieldName === 'planStartTime') {
          if(safeNewDate(newValue).getTime() > safeNewDate(this.eventValue.planEndTime).getTime()) {
            this.eventValue[fieldName] = oldValue
            this.$emit('update:value', this.eventValue);
            return this.$platform.notification({
              title: this.$t('event.components.eventEditForm.text6'),
              type: 'error'
            });
          }
          this.$eventBus.$emit('agenda-event-plan-start-time', newValue)
        } else if(fieldName === 'planEndTime') {
          if(safeNewDate(newValue).getTime() < safeNewDate(this.eventValue.planStartTime).getTime()) {
            this.eventValue[fieldName] = oldValue
            this.$emit('update:value', this.eventValue);
            return this.$platform.notification({
              title: this.$t('task.tip.planTimeTip7'),
              type: 'error'
            });
          }
          this.$eventBus.$emit('agenda-event-plan-end-time', newValue)
        }
      }
      // }
    },
    handlePlantimeFields(){
      // 日程新建事件需要将计划开始时间和结束时间字段调整到表单最前面
      if(this.planStartTime && this.planEndTime) {
        let newFields = []
        const planStartTimeField = this.eventFields.filter(field=>field.formType === 'planStartTime')
        const planEndTimeField = this.eventFields.filter(field=>field.formType === 'planEndTime')
        for (let index = 0; index < this.eventFields.length; index++) {
          const field = this.eventFields[index];
          if(field.formType != 'planStartTime' && field.formType != 'planEndTime') {
            newFields.push(field)
          }
        }
        this.eventFields = [...planStartTimeField, ...planEndTimeField, ...newFields]
      } 
    },
    // 初始化选择事件类型
    initSelectedEventType() {
      let defaultType = {}
      if(this.urlParams.defaultTypeId) {
        defaultType = this.eventTypes.filter(item => {
          return item.value === this.urlParams.defaultTypeId
        })[0]
      } else {
        defaultType =this.agendaEvent ? this.eventTypes[0] : (this.defaultType || this.eventTypes[0])
      }
      this.selectedType = defaultType
      this.$emit('updatetemplateId', this.selectedType);
    },

    /** 
     * @description 添加客户 提交
    */
    addCustomerSubmit() {
      // 提交
      this.customerFormDom.submit(data => {
        this.isCreateCustomer = true;
        
        // 绑定客户
        this.updateCustomerValue([{
          label: data.name,
          value: data.id,
          id: data.id
        }])

        // 绑定联系人
        this.bindLinkman({
          id: data.lmId,
          name: data.lmName,
          phone: data.lmPhone
        })

        // 绑定地址
        data.addressId && this.bindAddress({
          id: data.addressId,
          country: data.country,
          province: data.province,
          city: data.city,
          dist: data.dist,
          address: data.address,
          latitude: data.latitude,
          longitude: data.longitude,
        })

        // 查询客户关联字段
        let customerId = data.id || '';
        this.selectCustomerRelation(customerId);
        // 关闭弹窗
        this.addCustomerDialog = false;

      });
    },
    /** 
     * @description 添加产品 提交
    */
    addProductSubmit() {
      let selectedCustomer = this.selectedCustomer
      let customer = {
        ...selectedCustomer,
        id: selectedCustomer.id || selectedCustomer.value
      }
      // 提交
      this.productFormDom.submit(customer, data => {

        let productArr = this.value.product?.length ? _.cloneDeep(this.value.product) : [];
        productArr.push({
          value: data.productId,
          label: data.productName,
          id: data.productId,
          name: data.productName,
          customerId:customer.id,
          ...data
        })
        // 绑定产品
        this.updateProductValue(productArr);
        // 更新产品信息
        this.updateProduct(productArr, {
          isForceUpdateCustomer: false,
          isUpdateCustomerProduct: true,
          isSilentUpdateLinkmanAndAddress: true
        });
        // 关闭弹窗
        this.addProductDialog = false;

      });
    },
    buildValidation(){
      const that = this;
      return Object.freeze({
        product(value, field, changeStatus){
          changeStatus(true);
          let isProductRequired = that.customerOption?.productNotNull === true;
          let isSelectedProduct = Array.isArray(value) && value.length > 0;

          return new Promise((resolve, reject) => {
            changeStatus(false);
            let errorMessage = isSelectedProduct ? '' : REQUIRES_PRODUCT_MESSAGE;
            resolve(isProductRequired ? errorMessage : '')
          })
        },
        planTime(value, field, changeStatus){
          changeStatus(true);
          
          let isDateTimeType = field?.setting?.dateType == 'dateTime';
          let errorMessage = '';

          if(isDateTimeType) {
            let planTime = parseDateTime(value).getTime();
            let nowTime = safeNewDate().getTime();
            errorMessage = planTime < nowTime && that.isVilidatePlantime ? PLAN_TIME_NOT_LESS_THEN_NOW_MEESSAGE : '';
          }

          return new Promise((resolve, reject) => {
            changeStatus(false);
            resolve(errorMessage)
          })
        },
        planStartOrEndTime(value, field, changeStatus){
          changeStatus(true);
          let { formType } = field
          let isDateTimeType = field?.setting?.dateType == 'date';
          if(isDateTimeType) {
            value = getTimestamp(value)
          }
          let planTime = parseDateTime(value).getTime();
          let nowTime = safeNewDate().getTime();
          let tip = formType == 'planStartTime' ? PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE : PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE
          
          // 是否为年月日类型
          const dateType = field?.setting?.dateType || '';
          const isDateType = dateType.toLowerCase() == 'yyyy-mm-dd';
          let isToday = false
          
          if (isDateType) {
            const today = moment().format(DateFormatEnum.YMD_UP)
            const planTime = moment(newValue).format(DateFormatEnum.YMD_UP)
            isToday = (today === planTime)
          }
          
          let errorMessage = ((planTime < nowTime) && !isToday) ? tip : '';
          
          return new Promise((resolve, reject) => {
            changeStatus(false);
            resolve(errorMessage)
          })
        }
      });
    },
    /** 
     * @description 新增地址成功后触发
     * @param {Object} address 地址数据
    */
    addAddressCallback(address = {}) {
      // 获取列表数据
      this.$refs.bizRemoteSelectAddress.search()
      this.bindAddress(address)
    },
    /** 
     * @description 绑定地址
     * @param {Object} address 地址数据
    */
    bindAddress(address = {}) {
      this.updateAddressValue([customerAddressSelectConversion(address)])
    },
    /** 
     * @description 绑定联系人
     * @param {Object} linkman 联系人数据
    */
    bindLinkman(linkman = {}) {
      this.updateLinkmanValue([linkmanSelectConversion(linkman)])
    },
    bindProductLinkmanAndAddress(product = {}) {
      let { linkman, address } = product;
      let linkmanId = linkman?.id || '';
      let addressId = address?.id || '';
      
      linkmanId && this.bindLinkman(linkman);
      addressId && this.bindAddress(address);
    },
    /** 
     * @description 选择工单类型
     * @param {String} templateId 工单类型id
    */
    async chooseTemplate(templateId) {
      let loading = this.$loading();
      try {
        // 复制事件切换事件类型时处理
        if(this.isCopy) {
          return this.copyEventHandler(templateId);
        }
        let planStartTime = this.eventValue.planStartTime
        let planEndTime = this.eventValue.planEndTime
        this.eventFields = await this.fetchEventTemplateFields({ templateId, tableName: 'event', isFromSetting:true });
        this.eventFields.unshift({
          ...this.fieldIntLabel()
        })
        this.handlePlantimeFields()
        this.eventValue = FormUtil.initialize(this.eventFields, {});
        this.eventValue.planStartTime = planStartTime 
        this.eventValue.planEndTime = planEndTime 
        // 表单初始化
        this.$emit('update:value', this.eventValue);
        this.$emit('update:fields', this.eventFields);

        // 清空校验结果
        setTimeout(() => {
          this.$refs.form.$children.map(child => {
            if (child.$el.className == 'form-item err') {
              child.$el.dispatchEvent(new CustomEvent('form.clear.validate', {bubbles: false}));
            }
          })
        }, 0);
        
        // 更新工单类型数据
        this.selectedType = this.eventTypesMap[templateId];
        this.$emit('updatetemplateId', this.selectedType);

      } catch (error) {
        console.error(error)
      }

      loading.close();
    },
    // 复制事件
    copyEventHandler(templateId) {
      let fromId = window.frameElement.getAttribute('id');
      openAccurateTab({
        type:PageRoutesTypeEnum.PageCreateEventByCopy,
        params:`id=${this.initData?.workEvent?.id}&newTemplateId=${templateId}`,
        closeNowPage:true,
        fromId
      })
    },
    convertCustomerOfSelect(customer = {}) {
      let customerCloneData = _.cloneDeep(customer)
      customerCloneData.id = (customer.id || customer.value || '')

      return customerCloneData
    },
    /**
     * @description 关闭弹窗
     * @param {String} 动作 customer/product 
    */
    dislogClose(action = '') {
      switch (action) {
      case 'customer': {
        this.customerFormDom.initFormData();
        this.customerFormDom.init = false;
        break;
      }
      case 'product': {
        this.productFormDom.initFormData();
        this.productFormDom.init = false;
        break;
      }
      default: {
        break;
      }
      }
    },
    /** 
     * @description 打开弹窗
     * @param {String} 动作 address/contact/customer/product 
    */
    async dialogOpen(action) {
      let { id, value } = this.selectedCustomer;
      let customerId = id || value;

      if (!customerId && action != TaskFieldNameMappingEnum.Customer) {
        this.$message.warning(this.$t('product.form.tips.tip7'));
        return;
      }

      switch (action) {
        case 'address': {
          this.$refs.EditAddressDialog.openDialog();
          break;
        }
        case 'contact': {
          this.$refs.EditContactDialog.openDialog();
          break;
        }
        case 'customer': {
          this.openCustomerDialogHandler();
          break;
        }
        case 'product': {
          this.openProductDialogHandler();
          break;
        }
        default: {
          break;
        }
      }
    },
    judegeSelectTaskType(value) {
      return !value
      // return !value || this.state.isFromEvent;
    },
    /** 
     * @description 同时通知客户 checkbox变化
    */
    noticeCustomerCheckdChange(value) {
      this.updateTickValue(Number(value));
    },
    /** 
     * @description 打开客户详情
    */
    openCustomerView() {
      let fromId = window.frameElement.getAttribute('id');
      let { id, value } = this.selectedCustomer;
      let customerId = id || value;
      if(!customerId) return

      // this.$platform.openTab({
      //   id: `customer_view_${customerId}`,
      //   title: '客户详情',
      //   close: true,
      //   url: `/customer/view/${customerId}?noHistory=1`,
      //   fromId
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageCustomerView,
        key: customerId,
        params: 'noHistory=1',
        fromId
      })
    },
    /** 
     * @description 打开产品详情
    */
    openProductView() {
      let fromId = window.frameElement.getAttribute('id');
      let productId = this.selectProduct.id;
      if(!productId) return
      
      // this.$platform.openTab({
      //   id: `product_view_${productId}`,
      //   title: '产品详情',
      //   close: true,
      //   url: `/customer/product/view/${productId}?noHistory=1`,
      //   fromId
      // })
      openAccurateTab({
        type: PageRoutesTypeEnum.PageProductView,
        key: productId,
        params: 'noHistory=1',
        fromId
      })
    },
    /** 
     * @description 打开客户弹窗处理
    */
    async openCustomerDialogHandler() {
      try {
        // 打开客户弹窗前，判断是否存在客户初始化数据
        let isHaveCustomerInitData = Object.keys(this.customerInitData) > 0;
        if(!isHaveCustomerInitData) {
          this.loading = true;
          await this.fetchCustomerData();
        }
        
        this.addCustomerDialog = true;
        this.customerFormDom.init = true;

      } catch (error) {
        this.$message.warning(this.$t('common.base.tip.pleaseTryAgainLater'));
        console.warn('openCustomerDialogHandler -> error', error)
      } finally {
        this.loading = false;
      }
    },
    /** 
     * @description 打开产品弹窗处理
    */
    async openProductDialogHandler() {
      try {
        // 打开产品弹窗前，判断是否存在产品初始化数据
        let isHaveProductInitData = Object.keys(this.productInitData) > 0;
        if(!isHaveProductInitData) {
          this.loading = true;
          await this.fetchProductData();
        }
        
        this.addProductDialog = true;
        this.productFormDom.init = true;
        
      } catch (error) {
        this.$message.warning(this.$t('common.base.tip.pleaseTryAgainLater'));
        console.warn('openProductDialogHandler -> error', error)
      } finally {
        this.loading = false;
      }
    },
    // 获取产品表单移动端展示字段
    getProductFields() {
      getProductFields({isFromSetting:false}).then(res => {
        if (res.status == 0) {
          this.productAppShowFields = (res.data || []).filter(item => item.isAppShow)
        }
      })
    },
    /** 
     * @description 产品关联联系人/地址处理
     * 判断选择的产品的 关联联系人/地址 是否 当前选择的的 联系人/地址相同，如果不相同，则提示，并替换
     * @param {Object} product 产品
    */
    async productBindLinkmanAndAddressHandler(product) {
      // 产品联系人
      let productLinkman = product.linkman || {};
      // 产品地址
      let productAddress = product.address || {};
      // 当前已选择的联系人地址
      let { linkman, address } = this.value;
      linkman = linkman[0] || {};
      address = address[0] || {};

      // 是否是相同的 联系人/地址
      let isSameLinkman = productLinkman.id === linkman.id || !productLinkman.id;
      // 地址需要判断是否 值也相同
      let isSameAddress = productAddress.id === address.id || !productAddress.id;
      let productAddressText = Filter.prettyAddress(productAddress)
      let addressText = Filter.prettyAddress(address)
      if (productAddressText && addressText && (productAddressText == addressText)) {
        isSameAddress = true
      }
      
      let confirm = false;
      
      // 联系人和地址都不相同
      if(!isSameLinkman && !isSameAddress) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_LINKMAN_AND_ADDRESS_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindLinkman(productLinkman);
        this.bindAddress(productAddress);
        
      }
      // 联系人不相同
      else if(!isSameLinkman) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_LINKMAN_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindLinkman(productLinkman);
        
      } 
      // 地址不相同
      else if(!isSameAddress) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_ADDRESS_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindAddress(productAddress);
      }
      
    },
    relationFieldsFilter(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = [];

      // 判断类型是否存在
      let types = [TaskFieldNameMappingEnum.Customer, TaskFieldNameMappingEnum.Product];
      if(types.indexOf(type) < 0) {
        console.warn(`Caused: relationFieldHandler params.type is not in ${types}`);
        return relationFields;
      }
      
      // 判断对应类型的关联显示项字段是否存在
      let formType = RELATION_TYPE_MAP[type];
      relationFields = this.eventFields.filter(field => field.formType == formType);
      if(relationFields.length <= 0) {
        console.warn(`Caused: this.eventFields not have ${formType} field`);
      }

      return relationFields;
    },
    /** 
     * @description 关联显示项字段清空
     * @param {string} type customer/product
    */
    async relationFieldClear(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = this.relationFieldsFilter(type);
      relationFields.forEach(relationField => { 
        this.update({ field: relationField, newValue: '' }) 
      })
    },
    /** 
     * @description 关联显示项字段选择处理
     * @param {string} type customer/product
    */
    async relationFieldSelectHandler(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = this.relationFieldsFilter(type)
      if (relationFields.length <= 0) return

      let productIds = [];
      if (Array.isArray(this.selectProduct) && this.selectProduct.length) {
        productIds = this.selectProduct.map(product => product.value);
      }

      try {
        let params = {
          customerId: this.selectedCustomer?.value || '',
          productIds
        }
        let res = await this.fetchRelatedInfo(params);
        let isSuccess = res.success;

        if (isSuccess) {
          let result = res.result || {};
          let { customerInfo, productInfo, relateCustomerFields, relateProductFields } = result;
          let isCustomerRelation = type == TaskFieldNameMappingEnum.Customer;

          this.relationFieldUpdateHandler(
            isCustomerRelation ? customerInfo : productInfo,
            relationFields,
            isCustomerRelation ? relateCustomerFields : relateProductFields,
            isCustomerRelation
          );

        } else {
          this.$platform.alert(res.message);
        }

      } catch (error) {
        console.warn('relationFieldSelectHandler -> error', error)
      }
    },

    /** 
     * @description 关联显示项字段更新处理
    */
        relationFieldUpdateHandler(info, relationFields = [], customerOrPorductFields = [], isCustomerRelation = true) {
          let fieldName = '';
          let formType = '';
          let fieldValue = '';
    
          relationFields.forEach(relationField => {
            fieldName = relationField?.setting?.fieldName;
            formType = relationField?.setting?.formType;
    
            if (isCustomerRelation) {
              fieldValue = getFieldValue2string(info, fieldName, formType, customerOrPorductFields, isCustomerRelation);
            } else {
              fieldValue = [];
              if (Array.isArray(info)) {
                info.map(item => {
                  let productString = getFieldValue2string(item, fieldName, formType, customerOrPorductFields, isCustomerRelation)
                  // 做字符串为空过滤
                  if (productString) {
                    fieldValue.push(productString)
                  }
                })
              }
            }
            this.update({ field: relationField, newValue: fieldValue });
          })
    
        },
    
    /** 
     * @description 搜索地址 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchAddressOuterHandler(params = {}) {
      let { id, value } = this.selectedCustomer;
      params.customerId = id || value;
      return this.searchAddress(params);
    },
    /** 
     * @description 搜索联系人 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchLinkmanOuterHandler(params = {}) {
      let customerId = this.selectedCustomer?.value || '';
      params.customerId = this.selectedCustomer?.value || '';

      return customerId ? this.searchLinkman(params) : this.searchCustomerByPhone(params);
    },
    /** 
     * @description 搜索产品 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchProductOuterHandler(params = {}) {
     let { id, value } = this.selectedCustomer;
      // 清空的时候清空customerId
      params.customerId = id || value || '';
      if(this.value?.product?.filter(item=>item.customerId).length){
        params.customerId=this.value?.product?.filter(item=>item.customerId)[0].customerId
      }
      return this.searchProduct(params);
    },
    /** 
     * @deprecated 旧的方法，已废弃
     * @description 选择客户关联
     * @param {String} customerId 客户id
    */
    selectCustomerRelation(customerId) {
      let forRelation = {
        module: 'customer',
        id: customerId
      };

      this.$eventBus.$emit('es.Relation.Customer', forRelation);
    },
    /** 
     * @description 更新客户信息
     * @param {Array<Object>} value 客户数据
     * @param {Object} options 配置
    */
    async updateCustomer(value = [], options = { 
      isForceUpdateCustomer: false,
      isUpdateCustomerProduct: true,
      isGetDefaultInfo: true
    }) {
      this.isInFirst++;

      let { isForceUpdateCustomer, isUpdateCustomerProduct, isGetDefaultInfo } = options;
      let selectedCustomer = value?.[0] || {};
      let currentCustomerId = this.selectedCustomer?.id || this.selectedCustomer?.value;
      let selectedCustomerId = selectedCustomer?.id || selectedCustomer?.value || '';

      // isInFirst 只有初始化的时候才会传linkmanId参数，切换客户的时候不传
      let defaultLinkManId = (this.isInFirst > 1 ) ? '' : this.urlParams.linkmanId ;
      
      // 更新客户数据
      this.updateCustomerValue(value.slice());

      // 判断选中的客户是否与当前客户数据一致
      if(currentCustomerId == selectedCustomerId && !isForceUpdateCustomer) return
      try {
        // 外部联系人手动添加的name没有id。可以不查询详情
        if(isGetDefaultInfo) {
          const result = await this.fetchEventDefaultInfo({ customerId: selectedCustomerId,linkmanId:defaultLinkManId });
          let { linkman, address } = result;

          // 重置联系人和地址
          this.updateLinkmanValue([]);
          this.updateAddressValue([]);

          // 绑定联系人和地址
          if (this.$refs.linkman) await this.$refs.linkman.search();
          linkman && this.bindLinkman(linkman);
          address && this.bindAddress(address);
        }
        
        // 更新产品数据
        if (Array.isArray(this.value.product) && this.value.product.filter(item=>item.customerId).length && isUpdateCustomerProduct) {
          this.updateProductValue(
            this.value.product.filter(item => item.customerId == selectedCustomer.value)
          )
        }

        // 自动将客户负责人带入工单或事件协同人
        const exeInSynOfTaskOrEvent = this.initData?.customerConfig?.exeInSynOfTaskOrEvent || false
        if(exeInSynOfTaskOrEvent) {
          this.$emit('updateSynergyByCustomer', selectedCustomerId)
        }

        // 查询关联工单数量
        this.fetchCountForCreate({ module: TaskFieldNameMappingEnum.Customer, id: selectedCustomerId });

      } catch (error) {
        console.warn('event-edit-form: updateCustomer -> error', error);
      }

      // 查询客户关联字段
      this.relationFieldSelectHandler();
      // 查询产品关联字段
      this.relationFieldSelectHandler(TaskFieldNameMappingEnum.Product);
    },
    /** 
     * @description 更新联系人信息
    */
    async updateLinkman(linkman = {}) {
      if (ObjectUtil.isEmpty(linkman)) {
        return console.warn('Caused: linkman is not object or is empty')
      }
      
      let isHaveCustomer = this.value.customer && this.value.customer.length;
      let linkmanCustomer = linkman?.customer || {};

      try {
        // 判断客户是否存在
        if (!isHaveCustomer) {
          // 客户不存在时则下拉框隐藏
          findComponentDownward(this.$refs.linkman, 'base-select')?.close();
        
          const customerData = [{
            label: linkmanCustomer.name,
            value: linkmanCustomer.id,
            id: linkmanCustomer.id
          }];

          // 设置客户数据
          this.updateCustomerValue(customerData);
          // 更新客户信息
          await this.updateCustomer(customerData);
        }

        // 绑定联系人数据
        this.bindLinkman(linkman);
        const addressResult = await this.fetchLmBindAddress({ lmId: linkman.id });
        // 如果存在地址信息则绑定地址
        addressResult.data.id && this.bindAddress(addressResult.data);

      } catch (error) {
        console.warn('event-edit-form: updateLinkman -> error', error);
      }

    },
    /** 
     * @description 更新产品信息
     * @param {Array[Product]} value 产品数据
     * @param {Object} options 配置
    */
    async updateProduct(value, options = {
      isForceUpdateCustomer: false,
      isUpdateCustomerProduct: true,
      isSilentUpdateLinkmanAndAddress: false
    }) {
      let { isForceUpdateCustomer, isUpdateCustomerProduct, isSilentUpdateLinkmanAndAddress } = options;
      //选择的产品未关联用户则不需要更新客户数据
      let product = this.value?.product.filter(item=>item.customerId)[0] || {};
      let isHaveCustomer = (this.value.customer && this.value.customer.length);
      try {
        // 判断客户是否存在
        if ((!isHaveCustomer || isForceUpdateCustomer) && product.value) {
          // 客户不存在时则下拉框隐藏
          findComponentDownward(this.$refs.product, 'base-select')?.close();

          const result = await this.fetchCustomerByProduct({ id: product.value });
          const customerData = [{
            ...result.customer,
            label: result.customerName,
            value: result.customerId,
            id: result.customerId
          }];
          // 设置客户数据
          this.updateCustomerValue(customerData);
          // 当前已选择的客户联系人地址
          let { linkman = [], address = [] } = this.value;  
          // 如果当前客户没有联系人和地址，再更新客户信息
          if(linkman.length <= 0 || address.length <= 0) {
            // 更新客户信息
            await this.updateCustomer(customerData, { isForceUpdateCustomer, isUpdateCustomerProduct });
          }
          
        }

        // 查询产品关联字段, 选一个产品才带入
        let isOnlyOneProduct = value.length === 1;
        
        if(isOnlyOneProduct && isSilentUpdateLinkmanAndAddress) {
          // 静默绑定联系人/地址
          this.bindProductLinkmanAndAddress(product);
        }
        // 只有一个产品 且 客户存在
        else if (isOnlyOneProduct && isHaveCustomer) {
          // 产品关联联系人地址
          this.productBindLinkmanAndAddressHandler(product);
        }
        // 只有一个产品 且 客户不存在
        else if(isOnlyOneProduct && !isHaveCustomer) {
          this.bindProductLinkmanAndAddress(product);
        }

        if (isOnlyOneProduct) {
          // 产品关联字段数据
          this.relationFieldSelectHandler(TaskFieldNameMappingEnum.Product);
          // 查询关联工单数量
          this.fetchCountForCreate({ module: TaskFieldNameMappingEnum.Product, id: product.id });
        } else {
          // 渲染多个产品的数据
          this.relationFieldSelectHandler(TaskFieldNameMappingEnum.Product);
          // this.relationFieldClear(TaskFieldNameMappingEnum.Product);
        }
        
        // 第一次选中产品的时候过滤同客户的产品
       if(this.value?.product.filter(item=>item.customerId).length===1 || !this.value?.product.length){
        this.$nextTick(() => {
          let customerId=this.value?.product.filter(item=>item.customerId)[0]?.customerId?this.value?.product.filter(item=>item.customerId)[0]?.customerId:''
          this.$refs.product.outsideSearch('', {
            customerId
          })
        })
      }

      // 带出设备信息
      this.setDeviceInfo(value)

      } catch (error) {
        console.warn('event-edit-form: updateProduct -> error', error);
      }
    },
    updateProductForProductSelect(value = []) {
      if(!this.initData?.auth?.PRODUCT_EDIT && !value[0].customerId){
        this.$nextTick(()=>{
          this.$refs.product.$refs.BaseRemoteSelect.$refs.PublinkElement ? this.$refs.product.$refs.BaseRemoteSelect.$refs.PublinkElement.$refs.ElElement.visible = false : '';
          this.$confirm(this.$t('event.components.eventEditForm.text7'), this.$t('common.base.toast'), {
            confirmButtonText: this.$t('common.base.makeSure'),
            cancelButtonText: this.$t('common.base.cancel'),
            type: 'warning',
           }).then().catch(() => { })
        })
         this.value.product=[]
      }else{
        this.updateProduct(value, { isForceUpdateCustomer: true })
      }
      this.$refs.product.$refs.BaseRemoteSelect.$refs.PublinkElement ? this.$refs.product.$refs.BaseRemoteSelect.$refs.PublinkElement.$refs.ElElement.query = '' : '';
    },
    /** 
     * @description 效验
    */
    validate() {
      return this.$refs.form.validate();
    },
    outsideUpdateCustomer() {
      if(this.eventValue.customer && !this.eventValue.isEdit) {
        this.updateCustomer(this.eventValue.customer, { 
          isForceUpdateCustomer: true,
          isUpdateCustomerProduct: true
        })
      }
    },
    validateProductNumber(value, field){
      // 小数位数
      if(value) {
        let count = MathUtil.decimalNumber(value);
        let limitConig = field.setting.limitConig
        let { min, max } = limitConig
        let stringValueLength = String(value).length
        let errorMsg = ''
        if((stringValueLength !== min.length && stringValueLength !== max.length) ) {
          errorMsg = this.$t('common.form.tip.event.limitTips', {min, max})
        }
        if(count > 0 || value < 0) errorMsg = this.$t('common.form.tip.event.numberLimit')
        return new Promise((resolve, reject) => {
          resolve(errorMsg);
        });
      }
    },
    updateCustomerAssociationValue(value) {
      this.customerAssociationValue = value
    },
    // 带出设备信息
    setDeviceInfo(value) {
      const eventTemplates = [HSConst.SERVICE_ADD_TEMPLATE_ID, HSConst.ORDER_SERVICE_TEMPLATE_ID]
      if(!eventTemplates.includes(this.urlParams.defaultTypeId)) return
      try {
        const productInfo = value?.[0]
        if(!productInfo) return
        const deviceInfo = {
          field_publink_productNo: productInfo.serialNumber, // 产品编号
          field_publink_productn_name:  productInfo.name, // 产品名称
          field_publink_productn_type: productInfo.type, // 产品类型
          field_publink_product_power_level: productInfo?.attribute?.field_m5EJZbA6w5ngbdpb, // 功率级别
          field_publink_product_console_serial_number: productInfo?.attribute?.field_9aBYykuC5qdlJPp7, // 主机序列号
          // field_fnMYKpnBZTMGj0wr: productInfo?.attribute?.field_4mLVPq1DEQSIS43l, // 上门服务是否在保
          field_publink_product_warranty_start_date: productInfo?.attribute?.field_VCZz2KasyvmeY0MQ, //出机日期（保修开始日期）
        }
        this.$set(this, 'eventValue', {
          ...this.eventValue,
          ...deviceInfo
        })
        this.$emit('update:value', this.eventValue)
      } catch (error) {
        console.warn('event-edit-form: setDeviceInfo -> error', error);
      }
    }
  },
  components: {
    [EditAddressDialog.name]: EditAddressDialog,
    [EditContactDialog.name]: EditContactDialog
  }
}