<template>
  <div :class="[formCellCount > 1 ? 'event-form-cell' : '']" v-loading.fullscreen.lock="loading">
    <form-builder ref="form" :fields="eventFields" :value="eventValue" mode="event" @update="update" class="base-form-builder bbx-cell-form-builder" :form-cell-count="formCellCount" :formEditingMode="formEditingMode" @getDeleteFiles="(value)=>{$emit('getDeleteFiles',(value))}">

      <template slot="eventNo" slot-scope="{ field, value }">

        <!-- start 事件编号 -->
        <form-item :label="field.displayName" class="bbx-form-cell-item" :validation="false" v-if="value">
          <div class="form-taskNo">{{ value || $t('common.form.preview.eventNo.placeHolder') }}</div>
        </form-item>
        <!-- end 事件编号 -->

        <!-- start 事件类型 -->
        <form-item  class="bbx-form-cell-item" :label="$t('customer.detail.customerEventTable.table.label.templateId')" :validation="false">
          <form-select v-if="judegeSelectTaskType(value)" :field="field" :source="eventTypes" :value="selectedType.value" :clearable="false" @input="chooseTemplate"/>
          <div class="form-taskNo" v-else>{{ eventValue.templateName }}</div>
          <div v-if="agendaEvent" class="event-type-tip"><i class="iconfont icon-fdn-info"></i>{{$t('event.components.eventEditForm.text1')}}</div>
        </form-item>
        <!-- end 事件类型 -->

      </template>

      <!-- start 计划时间 -->
      <template slot="planTime" slot-scope="{ field, value }">
        <form-item  class="bbx-form-cell-item" :label="field.displayName" :validation="validation.planTime">
          <form-plantime :picker-options="isVilidatePlantime ? planTimeDatePickerOptions : {}" :field="field" :value="value" @update="update"></form-plantime>

          <!-- start 通知客户 checkbox -->
          <div class="task-notice-customer-block" v-if="isShowNoticeCustomer">
            <el-checkbox :value="value ? value.tick : false" @input="noticeCustomerCheckdChange">{{$t('task.edit.sameTimeInformCustomer')}}</el-checkbox>
            <el-tooltip placement="top" :content="$t('event.components.eventEditForm.text3')">
              <i class="iconfont icon-info"></i>
            </el-tooltip>
          </div>
          <!-- end 通知客户 checkbox -->

        </form-item>
      </template>
      <!-- end 计划时间 -->

      <!-- start 计划开始时间 -->
      <template slot="planStartTime" slot-scope="{ field }">
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <form-planstarttime :picker-options="planTimeDatePickerOptions" :field="field" :clearable="isCanlendarClearadle" :value="eventValue.planStartTime | fmt_datetime" @update="update" @planTimeChange="planTimeChange"></form-planstarttime>
        </form-item>
      </template>
      <!-- end 计划时间 -->

      <!-- start 计划完成时间 -->
      <template slot="planEndTime" slot-scope="{ field }">
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <form-planendtime :picker-options="planTimeDatePickerOptions" :field="field" :clearable="isCanlendarClearadle" :value="eventValue.planEndTime | fmt_datetime" @update="update" @planTimeChange="planTimeChange"></form-planendtime>
        </form-item>
      </template>
      <!-- end 计划完成时间 -->

      <!-- start 客户字段 -->
      <template slot="customer" slot-scope="{ field }">
        
        <!-- start 客户 -->
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <div class="input-and-btn flex-1 input-btn-change">
            <biz-remote-select
              :key="JSON.stringify(value.customer)"
              :value="value.customer"
              :field="customerField"
              :remote-method="searchCustomer"
              @input="updateCustomer"
              :placeholder="$t('common.form.preview.relatedCustomers.pla1')"
              :input-disabled="isCreateCustomer"
              :computed-width-keys="['name']"
            >
              <div class="customer-template-option" slot="option" slot-scope="{ option }">
                <h3>{{ option.name }}</h3>
                <p>
                  <span>
                    <label>{{$t('common.base.phone')}}：</label>
                    <span>{{ option.lmPhone }}</span>
                  </span>
                  <span>
                    <label>{{$t('common.base.serialNumber')}}：</label>
                    <span>{{ option.serialNumber }}</span>
                  </span>
                  <span v-if="option && option.customerAddress">
                    <label>{{$t('common.base.address')}}：</label>
                    <span>{{ option.customerAddress | fmt_address }}</span>
                  </span>
                </p>
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('customer')" v-if="!isCreateCustomer && allowCreateCustomer">{{$t('common.base.create')}}</el-button>
          </div>

          <el-tooltip v-if="isShowCustomerRelevanceTaskCountButton" placement="top">
            <div
              slot="content" 
              v-html="`${$t('event.components.eventEditForm.text2')}：${customerRelevanceEventCountData.unfinished} </br> ${$t('event.components.eventEditForm.text5')}：${customerRelevanceEventCountData.all}`">
            </div>
            <div class="task-count-button mar-l-10" @click="openCustomerView">
              {{ `${customerRelevanceEventCountData.unfinished}/${customerRelevanceEventCountData.all}` }}
            </div>
          </el-tooltip>

        </form-item>
        <!-- end 客户 -->

        <!-- start 联系人 -->
        <form-item class="bbx-form-cell-item" v-if="customerOption.linkman"  :label="$t('common.base.contact')" :key="`${JSON.stringify(value.linkman)}${selectedType.value}`">
          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              ref="linkman"
              :key="JSON.stringify(value.linkman)"
              v-model="value.linkman"
              :remote-method="searchLinkmanOuterHandler"
              @input="updateLinkman(value.linkman[0])"
              :placeholder="$t('common.form.placeHolder.linkman.pla1')"
              :input-disabled="isCreateCustomer"
              :cleared="true"
              :computed-width-keys="['name']"
            >
              <div class="customer-template-option task-ptb5" slot="option" slot-scope="{ option }">
                <p>{{ option.label }}</p>
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('contact')" v-if="!isCreateCustomer && allowCreateLinkman">{{$t('common.base.create')}}</el-button>
          </div>
        </form-item>
        <!-- end 联系人 -->

        <!-- start 地址 -->
        <form-item class="bbx-form-cell-item" v-if="customerOption.address" :label="$t('common.base.locationMap.address')">
          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              ref="bizRemoteSelectAddress"
              v-model="value.address"
              :cleared="true"
              :remote-method="searchAddressOuterHandler"
              :placeholder="$t('common.form.placeHolder.customerAddress.pla1')"
              :computed-width-keys="['address']"
              :mounted-search="true"
            >
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('address')" v-if="!isCreateCustomer && allowCreateCustomer">{{$t('common.base.create')}}</el-button>
          </div>
        </form-item>
        <!-- end 地址 -->
        <!-- start 客户负责人 -->
        <form-item class="bbx-form-cell-item" label="客户负责人" disabled :is-not-null="field.setting.customerOption.customerManagerNotNull" v-if="isShowCustomerManager">
          <form-text v-model="customerAssociationValue.customerManager"  placeholder="请先选择客户" :field="{fieldName: 'customerManager', disabled: true}"></form-text>
        </form-item>
        <!-- end 客户负责人 -->
        
        <!-- start 联系方式 -->
        <form-item class="bbx-form-cell-item" label="联系方式" disabled :is-not-null="field.setting.customerOption.customerContactNotNull" v-if="isShowCustomerContact">
          <form-text v-model="customerAssociationValue.customerContact"  placeholder="请先选择客户" :field="{fieldName: 'customerContact', disabled: true}"></form-text>
        </form-item>
        <!-- end 联系方式 -->

        <!-- 客户等级 -->
        <form-item class="bbx-form-cell-item" label="客户等级" disabled :is-not-null="field.setting.customerOption.customerLevelNotNull" v-if="isShowCustomerLevel">
          <form-text v-model="customerAssociationValue.customerLevel"  placeholder="请先选择客户" :field="{fieldName: 'customerLevel', disabled: true}"></form-text>
        </form-item>
        <!-- end 客户等级 -->
        
        <!-- 客户来源 -->
        <form-item class="bbx-form-cell-item" label="客户来源" disabled :is-not-null="field.setting.customerOption.customerSourceNotNull" v-if="isShowCustomerSource">
          <form-text v-model="customerAssociationValue.customerSource"  placeholder="请先选择客户" :field="{fieldName: 'customerSource', disabled: true}"></form-text>
        </form-item>
        <!-- end 客户来源 -->

        <!-- start 产品 -->
        <form-item class="bbx-form-cell-item" v-if="customerOption.product" :label="$t('common.base.product')" :validation="validation.product">

          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              ref="product"
              :field="productField"
              v-model="value.product"
              :remote-method="searchProductOuterHandler"
              @input="updateProductForProductSelect"
              :placeholder="$t('event.components.eventEditForm.text4')"
              :computed-width-keys="['name', 'serialNumber']"
              :keyword-length-limit="true"
              multiple>
              <div class="product-template-option" slot="option" slot-scope="{ option }">
                <h3>{{ option.name }}</h3>
                <p>
                  <label>{{$t('common.form.type.productNo')}}：</label>
                  <span :title="option.serialNumber">{{ option.serialNumber }}</span>
                </p>
                <p>
                  <label>{{$t('common.base.productType')}}：</label>
                  <span :title="option.type">{{ option.type }}</span>
                </p>
                <p>
                  
                  <label>{{$t('common.base.contact')}}：</label>
                  <span :title="option.linkman && option.linkman.name">{{ option.linkman && option.linkman.name }}</span>
                </p>
                <p v-if="option.containsProductCompleteAddress">
                  <label>{{$t('common.form.type.productCompleteAddress')}}：</label>
                  <span :title="option.serialNumber | fmt_address">{{ option.productCompleteAdd }}</span>
                </p>
                <p>
                  <label>{{$t('common.base.customer')}}：</label>
                  <span v-if="option.customer && option.customer.id">{{option.customer.name}}</span>
                  <span class="customer-unbind-name" v-else>{{$t('common.base.notContact')}}</span>
                </p>
                <!-- 产品表单移动端展示字段 -->
                <p v-for="item in productAppShowFields" :key="item.fieldName">
                  <label>{{ item.displayName }}：</label>
                  <!-- 自定义人员 -->
                  <template v-if="item.formType === 'user' && option.attribute[item.fieldName]">
                    <template v-if="isOpenData">
                      <template v-if="Array.isArray(option.attribute[item.fieldName])">
                        <template v-for="(userItem, index) in option.attribute[item.fieldName]">
                          <open-data :key="index" type='userName' :openid="userItem.staffId"></open-data>{{index + 1 === option.attribute[item.fieldName].length ? '' : ','}}
                        </template>
                      </template>
                      <template v-else>
                        <open-data type='userName' :openid="option.attribute[item.fieldName] && option.attribute[item.fieldName].staffId"></open-data>
                      </template>
                    </template>
                    <template v-else>
                      {{ $formatFormField(item, option) }}
                    </template>
                  </template>
                  <!-- 其它字段 -->
                  <template v-else>
                    <span>{{ $formatFormField(item, option) }}</span>
                  </template>
                </p>
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('product')" v-if="allowCreateProduct">{{$t('common.base.create')}}</el-button>
          </div>

          <el-tooltip v-if="isShowProductRelevanceTaskCountButton" placement="top">
            <div
              slot="content"
              v-html="`${$t('event.components.eventEditForm.text2')}：${productRelevanceEventCountData.unfinished} </br> ${$t('event.components.eventEditForm.text5')}：${productRelevanceEventCountData.all}`">
            </div>
            <el-button class="task-count-button" @click="openProductView">
              {{ `${productRelevanceEventCountData.unfinished}/${productRelevanceEventCountData.all}` }}
            </el-button>
          </el-tooltip>

        </form-item>
        <!-- end 产品 -->

      </template>
      <!-- end 客户字段 -->

      <!-- 客户产品关联字段跟人有关的 -->
      <template slot="relationCustomer" slot-scope="{ field, value }">
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <template v-if="isOpenData && value">
            <template v-if="field.setting.fieldName === 'customerManager'">
              <div class="form-relation-text">
                <open-data type="userName" :openid="value"></open-data>
              </div>
            </template>
            <template v-if="field.setting.formType === 'user'">
              <!-- 这里人可能是多选 -->
              <div class="form-relation-text">
                <open-data v-for="staffId in value.split(',')" :key="staffId" type="userName" :openid="staffId"></open-data>
              </div>
            </template>
          </template>
          <template v-else>
            <form-relation :value="value"></form-relation>
          </template>
        </form-item>
      </template>
      
      <!-- start 建机的产品编码需要特殊处理 -->
      <template slot="field_iUdfKUTLRarOlRDk" slot-scope="{ field, value }">
        <form-item class="bbx-form-cell-item" :label="field.displayName" :validation="validateProductNumber">
          <form-number :field="field" :value="value" @update="update"></form-number>
        </form-item>
      </template>

    </form-builder>

    <!-- start 新建客户弹窗 -->
    <base-modal :title="$t('common.pageTitle.pageCreateCustomer')" :show.sync="addCustomerDialog" class="add-dialog-container" width="800px" @closed="dislogClose('customer')">
      <div id="createCustomerView"></div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="addCustomerDialog = false">{{$t('common.base.close')}}</el-button>
        <el-button type="primary" @click="addCustomerSubmit">{{$t('common.base.save')}}</el-button>
      </div>
    </base-modal>
    <!-- end 新建客户弹窗 -->

    <!-- start 新建产品弹窗 -->
    <base-modal :title="$t('common.pageTitle.pageCreateProduct')" :show.sync="addProductDialog" class="add-dialog-container" width="800px" @closed="dislogClose('product')">
      <div id="createProductView"></div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="addProductDialog = false">{{$t('common.base.close')}}</el-button>
        <el-button type="primary" @click="addProductSubmit">{{$t('common.base.save')}}</el-button>
      </div>
    </base-modal>
    <!-- end 新建产品弹窗 -->

    <!-- start 联系人弹窗 -->
    <edit-contact-dialog ref="EditContactDialog" :customer="convertCustomerOfSelect(selectedCustomer)" :is-phone-unique="customerInitData.isPhoneUnique"/>
    <!-- end 联系人弹窗 -->

    <!-- start 地址弹窗 -->
    <edit-address-dialog ref="EditAddressDialog" :customer-id="selectedCustomer.id || selectedCustomer.value" :default-address="customerInitData.customerAddress"/>
    <!-- end 地址弹窗 -->

  </div>
</template>

<script>
import EventEditForm from './EventEditForm';
export default EventEditForm;
</script>

<style lang="scss">
  @import './EventEditForm.scss';
  .form-relation-text {
    background: #F5F5F5;
    padding: 3px 10px;
    line-height: 24px;
    min-height: 32px;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    margin: 0;
    outline: none;
    color: #BFBFBF !important;
  }
  .event-form-cell{
    .form-builder{
      padding: 24px 20px 24px 32px !important;
    }
  }
  .form-builder {
    --input-btn-max-width: 77px;
    --input-btn-margin-left: 10px;
    --input-right: 10px;
    --input-icon-width: 25px;
    --input-icon-right: calc(var(--input-btn-max-width) + var(--input-right));
    --input-padd-right: calc(var(--input-icon-right) + var(--input-icon-width));
    --input-tag-right: var(--input-padd-right);
    .input-btn-change {
      .biz-form-remote-select {
        input {
          // 主要是客户那边没有close 所以剪掉icon的宽度
          padding-right: calc(var(--input-padd-right) - var(--input-icon-width));
        }
        .el-input__suffix {
          right: var(--input-icon-right);
        }
        // 产品选中的时候关闭按钮位置会偏移，去掉固定宽度，让其自适应
        .el-select__tags {
          width: calc(100% - var(--input-padd-right))!important;
        }
      }
      &+.el-tooltip {
        right: var(--input-icon-right)
      }
      position: relative;
      button {
        position: absolute;
        right: var(--input-right);
        top: 50%;
        transform: translateY(-50%);
        max-width: var(--input-btn-max-width);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
