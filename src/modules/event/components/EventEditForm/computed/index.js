/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-03-25 09:36:53
 * @LastEditTime: 2021-08-11 19:54:36
 * @LastEditors: Please set LastEditors
 * @Description: 
 * You build it, You run it.
 */
import _ from 'lodash';
import { TaskFieldNameMappingEnum } from '@model/enum/FieldMappingEnum.ts';
import { taskTypeSelectConversion } from '@src/util/conversionFunctionUtil.ts';
/* model */
import TaskStateEnum from '@model/enum/TaskStateEnum.ts'
import AuthUtil from '@src/util/auth'
import AuthEnum from '@model/enum/AuthEnum.ts'

export default {
  /** 
   * @description 是否允许新建客户
  */
  allowCreateCustomer() {
    return AuthUtil.hasAuth(this.initData.auth, AuthEnum.CUSTOMER_CREATE)
  },
  allowCreateProduct(){
    return this.initData?.auth?.PRODUCT_CREATE
  },
  // 是否有新建联系人权限
  allowCreateLinkman() {
    return AuthUtil.hasAuth(this.initData.auth, AuthEnum.LINKMAN_ADD)
  },
  /** 
   * @description 是否允许编辑客户
  */
  allowEditCustomer() {
    return this.hasEditCustomerAuth
  },
  /* 客户字段 */
  customerField() {
    return this.fields.filter(f => f.fieldName === 'customer')[0];
  },
  customerFormDom() {
    return this?.customerFormView?.$refs?.CustomerCreateView || {};
  },
  /* 客户字段配置 */
  customerOption(){
    return (this.customerField.setting && this.customerField.setting.customerOption) || {} ;
  },
  /* 客户关联事件的数量数据 */
  customerRelevanceEventCountData() {
    return this.relevanceEventCountData[TaskFieldNameMappingEnum.Customer];
  },
  /* 是否效验计划时间 */
  isVilidatePlantime() {
    let { isTaskEdit, isPlanTaskEdit } = this.state || {}
    // 非编辑状态
    return !isPlanTaskEdit && !isTaskEdit
  },
  /** 
   * @description 是否显示 客户关联的工单数量 按钮 
   * 1. 客户存在
   * 2. 且 全部数量 大于 0
  */
  isShowCustomerRelevanceTaskCountButton() {
    let { all } = this.customerRelevanceEventCountData;
    return (this.selectedCustomer?.id || this.selectedCustomer?.value) && Number(all) > 0;
  },
  /** 
   * @description 是否显示 产品关联的事件数量 按钮 
   * 1. 产品存在
   * 2. 且 全部数量 大于 0
   * 3. 且 当前选择的产品只有一个
  */
  isShowProductRelevanceTaskCountButton() {
    let { all } = this.productRelevanceEventCountData;
    return (this.selectProduct?.id || this.selectProduct?.value) && Number(all) > 0 && this.value?.product?.length == 1;
  },
  /** 
   * @description 是否显示 同时通知客户 字段
   * 1. 新建工单/新建计划任务不显示
  */
  isShowNoticeCustomer() {
    let { isFromPlan, isTaskCreate } = this.state;
    return !isFromPlan && !isTaskCreate;
  },
  // 产品字段
  productField(){
    return {
      displayName: this.$t('common.base.product'),
      fieldName: 'product',
      formType: 'select',
      isNull: this.customerOption?.productNotNull === true ? 0 : 1
    }
  },
  productFormDom() {
    return this?.productFormView?.$refs?.ProductCreateView || {};
  },
  /* 产品关联工单的数量数据 */
  productRelevanceEventCountData() {
    return this.relevanceEventCountData[TaskFieldNameMappingEnum.Product];
  },
  // 选择的客户值
  selectedCustomer(){
    return (this.value.customer && this.value.customer[0]) || {};
  },
  // 选择的产品值
  selectProduct() {
    return this.value?.product || {};
  },
  /* 事件类型 */
  defaultType(){
    let type = null
    if(this.initData.defaultType) {
      const {id, name } = this.initData.defaultType
      type = {
        text: name,
        value: id 
      }
    }
    return type
  },
  /* 事件类型 */
  eventTypes(){
    return this.types.map(type => taskTypeSelectConversion(type)) || [];
  },
  /* 事件类型对象 */
  eventTypesMap() {
    return _.reduce(this.eventTypes, (result, value) => {
      result[value.value] = value;
      return result;
    }, {})
  },

  /**
   * 当前用户是否是该客户负责人
   * 客户负责人用于和客户创建人相同权限
   */
  isCustomerManager() {
    const customer = this.value.customer?.length ? this.value.customer[0] : {}
    const currentUser = this.initData.currentUser
    return currentUser?.userId === customer?.customerManager
  },

  /**
   * 当前用户是否是该客户协同人
  */
  isCustomerSynergies() {
    const customer = this.value.customer?.length ? this.value.customer[0] : {}
    const currentUser = this.initData.currentUser
    let customerSynergies = customer?.synergies || []
    return !!customerSynergies.find(v => v.userId === currentUser?.userId)
  },

  /**
   * 当前用户是否是该客户负责人或协同人
   */
  isCustomerManagerOrSynergies() {
    return this.isCustomerManager || this.isCustomerSynergies
  },

  /**
   * 是否有编辑客户权限，需要满足以下条件之一：
   *
   * 1. 编辑客户全部权限： 全部客户
   * 2. 编辑客户团队权限： 没有团队的客户都可编辑，有团队的按团队匹配。 包含个人权限
   * 3. 编辑客户个人权限： 自己创建的 或 客户负责人
   */
   hasEditCustomerAuth() {
    const customer = this.value.customer?.length ? this.value.customer[0] : {}
    const loginUserId = this.initData?.currentUser?.userId

    return AuthUtil.hasAuthWithDataLevel(
      this.initData.auth,
      'CUSTOMER_EDIT',
      // 团队权限判断
      () => {
        let tags = Array.isArray(customer?.tags) ? customer.tags : []
        // 无团队则任何人都可编辑
        if (tags.length == 0) return true

        let loginUserTagIds = this.initData.currentUser?.tagList.map(item => {
          return item.id
        })
        return tags.some(tag => loginUserTagIds.indexOf(tag.id) >= 0)
      },
      // 个人权限判断
      () => {
        return customer?.createUser == loginUserId || this.isCustomerManagerOrSynergies
      }
    )
  },
  isCanlendarClearadle(){
    // !this.agendaEvent
    return !this.initData?.event?.id;
  },
  // 是否显示联系方式
  isShowCustomerContact() {
    return this.customerField.setting?.customerOption?.customerContact ?? false
  },
  // 是否显示客户负责人
  isShowCustomerManager() {
    return this.customerField.setting?.customerOption?.customerManager ?? false
  },
  // 是否显示客户等级
  isShowCustomerLevel() {
    return this.customerField.setting?.customerOption?.customerLevel ?? false
  },
  // 是否显示客户来源
  isShowCustomerSource() {
    return this.customerField.setting?.customerOption?.customerSource ?? false
  }
};
