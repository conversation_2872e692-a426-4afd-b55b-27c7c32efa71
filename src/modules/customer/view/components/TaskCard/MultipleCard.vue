<template>
  <div class="multiple-card-container">
    <!-- start 按钮组 -->
    <div class="btn-wrap" v-if="allowCreate">
      <el-button
        @click="handleEvent({action: 'create'})"
        type="primary"
        size="mini"
        plain
      >{{$t('customer.detail.taskCard.multipleCard.buttonGroup.add')}}</el-button>
      <el-button
        v-if="cardType !== 'customer'"
        @click="handleEvent({action: 'updata'})"
        type="primary"
        size="mini"
        plain
      >{{$t('customer.detail.taskCard.multipleCard.buttonGroup.import')}}</el-button>
      <el-button
        v-if="isShowDialog"
        @click="changeList"
        type="primary"
        size="mini"
        plain
      >{{$t('customer.detail.taskCard.multipleCard.buttonGroup.sparePart')}}</el-button>
    </div>
    <!-- end 按钮组 -->

    <!-- start 附加组件列表 -->
    <multiple-card-table
      :data="value.attribute"
      :columns="columns"
      :allow-create="allowCreate"
      :allow-edit="allowEdit"
      :allow-delete="allowDelete"
      :listLoading="listLoading"
      @action="handleEvent"
    />
    <!-- end 附加组件列表 -->

    <!-- start 附加组件详情弹窗 -->
    <task-card-view-dialog
      ref="taskCardView"
      :fields="card.fields"
      :value.sync="cardInstance"
    />
    <!-- end 附加组件详情弹窗 -->
    <!-- start 备件更换记录 -->
    <change-dialog ref="changeDialog"></change-dialog>
    <!-- end 备件更换记录 -->
    <!-- start 新增/编辑附加组件弹窗 -->
    <task-card-edit-dialog
      ref="taskCardEdit"
      :fields="card.fields"
      :cardName='cardName'
      :product='product'
      :value.sync="cardInstance"
      :action="action"
      :cardType='cardType'
      @submit="submit"
    />
    <!-- end 新增/编辑附加组件弹窗 -->
    <!-- start 批量导入 -->
    <base-import
      :title="$t('common.base.batchImport')"
      ref="bulkImport"
      :template-url="`${productModuleImport}?cardId=${card.id}`"
      :action="`${excelsProductPartsImport}?cardId=${card.id}&productId=${product.id}`"
    >
      
    </base-import>
    <!-- 批量导入 -->

  </div>
</template>

<script>
/* components */
import MultipleCardTable from './CardTable';


import ChangeDialog from './ChangeDialog.vue';
/* mixin */
import cardMixin from './mixin';
import { productModuleImport,excelsProductPartsImport } from '@src/api/Import';

export default {
  name: 'multiple-card',
  mixins: [cardMixin],
  data(){
    return {
      maxDeep: 2,
      productModuleImport,
      excelsProductPartsImport,
      isShowDialog:false
    }
  },
  props: {
    collapseDirection: {
      type: String
    },
    value: {
      type: Object,
      default: () => ({
        attribute: []
      })
    },
    cardType:{
      type:String,
      default:''
    },
    cardName:{
      type:String,
      default:''
    },
    product: {
      type: Object,
      default: () => {}
    },
    listLoading: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    columns() {
      let formTypes = ['autograph', 'attachment', 'separator', 'info'];
      let fields = [
        ...this.card.fields,
        {
          displayName: this.$t('customer.detail.taskCard.multipleCard.table.label.userName'),
          fieldName: 'userName',
          minWidth: '80px'
        },
        {
          displayName: this.$t('customer.detail.taskCard.multipleCard.table.label.updateTime'),
          fieldName: 'updateTime',
          minWidth: '160px'
        },
        {
          displayName: this.$t('customer.detail.taskCard.multipleCard.table.label.action'),
          fieldName: 'action',
          minWidth: '120px'
        }
      ]

      fields = fields.map(field => {
        // 核心物料清单附加组件，只允许编辑部件批次号
        if (this.isCoreMaterialList) {
          field.disabled = field.fieldName !== 'field_JH3gM0ApHhVbzT5P';
        }
        return field;
      })
      
      return fields.filter(field => !field.isHidden && (field.isVisible == undefined || field.isVisible) && formTypes.indexOf(field.formType) < 0);
    }
  },
  mounted(){
    this.getIsShowDialog()
  },
  methods:{
    changeList(){
      this.$refs.changeDialog.openDialog(this.product,this.card);
    },
  },
  components: {
    [MultipleCardTable.name]: MultipleCardTable,
    [ChangeDialog.name]: ChangeDialog
  }
}
</script>
<style scoped lang="scss"> 
  .multiple-card-container {
    padding-bottom: 50px;
  }
  .btn-wrap {
    padding: 10px 0;
    background: #fff;
  }
</style>