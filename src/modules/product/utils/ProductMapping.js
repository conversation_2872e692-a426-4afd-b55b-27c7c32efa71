import { isQualityInfoField } from '@service/FieldService.ts'
import { convertFormQualityInfoGroupValueToQualityInfoRemoteData, convertQualityInfoRemoteDataToFormQualityInfoGroupValue } from '@service/QualityInfoService.ts'
import { ProductFieldNameMappingEnum } from '@model/enum/FieldMappingEnum.ts'
import { formatAddress } from 'pub-bbx-utils';
/** 将form对象转成产品对象，用于提交表单 */
export function packToProduct(fields, form, originProduct){
  let product = {
    linkman: {},
    address: {},
    serviceProviders: []
  };
  let attribute = {};
  const {customer, template, type, serialNumber, name, id, linkman, customerAddress, catalogId, qrcodeId, serviceProviders, productManager } = form;
  let tv = null;
  let qualityInfoRemoteData = {}

  fields.forEach(f => {
    if (!f.isSystem) {
      attribute[f.fieldName] = form[f.fieldName];
    }
    
    if (f.formType === 'address') {
      tv = form[f.fieldName];
      let all = formatAddress(tv,'')
      f.isSystem ? product[f.fieldName] = {
        ...tv,
      } : attribute[f.fieldName] = {
        ...tv,
      };
      
      all ? f.isSystem ? product[f.fieldName]['all'] = all : attribute[f.fieldName]['all'] = all : '';
    }
    
    if ((f.formType === 'location') && !attribute[f.fieldName].isHaveLocation) {
      attribute[f.fieldName] = {};
    }
    
    if (isQualityInfoField(f)) {
      const qualityInfoFormData = form[f.fieldName] || form[f.formType] || {}
      qualityInfoRemoteData = convertFormQualityInfoGroupValueToQualityInfoRemoteData(qualityInfoFormData, originProduct)
    }

    // 智能标签
    if (f.formType === 'intLabel') {
      if (form[f.fieldName]) {
        product['changeLabel'] = form[f.fieldName]?.changeLabel || void 0;
        product['label'] = form[f.fieldName]?.label || null;
      }
    }
    
  });
  
  
  if (Array.isArray(customer) && customer.length) {
    product.customerId = customer[0].value
  }
  
  if (Array.isArray(template) && template.length) {
    product.templateId = template[0].value;
    product.templateName = template[0].label;
  }
  
  if(catalogId){
    try {
      product['catalogId'] = catalogId.id ? catalogId.id : catalogId[0].id
    } catch (error) {
      console.warn(error, 'error try catch');
    }
  }
  
  if(qrcodeId){
    product['qrcodeId'] = qrcodeId
  }
  
  if (id) {
    product.id = id;
  }
  
  if (Array.isArray(linkman) && linkman.length) {
    product.linkman.id = linkman[0].value
  }
  
  if (Array.isArray(customerAddress) && customerAddress.length) {
    product.address.id = customerAddress[0].value
  }
  
  // 服务商提交参数处理
  if (serviceProviders && serviceProviders.length > 0) {
    product.serviceProviders = serviceProviders.map(item => {
      if (item.attribute) {
        return {
          ...item
        }
      } else {
        let { serviceProviderId, ...attribute } = item;
          
        return {
          serviceProviderId,
          attribute
        }
      }
    })
  }

  // 产品负责人
  if(productManager) {
    product.productManager = productManager.userId || '';
    product.productManagerName = productManager.displayName || '';
  }
  
  return {
    ...product,
    name,
    type,
    serialNumber,
    attribute,
    ...qualityInfoRemoteData
  };
}


/** 将产品对象转成form表单，用于初始化表单 */
export function packToForm(field, product, isEdit){
  const {id, name, serialNumber, type, templateId, templateName, customerId, customerName,customer,address, linkman, catalogId, productCompleteAddress, serviceProviders, productManager } = product;
  let form = {};
  
  if (customerId) {
    const label = customerName || customer?.name;
    form.customer = [{
      ...customer,
      value: customerId,
      label,
    }]
  } else {
    form.customer = []
  }
  
  if (templateId) {
    form.template = [{
      value: templateId,
      label: templateName
    }]
  } else {
    form.template = []
  }

  if (linkman?.id) {
    form.linkman = [{
      value: linkman.id,
      label: linkman.name,
      phone: linkman.phone
    }]
  } else {
    form.linkman = []
  }

  if (address?.id) {
    form.customerAddress = [{
      value: address.id,
      label: formatAddress(address)
    }]
  } else {
    form.customerAddress = []
  }

  if(catalogId){
    // 产品类型关联组件解析form
    let obj = {
      id:catalogId,
      pathName:product.catalogPathName
    }
    form['catalogId'] = [obj]
  }

  // 产品负责人
  if(productManager) {
    form['productManager'] = {
      userId: product.productManager,
      staffId: product.productManagerStaffId,
      displayName: product.productManagerName,
    }
  }
  
  // 质保信息字段的新建和编辑的数据处理
  form[ProductFieldNameMappingEnum.QualityInfo] = convertQualityInfoRemoteDataToFormQualityInfoGroupValue(product, true)
  
  // 服务商数据显示
  if (serviceProviders && serviceProviders.length > 0) {
    form.serviceProviders = serviceProviders.map(item => {      
      return {
        serviceProviderId: item.serviceProviderId,
        ...item
      }
    })
  }

  // 如果有标签 将值赋值
  if (product?.label) {
    form['intLabel'] = product?.label || {}
  }
  
  return {
    id,
    name,
    serialNumber,
    type,
    productCompleteAddress,
    ...form,
    ...product.attribute,
  }
}
