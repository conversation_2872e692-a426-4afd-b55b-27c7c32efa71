"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[212],{15842:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAx9JREFUSEu9Vk1oHVUYPWcSQxYv+B4oYsxCLbS6ERSluHXXWPsjZBamRB5x7l0ogpSKdBMqgqVdFESReyeTEmhFnmCrUsVVXLnQgiupfVCoUNx0kWgrpnYyR254r0zfm7xXwXhhhoHv59zv+849d4ghK03TKQD7Jb0IYIekyRBC8jcAV0heAPBFkiTXBqXiVsYsyybzPD9GsilpZGASckPS6dHR0YX5+fmwgb5VCeS93wfgjKQJALcBnA8PyYv1en0z0dra2qSkZwEc6Dz3kbwB4JAx5stepD4g59ybAE4BiEiek3TEWntlUEXOuR0kT0o6CKAA8Ja19oNyzF1AnUrOS1IURe8kSXJy2AzL9jRNjxRFcbwzw4Plyu4AhZlsbGz8EtoVRdHbVSDOuQeiKHpK0v0AfjbGtHs30gE7Edo4MjLyRHdmd4CccymA10K7jDEv9yZwzh0m+Z6k8a6N5Kf1ev3VOI7/Lvt77z/vtHHRWptsVhhegcKSrkoK/X2ydybe+72SvgJwMYqiBZK3iqI4KukFku8aYxbKQGFmAC6RDHN+NFC/C/R6URQfAvjMWhtXVBNY9NLY2NhjzWbzarAvLy8/sr6+fo3kD8aY3RUxLQAzURS9kSTJR5tA3vuvJe0BMGut/aQi6BTJB40xh7q2MC8A1wH8ZK19piLmFQBnSX5jjJnuAl2WtJPkrqoBVzHPObcEoEnyfWPM0V4f7/1OSZdJto0xuzaBnHPhoNUajcZEHMc3B1FaEtM0/ViSJfnd+Pj43rm5uT97Y1qtVm11dTXkvWmtnfjXQCX6fjs1NbV/enr6VtXGsiybyPP8j7uAvPf33Drn3KUgrrVa7aHZ2dnVraqvbN0wMnSTraysjLbb7QskfzXGmCGy1E+GNE0H0ruccHFx8WlJ14ddC865fnoPO7BdoCzLns/z/HsAvzcajYfjOP5rC0ZWH9gO8wZKUPBZWlp6PM/zH4OKGGOeIxmUpG9tKUHB815ENfi1Wq2xmZmZ2yRVBTJUVDsKsU/SufC9bddESVq2/+Lrgv0vV3mJYdv/c1Ie8n/1u/UP56v8KEaK2TcAAAAASUVORK5CYII="},67289:function(e,t,i){i.r(t),i.d(t,{default:function(){return dn}});var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"form-container"},[t("ul",{staticClass:"form-container-left"},e._l(e.menus,(function(i,n){return t("li",{key:n,class:{active:n==e.active},on:{click:function(t){return e.handleTableClick(i,n)}}},[t("i",{class:["iconfont",i.icon]}),t("span",[e._v(e._s(i.name))])])})),0),t("div",{staticClass:"form-container-right"},[e.menus[e.active].component?t(e.menus[e.active].component,{ref:"component",tag:"component",attrs:{"template-id":e.templateId,"form-name":e.formName}}):e._e()],1)])},s=[],a=i(71357),o=i(62361),r=(i(67880),i(2286),i(80793),i(21484),i(33438),i(75069),i(21633),i(3292),function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.pending,expression:"pending",modifiers:{lock:!0}}],staticClass:"external-form-rule"},[t("div",{staticClass:"external-form-rule-context"},[t("div",{staticClass:"external-form-rule-context-title"},[t("h3",[t("span",[e._v(e._s(e.$t("view.designer.rule.externalForm.label1")))]),t("el-switch",{on:{change:function(t){return e.changeSwitchHandler(t,2)}},model:{value:e.formWriteConfig.switchOpen,callback:function(t){e.$set(e.formWriteConfig,"switchOpen",t)},expression:"formWriteConfig.switchOpen"}})],1),t("p",[e._v(e._s(e.$t("view.designer.rule.externalForm.label2")))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.formWriteConfig.switchOpen,expression:"formWriteConfig.switchOpen"}],staticClass:"external-form-rule-context-main mt24"},[t("div",{staticClass:"external-form-rule-item align-items-center"},[e._v(" "+e._s(e.$t("view.designer.rule.visitAddress"))+"： "),t("el-input",{staticClass:"external-form-url",attrs:{disabled:""},model:{value:e.outerUrl,callback:function(t){e.outerUrl=t},expression:"outerUrl"}},[t("el-button",{attrs:{slot:"append"},on:{click:e.copyText},slot:"append"},[e._v(e._s(e.$t("common.base.copy")))])],1),t("i",{staticClass:"iconfont icon-qrcode",on:{click:e.createQrcode}})],1),t("div",{staticClass:"external-form-rule-item mt24"},[t("strong",[e._v(e._s(e.$t("view.designer.rule.externalForm.label3")))]),t("span",{staticClass:"tips"},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label4"))+" ")]),t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.setFieldAuth(e.formWriteConfig.bizId,2)}}},[e._v(" "+e._s(e.$t("view.designer.rule.setFormAuth"))+" ")])],1),t("div",{staticClass:"external-form-rule-item"},[t("el-checkbox",{model:{value:e.formWriteConfig.setting.showFlowDetail,callback:function(t){e.$set(e.formWriteConfig.setting,"showFlowDetail",t)},expression:"formWriteConfig.setting.showFlowDetail"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label14"))+" "),t("el-tooltip",{attrs:{content:e.$t("view.designer.rule.externalForm.label15"),placement:"top"}},[t("i",{staticClass:"iconfont icon-question"})])],1)],1),t("div",{staticClass:"external-form-rule-panel mt24"},[t("h4",[e._v(e._s(e.$t("view.designer.rule.externalForm.label5")))]),t("el-radio-group",{staticClass:"mt12",on:{change:function(t){return e.changeVisitorSetting(t,"formWriteConfig")}},model:{value:e.formWriteConfig.setting.visitorSetting,callback:function(t){e.$set(e.formWriteConfig.setting,"visitorSetting",t)},expression:"formWriteConfig.setting.visitorSetting"}},[t("el-radio",{attrs:{label:0}},[e._v(e._s(e.$t("view.designer.rule.externalForm.label6")))]),t("el-radio",{attrs:{label:1}},[e._v(e._s(e.$t("view.designer.rule.externalForm.label7")))])],1),e.formWriteConfig.setting.visitorSetting?t("div",{staticClass:"external-form-user-auth"},[t("el-checkbox",{model:{value:e.formWriteConfig.setting.customerUserLimit,callback:function(t){e.$set(e.formWriteConfig.setting,"customerUserLimit",t)},expression:"formWriteConfig.setting.customerUserLimit"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label8"))+" ")]),t("el-checkbox",{model:{value:e.formWriteConfig.setting.appointUserLimit,callback:function(t){e.$set(e.formWriteConfig.setting,"appointUserLimit",t)},expression:"formWriteConfig.setting.appointUserLimit"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label9"))+" ")]),t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.setPersonAuth(e.formWriteConfig.bizId)}}},[e._v(" "+e._s(e.$t("common.base.setting"))+" ")])],1):e._e()],1),t("div",{staticClass:"external-form-rule-panel mt24"},[t("div",{staticClass:"external-form-rule-item"},[t("h4",[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label18"))+" "),t("el-tooltip",{attrs:{content:e.$t("view.designer.rule.externalForm.label19"),placement:"top"}},[t("i",{staticClass:"iconfont icon-question"})])],1),t("el-radio-group",{staticClass:"mt12",on:{change:e.shareLinkOpenChange},model:{value:e.shareLinkOpen,callback:function(t){e.shareLinkOpen=t},expression:"shareLinkOpen"}},[t("el-radio",{attrs:{label:!0}},[e._v(e._s(e.$t("common.base.allowed")))]),t("el-radio",{attrs:{label:!1}},[e._v(e._s(e.$t("common.base.notAllowed")))])],1)],1)])])]),t("div",{staticClass:"external-form-rule-context"},[t("div",{staticClass:"external-form-rule-context-title"},[t("h3",[t("span",[e._v(e._s(e.$t("view.designer.rule.externalForm.label10")))]),t("el-switch",{on:{change:function(t){return e.changeSwitchHandler(t,1)}},model:{value:e.formShareConfig.switchOpen,callback:function(t){e.$set(e.formShareConfig,"switchOpen",t)},expression:"formShareConfig.switchOpen"}})],1),t("p",[e._v(e._s(e.$t("view.designer.rule.externalForm.label11")))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.formShareConfig.switchOpen,expression:"formShareConfig.switchOpen"}],staticClass:"external-form-rule-context-main mt24"},[t("div",{staticClass:"external-form-rule-item"},[t("strong",[e._v(e._s(e.$t("view.designer.rule.externalForm.label12")))]),t("span",{staticClass:"tips"},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label13"))+" ")]),t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.setFieldAuth(e.formShareConfig.bizId,1)}}},[e._v(" "+e._s(e.$t("view.designer.rule.setFormAuth"))+" ")])],1),t("div",{staticClass:"external-form-rule-item"},[t("el-checkbox",{model:{value:e.formShareConfig.setting.showFlowDetail,callback:function(t){e.$set(e.formShareConfig.setting,"showFlowDetail",t)},expression:"formShareConfig.setting.showFlowDetail"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label14"))+" "),t("el-tooltip",{attrs:{content:e.$t("view.designer.rule.externalForm.label15"),placement:"top"}},[t("i",{staticClass:"iconfont icon-question"})])],1)],1),t("div",{staticClass:"external-form-rule-panel mt24"},[t("h4",[e._v(e._s(e.$t("view.designer.rule.externalForm.label5")))]),t("el-radio-group",{staticClass:"mt12",on:{change:function(t){return e.changeVisitorSetting(t,"formShareConfig")}},model:{value:e.formShareConfig.setting.visitorSetting,callback:function(t){e.$set(e.formShareConfig.setting,"visitorSetting",t)},expression:"formShareConfig.setting.visitorSetting"}},[t("el-radio",{attrs:{label:0}},[e._v(e._s(e.$t("view.designer.rule.externalForm.label6")))]),t("el-radio",{attrs:{label:1}},[e._v(e._s(e.$t("view.designer.rule.externalForm.label7")))])],1),e.formShareConfig.setting.visitorSetting?t("div",{staticClass:"external-form-user-auth"},[t("el-checkbox",{model:{value:e.formShareConfig.setting.customerUserLimit,callback:function(t){e.$set(e.formShareConfig.setting,"customerUserLimit",t)},expression:"formShareConfig.setting.customerUserLimit"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label8"))+" ")]),t("el-checkbox",{model:{value:e.formShareConfig.setting.appointUserLimit,callback:function(t){e.$set(e.formShareConfig.setting,"appointUserLimit",t)},expression:"formShareConfig.setting.appointUserLimit"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label9"))+" ")]),t("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(t){return e.setPersonAuth(e.formShareConfig.bizId)}}},[e._v(" "+e._s(e.$t("common.base.setting"))+" ")])],1):e._e()],1),t("div",{staticClass:"external-form-password mt24"},[t("el-checkbox",{model:{value:e.formShareConfig.setting.needPwd,callback:function(t){e.$set(e.formShareConfig.setting,"needPwd",t)},expression:"formShareConfig.setting.needPwd"}},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label16"))+" ")]),t("div",{staticClass:"tips"},[e._v(" "+e._s(e.$t("view.designer.rule.externalForm.label17"))+" ")])],1)])]),t("base-modal",{attrs:{show:e.createQrcodeVisible,title:e.$t("view.template.detail.formQrCode"),width:"380px"},on:{"update:show":function(t){e.createQrcodeVisible=t}}},[t("div",{ref:"qrcode",attrs:{id:"qrcode"}}),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.createQrcodeVisible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.downloadQrcode}},[e._v(e._s(e.$t("view.designer.rule.saveToLocal")))])],1)]),t("field-auth-modal",{ref:"fieldAuthModal",attrs:{"form-id":e.templateId}}),t("person-auth-modal",{ref:"personAuthModal"})],1)}),l=[],c=i(97022),d=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("base-modal",{attrs:{title:e.$t("view.designer.rule.formFieldsAuthSetting"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("form-field-auth",{attrs:{fields:e.fields,value:e.value,disabled:e.disabled,type:"external"},on:{update:e.updateFieldAuth}}):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.close")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("common.base.save"))+" ")])],1)],1)},u=[],m=(i(35256),i(16961),i(54615),i(32807),i(69594),i(68735),i(12986)),f=i(74526),p=i(80906),h=i(87512),v=[h.E.User,h.E.RelatedTask,h.E.BackCheck,h.E.Tag,h.E.Contract],g=[h.E.SparePart,h.E.ServiceItem,h.E.MaterialOrder,h.E.OutWarehouse,h.E.InWarehouse,h.E.SunmiOutWarehouse,h.E.SunmiInWarehouse,h.E.OutSparepart,h.E.InSparepart],b={name:"field-auth-modal",props:{formId:{type:String,default:""}},data:function(){return{pending:!1,visible:!1,disabled:!1,fields:[],value:[],bizId:""}},methods:{open:function(e,t){var i=this;this.bizId=e,this.disabled=1==t,Promise.all([this.fetchFields(),this.fetchFieldAuth()]).then((function(e){i.fields=e[0]||[],i.value=e[1]||[],i.visible=!0}))["catch"]((function(e){console.log("err",e)}))},fetchFields:function(){var e=this;return f.QA({templateBizId:this.formId}).then((function(t){var i=t.success,n=t.message,s=t.data;return i?e.filterFields(s.paasFormFieldVOList):e.$message.error(n)}))["catch"]((function(e){return console.error("fetchFields err",e)}))},filterFields:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.map((function(t){return t.noInput=n,g.includes(t.formType)&&(t.noInput=!0),t.subFormFieldList=e.filterFields(t.subFormFieldList,p.hL(t),t.noInput),t})).filter((function(e){return!v.includes(e.formType)&&!e.isHidden})).filter((function(e){var t=e.isSystem,n=e.setting,s=void 0===n?{}:n;return!i||!t||t&&(s.isShow||void 0==s.isShow)}))},fetchFieldAuth:function(){var e=this;return c.b5({bizId:this.bizId}).then((function(t){var i=t.success,n=t.message,s=t.data;return i?s:e.$message.error(n)}))["catch"]((function(e){return console.error("fetchFieldAuthSetting err",e)}))},updateFieldAuth:function(e){this.value=e},submit:function(){var e=this;this.pending=!0,c.aU({bizId:this.bizId,fieldSettingList:this.value}).then((function(t){t.success?(e.$message.success(e.$t("common.base.saveSuccess")),e.visible=!1):e.$message.error(t.message)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("err",e)}))}},components:(0,o.A)({},m.A.name,m.A)},w=b,C=w,y=i(49100),_=(0,y.A)(C,d,u,!1,null,"517acd36",null),A=_.exports,$=(i(46622),function(){var e=this,t=e._self._c;e._self._setupProxy;return t("base-modal",{staticClass:"person-auth-modal",attrs:{title:e.$t("view.designer.rule.visitUsersList"),show:e.visible,width:"416px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.loading,expression:"loading",modifiers:{lock:!0}}]},[t("div",{staticClass:"input-container"},[t("el-input",{attrs:{placeholder:e.$t("view.designer.rule.tip6")},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchList.apply(null,arguments)}},model:{value:e.params.phone,callback:function(t){e.$set(e.params,"phone",t)},expression:"params.phone"}},[t("el-button",{attrs:{slot:"append",icon:"el-icon-search",disabled:e.pending},on:{click:e.fetchList},slot:"append"})],1),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.add}},[e._v(" "+e._s(e.$t("common.base.add2"))+" ")])],1),t("el-table",{attrs:{data:e.data,border:""}},[t("el-table-column",{attrs:{label:e.$t("view.designer.rule.mobilePhoneNum"),prop:"phone"}}),t("el-table-column",{attrs:{label:e.$t("common.base.operation"),width:"120"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{size:"mini",type:"text",disabled:e.pending},on:{click:function(t){return e.remove(i.row)}}},[e._v(" "+e._s(e.$t("common.base.delete"))+" ")])]}}])}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.loading,expression:"!loading"}],attrs:{slot:"empty"},slot:"empty"},[e._v(e._s(e.$t("common.base.noData")))])],1),t("el-pagination",{attrs:{small:"",total:e.total,"page-size":e.params.pageSize,"current-page":e.params.pageNum,layout:"total, prev, pager, next"},on:{"current-change":e.jump}})],1)])}),k=[],S=i(18885),x=i(42881),I=i(78670),T={name:"person-auth-modal",data:function(){return{loading:!1,pending:!1,visible:!1,data:[],total:0,params:this.buildParams(),bizId:""}},methods:{buildParams:function(){return{bizId:this.bizId,phone:"",pageNum:1,pageSize:10}},open:function(e){this.data=[],this.total=0,this.bizId=e,this.params=this.buildParams(),this.fetchList(),this.visible=!0},fetchList:function(){var e=this;this.loading=!0,c.XZ(this.params).then((function(t){if(t&&t.data){var i=t.data,n=i.total,s=i.current,a=i.records;e.total=n,e.data=a,e.params.pageNum=s}else e.data=[],e.total=0,e.params.pageNum=1}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("fetchList err",e)}))},add:function(){var e=this,t=this.params.phone;if(!t||!(0,I.FY)(t))return this.$message.error(this.$t("view.designer.rule.tip6"));this.pending=!0,c.IQ({bizId:this.bizId,phone:t}).then((function(t){if(!t.success)return e.$message.error(t.message);e.params.phone="",e.params.pageNum=1,e.fetchList(),e.$message.success(e.$t("common.base.tip.addSuccess"))}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("add phone err",e)}))},remove:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$platform.confirm(t.$t("view.designer.rule.tip7"));case 2:if(n=i.sent,n){i.next=5;break}return i.abrupt("return");case 5:t.pending=!0,c.uN({bizId:t.bizId,phone:e.phone}).then((function(e){if(!e.success)return t.$message.error(e.message);t.fetchList(),t.$message.success(t.$t("common.base.deleteSuccess"))}))["finally"]((function(){t.pending=!1}))["catch"]((function(e){return console.error("remove phone err",e)}));case 7:case"end":return i.stop()}}),i)})))()},jump:function(e){this.params.pageNum=e,this.fetchList()}}},L=T,N=L,F=(0,y.A)(N,$,k,!1,null,"02b6d42f",null),z=F.exports,E=i(26183),O=i(52275),D=(0,E.A)((function e(){(0,O.A)(this,e),(0,o.A)(this,"appointUserLimit",!1),(0,o.A)(this,"customerUserLimit",!1),(0,o.A)(this,"visitorSetting",0),(0,o.A)(this,"showFlowDetail",!1),(0,o.A)(this,"needPwd",!1)})),R=(0,E.A)((function e(t){(0,O.A)(this,e),(0,o.A)(this,"bizId",void 0),(0,o.A)(this,"switchOpen",void 0),(0,o.A)(this,"setting",void 0);var i=t||{},n=i.bizId,s=void 0===n?"":n,r=i.switchOpen,l=void 0!==r&&r,c=i.setting,d=void 0===c?{}:c;this.bizId=s,this.switchOpen=l,this.setting=(0,a.A)((0,a.A)({},new D),d)})),P=R,B=(0,E.A)((function e(){(0,O.A)(this,e),(0,o.A)(this,"appointUserLimit",!1),(0,o.A)(this,"customerUserLimit",!1),(0,o.A)(this,"visitorSetting",0)})),M=(0,E.A)((function e(t){(0,O.A)(this,e),(0,o.A)(this,"bizId",void 0),(0,o.A)(this,"switchOpen",void 0),(0,o.A)(this,"setting",void 0);var i=t||{},n=i.bizId,s=void 0===n?"":n,r=i.switchOpen,l=void 0!==r&&r,c=i.setting,d=void 0===c?{}:c;this.bizId=s,this.switchOpen=l,this.setting=(0,a.A)((0,a.A)({},new B),d)})),V=M,U=(0,E.A)((function e(t){(0,O.A)(this,e),(0,o.A)(this,"paasFormTemplateId",void 0),(0,o.A)(this,"paasFormOuterShareForm",void 0),(0,o.A)(this,"paasFormOuterWriteForm",void 0);var i=t||{},n=i.paasFormTemplateId,s=void 0===n?"":n,a=i.paasFormOuterShareForm,r=i.paasFormOuterWriteForm;this.paasFormTemplateId=s,this.paasFormOuterShareForm=new P(a),this.paasFormOuterWriteForm=new V(r)})),q=U,W=i(20548),j=i.n(W),H={name:"external-form",props:{templateId:{type:String,default:""}},data:function(){return{pending:!1,createQrcodeVisible:!1,shareLinkOpen:!1,config:new q}},computed:{formWriteConfig:function(){return this.config.paasFormOuterWriteForm||{}},formShareConfig:function(){return this.config.paasFormOuterShareForm||{}},outerUrl:function(){var e;return(null===(e=this.formWriteConfig)||void 0===e||null===(e=e.setting)||void 0===e?void 0:e.url)||""}},mounted:function(){this.initialize()},methods:{initialize:function(){var e=this;this.pending=!0,c.Vp({paasFormTemplateId:this.templateId}).then((function(t){var i,n=t.success,s=t.message,a=t.data;n?(e.config=new q(a),e.shareLinkOpen=(null===(i=e.config)||void 0===i||null===(i=i.paasFormOuterWriteForm)||void 0===i||null===(i=i.setting)||void 0===i?void 0:i.shareLinkOpen)||!1,e.$parent.$parent.formName=null===a||void 0===a?void 0:a.paasFormTemplateName):e.$message.error(s)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("external form initialize",e)}))},changeSwitchHandler:function(e,t){var i=this;this.pending=!0,c.Jg({paasFormTemplateId:this.templateId,switchOpen:e,type:t}).then((function(n){var s=n.success,a=n.message,o=n.data;s?(2==t?i.config.paasFormOuterWriteForm.bizId=o:i.config.paasFormOuterShareForm.bizId=o,e&&i.initialize()):i.$message.error(a)}))["finally"]((function(){i.pending=!1}))["catch"]((function(e){return console.error("changeSwitchHandler err",e)}))},shareLinkOpenChange:function(e){this.config.paasFormOuterWriteForm.setting.shareLinkOpen=e},changeVisitorSetting:function(e,t){var i;if(e){var n=(null===(i=this[t])||void 0===i?void 0:i.setting)||{},s=n.customerUserLimit,a=n.appointUserLimit;s||a||(this[t].setting.customerUserLimit=!0)}},save:function(){var e=this;this.pending||(this.pending=!0,c.XX((0,a.A)((0,a.A)({},this.config),{},{paasFormTemplateId:this.templateId})).then((function(t){if(!t.success)return e.$message.error(t.message);e.$message.success(e.$t("common.base.saveSuccess")),e.initialize()}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("save err",e)})))},setFieldAuth:function(e,t){this.$refs.fieldAuthModal.open(e,t)},setPersonAuth:function(e){this.$refs.personAuthModal.open(e)},copyText:function(){var e=document.createElement("input");e.value=this.outerUrl,document.body.appendChild(e),e.select(),document.execCommand("Copy"),document.body.removeChild(e),this.$message.success(this.$t("view.template.detail.tip3"))},createQrcode:function(){this.$refs.qrcode.innerHTML="";new(j())("qrcode",{width:132,height:132,text:this.outerUrl});this.createQrcodeVisible=!0},downloadQrcode:function(){var e=document.getElementById("qrcode"),t=e.getElementsByTagName("canvas"),i=document.createElement("a");i.href=t[0].toDataURL("image/png"),i.download=this.$t("view.template.detail.formQrCode"),i.click()}},components:(0,o.A)((0,o.A)({},A.name,A),z.name,z)},Q=H,J=Q,K=(0,y.A)(J,r,l,!1,null,"54802626",null),Y=K.exports,X=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container"},[t("h1",{staticClass:"title"},[e._v(e._s(e.$t("view.designer.rule.menusName.authoritySetting")))]),e._l(e.panelItems,(function(i,n){return t("panel-item",{key:i.type,attrs:{title:i.title,type:i.type,values:i.values},on:{handleSetting:function(t){return e.handleSetting(n)},handleDelete:function(t){return e.handleDeleteItem(t,n)}}})})),t("setting-modal",{ref:"settingModal",attrs:{"show-left-panel":"consult"===e.currentModalType},on:{handleConfirm:e.handleConfirm}})],2)},Z=[],G=i(35730),ee=(i(62838),i(3923),i(27408),i(89370),i(13262),function(){var e=this,t=e._self._c;return t("div",{staticClass:"panel"},[t("div",{staticClass:"title-box"},[t("div",{staticClass:"left"},[t("h2",{staticClass:"panel-title"},[e._v(e._s(e.title.main))]),t("span",[e._v(e._s(e.title.sub))])]),t("div",{staticClass:"right",on:{click:e.handleSetting}},[e._v(" "+e._s(e.$t("view.designer.rule.powerSetting.setAuth"))+" ")])]),t("div",{staticClass:"content"},[t("div",{staticClass:"common-box"},["consult"!==e.type?e._l(e.values,(function(i,n){return t("custom-tag",{key:i.id,staticClass:"rule-tag",attrs:{prefix:"",closable:"1"!==i.id},on:{close:function(t){return e.handleDelete(i,n)}}},["1"===i.type?t("i",{staticClass:"iconfont icon-fdn-user",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"2"===i.type?t("i",{staticClass:"iconfont icon-apartment",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"3"===i.type?t("i",{staticClass:"iconfont icon-idcard",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),e.isOpenData&&i.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:[e._v(" "+e._s(i.name)+" ")]],2)})):[t("div",{staticClass:"txt"},[e._v(" "+e._s(e.$t("view.designer.rule.powerSetting.tip1"))+" ")])]],2)])])}),te=[],ie=i(10888),ne={name:"panel",components:{CustomTag:ie.A},props:{title:{type:Object,default:function(){return{main:"",sub:""}}},type:{type:String,default:function(){return"initiate"}},values:{type:Array,default:function(){return[]}}},computed:{isOpenData:function(){return this.$platform.isOpenData}},methods:{handleSetting:function(){this.$emit("handleSetting")},handleDelete:function(e,t){this.$emit("handleDelete",{tag:e,index:t})}}},se=ne,ae=(0,y.A)(se,ee,te,!1,null,"4abaa921",null),oe=ae.exports,re=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"choose-user-modal",attrs:{show:e.visible,width:e.showLeftPanel?"870px":"700px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"title-box",attrs:{slot:"title"},slot:"title"},[t("span",[e._v(e._s(e.nodeCandidateTitle))]),e.showTitleIcon?t("img",{staticClass:"icon",attrs:{src:i(15842),alt:""}}):e._e()]),t("div",{staticClass:"modal-content"},[e.showLeftPanel?t("div",{staticClass:"left-panel"},e._l(e.panelList,(function(i,n){return t("div",{key:n,class:["item",n===e.actPowerType&&"active"],on:{click:function(t){return e.handlePowerType(n)}}},[e._v(" "+e._s(i.name)+" ")])})),0):e._e(),e.visible?t("config-contact",{ref:"configContact",staticClass:"contact-box",attrs:{title:e.nodeCandidateTitle,value:e.candidates,"disable-values":e.disableArr,"show-dynamic":!1,"show-service-provider":!0,"flow-api":e.flowApi},on:{checkedChange:e.checkedChange}},[t("template",{slot:"tag"},e._l(e.candidates,(function(i,n){return t("custom-tag",{key:n,staticClass:"result-tag",attrs:{closable:"1"!==i.id,prefix:""},on:{close:function(t){return e.handleDelete(i,n)}}},["1"===i.type?t("i",{staticClass:"iconfont icon-fdn-user",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"2"===i.type?t("i",{staticClass:"iconfont icon-apartment",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"3"===i.type?t("i",{staticClass:"iconfont icon-idcard",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),e.isOpenData&&i.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:[e._v(" "+e._s(i.name)+" ")]],2)})),1)],2):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},le=[],ce=(i(92982),i(32914)),de=i(69396),ue=i(84859),me={name:"setting-modal",components:{ConfigContact:ce.A,CustomTag:ie.A},props:{showTitleIcon:{type:Boolean,default:function(){return!1}},showLeftPanel:{type:Boolean,default:function(){return!1}}},computed:{flowApi:function(){return(0,a.A)({},de)},isOpenData:function(){return this.$platform.isOpenData}},data:function(){return{nodeCandidateTitle:"",candidates:[],visible:!1,panelList:[{name:ue.Ay.t("view.designer.rule.powerSetting.allAuth"),val:[],currentTab:"1",authType:0},{name:ue.Ay.t("view.designer.rule.powerSetting.teamAuth"),val:[],currentTab:"1",authType:1}],actPowerType:0,disableArr:["1"]}},watch:{actPowerType:function(e,t){if(this.$refs.configContact.checked.length>0&&(this.panelList[t].val=(0,G.A)(this.$refs.configContact.checked)),this.panelList[e].val.length>0){var i=this.panelList[e].val;this.candidates=i}else this.candidates=[];this.filterDisableArr(e)}},methods:{checkedChange:function(e){this.candidates=e},handleDelete:function(e,t){this.candidates.splice(t,1),this.$refs.configContact.handleDelete(e,t)},handlePowerType:function(e){this.actPowerType=e,this.$refs.configContact.currTab="1"},handleConfirm:function(){var e=this.$refs.configContact.checked;this.showLeftPanel?(e=[],this.panelList[this.actPowerType].val=this.$refs.configContact.checked,this.panelList.forEach((function(t){t.val.length>0&&t.val.forEach((function(i){var n=i.type,s=i.id,a=i.name,o=i.staffId,r=void 0===o?"":o,l=i.rootType,c=void 0===l?"":l;e.push({type:n,id:s,name:a,authType:t.authType,staffId:r,rootType:c})}))}))):e=e.map((function(e){var t=e.id,i=e.name,n=e.type,s=e.staffId,a=void 0===s?"":s,o=e.rootType,r=void 0===o?"":o;return{id:t,name:i,type:n,staffId:a,rootType:r}})),this.$emit("handleConfirm",e)},hideModal:function(){this.visible=!1},showModal:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.title,i=e.values;this.nodeCandidateTitle=t,this.visible=!0,this.replaceData(i)},replaceData:function(e){this.showLeftPanel&&e.length>0?(this.panelList.map((function(t){return t.val=[],e.forEach((function(e){t.authType===parseInt(e.authType)&&t.val.push(e)})),t})),this.candidates=(0,G.A)(this.panelList[this.actPowerType].val)):this.candidates=(0,G.A)(e),this.filterDisableArr(this.actPowerType)},filterDisableArr:function(e){var t=this.panelList.filter((function(t,i){return e!==i})),i=[];t.forEach((function(e){e.val.length>0&&e.val.forEach((function(e){return i.push(e.id)}))})),this.disableArr=i.concat(["1"])}}},fe=me,pe=(0,y.A)(fe,re,le,!1,null,"7477b77c",null),he=pe.exports,ve={name:"power-setting",components:{PanelItem:oe,SettingModal:he},inject:["mode"],data:function(){return{panelItems:[{title:{main:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[0].main"),sub:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[0].sub"),modalTitle:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[0].modalTitle")},operateType:0,type:"initiate",values:[]},{title:{main:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[1].main"),sub:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[1].sub"),modalTitle:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[1].modalTitle")},type:"consult",operateType:1,values:[]},{title:{main:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[2].main"),sub:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[2].sub"),modalTitle:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[2].modalTitle")},operateType:2,type:"delete",values:[]},{title:{main:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[4].main"),sub:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[4].sub"),modalTitle:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[4].modalTitle")},operateType:4,type:"edit",values:[]},{title:{main:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[3].main"),sub:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[3].sub"),modalTitle:ue.Ay.t("view.designer.rule.powerSetting.panelItemsTitle[3].modalTitle")},operateType:3,type:"export",values:[]}],currentModalType:"initiate"}},computed:{templateBizId:function(){return this.$route.query.formId}},created:function(){this.fetchInitializeData()},methods:{fetchInitializeData:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,c.hZ)({templateBizId:e.templateBizId});case 2:i=t.sent,i.success&&(n=i.data,e.panelItems=e.panelItems.map((function(e){return n.forEach((function(t){t.operateType===e.operateType&&(e.values=(0,G.A)(t.authority))})),e})));case 4:case"end":return t.stop()}}),t)})))()},handleConfirm:function(e){var t=this.currentModalType,i=this.panelItems.findIndex((function(e){return e.type===t}));this.$set(this.panelItems[i],"values",e),this.$refs.settingModal.hideModal()},save:function(){var e=this;if(!this.pending){this.pending=!0;var t=this.templateBizId,i=this.panelItems.map((function(e){var i=e.operateType,n=e.values;return{templateBizId:t,authority:n,operateType:i}}));(0,c.zH)(i).then((function(t){if(!t.success)return e.$message.error(t.message);e.$message.success(e.$t("common.base.saveSuccess"))}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("save err",e)}))}},handleDeleteItem:function(e,t){var i=e.index,n=(0,G.A)(this.panelItems[t].values);n.splice(i,1),this.$set(this.panelItems[t],"values",n)},handleSetting:function(e){var t=this,i=this.panelItems[e],n=i.title.modalTitle,s=i.values;this.currentModalType=i.type;var a={title:n,values:s};this.$nextTick((function(){t.$refs["settingModal"].showModal(a)}))}}},ge=ve,be=(0,y.A)(ge,X,Z,!1,null,"268691c9",null),we=be.exports,Ce=function(){var e=this,t=e._self._c;return t("div",{staticClass:"print-box"},[t("div",{staticClass:"print-header"},[t("div",{staticClass:"top-panel"},[t("h1",[e._v(" "+e._s(e.$t("view.designer.rule.printSetting.printTemplate"))+" "),t("el-tooltip",{attrs:{content:e.$t("view.designer.rule.printSetting.tip1"),placement:"top"}},[t("i",{staticClass:"iconfont icon-question"})])],1),t("span",[e._v(e._s(e.$t("view.designer.rule.printSetting.tip1")))])]),t("div",{staticClass:"customize-setting-header-right"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleCreate}},[t("i",{staticClass:"iconfont icon-add2"}),e._v(e._s(e.$t("common.base.create")))])],1)]),t("div",{staticClass:"customize-print-list"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"print-template-box"},[t("div",{staticClass:"print-template-list"},[e.dataList.length>0?e._l(e.dataList,(function(i){var n;return t("div",{key:i.id,class:["print-template-item","flow"!==e.formType&&"auto-height"]},[t("div",{staticClass:"print-template-item-main"},[t("div",{staticClass:"print-template-name"},[t("span",[e._v(e._s(e._f("formatSysName")(i.templateName)))]),t("el-switch",{attrs:{value:i.enable,"inactive-value":0,"active-value":1},on:{change:function(t){return e.handleUpdateStatus(t,i)}}})],1),"flow"===e.formType?t("div",{staticClass:"print-instance"},[e._v(" "+e._s(e.$t("view.designer.rule.printSetting.printableNode"))+"： "),i.setting.allNodeSupport?[t("span",[e._v(e._s(e.$t("view.designer.rule.printSetting.allNode")))])]:i.setting.allNodeSupport||0!==(null===(n=i.setting.supportNodeList)||void 0===n?void 0:n.length)?t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.formatNode(i.setting.supportNodeList),placement:"top"}},[t("span",[e._v(e._s(e.$t("view.designer.rule.printSetting.partNode")))])]):[t("span",[e._v(e._s(e.$t("common.base.notSet")))])],t("i",{staticClass:"iconfont icon-edit-square",on:{click:function(t){return e.handleSettingNodes(i)}}})],2):e._e(),i.updateTime?t("div",{class:["update-time","flow"!==e.formType?"mt-26":null]},[e._v(e._s(e.$t("view.designer.rule.printSetting.lastUpdateTime"))+"："),t("span",[e._v(e._s(i.updateUserName)+" "+e._s(e._f("fmt_datetime")(i.updateTime)))])]):e._e()]),t("div",{staticClass:"bottom"},[1!==i.type?t("div",{staticClass:"text delete",on:{click:function(t){return e.handleDelete(i)}}},[t("span",[e._v(e._s(e.$t("common.base.delete")))]),t("i",{staticClass:"iconfont icon-delete"})]):e._e(),t("div",{staticClass:"text",on:{click:function(t){return e.handleEdit(i)}}},[t("span",[e._v(e._s(e.$t("common.base.edit")))]),t("i",{staticClass:"iconfont icon-edit-square"})])])])})):e._e()],2)])]),t("node-setting-dialog",{ref:"nodeSettingDialog",attrs:{printVersion:e.printVersion},on:{update:e.fetchAllTemplateList}}),t("CustomizeTemplateCreate",{ref:"CustomizeTemplateCreateRef",attrs:{"print-type-list":e.newPrintTemplateList}}),t("setting-template-dialog",{ref:"settingTemplateDialog",on:{update:e.fetchAllTemplateList}})],1)},ye=[],_e=(i(48152),function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{show:e.visible,title:e.$t("view.designer.rule.printSetting.selectPrintableNode"),width:"556px"},on:{close:function(t){e.visible=!1}}},[t("div",{staticClass:"node-list-box"},[t("el-checkbox",{staticClass:"check-all-box",attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v(e._s(e.$t("common.base.selectAll")))]),t("el-checkbox-group",{staticClass:"node-list",on:{change:e.handleNodeItemChange},model:{value:e.checkedList,callback:function(t){e.checkedList=t},expression:"checkedList"}},e._l(e.nodeList,(function(i){return t("el-checkbox",{key:i.bizId,attrs:{label:i.bizId}},[i.name.length>8?[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:i.name,placement:"top"}},[t("span",[e._v(e._s(i.name))])])]:t("span",[e._v(e._s(i.name))])],2)})),1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])}),Ae=[],$e=(i(44807),i(7354),{name:"setting-template-dialog",props:{printVersion:{type:String,default:"v1"}},data:function(){return{checkAll:!1,visible:!1,isIndeterminate:!1,nodeList:[],checkedList:[],bizId:"",id:""}},computed:{templateBizId:function(){return this.$route.query.formId}},methods:{fetchAllNode:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n,s,a;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,(0,c.ao)({formTemplateBizId:t.templateBizId});case 2:n=i.sent,s=n.data,t.nodeList=s,a=s.filter((function(t){return e.find((function(e){return e.bizId===t.bizId}))})).map((function(e){return e.bizId})),t.checkedList=a,a.length===s.length?t.checkAll=!0:(t.checkAll=!1,t.isIndeterminate=a.length>0&&a.length<t.nodeList.length);case 8:case"end":return i.stop()}}),i)})))()},handleConfirm:function(){"v1"===this.printVersion?this.handleConfirmOld():this.handleConfirmNew()},handleConfirmOld:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=e.checkedList.length===e.nodeList.length,t.next=3,(0,c.jV)({bizId:e.bizId,allNodeSupport:i,supportNodeList:e.nodeList.filter((function(t){return e.checkedList.includes(t.bizId)}))});case 3:n=t.sent,n&&(e.$message.success(e.$t("common.base.saveSuccess")),e.visible=!1,e.$emit("update"));case 5:case"end":return t.stop()}}),t)})))()},handleConfirmNew:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n,s;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=e.checkedList.length===e.nodeList.length,n={id:e.id,bizId:e.templateBizId,module:"PAAS",setting:{allNodeSupport:i,supportNodeList:e.nodeList.filter((function(t){return e.checkedList.includes(t.bizId)}))}},t.next=4,(0,c.TT)(n)["catch"]((function(e){return console.log(e)}));case 4:s=t.sent,s&&(e.$message.success(s.message),e.$emit("update")),e.visible=!1;case 7:case"end":return t.stop()}}),t)})))()},handleCheckAllChange:function(e){this.checkedList=e?this.nodeList.map((function(e){return e.bizId})):[],this.isIndeterminate=!1},handleNodeItemChange:function(e){var t=e.length;this.checkAll=t===this.nodeList.length,this.isIndeterminate=t>0&&t<this.nodeList.length},open:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n,s,a,o,r;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n="v1"===t.printVersion?e:e.setting,s=n.supportNodeList,a=void 0===s?[]:s,o=n.bizId,r=void 0===o?"":o,t.bizId=r,t.id=e.id,t.visible=!0,i.next=6,t.fetchAllNode(a);case 6:case"end":return i.stop()}}),i)})))()}}}),ke=$e,Se=(0,y.A)(ke,_e,Ae,!1,null,"7888ce30",null),xe=Se.exports,Ie=(i(33656),function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.title,width:"500px",show:e.dialogFormVisible,"mask-closeable":!1},on:{cancel:e.cancel,"update:show":function(t){e.dialogFormVisible=t}}},[e.clickTaskType?t("el-form",{ref:"form",staticClass:"add-task-form",attrs:{"label-position":"top",model:e.form,rules:e.rules}},["exist"===e.clickTaskType?t("el-form-item",{attrs:{label:e.$t("view.designer.rule.printSetting.tip6"),prop:"templateId"}},[t("el-select",{staticClass:"w-360",attrs:{placeholder:e.$t("view.designer.rule.printSetting.tip7")},model:{value:e.form.templateId,callback:function(t){e.$set(e.form,"templateId",t)},expression:"form.templateId"}},e._l(e.printTypeList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.templateName,value:e.id}})})),1)],1):e._e(),t("el-form-item",{attrs:{label:e.$t("common.base.name"),prop:"typeName"}},[t("el-input",{staticClass:"w-360",attrs:{placeholder:e.$t("view.designer.rule.printSetting.tip4"),maxlength:"20"},model:{value:e.form.typeName,callback:function(t){e.$set(e.form,"typeName","string"===typeof t?t.trim():t)},expression:"form.typeName"}})],1)],1):t("div",{staticClass:"add-task-type"},[t("el-row",{attrs:{type:"flex",justify:"space-between"}},e._l(e.taskTemplateList,(function(i){return t("el-card",{key:i.type,staticClass:"choose-type-box",class:e.getCardActive(i.type)&&"active",style:{"background-image":"url("+i.bgImg+")"},attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.chooseTypeTemplate(i.type)},mouseenter:function(t){return e.enterCard(i.type)},mouseleave:function(t){e.hoverTaskType=""}}},[t("div",{staticClass:"choose-type-box-content"},[t("h2",[e._v(e._s(i.title))]),t("p",{staticClass:"task-type-desc"},[e._v(e._s(i.desc))])])])})),1)],1),e.clickTaskType?t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{loading:e.padding,type:"primary"},on:{click:e.createPrintType}},[e._v(e._s(e.$t("common.base.makeSure")))])],1):e._e()],1)}),Te=[],Le=i.p+"img/task-template-blank.8346f1b6.png",Ne=i.p+"img/task-template-copy.5539ce5f.png",Fe={name:"customize-template-create",props:{printTypeList:{type:Array,default:function(){return[]}}},components:{},data:function(){return{hoverTaskType:"",clickTaskType:"",form:{typeName:"",templateId:""},rules:{templateId:[{required:!0,message:ue.Ay.t("view.designer.rule.printSetting.tip7"),trigger:"blur"}],typeName:[{required:!0,message:ue.Ay.t("view.designer.rule.printSetting.tip4"),trigger:"blur"}]},padding:!1,dialogFormVisible:!1,nodeList:[],htmlContentResult:{}}},computed:{title:function(){var e=this;return this.clickTaskType?this.taskTemplateList.find((function(t){return t.type===e.clickTaskType})).title:ue.Ay.t("view.designer.rule.printSetting.tip3")},taskTemplateList:function(){return[{type:"blank",title:ue.Ay.t("view.designer.rule.printSetting.tip8"),desc:ue.Ay.t("view.designer.rule.printSetting.tip9"),bgImg:Le},{type:"exist",title:ue.Ay.t("view.designer.rule.printSetting.tip10"),desc:ue.Ay.t("view.designer.rule.printSetting.tip11"),bgImg:Ne}]}},created:function(){this.fetchAllNode()},methods:{enterCard:function(e){this.hoverTaskType=e},getCardActive:function(e){return this.hoverTaskType===e},cancel:function(){var e=this;this.dialogFormVisible=!1,setTimeout((function(){Object.assign(e.$data,e.$options.data()),e.$refs.form.resetFields()}),100)},chooseTypeTemplate:function(e){this.clickTaskType=e},createPrintType:function(){var e=this;this.$refs.form.validate(function(){var t=(0,x.A)((0,S.A)().mark((function t(i){var n,s,o,r,l;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i){t.next=2;break}return t.abrupt("return");case 2:if("exist"!==e.clickTaskType){t.next=13;break}return t.next=5,(0,c.om)({id:e.form.templateId})["catch"]((function(e){return console.log(e)}));case 5:n=t.sent,s=n.result,o=void 0===s?{}:s,r=(0,a.A)((0,a.A)({},o),{},{type:0,enable:0,templateName:e.form.typeName}),delete r.id,e.handleCreate(r),t.next=15;break;case 13:l={module:"PAAS",templateName:e.form.typeName,bizId:e.$route.query.formId,enable:0,setting:{allNodeSupport:!0,supportNodeList:e.nodeList}},e.handleCreate(l);case 15:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleCreate:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.padding=!0,i.next=3,(0,c.I2)(e)["catch"]((function(e){return console.log(e)}))["finally"]((function(){return t.padding=!1}));case 3:n=i.sent,n.success&&(t.$parent.fetchAllTemplateList(),t.$message.success(n.message)),t.cancel();case 6:case"end":return i.stop()}}),i)})))()},fetchAllNode:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n,s;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,c.ao)({formTemplateBizId:e.$route.query.formId});case 2:n=t.sent,s=null!==(i=n.data)&&void 0!==i?i:[],e.nodeList=s;case 5:case"end":return t.stop()}}),t)})))()}}},ze=Fe,Ee=(0,y.A)(ze,Ie,Te,!1,null,"ebd6c386",null),Oe=Ee.exports,De=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{show:e.visible,title:e.$t("view.designer.rule.printSetting.setPrintTemplate")},on:{close:function(t){e.visible=!1}}},[e.list.length>0?t("div",{staticClass:"field-list"},e._l(e.list,(function(i,n){return t("div",{key:n,staticClass:"field-list-item"},[t("span",[e._v(e._s(i.title))]),t("div",{staticClass:"field-list-item-content"},[i.checkboxList?t("el-checkbox-group",{model:{value:i.checkedList,callback:function(t){e.$set(i,"checkedList",t)},expression:"item.checkedList"}},e._l(i.checkboxList,(function(i){return t("el-checkbox",{key:i.id,attrs:{label:i.fieldName}},[i.displayName.length>8?[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:i.displayName,placement:"top"}},[t("span",[e._v(e._s(i.displayName))])])]:t("span",[e._v(e._s(i.displayName))])],2)})),1):e._e()],1)])})),0):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},Re=[],Pe=(i(76119),i(98216)),Be=["info","separator","attachment","relatedData","subFormApi","connector",h.E.JsCodeBlock],Me=[h.E.LackStock,h.E.InStock,h.E.OutStock,h.E.OutSparepartStock],Ve=[h.E.OutWarehouse,h.E.SunmiOutWarehouse,h.E.OutSparepart],Ue={name:"node-setting-dialog",data:function(){return{visible:!1,list:[],fieldJson:[]}},computed:{templateBizId:function(){return this.$route.query.formId},stockSubFormListMap:function(){return{inStock:Pe.WK,outStock:Pe.ns,lackStock:Pe.c6,outSparepartStock:Pe.ng}},formType:function(){return this.$route.query.formType}},methods:{handleConfirm:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n,s;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i=[],n=e.list,n.forEach((function(t){if(t.isChildForm){var n=i.find((function(e){return e.fieldName===t.checkboxList[0].parentFieldName}));n?n.subFormFieldList=t.checkboxList.filter((function(e){return t.checkedList.includes(e.fieldName)})).map((function(t){return e.formatRsObject(t)})):i.push(e.formatRsObject((0,a.A)((0,a.A)({},t),{},{subFormFieldList:t.checkboxList.filter((function(e){return t.checkedList.includes(e.fieldName)})).map((function(t){return e.formatRsObject(t)}))})))}else{var s=t.checkboxList.filter((function(e){return t.checkedList.includes(e.fieldName)})).map((function(t){return e.formatRsObject(t)}));i=i.concat(s)}})),t.next=5,(0,c.jV)({bizId:e.bizId,fieldJson:i});case 5:s=t.sent,s&&(e.$message.success(e.$t("common.base.tip.editSuccess")),e.visible=!1,e.$emit("update"));case 7:case"end":return t.stop()}}),t)})))()},fetchAllField:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n,s,o,r,l,c,d,u,m,h,v;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=e.map((function(e){return e.fieldName})),s=t.stockSubFormListMap,o=function(e){return e.filter((function(e){return![].concat(Me,["material","attachment"]).includes(e.formType)}))},i.next=5,(0,f.QA)({templateBizId:t.templateBizId});case 5:r=i.sent,l=r.data,c=l.paasFormFieldVOList,d=l.systemFormFieldVOList,u=[t.formatCheckBoxListObject({title:t.$t("view.template.print.formFields")})],m=c.filter((function(e){return!Be.includes(e.formType)&&!e.isHidden})),h=[],m.filter((function(e){return Ve.includes(e.formType)})).forEach((function(e){e.subFormFieldList.forEach((function(e){Me.includes(e.formType)&&h.push(e)}))})),v="plain"===t.formType?[]:d.map((function(e){return e.systemInitField=!0,e})),[].concat((0,G.A)(m),h,(0,G.A)(v)).forEach((function(i){if((0,p.hL)(i)||Me.includes(i.formType)){var r,l=(null===(r=e.find((function(e){return e.fieldName===i.fieldName})))||void 0===r?void 0:r.subFormFieldList)||[],c=Me.includes(i.formType)?o(s[i.formType]):o((null===i||void 0===i?void 0:i.subFormFieldList)||[]);c.length>0&&u.push(t.formatCheckBoxListObject((0,a.A)((0,a.A)({},i),{},{title:"".concat(t.$t("view.designer.rule.printSetting.subFormFields"),"-").concat(i.displayName),isChildForm:!0,checkboxList:c,checkedList:l.map((function(e){return e.fieldName}))})))}else i.systemInitField?t.checkedCanPrintFieldsIsExistField(i,u,n,t.$t("common.form.systemWidget")):t.checkedCanPrintFieldsIsExistField(i,u,n,t.$t("view.template.print.formFields"))})),u.unshift(u.pop()),t.list=u;case 15:case"end":return i.stop()}}),i)})))()},checkedCanPrintFieldsIsExistField:function(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.$t("common.form.systemWidget"),s=t.find((function(e){return e.title===n}));s?(s.checkboxList.push(e),i.includes(e.fieldName)&&s.checkedList.push(e.fieldName)):t.push(this.formatCheckBoxListObject({title:n,checkboxList:[e],checkedList:i.includes(e.fieldName)?[e.fieldName]:[]}))},open:function(e){var t=e.bizId,i=e.fieldJson,n=void 0===i?[]:i;this.visible=!0,this.fieldJson=n,this.bizId=t,this.fetchAllField(n)},formatRsObject:function(e){var t=e.displayName,i=e.fieldName,n=e.formType,s=e.subFormFieldList,a=void 0===s?[]:s;return{displayName:t,fieldName:i,formType:n,subFormFieldList:a}},formatCheckBoxListObject:function(e){var t=e.title,i=e.displayName,n=void 0===i?"":i,s=e.fieldName,a=void 0===s?"":s,o=e.formType,r=void 0===o?"":o,l=e.checkboxList,c=void 0===l?[]:l,d=e.isChildForm,u=void 0!==d&&d,m=e.checkedList,f=void 0===m?[]:m;return{title:t,displayName:n,fieldName:a,formType:r,checkboxList:c,isChildForm:u,checkedList:f}}}},qe=Ue,We=(0,y.A)(qe,De,Re,!1,null,"7b2b606a",null),je=We.exports,He={name:"print-setting-v2",components:{SettingTemplateDialog:je,NodeSettingDialog:xe,CustomizeTemplateCreate:Oe},data:function(){return{show:!1,loading:!1,dataList:[],nodeList:[],printVersion:"v2"}},computed:{templateBizId:function(){return this.$route.query.formId},formType:function(){return this.$route.query.formType},newPrintTemplateList:function(){return this.dataList.filter((function(e){return!(null!==e&&void 0!==e&&e.isOld)}))}},filters:{formatSysName:function(e){var t={"系统默认模板":"systemTemplateName"};return ue.Ru.t("view.designer.rule.printSetting.".concat(t[e])).includes("undefined")?e:ue.Ru.t("view.designer.rule.printSetting.".concat(t[e]))}},methods:{handleCreate:function(){this.$refs.CustomizeTemplateCreateRef.dialogFormVisible=!0,this.$refs.CustomizeTemplateCreateRef.customizeName=""},handleDelete:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n,s,a;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$platform.confirm(t.$t("view.designer.workFlow.tip1"))["catch"]((function(e){return console.log(e)}));case 2:if(n=i.sent,n){i.next=5;break}return i.abrupt("return");case 5:return s={id:e.id},i.next=8,(0,c.$u)(s)["catch"]((function(e){return console.log(e)}));case 8:a=i.sent,a.success&&(t.$message.success(a.message),t.fetchAllTemplateList());case 10:case"end":return i.stop()}}),i)})))()},formatNode:function(e){return e.map((function(e){return e.name})).join(",")},printSettingInitPrintTemplate:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,c.r5)({formTemplateId:e.templateBizId})["catch"]((function(e){return console.log(e)}));case 2:e.fetchAllTemplateList();case 3:case"end":return t.stop()}}),t)})))()},fetchAllTemplateList:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n,s;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,(0,c.Aw)({module:"PAAS",bizId:e.templateBizId})["finally"]((function(){return e.loading=!1}));case 3:return n=t.sent,t.next=6,e.fetchAllTemplateListForV1();case 6:s=t.sent,e.dataList=(null!==(i=n.result)&&void 0!==i?i:[]).concat(s);case 8:case"end":return t.stop()}}),t)})))()},handleSettingNodes:function(e){var t=this;if(null!==e&&void 0!==e&&e.isOld)return this.printVersion="v1",this.$nextTick((function(){return t.handleEditV1(e,"node")}));this.printVersion="v2",this.$nextTick((function(){return t.$refs.nodeSettingDialog.open(e)}))},handleEdit:function(e){if(null!==e&&void 0!==e&&e.isOld)return this.handleEditV1(e,"field");this.$router.push({path:"/customize",query:{customizeName:e.templateName,formId:this.templateBizId,appId:this.$route.query.appId,customizeTemplateId:e.id,formType:this.formType,formName:this.$route.query.formName}})},handleUpdateStatus:function(e,t){var i=this;return(0,x.A)((0,S.A)().mark((function n(){var s,a;return(0,S.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(null===t||void 0===t||!t.isOld){n.next=2;break}return n.abrupt("return",i.handleUpdateStatusV1(e,t));case 2:return s={id:t.id,enable:e,bizId:i.templateBizId,module:"PAAS"},n.next=5,(0,c.TT)(s)["catch"]((function(e){return console.log(e)}));case 5:a=n.sent,a.success?(i.$message.success(a.message),i.fetchAllTemplateList()):i.$message.warning(a.message);case 7:case"end":return n.stop()}}),n)})))()},fetchAllTemplateListForV1:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,(0,c.j4)({formTemplateBizId:e.templateBizId})["finally"]((function(){return e.loading=!1}));case 3:return i=t.sent,t.abrupt("return",i.data.map((function(e){return e.enable=e.status,e.isOld=!0,e.templateName=e.name,e.setting={allNodeSupport:e.allNodeSupport,supportNodeList:e.supportNodeList},e})));case 5:case"end":return t.stop()}}),t)})))()},handleUpdateStatusV1:function(e,t){var i=this;return(0,x.A)((0,S.A)().mark((function n(){var s;return(0,S.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.enable=e,n.next=3,(0,c.jV)({status:Number(e),bizId:t.bizId});case 3:s=n.sent,s&&i.$message.success(i.$t("common.base.tip.editSuccess"));case 5:case"end":return n.stop()}}),n)})))()},handleEditV1:function(e,t){"node"===t?this.$refs.nodeSettingDialog.open(e):this.$refs.settingTemplateDialog.open(e)}},created:function(){this.printSettingInitPrintTemplate()}},Qe=He,Je=(0,y.A)(Qe,Ce,ye,!1,null,"61bb1a8e",null),Ke=Je.exports,Ye=function(){var e=this,t=e._self._c;return t("div",{staticClass:"satisfaction-setting"},[t("div",{staticClass:"satisfaction-setting-header"},[t("div",{staticClass:"satisfaction-setting-header-left"},[t("h2",[e._v(" "+e._s(e.$t("view.designer.rule.menusName.satisfactionSetting"))+" "),t("el-tooltip",{attrs:{trigger:"hover"}},[t("div",{staticStyle:{width:"420px"},attrs:{slot:"content"},slot:"content"},[t("h3",[e._v(e._s(e.$t("common.base.rulerTip"))+"：")]),t("p",[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.label1")))]),t("h3",[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.useDescription"))+"：")]),t("div",[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.label2")))])]),t("i",{staticClass:"iconfont icon-question"})])],1),t("p",[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.label3")))])]),t("div",{staticClass:"satisfaction-setting-header-right"},[t("el-button",{attrs:{type:"primary"},on:{click:e.create}},[t("i",{staticClass:"iconfont icon-add2"}),e._v(e._s(e.$t("common.base.add2")))])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"satisfaction-setting-content"},[e._l(e.satisfactionList,(function(i,n){return t("satisfaction-setting-item",{key:i.id,attrs:{index:n,item:i},on:{delete:e.handleDelete,preview:e.handlePreview,action:e.handleAction}})})),e.loading||e.satisfactionList.length?e._e():t("no-data-view")],2),t("satisfaction-create-dialog",{ref:"createDialog",attrs:{value:e.satisfactionList},on:{preview:e.handlePreview,create:e.handleCreate}}),t("satisfaction-send-rule-setting",{ref:"sendRuleSetting",attrs:{fields:e.fields,"node-list":e.nodeTemplateList,"selected-nodes":e.selectedNodes,"action-type":e.actionType},on:{"update:actionType":function(t){e.actionType=t},"update:action-type":function(t){e.actionType=t},update:e.handleUpdate}}),t("satisfaction-preview-dialog",{ref:"previewDialog"})],1)},Xe=[],Ze=(i(55650),i(53417),i(8200),i(36886),i(56831),i(4118),i(5981),i(63074),i(39724),function(){var e=this,t=e._self._c;return t("div",{staticClass:"satisfaction-setting-item"},[t("div",{staticClass:"satisfaction-setting-item-top"},[t("div",{staticClass:"satisfaction-setting-item-title"},[t("h2",[e._v(e._s(e.item.satisfactionName))]),t("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:e.enableHandler},model:{value:e.item.enable,callback:function(t){e.$set(e.item,"enable",t)},expression:"item.enable"}})],1),t("div",{staticClass:"satisfaction-setting-item-info"},[t("p",[e._v(" "+e._s(e.$t("view.template.detail.sendTo"))+"： "),e.candidates.length?t("span",[e._v(e._s(e.$t("common.base.haveSet")))]):t("span",{staticClass:"placeholder"},[e._v(e._s(e.$t("common.base.notSet")))]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:function(t){return e.handleAction(1)}}})]),t("p",[e._v(" "+e._s(e.$t("view.designer.rule.satisfactionSetting.sendableNode"))+"： "),e.sendNodeTemplateBizId.length?t("span",[e._v(e._s(e.$t("common.base.haveSet")))]):t("span",{staticClass:"placeholder"},[e._v(e._s(e.$t("common.base.notSet")))]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:function(t){return e.handleAction(2)}}})])]),t("div",{staticClass:"satisfaction-setting-item-info"},[t("p",[e._v(" "+e._s(e.$t("view.designer.rule.satisfactionSetting.autoSendRule"))+"： "),e.sendRule.nodeState?t("span",[e._v(e._s(e.$t("common.base.haveSet")))]):t("span",{staticClass:"placeholder"},[e._v(e._s(e.$t("common.base.notSet")))]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:function(t){return e.handleAction(3)}}})]),t("p",[e._v(" "+e._s(e.$t("view.template.detail.sendType"))+"： "),e.sendType.length?t("span",[e._v(e._s(e.$t("common.base.haveSet")))]):t("span",{staticClass:"placeholder"},[e._v(e._s(e.$t("common.base.notSet")))]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:function(t){return e.handleAction(4)}}})])])]),t("div",{staticClass:"satisfaction-setting-item-bottom"},[t("div",{on:{click:e.handleDelete}},[t("i",{staticClass:"iconfont icon-delete"}),e._v(e._s(e.$t("common.base.delete")))]),t("div",{on:{click:e.handlePreview}},[t("i",{staticClass:"iconfont icon-yuedu"}),e._v(e._s(e.$t("common.base.preview")))])])])}),Ge=[],et={name:"satisfaction-setting-item",props:{item:{type:Object,default:function(){return{}}},index:{type:Number,default:null}},computed:{sendRule:function(){var e;return(null===(e=this.item)||void 0===e?void 0:e.sendRule)||{}},candidates:function(){var e;return(null===(e=this.sendRule)||void 0===e?void 0:e.candidates)||[]},sendNodeTemplateBizId:function(){var e;return(null===(e=this.item)||void 0===e?void 0:e.sendNodeTemplateBizId)||[]},sendType:function(){var e;return(null===(e=this.sendRule)||void 0===e?void 0:e.sendType)||[]}},methods:{handleDelete:function(){var e=this;this.$confirm(this.$t("view.designer.rule.satisfactionSetting.tip6"),this.$t("common.base.toast"),{confirmButtonText:this.$t("common.base.makeSure"),cancelButtonText:this.$t("common.base.cancel"),type:"warning"}).then((function(){e.$emit("delete",e.index)}))},handlePreview:function(){this.$emit("preview",this.item.satisfactionId)},handleAction:function(e){this.$emit("action",this.item,e)},enableHandler:function(e){e||this.$set(this.item,"sendNodeTemplateBizId",[])}}},tt=et,it=(0,y.A)(tt,Ze,Ge,!1,null,"8b053f6e",null),nt=it.exports,st=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"satisfaction-create-dialog",attrs:{title:e.$t("view.designer.rule.satisfactionSetting.addQuestionnaireScheme"),width:"800px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"satisfaction-list"},[e._l(e.satisfactionList,(function(i){return t("div",{key:i.id,staticClass:"satisfaction-list-item"},[t("h2",[e._v(e._s(i.satisfactionName))]),t("p",[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.topicCount",{count:i.topicCount})))]),t("p",[e._v(e._s(e.$t("view.designer.rule.printSetting.lastUpdateTime"))+"："),t("span",[e._v(e._s(i.userName)+" "+e._s(e._f("fmt_datetime")(i.updateTime)))])]),t("div",{staticClass:"button-group"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.preview(i)}}},[e._v(e._s(e.$t("common.base.preview")))]),t("el-button",{attrs:{type:"primary",disabled:i.checked},on:{click:function(t){return e.submit(i)}}},[e._v(e._s(i.checked?e.$t("common.base.added"):e.$t("common.base.add2")))])],1)])})),e.loading||e.satisfactionList.length?e._e():t("no-data-view",{attrs:{"notice-msg":e.$t("view.designer.rule.satisfactionSetting.tip5")}})],2),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},at=[],ot={name:"satisfaction-create-dialog",props:{value:{type:Array,default:function(){return[]}}},data:function(){return{visible:!1,loading:!1,satisfactionList:[]}},methods:{open:function(){this.satisfactionList=[],this.visible=!0,this.getSatisfactionList()},preview:function(e){this.$emit("preview",e.satisfactionId)},submit:function(e){this.$emit("create",e),this.visible=!1},getSatisfactionList:function(){var e=this;this.loading=!0,c.ny().then((function(t){var i=t.success,n=t.message,s=t.result;if(i){var a=s||{},o=a.usedList,r=void 0===o?[]:o;e.satisfactionList=r.map((function(t){var i,n=e.value.findIndex((function(e){return e.satisfactionId==t.satisfactionId}));return t.checked=n>-1,t.userName=(null===t||void 0===t||null===(i=t.userInfoModel)||void 0===i?void 0:i.displayName)||"",t}))}else e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("getSatisfactionList",e)}))}}},rt=ot,lt=(0,y.A)(rt,st,at,!1,null,"51f6b0b2",null),ct=lt.exports,dt=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"satisfaction-preview-dialog",attrs:{title:e.$t("common.base.preview"),width:"480px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"form-design-main"},[e.visible?t("div",{staticClass:"form-design-center"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"form-design-phone"},[t("iframe",{attrs:{id:"iframepage",src:e.iframeUrl,height:"100%",width:"100%"}})])]):e._e()]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},ut=[],mt={name:"satisfaction-preview-dialog",data:function(){return{loading:!1,visible:!1,iframeUrl:""}},created:function(){var e=this;window.addEventListener("message",(function(t){var i=t.data;e.loading=i.showLoading}))},methods:{open:function(e){this.loading=!0,this.iframeUrl="/pcoperation/task/evaluate?id=".concat(e,"&isReviewed=0&isEvaluated=0&isShowPhone=true"),this.visible=!0}}},ft=mt,pt=(0,y.A)(ft,dt,ut,!1,null,"937c8e70",null),ht=pt.exports,vt=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"satisfaction-send-rule-setting",attrs:{title:e.getTitle(),width:"640px",show:e.visible},on:{"update:show":function(t){e.visible=t},close:e.close}},[1==e.actionType?[t("config-contact",{ref:"configContact",attrs:{fields:e.fields,value:e.candidates,"flow-api":e.flowApi,"show-dynamic-menus":e.configContactDynamicMenus,"show-dynamic":""}})]:2==e.actionType?t("div",{staticClass:"send-node-setting"},[t("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.isCheckedAll,callback:function(t){e.isCheckedAll=t},expression:"isCheckedAll"}},[e._v(e._s(e.$t("common.base.selectAll")))]),t("el-checkbox-group",{model:{value:e.checkedNodes,callback:function(t){e.checkedNodes=t},expression:"checkedNodes"}},e._l(e.nodeList,(function(i){return t("el-checkbox",{key:i.id,attrs:{label:i.id,disabled:e.disabledNodes.includes(i.id)}},[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:i.name,placement:"top",disabled:i.name.length<=10}},[t("span",[e._v(e._s(i.name))])])],1)})),1)],1):3==e.actionType?t("div",{staticClass:"auto-send-setting"},[t("p",{staticClass:"mb_8"},[e._v(e._s(e.$t("view.designer.rule.satisfactionSetting.label4")))]),t("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:e.$t("view.designer.rule.satisfactionSetting.placeholder1")},model:{value:e.value.sendRule.nodeState,callback:function(t){e.$set(e.value.sendRule,"nodeState",t)},expression:"value.sendRule.nodeState"}},[t("el-option",{attrs:{label:e.$t("view.designer.rule.satisfactionSetting.enterThisNode"),value:1}}),t("el-option",{attrs:{label:e.$t("view.designer.rule.satisfactionSetting.finishThisNode"),value:2}})],1)],1):4==e.actionType?t("div",{staticClass:"send-type-setting"},[t("el-select",{staticClass:"w100",attrs:{clearable:"",multiple:"",placeholder:e.$t("view.designer.rule.satisfactionSetting.placeholder2")},model:{value:e.value.sendRule.sendType,callback:function(t){e.$set(e.value.sendRule,"sendType",t)},expression:"value.sendRule.sendType"}},e._l(e.sendTypeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)],2)},gt=[],bt=(i(87313),i(14126),i(19447)),wt=i(51668),Ct=i(92935),yt=i.n(Ct),_t=wt.A.NODE_OWNER,At=wt.A.DEPT_MANAGER,$t=wt.A.USER,kt=wt.A.CUSTOMER,St=wt.A.FORM_CUSTOMER,xt=wt.A.FORM_LINK_MAN,It=wt.A.USER_PHOTO,Tt=wt.A.USER_EMAIL,Lt={name:"satisfaction-send-rule-setting",props:{actionType:{type:Number,default:null},fields:{type:Array,default:function(){return[]}},nodeList:{type:Array,default:function(){return[]}},selectedNodes:{type:Array,default:function(){return[]}}},data:function(){return{visible:!1,isIndeterminate:!1,isCheckedAll:!1,value:{},checkedNodes:[],disabledNodes:[],sendTypeOptions:[bt.A.DX,bt.A.SYSTEM,bt.A.EMAIL]}},computed:{candidates:function(){var e;return(null===(e=this.value)||void 0===e||null===(e=e.sendRule)||void 0===e?void 0:e.candidates)||[]},flowApi:function(){return(0,a.A)({},de)},configContactDynamicMenus:function(){return[_t.name,At.name,$t.name,kt.name,St.name,xt.name,It.name,Tt.name]}},methods:{getTitle:function(){switch(this.actionType){case 1:return this.$t("view.designer.rule.satisfactionSetting.title1");case 2:return this.$t("view.designer.rule.satisfactionSetting.title2");case 3:return this.$t("view.designer.rule.satisfactionSetting.title3");case 4:return this.$t("view.designer.rule.satisfactionSetting.title4")}},open:function(e){this.value=yt().cloneDeep(e);var t=this.value.sendNodeTemplateBizId,i=void 0===t?[]:t;this.checkedNodes=i,this.disabledNodes=this.selectedNodes.filter((function(e){return i.every((function(t){return e!==t}))})),this.visible=!0},close:function(){this.$emit("update:actionType",null),this.visible=!1},save:function(){1==this.actionType&&this.chooseUser(),this.$emit("update",this.value),this.close()},chooseUser:function(){var e=this.$refs.configContact.checked;e=e.map((function(e){var t=e.id,i=e.name,n=e.type,s=e.extend,a={id:t,name:i,type:n};return s&&(a.extend=s),a})),this.$set(this.value.sendRule,"candidates",e)},handleCheckAllChange:function(e){var t=this;this.checkedNodes=e?this.nodeList.filter((function(e){return!t.disabledNodes.includes(e.id)})).map((function(e){return e.id})):[],this.isIndeterminate=!1}},watch:{checkedNodes:function(e){var t=e.length;this.isCheckedAll=t===this.nodeList.length,this.isIndeterminate=t>0&&t<this.nodeList.length,this.$set(this.value,"sendNodeTemplateBizId",e)}},components:(0,o.A)({},ce.A.name,ce.A)},Nt=Lt,Ft=(0,y.A)(Nt,vt,gt,!1,null,"3183656c",null),zt=Ft.exports,Et=i(80602),Ot={name:"satisfaction-setting",provide:function(){return{flowData:{}}},props:{templateId:{type:String,default:""}},data:function(){return{pending:!1,loading:!1,fields:[],actionType:null,nodeTemplateList:[],satisfactionList:[]}},computed:{appId:function(){return this.$route.query.appId},selectedNodes:function(){var e=[];return this.satisfactionList.forEach((function(t){var i=t.sendNodeTemplateBizId,n=void 0===i?[]:i;e.push.apply(e,(0,G.A)(n))})),(0,G.A)(new Set(e))}},mounted:function(){this.initialize(),this.fetchFormFields(),this.fetchFlowProcess()},methods:{initialize:function(){var e=this;this.loading=!0,c.Jm({formTemplateBizId:this.templateId}).then((function(t){var i=t.success,n=t.message,s=t.data,a=void 0===s?[]:s;i?e.satisfactionList=a.map((function(t){return e.formatSatisfaction(t)})):e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("satisfaction setting initialize",e)}))},fetchFormFields:function(){var e=this;f.QA({templateBizId:this.templateId}).then((function(t){var i=t.success,n=t.data;i&&(e.fields=(null===n||void 0===n?void 0:n.paasFormFieldVOList)||[])}))["catch"]((function(e){return console.log(e)}))},fetchFlowProcess:function(){var e=this;de.getProcess({appId:this.appId,formTemplateId:this.templateId}).then((function(t){var i=t.success,n=t.data,s=void 0===n?{}:n;if(i){var a=s.attribute,o=JSON.parse(a||"{}"),r=[Et.A.PROCEDD_NODE,Et.A.APPROVE_NODE,Et.A.END_NODE],l=o.cells.filter((function(e){return r.includes(e.shape)}));e.nodeTemplateList=l.map((function(t){var i,n=(null===t||void 0===t||null===(i=t.data)||void 0===i||null===(i=i.attribute)||void 0===i||null===(i=i.nameLanguage)||void 0===i?void 0:i[e.$i18n.locale])||t.data.name;return{id:t.id,name:n,shape:t.shape}}))}}))["catch"]((function(e){return console.log(e)}))},formatSatisfaction:function(e){var t={nodeState:"",candidates:[],sendType:[1]},i=[],n=e.enable,s=void 0===n?1:n;return{bizId:e.bizId,satisfactionId:e.satisfactionId,satisfactionName:e.satisfactionName,sendRule:e.sendRule||t,sendNodeTemplateBizId:e.sendNodeTemplateBizId||i,enable:s}},handleCreate:function(e){this.satisfactionList.unshift(this.formatSatisfaction(e))},handleDelete:function(e){this.satisfactionList.splice(e,1)},handlePreview:function(e){this.$refs.previewDialog.open(e)},handleAction:function(e,t){this.actionType=t,this.$refs.sendRuleSetting.open(e)},handleUpdate:function(e){var t=this.satisfactionList.findIndex((function(t){return t.satisfactionId==e.satisfactionId}));this.$set(this.satisfactionList,t,e)},create:function(){this.$refs.createDialog.open()},save:function(){var e=this,t=this.validate(this.satisfactionList);return t.length?(this.showNotification(t),Promise.reject("error")):this.pending?void 0:(this.pending=!0,c.Dw({formTemplateBizId:this.templateId,returnVisitSettingForms:this.satisfactionList}).then((function(t){if(!t.success)return e.$message.error(t.message);e.$message.success(e.$t("common.base.saveSuccess")),e.satisfactionList=(t.data||[]).map((function(t){return e.formatSatisfaction(t)}))}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("save err",e)})))},validate:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter((function(e){return e.enable})).map((function(t){var i=t.satisfactionName,n=t.sendRule,s=t.sendNodeTemplateBizId,a=void 0===s?[]:s,o=n||{},r=o.candidates,l=void 0===r?[]:r,c=o.sendType,d=void 0===c?[]:c,u=[];return l.length||u.push(e.$t("view.designer.rule.satisfactionSetting.tip1")),a.length||u.push(e.$t("view.designer.rule.satisfactionSetting.tip2")),d.length||u.push(e.$t("view.designer.rule.satisfactionSetting.tip3")),u.length>0?{message:u,title:i}:null})).filter((function(e){return null!=e}))},showNotification:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.$notify({type:"error",title:this.$t("view.designer.rule.satisfactionSetting.tip4"),duration:0,customClass:"base-notification",message:function(t){var i=e.map((function(e){var i=e.message.map((function(e){return t("p",["- ",e])}));return i.unshift(t("h3",[e.title])),i}));return t("div",[i])}(this.$createElement)})}},components:(0,o.A)((0,o.A)((0,o.A)((0,o.A)({},nt.name,nt),ct.name,ct),ht.name,ht),zt.name,zt)},Dt=Ot,Rt=(0,y.A)(Dt,Ye,Xe,!1,null,"fb9dec18",null),Pt=Rt.exports,Bt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-setting"},[t("div",{staticClass:"card-setting-header"},[t("div",{staticClass:"card-setting-header-left"},[t("h2",[e._v(e._s(e.$t("view.designer.rule.menusName.componentsSetting")))]),t("p",[e._v(e._s(e.$t("view.designer.rule.tip1")))])]),t("div",{staticClass:"card-setting-header-right"},[t("el-button",{attrs:{type:"primary"},on:{click:e.openCreateDialog}},[t("i",{staticClass:"iconfont icon-add2"}),e._v(e._s(e.$t("common.base.add2")))])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-setting-content"},[e._l(e.cardList,(function(i,n){return t("card-setting-item",{key:i.id,attrs:{index:n,item:i},on:{delete:e.handleDelete,preview:e.handlePreview}})})),e.loading||e.cardList.length?e._e():t("no-data-view")],2),t("card-create-dialog",{ref:"createDialog",attrs:{value:e.cardList},on:{preview:e.handlePreview,create:e.handleCreate}}),t("card-preview-dialog",{ref:"previewDialog"})],1)},Mt=[],Vt=i(93526),Ut=function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-setting-item"},[t("div",{staticClass:"card-setting-item-top"},[t("div",{staticClass:"card-setting-item-title"},[t("h2",[e._v(e._s(e.moduleName))]),t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.item.enable,callback:function(t){e.$set(e.item,"enable",t)},expression:"item.enable"}})],1),t("div",{staticClass:"card-setting-item-info"},[t("p",{staticClass:"title"},[e._v(e._s(e.$t("view.designer.rule.includeFields"))+"：")]),t("p",{staticClass:"fields"},[e._v(e._s(e.moduleFields))])])]),t("div",{staticClass:"card-setting-item-bottom"},[t("div",{on:{click:e.handlePreview}},[t("i",{staticClass:"iconfont icon-yuedu"}),e._v(e._s(e.$t("common.base.preview")))]),t("div",{on:{click:e.handleDelete}},[t("i",{staticClass:"iconfont icon-delete"}),e._v(e._s(e.$t("common.base.delete")))])])])},qt=[],Wt=i(78528),jt={name:"card-setting-item",props:{item:{type:Object,default:function(){return{}}},index:{type:Number,default:null}},computed:{moduleFields:function(){var e,t=(null===(e=this.item)||void 0===e?void 0:e.cardFieldVoList)||[];return t.map((function(e){return e.displayName})).join("、")},moduleName:function(){return Wt.A[this.item.module]}},methods:{handleDelete:function(){var e=this;this.$confirm(this.$t("view.designer.rule.tip3"),this.$t("common.base.toast"),{confirmButtonText:this.$t("common.base.makeSure"),cancelButtonText:this.$t("common.base.cancel"),type:"warning"}).then((function(){e.$emit("delete",e.index)}))},handlePreview:function(){this.$emit("preview",this.item.cardFieldVoList)}}},Ht=jt,Qt=(0,y.A)(Ht,Ut,qt,!1,null,"92b386ac",null),Jt=Qt.exports,Kt=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-create-dialog",attrs:{title:e.$t("view.designer.rule.addComponent"),width:"800px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-list"},e._l(e.cardList,(function(i){return t("div",{key:i.module,staticClass:"card-list-item"},[t("h2",[e._v(e._s(e.getModuleName(i)))]),t("p",[e._v(e._s(e.$t("view.designer.rule.includeFields"))+"：")]),t("p",{staticClass:"field-list"},[e._v(e._s(e.getModuleFields(i)))]),t("div",{staticClass:"button-group"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.preview(i)}}},[e._v(e._s(e.$t("common.base.preview")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submit(i)}}},[e._v(e._s(e.$t("common.base.add2")))])],1)])})),0),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},Yt=[],Xt={name:"card-create-dialog",props:{value:{type:Array,default:function(){return[]}}},data:function(){return{visible:!1,loading:!1,cardList:[]}},methods:{getModuleName:function(e){return Wt.A[e.module]},getModuleFields:function(e){var t=(null===e||void 0===e?void 0:e.cardFieldVoList)||[];return t.map((function(e){return e.displayName})).join("、")},open:function(){this.cardList=[],this.visible=!0,this.getcardList()},preview:function(e){this.$emit("preview",e.cardFieldVoList)},submit:function(e){if(e.checked)return this.$message.warning(this.$t("view.designer.rule.tip2"));this.$emit("create",e),this.visible=!1},getcardList:function(){var e=this;this.loading=!0,Vt.uq().then((function(t){var i=t.success,n=t.message,s=t.data,a=void 0===s?[]:s;i?e.cardList=a.map((function(t){var i=e.value.findIndex((function(e){return e.module==t.module}));return t.checked=i>-1,t})):e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("getcardList",e)}))}}},Zt=Xt,Gt=(0,y.A)(Zt,Kt,Yt,!1,null,"92441010",null),ei=Gt.exports,ti=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-preview-dialog",attrs:{title:e.$t("common.base.preview"),width:"480px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"form-design-main"},[t("div",{staticClass:"form-design-center"},[t("div",{staticClass:"form-design-phone"},[t("form-preview",{attrs:{value:e.fields}})],1)])]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},ii=[],ni={name:"card-preview-dialog",data:function(){return{visible:!1,fields:[]}},methods:{open:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.fields=e,this.visible=!0}}},si=ni,ai=(0,y.A)(si,ti,ii,!1,null,"d7ab7bf8",null),oi=ai.exports,ri={name:"card-setting",props:{templateId:{type:String,default:""}},data:function(){return{pending:!1,loading:!1,cardList:[]}},mounted:function(){this.initialize()},methods:{initialize:function(){var e=this;this.loading=!0,Vt.h_({formTemplateBizId:this.templateId}).then((function(t){var i=t.success,n=t.message,s=t.data,a=void 0===s?[]:s;i?e.cardList=a.map((function(t){return e.formatCardData(t)})):e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("satisfaction setting initialize",e)}))},formatCardData:function(e){var t=e.enable,i=void 0===t?1:t;return{bizId:e.bizId,module:e.module,cardFieldVoList:e.cardFieldVoList,enable:i}},openCreateDialog:function(){this.$refs.createDialog.open()},handleCreate:function(e){this.cardList.unshift(this.formatCardData(e))},handleDelete:function(e){this.cardList.splice(e,1)},handlePreview:function(e){this.$refs.previewDialog.open(e)},save:function(){var e=this;this.pending||(this.pending=!0,Vt.Zg({formTemplateBizId:this.templateId,cardSettingFormForms:this.cardList}).then((function(t){if(!t.success)return e.$message.error(t.message);e.$message.success(e.$t("common.base.saveSuccess")),e.cardList=(t.data||[]).map((function(t){return e.formatCardData(t)}))}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("save err",e)})))}},components:(0,o.A)((0,o.A)((0,o.A)({},Jt.name,Jt),ei.name,ei),oi.name,oi)},li=ri,ci=(0,y.A)(li,Bt,Mt,!1,null,"c1b8733c",null),di=ci.exports,ui=function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-setting"},[t("div",{staticClass:"card-setting-header"},[t("div",{staticClass:"card-setting-header-left"},[t("h2",[e._v(e._s(e.$t("view.designer.rule.menusName.componentsSetting")))]),t("p",[e._v(e._s(e.$t("view.designer.rule.tip1")))])]),t("div",{staticClass:"card-setting-header-right"},[e.isShowExternalComponentSetting?t("el-button",{attrs:{type:"primary"},on:{click:e.openCreateDialog}},[t("i",{staticClass:"iconfont icon-add2"}),e._v(e._s(e.$t("common.base.add2")))]):e._e()],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-setting-content"},[e._l(e.cardList,(function(i,n){return t("record-item",{key:i.id,attrs:{index:n,"event-card":i,refresh:e.initialize},on:{deleteCard:e.deleteCard,showPreview:e.onShowPreviewCardDialog,showEditRule:e.onShowEditRuleDialog,showEditRecord:e.onShowEditRecord,showEditPremiss:e.onShowEditPremissDialog}})})),e.loading||e.cardList.length?e._e():t("no-data-view")],2),t("card-rule-dialog",{ref:"cardRuleDialog",on:{submit:e.handeUpdateRule}}),t("create-dialog",{ref:"createDialog",on:{refreshData:e.initialize}}),t("connector-module-create-connector-dialog",{attrs:{title:e.$t("view.designer.rule.createAdditionalComponents"),visible:e.visibleConnectorModuleCreateConnectorDialog,"biz-type-id":e.templateId,"from-biz-type":e.ConnectorBizTypeEnum.Paas,"from-biz-type-name":e.formName},on:{close:e.onCloseCreateConnectorModal,finish:e.onFinishCreateConnectorModal}}),t("connector-module-edit-connector-dialog",{attrs:{title:e.$t("view.designer.rule.editAdditionalComponents"),"from-biz-type-name":e.formName,visible:e.visibleConnectorModuleEditConnectorDialog,"connector-info":e.connectorInfo},on:{close:e.onCloseEditConnectorModal,finish:e.onFinishCreateConnectorModal}})],1)},mi=[],fi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"task-card",attrs:{shadow:"hover"}},[t("el-row",{staticClass:"task-card-main",attrs:{type:"flex",justify:"space-between"}},[t("el-row",{attrs:{type:"flex"}},[t("el-row",{staticClass:"task-card-content",attrs:{type:"flex"}},[t("h2",{staticClass:"task-card-title"},[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.eventCard.name,disabled:!e.isCardNameOverflow,placement:"top"}},[t("p",{ref:"parentNameWidth",class:["task-card-name",0==e.eventCard.enabled&&"task-card-enabled"]},[t("span",{ref:"childNameWidth"},[e._v(e._s(e.eventCard.name))])])]),t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.$t("view.designer.rule.tip5"),placement:"top"}},[0==e.eventCard.enabled?t("span",{staticClass:"task-card-disable"},[e._v(e._s(e.$t("common.base.disabled")))]):e._e()])],1),t("el-row",{staticClass:"task-card-others"},[e.eventCard.isCommonTemplate&&"flow"!==e.formType?e._e():t("p",[e._v(" "+e._s(e.$t("common.form.preview.qualityField.title2"))+"： "),e.eventCard.notNullFlow&&e.eventCard.notNullFlow.length||e.eventCard.stateCanEdit&&e.eventCard.stateCanEdit.length>0||e.eventCard.requiredNodeId?t("span",[e._v(e._s(e.$t("common.base.haveSet")))]):t("span",[e._v(e._s(e.$t("common.base.notSet"))+" ")]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:function(t){return e.$emit("showEditRule",e.eventCard)}}})]),t("p",[e._v(" "+e._s(e.$t("view.designer.rule.numberOfAdditions"))+"： "),"single"==e.eventCard.inputType?t("span",[e._v(e._s(e.$t("common.base.onceTime")))]):"multiple"==e.eventCard.inputType?t("span",[e._v(e._s(e.$t("common.base.moreTime")))]):e._e()])]),t("el-row",{staticClass:"task-card-others"},[t("p",[e._v(" "+e._s(e.$t("common.base.usrPermissions"))+"： "),t("span",{staticClass:"link-text",on:{click:e.editAuth}},[e._v(e._s(e.$t("common.paas.view.designer.rule.viewRoleAuth"))+" ")]),t("i",{staticClass:"iconfont icon-bianji1 pointer",on:{click:e.editAuth}})])])],1)],1)],1),t("el-row",{staticClass:"task-card-opearte",attrs:{type:"flex"}},[t("span",{staticStyle:{color:"#999999","font-size":"12px",width:"50%","text-align":"center"}},[t("el-popover",{attrs:{placement:"bottom",trigger:"click","popper-class":"popper-defined",width:"40px"},scopedSlots:e._u([{key:"reference",fn:function(){return[t("span",{staticClass:"task-card-opearte-del",staticStyle:{cursor:"pointer"}},[t("i",{staticClass:"iconfont icon-MoreOutlined",staticStyle:{display:"inline-block",transform:"rotate(90deg)"}}),e._v(e._s(e.$t("common.base.moreOperator")))])]},proxy:!0}])},[t("el-dropdown-item",[t("div",{staticClass:"tsak-card-opearte-rename",on:{click:e.openRename}},[t("i",{staticClass:"iconfont icon-edit-square"}),e._v(e._s(e.$t("common.base.rename"))+" ")])]),t("el-dropdown-item",[t("div",{staticClass:"tsak-card-opearte-delete",on:{click:e.handleDeleteCard}},[t("i",{staticClass:"iconfont icon-delete"}),e._v(e._s(e.$t("common.base.delete"))+" ")])])],1)],1),t("div",{staticClass:"task-card-opearte-modify",on:{click:function(t){return e.$emit("showEditRecord",e.eventCard)}}},[t("i",{staticClass:"iconfont icon-edit-square"},[e._v(e._s(e.$t("common.base.edit")))])])]),t("el-dialog",{attrs:{title:e.$t("common.connector.editConnector"),visible:e.visible,width:"400px"},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{ref:"formRef",attrs:{model:e.form,rules:e.rules}},[t("el-form-item",{staticClass:"form-item-language",attrs:{label:e.$t("common.base.name"),prop:"connectorName"}},[t("el-input",{attrs:{value:e.form.connectorName,autocomplete:"off",placeholder:e.$t("common.placeholder.inputName")},on:{input:e.nameInput}}),e.isOpenMultiLanguage?t("p",{staticClass:"form-language-btn",on:{click:function(t){return e.handleShowChooseLanguage(e.form.connectorName,"connectorName","titleLanguage","input")}}},[t("i",{staticClass:"iconfont icon-earth"})]):e._e()],1),t("el-form-item",{staticClass:"form-item-language",attrs:{label:e.$t("common.base.explain")}},[t("el-input",{attrs:{value:e.form.connectorRemarks,autocomplete:"off",placeholder:e.$t("common.connector.fields.description.placeholder1"),maxlength:"500",type:"textarea"},on:{input:e.descriptionInput}}),e.isOpenMultiLanguage?t("p",{staticClass:"form-language-btn",on:{click:function(t){return e.handleShowChooseLanguage(e.form.connectorRemarks,"connectorRemarks","descLanguage","textarea")}}},[t("i",{staticClass:"iconfont icon-earth"})]):e._e()],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v(e._s(e.$t("common.base.confirm")))])],1)],1),t("base-modal",{staticClass:"card-create-dialog",attrs:{title:e.$t("common.paas.view.designer.rule.powerSetting.panelItemsTitle.4.main"),show:e.authModalvisible,width:"500px"},on:{"update:show":function(t){e.authModalvisible=t}}},[t("div",{staticClass:"pad-16"},[t("div",[t("div",{staticClass:"mar-b-8"},[e._v(" "+e._s(e.$t("common.paas.view.designer.rule.powerSetting.panelItemsTitle.1.main"))+" ")]),t("div",{staticClass:"auth-res-view-box",on:{click:function(t){return e.choseByDept(1)}}},[e._l(e.viewAuthInfoStash,(function(i,n){return[t("span",{key:n},[n>0&&e.viewAuthInfoStash.length>1?t("span",[e._v("，")]):e._e(),t("span",{key:n},[e.isOpenData&&i.dingId?[t("open-data",{attrs:{type:"departmentName",openid:i.dingId}})]:e._e(),e.isOpenData&&e.dept.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:t("span",[e._v(" "+e._s(i.name||i.displayName)+" ")])],2)])]})),e.viewAuthInfoStash.length?e._e():t("span",[e._v(e._s(e.$t("forPaas.setting.label1")))])],2)])]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"plain-third",disabled:e.pending},on:{click:function(t){e.authModalvisible=!1}}},[e._v(e._s(e.$t("common.base.close")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submitEditAuth}},[e._v(e._s(e.$t("common.base.save")))])],1)])],1)},pi=[],hi=(i(62830),i(64055)),vi=i(70072),gi=i(56582),bi=i(51618),wi={name:"record-item",props:{eventCard:{type:Object,default:function(){return{}}},index:{type:Number},refresh:{type:Function,default:function(){}}},computed:{formType:function(){return this.$route.query.formType},isOpenMultiLanguage:function(){return(0,hi.g9)()||!1},currentLocale:function(){return ue.Ay.locale}},data:function(){return{visible:!1,isCardNameOverflow:!1,form:{id:void 0,connectorName:"",connectorRemarks:"",titleLanguage:{},descLanguage:{}},rules:{connectorName:[{required:!0,message:(0,ue.t)("common.placeholder.inputName"),trigger:"blur"}]},authModalvisible:!1,isOpenData:gi.xM,pending:!1,viewAuthInfoStash:[],httpParams:null}},mounted:function(){this.initOverflow()},methods:{handleDeleteCard:function(){var e=this;this.$confirm(this.$t("common.base.tip.areYouWantDeleteIt",{data1:this.eventCard.name}),this.$t("common.base.toast"),{confirmButtonText:this.$t("common.base.makeSure"),cancelButtonText:this.$t("common.base.cancel"),type:"warning"}).then((function(){e.$emit("deleteCard",e.index,e.eventCard)}))},confirm:function(){var e=this;this.$refs.formRef.validate((function(t){t&&(0,Vt.EL)(e.form).then((function(t){t.success?(e.visible=!1,e.$message.success(e.$t("common.base.saveSuccess")),e.refresh()):e.$message.error(t.message||"")}))}))},openRename:function(){var e=this;(0,Vt.pM)(JSON.parse(this.eventCard.config).connectorInfo).then((function(t){t.success&&(e.form=t.data,e.visible=!0)}))},initOverflow:function(){var e=this;this.$nextTick((function(){var t,i;e.isCardNameOverflow=(null===(t=e.$refs)||void 0===t||null===(t=t.parentNameWidth)||void 0===t?void 0:t.offsetWidth)<(null===(i=e.$refs)||void 0===i||null===(i=i.childNameWidth)||void 0===i?void 0:i.offsetWidth)}))},handleShowChooseLanguage:function(e,t,i,n){var s=this,r=(this.$i18n.locale,null);r=Reflect.has(this.form,i)?(0,a.A)((0,a.A)({},this.form[i]),{},(0,o.A)({},this.currentLocale,e)):(0,a.A)((0,a.A)({},(0,hi.T2)()),{},(0,o.A)({},this.currentLocale,e)),this.$fast.languageSetting.show({title:this.$t("common.base.languageSetting"),type:n,field:{},languageDefaultValueObj:r}).then((function(n){e!==n[ue.Ay.locale]&&s.$set(s.form,t,n[ue.Ay.locale]),s.$set(s.form,i,n)}))},nameInput:function(e){this.form.connectorName=e,this.$set(this.form.titleLanguage,ue.Ay.locale,e)},descriptionInput:function(e){this.form.connectorRemarks=e,this.$set(this.form.descLanguage,ue.Ay.locale,e)},editAuth:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){var i,n,s,a,o;return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,bi.kn)({paasCardBizId:e.eventCard.bizId});case 2:if(i=t.sent,0===i.code){for(o in s=(null===(n=i.result)||void 0===n||null===(n=n[0])||void 0===n?void 0:n.paasCardAuthRangeTypeVO)||{},a=[],s)s[o]||[],a=[].concat((0,G.A)(a),(0,G.A)(s[o]));e.viewAuthInfoStash=a}e.authModalvisible=!0;case 5:case"end":return t.stop()}}),t)})))()},submitEditAuth:function(){var e=this;if(!this.httpParams)return this.authModalvisible=!1;this.pending=!0,(0,bi.u4)({paasCardBizId:this.eventCard.bizId,paasCardAuthRangeTypeForm:this.httpParams,authType:0}).then((function(t){0===t.code?(e.$message.success(e.$t("common.base.saveSuccess")),e.authModalvisible=!1):e.$message.error(t.message||"")}))["finally"]((function(){setTimeout((function(){e.pending=!1}),500)}))},choseByDept:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function e(){var i,n;return(0,S.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=(0,vi.getRootWindow)(window),n={title:(0,ue.t)("common.base.pleaseSelect"),selectedAll:t.viewAuthInfoStash,max:-1,isTag:!0,showSpAll:!0,mode:"filter",isFromGeelyMenuSet:!0},i.$fast.select.multi.all(n).then((function(e){var i=t;if(0===e.status){var n=e.data||{},s=n.all,a=n.deptIds,o=n.roleIds,r=n.serviceProviderIds,l=n.userIds,c=n.tags,d=n.roles,u=n.providers,m=n.users;console.log(n);var f={};f.tagIds=a||c&&c.map((function(e){return e.id})),f.roleIds=o||d&&d.map((function(e){return e.id})),f.tenantProviderIds=r||u&&u.map((function(e){return e.id})),f.userIds=s&&s.filter((function(e){return e.userId})).map((function(e){return e.userId}))||l||m&&m.map((function(e){return e.userId})),t.httpParams=f,i.viewAuthInfoStash=s}}));case 3:case"end":return e.stop()}}),e)})))()}}},Ci=wi,yi=(0,y.A)(Ci,fi,pi,!1,null,"077059d4",null),_i=yi.exports,Ai=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-rule-dialog",attrs:{title:e.$t("view.designer.rule.setUseRule"),width:"420px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"card-rule-form"},[t("el-form",{ref:"form",attrs:{"label-position":"top",model:e.formValue}},[t("el-form-item",{attrs:{label:e.$t("view.designer.rule.tip4"),prop:"requiredNodeId",rules:[{required:!1,message:e.$t("common.placeholder.selectNode"),trigger:"change"}]}},[t("el-select",{staticClass:"card-rule-form__select",attrs:{placeholder:e.$t("common.placeholder.selectNode"),clearable:""},model:{value:e.formValue.requiredNodeId,callback:function(t){e.$set(e.formValue,"requiredNodeId",t)},expression:"formValue.requiredNodeId"}},e._l(e.filterFlowCells,(function(e){return t("el-option",{key:e.id,attrs:{label:e.data.name,value:e.id}})})),1)],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},$i=[],ki={name:"card-rule-dialog",data:function(){return{loading:!1,visible:!1,filterFlowCells:[],formValue:{requiredNodeId:""}}},computed:{appId:function(){return this.$route.query.appId},formTemplateId:function(){return this.$route.query.formId},supportCells:function(){return[Et.A.PROCEDD_NODE,Et.A.APPROVE_NODE]}},methods:{handleOpenModal:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n,s,a,o,r,l,c,d,u,m,f,p,h,v;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=e.bizId,s=void 0===n?"":n,a=e.requiredNodeId,o=void 0===a?"":a,r=t.appId,l=t.formTemplateId,t.formValue.bizId=s,i.next=5,de.getProcess({appId:r,formTemplateId:l});case 5:c=i.sent,d=c.data||{},u=d.attribute,m=JSON.parse(u||"{}"),f=m.cells,p=void 0===f?[]:f,p.length>0&&(h=p.filter((function(e){return t.supportCells.includes(e.shape)})),v=h.find((function(e){return e.id===o})),v&&(t.formValue.requiredNodeId=null===v||void 0===v?void 0:v.id),t.filterFlowCells=h.map((function(e){var i;return e.data.name=(null===e||void 0===e||null===(i=e.data)||void 0===i||null===(i=i.attribute)||void 0===i||null===(i=i.nameLanguage)||void 0===i?void 0:i[t.$i18n.locale])||e.data.name,e}))),t.visible=!0;case 11:case"end":return i.stop()}}),i)})))()},handleHideModal:function(){var e=this;return(0,x.A)((0,S.A)().mark((function t(){return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.visible=!1;case 1:case"end":return t.stop()}}),t)})))()},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,x.A)((0,S.A)().mark((function t(i){return(0,S.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i&&e.$emit("submit",e.formValue);case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},Si=ki,xi=(0,y.A)(Si,Ai,$i,!1,null,"5a25e04e",null),Ii=xi.exports,Ti=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-create-dialog connector-card-create-dialog",class:e.baseModalClassNames,attrs:{title:e.$t("view.designer.rule.createAdditionalComponents"),width:"640px",show:e.visible},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"create-form"},[e.visible?t("iframe",{ref:"createIframe",staticClass:"create-iframe",attrs:{loading:"lazy",src:"/shb/home/<USER>/connector/create/dialog"}}):e._e()]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},Li=[],Ni={name:"create-dialog",data:function(){return{loading:!1,visible:!1,isEdit:!1,connector:{}}},computed:{baseModalClassNames:function(){return{"connector-card-edit-dialog":this.isEdit}}},mounted:function(){this.loading=!0,this.listenedMessage()},methods:{listenedMessage:function(){var e=this,t=this;window.addEventListener("message",(function(i){var n=i.data,s=document.getElementsByTagName("iframe")[0],a={close:function(){e.visible=!1},next:function(){t.$nextTick((function(){var e=document.body.clientHeight||document.documentElement.clientHeight;s.setAttribute("height",e-760<=100?e-200:760)}))},prev:function(){t.$nextTick((function(){s.setAttribute("height",339)}))},finish:function(){t.$message.success(e.$t("common.base.submitSuccess")),t.$emit("refreshData")},ready:function(){t.iframeOnload(),setTimeout((function(){t.loading=!1}),300)}};Object.keys(a).forEach((function(e){n[e]&&a[e]()}))}))},handleOpenModal:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.visible=!0,this.connector=i,this.isEdit=n,this.$nextTick((function(){if(t.loading=!0,e){var i=document.getElementsByTagName("iframe")[0];i.setAttribute("src",e),i.contentWindow.location.reload()}}))},reloadIframes:function(){for(var e=document.getElementsByTagName("iframe"),t=0,i=e.length;t<i;++t)e[t].contentWindow.document.location.reload()},iframeOnload:function(e){for(var t=this,i=document.getElementsByTagName("iframe"),n=document.body.clientHeight||document.documentElement.clientHeight,s=0,a=i.length;s<a;++s)(function(s){i[s].onload=function(){this.style.visibility="hidden",this.setAttribute("height","auto"),setTimeout((function(){var a,o=i[s].contentWindow.document.getElementsByClassName("base-modal-mask");o.forEach((function(e){e.style.background="transparent",e.childNodes[0].style.height="100%"}));var r=null===(a=i[s].contentWindow.document.getElementsByClassName("form-builder")[0])||void 0===a?void 0:a.scrollHeight;i[s].setAttribute("height",r?r+120:n-760<=100?n-200:760),i[s].style.visibility="visible","function"===typeof e&&e(),t.loading=!1}),0)}})(s)}}},Fi=Ni,zi=(0,y.A)(Fi,Ti,Li,!1,null,"1ebf5313",null),Ei=zi.exports,Oi=i(84943),Di=i(25202),Ri=i(27695),Pi=i(87),Bi={name:"external-component-setting",props:{templateId:{type:String,default:""},formName:{type:String,default:""}},components:(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},Ii.name,Ii),_i.name,_i),Ei.name,Ei),Di.j0.name,Di.j0),Di.LY.name,Di.LY),data:function(){return{pending:!1,loading:!1,cardList:[],visibleConnectorModuleCreateConnectorDialog:!1,visibleConnectorModuleEditConnectorDialog:!1,ConnectorBizTypeEnum:Oi.U,connectorInfo:{}}},computed:{isShowExternalComponentSetting:function(){var e,t=(0,Pi.zO)(window);return(null===t||void 0===t||null===(e=t.grayAuth)||void 0===e?void 0:e.linkCard)||!1}},mounted:function(){this.initialize()},methods:{save:function(){this.$message.success("保存成功")},handeUpdateRule:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,Vt.Y7((0,a.A)({},e));case 2:n=i.sent,null!==n&&void 0!==n&&n.success?(t.$message.success(t.$t("common.base.submitSuccess")),t.$refs.cardRuleDialog.handleHideModal(),t.initialize()):t.$message.error(n.message);case 4:case"end":return i.stop()}}),i)})))()},deleteCard:function(e,t){var i=this;return(0,x.A)((0,S.A)().mark((function e(){var n;return(0,S.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Vt.p({cardBizId:t.bizId});case 3:n=e.sent,n.success?(i.$message.success(i.$t("common.base.deleteSuccess")),i.initialize()):i.$message.error(n.message),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),console.log("[fetch deleteConnectorDataFromPaasItem error]",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},onShowPreviewCardDialog:function(){},onShowEditRuleDialog:function(e){this.$refs.cardRuleDialog.handleOpenModal(e)},onShowEditPremissDialog:function(e){var t=e.appId,i=void 0===t?"":t,n=e.targetFormTemplateId,s=void 0===n?"":n,a=e.appName,o=void 0===a?"":a,r="/paas/#/designer/rule?appId=".concat(i,"&formId=").concat(s,"&formType=flow&activeTab=1");this.$platform.openTab({id:"frame_tab_".concat(s),title:o,close:!0,reload:!0,url:r})},onShowEditRecord:function(e){var t=this;return(0,x.A)((0,S.A)().mark((function i(){var n;return(0,S.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:n=(0,Ri.z)(e),t.connectorInfo=(null===n||void 0===n?void 0:n.connectorInfo)||{},t.openConnectorEditDialog();case 3:case"end":return i.stop()}}),i)})))()},initialize:function(){var e=this;this.loading=!0,Vt.HV({formTemplateId:this.templateId,source:0}).then((function(t){var i=t.success,n=t.message,s=t.data,a=void 0===s?[]:s;i?e.cardList=a:e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("satisfaction setting initialize",e)}))},openCreateDialog:function(){this.openConnectorCreateDialog()},openConnectorCreateDialog:function(){this.visibleConnectorModuleCreateConnectorDialog=!0},onCloseCreateConnectorModal:function(){this.visibleConnectorModuleCreateConnectorDialog=!1},openConnectorEditDialog:function(){this.visibleConnectorModuleEditConnectorDialog=!0},onCloseEditConnectorModal:function(){this.visibleConnectorModuleEditConnectorDialog=!1},onFinishCreateConnectorModal:function(){this.onCloseCreateConnectorModal(),this.initialize()},handleDelete:function(e){this.cardList.splice(e,1)}}},Mi=Bi,Vi=(0,y.A)(Mi,ui,mi,!1,null,"22d9ae12",null),Ui=Vi.exports,qi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"tag-setting"},[t("div",{staticClass:"tag-setting-header"},[t("div",{staticClass:"tag-setting-header-left"},[t("h2",[e._v(e._s(e.$t("view.designer.rule.menusName.tagSetSetting")))]),t("p",[e._v(e._s(e.$t("view.designer.rule.tagSetting.tagSetMsg")))])]),t("div",{staticClass:"tag-setting-header-right"},[t("el-switch",{attrs:{"active-text":e.switchState?e.$t("buttons.enable"):e.$t("buttons.disable"),"active-value":1,"inactive-value":0},on:{change:e.changeSwitchHandler},model:{value:e.switchState,callback:function(t){e.switchState=t},expression:"switchState"}})],1)]),e.switchState?t("div",{staticClass:"tag-setting-item"},[t("base-tags",{attrs:{value:e.tagsList,type:"text"}}),t("el-button",{staticClass:"tag-setting-item-btn",attrs:{type:"primary"},on:{click:e.edit}},[e._v(e._s(e.$t("buttons.edit")))])],1):e._e(),t("tag-edit-dialog",{attrs:{value:e.tagsList,visible:e.visible,"template-biz-id":e.templateBizId,"app-id":e.appId},on:{cancel:e.cancel,submit:e.submit}})],1)},Wi=[],ji=i(17319),Hi=i.n(ji),Qi=i(48649),Ji=i(38602),Ki=i.n(Ji),Yi=i(34987),Xi=i(3402),Zi=(0,E.A)((function e(){(0,O.A)(this,e),(0,o.A)(this,"tagName",""),(0,o.A)(this,"sort",null),(0,o.A)(this,"id",""),(0,o.A)(this,"tagId",""),(0,o.A)(this,"active",!1),(0,o.A)(this,"deleteFlag",!1)})),Gi=(0,Qi.defineComponent)({name:"tag-edit-dialog",components:{draggable:Ki()},props:{value:{type:Array,default:function(){return[]}},visible:{type:Boolean,default:function(){return!1}},templateBizId:{type:String,default:""},appId:{type:String,default:""}},setup:function(e,t){var i=t.emit,n=(0,Qi.toRefs)(e),s=n.visible,a=n.value,r=n.templateBizId,l=n.appId,c=(0,Qi.ref)(!1),d=(0,Qi.ref)([]),u=(0,Qi.ref)([]);(0,Qi.watch)([s,a],(function(){s.value&&(d.value=(0,Ct.cloneDeep)(a.value)),u.value=[]}));var m=function(){i("cancel")},f=function(){var e=d.value.every((function(e){return Boolean(e.tagName)}));if(!e)return gi.Ay.alert(ue.Ru.t("view.designer.rule.tagSetting.addTip"));b()},p=function(e){d.value=e},h=function(e,t){d.value[t].tagName=e.trim()},v=function(e,t){t&&u.value.push(d.value[e]),d.value.splice(e,1)},g=function(){d.value.push(new Zi)},b=function(){u.value.map((function(e){e.deleteFlag=!0}));var e=[].concat((0,G.A)(d.value),(0,G.A)(u.value));c.value=!0;var t=e.map((function(e,t){var i=e.tagName,n=e.tagId,s=e.deleteFlag,a=void 0!==s&&s;return{tagName:i,tagId:n,sort:t+1,deleteFlag:a,formTemplateId:r.value,appId:l.value}}));(0,Xi.C2)(t).then((function(e){null!==e&&void 0!==e&&e.success&&Yi.A.success(ue.Ru.t("common.base.saveSuccess")),i("submit")}))["catch"]((function(e){console.log("err",e)}))["finally"]((function(){c.value=!1}))},w=function(e){return(0,Qi.h)("div",[(0,Qi.h)(Ki(),{attrs:{animation:"180",tag:"div",value:e},class:"tag-setting-group",on:{input:function(e){return p(e)}}},[e.map((function(e,t){return(0,Qi.h)("div",{class:"tag-setting-item"},[(0,Qi.h)("i",{class:"iconfont icon-tuozhuaipaixu drag-icon"}),(0,Qi.h)("el-input",{attrs:{placeholder:ue.Ru.t("view.designer.rule.tagSetting.addPlaceholder"),clearable:!0,maxlength:"10",value:e.tagName},on:{input:function(e){return h(e,t)}}}),(0,Qi.h)("i",{class:"iconfont icon-delete delete-icon",on:{click:function(){return v(t,e.tagId)}}})])}))]),(0,Qi.h)("el-button",{class:"tag-setting-add-button",attrs:{type:"ghost"},on:{click:function(){return g()}}},[(0,Qi.h)("i",{class:"iconfont icon-add2"}),ue.Ru.t("view.designer.rule.tagSetting.addTag")])])};return function(){return(0,Qi.h)("base-modal",Hi()([{class:"tag-setting-modal",attrs:{title:ue.Ru.t("view.designer.rule.menusName.tagSetSetting"),show:s.value}},{on:(0,o.A)({},"update:show",(function(e){return m()}))},{attrs:{width:"775px"}}]),[w(d.value),(0,Qi.h)("div",{slot:"footer",class:"dialog-footer"},[(0,Qi.h)("span",{class:"tag-select-tips"},[ue.Ru.t("view.designer.rule.tagSetting.selectTagTip2")]),(0,Qi.h)("el-button",{attrs:{disabled:c.value,type:"ghost"},on:{click:m}},[" ",ue.Ru.t("buttons.cancel")]),(0,Qi.h)("el-button",{attrs:{disabled:c.value,type:"primary"},on:{click:f}},[ue.Ru.t("buttons.save")])])])}}}),en={name:"tag-setting",components:(0,o.A)({},Gi.name,Gi),data:function(){return{tagsList:[],visible:!1,switchState:0}},computed:{templateBizId:function(){return this.$route.query.formId},appId:function(){return this.$route.query.appId}},mounted:function(){this.getselectTagSwitchState(),this.getSelectListReq()},methods:{save:function(){this.$message.success(this.$t("common.base.saveSuccess"))},changeSwitchHandler:function(){var e=this,t={appId:this.appId,formTemplateId:this.templateBizId,switchState:this.switchState};(0,Xi.Y8)(t).then((function(t){t.code;var i=t.success;i||(e.switchState=0)}))["catch"]((function(t){e.switchState=0,console.log("err",t)}))},edit:function(){this.visible=!0},cancel:function(){this.visible=!1},submit:function(){this.getSelectListReq(),this.cancel()},getSelectListReq:function(){var e=this,t={appId:this.appId,formTemplateId:this.templateBizId};(0,Xi.TO)(t).then((function(t){t.code;var i=t.data,n=void 0===i?[]:i,s=t.success;s&&(e.tagsList=n)}))["catch"]((function(e){console.log("err",e)}))},getselectTagSwitchState:function(){var e=this,t={appId:this.appId,formTemplateId:this.templateBizId};(0,Xi.WE)(t).then((function(t){t.code;var i=t.success,n=t.data;i&&(e.switchState=(null===n||void 0===n?void 0:n.switchState)||0)}))["catch"]((function(e){console.log("err",e)}))}}},tn=en,nn=(0,y.A)(tn,qi,Wi,!1,null,"96368e30",null),sn=nn.exports,an=i(50651),on={components:(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},Y.name,Y),we.name,we),Ke.name,Ke),Pt.name,Pt),di.name,di),Ui.name,Ui),sn.name,sn),provide:function(){return{mode:"permission"}},data:function(){return{active:0}},created:function(){var e=this.$route.query.activeTab;e&&(this.active=Number(e))},computed:(0,a.A)((0,a.A)({},(0,an.aH)(["user"])),{},{isShowTagSetting:function(){return this.user.grayAuth.includes("PAAS_LABEL")||!1},appId:function(){return this.$route.query.appId},templateId:function(){return this.$route.query.formId},formName:function(){return this.$route.query.formName},isFlowForm:function(){return"flow"==this.$route.query.formType},menus:function(){return[{icon:"icon-share",name:this.$t("view.designer.rule.menusName.formOutsideLink"),component:Y.name,show:!0},{icon:"icon-lock",name:this.$t("view.designer.rule.menusName.authoritySetting"),component:we.name,show:!0},{icon:"icon-message1",name:this.$t("view.designer.rule.menusName.messageSetting"),link:"/shb/home/<USER>/message?appId=".concat(this.appId,"&formId=").concat(this.templateId),component:"",show:!0},{icon:"icon-pingjiati",name:this.$t("view.designer.rule.menusName.satisfactionSetting"),component:Pt.name,show:this.isFlowForm},{icon:"icon-printer",name:this.$t("view.designer.rule.menusName.printSetting"),component:Ke.name,show:!0},{icon:"icon-add3",name:this.$t("view.designer.rule.menusName.componentsSetting"),component:di.name,show:!1},{icon:"icon-fujiazujian-setting",name:this.$t("view.template.detail.additionalComponents"),component:Ui.name,show:!0}].filter((function(e){return e.show}))}}),methods:{handleTableClick:function(e,t){if(e.link)return this.$platform.openTab({id:"MESSAGE_DD_MESSAGE_NEW",title:this.$t("view.designer.rule.menusName.messageSetting"),close:!0,reload:!0,url:e.link});this.$router.push({path:this.$route.path,query:(0,a.A)((0,a.A)({},this.$route.query),{},{activeTab:t})}),this.active=t},save:function(){var e=this;return new Promise((function(t,i){var n=e.$refs.component.save;n&&n(),t()}))}}},rn=on,ln=(0,y.A)(rn,n,s,!1,null,"4308a8de",null),cn=ln.exports,dn=cn}}]);