"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[445],{9097:function(e,t,a){e.exports=a.p+"img/paas.49f04687.png"},9811:function(e,t,a){a.r(t),a.d(t,{default:function(){return N}});a(33656);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"setting-manager-container"},[t("div",{staticClass:"manager-header"},[t("el-row",{staticClass:"manager-header-left",attrs:{type:"flex"}},[t("p",{staticClass:"return-btn",on:{click:e.goBack}},[t("i",{staticClass:"iconfont icon-arrow-left"}),e._v(" "+e._s(e.$t("common.base.back"))+" ")]),t("div",[t("el-row",{staticStyle:{"max-width":"160px"},attrs:{type:"flex"}},[t("el-popover",{ref:"iconPopover",attrs:{placement:"bottom-start","popper-class":"paas-app-popover",width:"290",offset:-10}},[t("div",{staticClass:"choose-icon-box"},e._l(e.appIcons,(function(a){return t("div",{key:a,staticClass:"icon-item",on:{click:function(t){return e.onChangeIcon(a)}}},[t("i",{class:"iconfont ".concat(a)})])})),0)]),t("el-popover",{ref:"reAppNameRef",attrs:{placement:"bottom-end",width:"160",offset:10,"popper-class":"paas-app-popover"}},[t("div",[t("el-input",{directives:[{name:"input-filter",rawName:"v-input-filter:special-letter",value:e.reAppNamePrevVal,expression:"reAppNamePrevVal",arg:"special-letter"}],attrs:{size:"mini"},model:{value:e.reAppNamePrevVal,callback:function(t){e.reAppNamePrevVal=t},expression:"reAppNamePrevVal"}}),t("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[t("el-button",{attrs:{size:"mini",type:"text"},on:{click:e.handleCancelAppRename}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handleConfirmAppName}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)],1)]),t("i",{directives:[{name:"popover",rawName:"v-popover",value:"iconPopover",expression:"'iconPopover'"}],class:"iconfont ".concat(e.appIcon," app-icon n-lick"),on:{click:function(e){e.preventDefault()}}}),t("span",{directives:[{name:"popover",rawName:"v-popover",value:"reAppNameRef",expression:"'reAppNameRef'"}],staticClass:"app-name n-lick"},[e._v(e._s(e.appName))]),t("el-popover",{attrs:{placement:"bottom","popper-class":"pass-manager-main-popover",trigger:"hover"}},[t("ul",{staticClass:"popover-list"},e._l(e.mainPopoverList,(function(a,n){return t("li",{key:n,staticClass:"popover-list-item",attrs:{"data-type":a.type},on:{click:function(t){return e.popoverItemClick(t,n,"app")}}},[e._v(" "+e._s(a.name)+" "),e.isOpenMultiLanguage&&"rename"===a.type?t("i",{staticClass:"iconfont icon-earth",attrs:{slot:"suffix"},on:{click:function(t){return t.stopPropagation(),e.popoverItemClick(t,n,"app")}},slot:"suffix"}):e._e()])})),0),t("i",{staticClass:"iconfont icon-setting pointer",attrs:{slot:"reference"},slot:"reference"})])],1)],1)]),t("div",{staticClass:"manager-header-right"},[t("el-button",{staticClass:"header-save-btn",attrs:{plain:""},on:{click:e.save}},[e._v(e._s(e.$t("common.base.save")))])],1)],1),t("div",{staticClass:"manager-content"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"manager-main"},[e.fromTemplates.length>0?t("div",{staticClass:"manager-left"},[t("div",{staticClass:"title text-center"},[e._v(e._s(e.$t("view.manager.applicationForm")))]),t("base-nest-draggable",{attrs:{lists:e.fromTemplates,"list-key":"displayName"},on:{sortListDragEndEventHandler:e.sortListDragEndEventHandler}},e._l(e.fromTemplates,(function(a,n){return t("div",{key:a.bizId,staticClass:"list-item",attrs:{slot:"item"},on:{mouseenter:function(t){return e.enterMLeftItem(n)},mouseleave:function(t){return e.leaveMLeftItem(n)},click:function(e){e.stopPropagation()}},slot:"item"},[t("i",{staticClass:"iconfont icon-tuozhuaipaixu"}),t("el-popover",{ref:"mLeftNamePopoverRef"+n,refInFor:!0,attrs:{placement:"bottom-end",width:"160",offset:10}},[t("div",[t("el-input",{directives:[{name:"input-filter",rawName:"v-input-filter:special-letter",value:e.reNamePrevVal,expression:"reNamePrevVal",arg:"special-letter"}],attrs:{size:"mini"},model:{value:e.reNamePrevVal,callback:function(t){e.reNamePrevVal="string"===typeof t?t.trim():t},expression:"reNamePrevVal"}}),t("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[t("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(t){return e.handleCancelRenamePopover(n)}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handleFormItemRename(n,a)}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)],1)]),t("span",{staticClass:"name",on:{click:function(t){return e.gotoDesigne(a.bizId,a.isCommonTemplate,a)}}},[e._v(e._s(a.displayName))]),t("div",{directives:[{name:"popover",rawName:"v-popover",value:"mLeftNamePopoverRef".concat(n),expression:"`mLeftNamePopoverRef${index}`"}],staticClass:"temp-icon-block"}),t("el-popover",{ref:"mLeftItemPopoverRef"+n,refInFor:!0,attrs:{placement:"bottom",trigger:"manual","popper-class":"pass-manager-main-popover"},model:{value:a.isShowSetting,callback:function(t){e.$set(a,"isShowSetting",t)},expression:"item.isShowSetting"}},[t("ul",{staticClass:"popover-list",on:{mouseenter:function(t){return e.popoverListEnter(n)}}},e._l(e.popoverListSift,(function(r,i){return t("li",{key:i,staticClass:"popover-list-item",attrs:{"data-type":r.type,"data-value":a.displayName,"data-formid":a.bizId,"data-formtype":a.isCommonTemplate},on:{click:function(t){return e.popoverItemClick(t,n,"item")}}},[[e._v(e._s(r.name))],e.isOpenMultiLanguage&&"rename"===r.type?t("i",{staticClass:"iconfont icon-earth",attrs:{slot:"suffix"},on:{click:function(t){return t.stopPropagation(),e.popoverItemClick(t,n,"item")}},slot:"suffix"}):e._e()],2)})),0),t("i",{directives:[{name:"show",rawName:"v-show",value:e.mLeftItemHoverIdx===n,expression:"mLeftItemHoverIdx === index"}],staticClass:"iconfont icon-setting pointer",attrs:{slot:"reference"},on:{click:function(t){return t.stopPropagation(),e.handleShowSettingListPopover(n)}},slot:"reference"})])],1)})),0)],1):e._e(),t("div",{staticClass:"manager-right"},[e.newCreateGray?t("div",{staticClass:"title pl_20"},[e._v(e._s(e.$t("view.manager.title1")))]):e._e(),e.newCreateGray?t("div",{staticClass:"content"},[0==e.fromTemplates.length?t("h2",[e._v(e._s(e.$t("view.manager.title2")))]):e._e(),t("div",{staticClass:"add-app-box"},[t("div",{staticClass:"plain-form"},[t("img",{attrs:{src:e.templateImg.PlainFormImg,alt:e.$t("view.manager.title3")}}),t("div",{staticClass:"plain-form-operate"},[t("div",{staticClass:"plain-form-title"},[t("h3",[e._v(e._s(e.$t("common.otherPageTitle.createNewForm")))]),t("p",[e._v(e._s(e.$t("view.manager.title4")))])]),t("el-button",{staticClass:"mt_12",attrs:{type:"primary"},on:{click:function(t){return e.createForm("plain")}}},[e._v(" "+e._s(e.$t("common.base.create"))+" ")])],1)]),t("div",{staticClass:"flow-form"},[t("img",{attrs:{src:e.templateImg.FlowFormImg,alt:"流程表单示例图"}}),t("div",{staticClass:"flow-form-operate"},[t("div",{staticClass:"plain-form-title"},[t("h3",[e._v(e._s(e.$t("view.manager.createFlowForm")))]),t("p",[e._v(e._s(e.$t("view.manager.title5")))])]),t("el-button",{staticClass:"mt_12",attrs:{type:"primary"},on:{click:function(t){return e.createForm("flow")}}},[e._v(" "+e._s(e.$t("common.base.create"))+" ")])],1)])])]):t("div",{staticClass:"no-data"},[t("div",{staticClass:"no-data-content"},[e._m(0),t("p",[e._v(e._s(e.$t("forPaas.generate")))]),t("div",{staticClass:"no-data-content-more"},[t("span",[e._v(e._s(e.$t("forPaas.moreNews")))]),t("a",{attrs:{href:"https://doc.shb.ltd/shb_xoqazk/vbsrn64lhitx6kwv.html",target:"_blank"}},[e._v(e._s(e.$t("forPaas.clickHere")))])])])])])])]),t("del-app-modal-confirm",{ref:"delAppModalConfirmRef",on:{goBack:e.goBack}})],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"no-data-content-pic"},[t("img",{attrs:{src:a(9097),alt:""}})])}],i=a(18885),o=a(42881),s=a(71357),p=(a(67880),a(87313),a(2286),a(44807),a(80793),a(36700),a(48152),a(35256),a(21484),a(13560),a(94e3),a(16961),a(14126),a(54615),a(7354),a(32807),a(55650),a(75069),a(28244),a(62830),a(42925),a(944),a(21633),a.p+"img/plain-form.9b8db0ff.png"),c=a.p+"img/flow-form.f05f3864.png",m=a(74526),l=function(){var e=this,t=e._self._c;return t("el-dialog",{staticClass:"del-apply-dialog",attrs:{visible:e.visible,"show-close":!1,width:"480px"}},[t("div",{staticClass:"del-apply-box"},[t("div",{staticClass:"txt-box"},[t("i",{staticClass:"el-icon-warning"}),t("div",{staticClass:"main-txt"},[t("h1",[e._v(e._s(e.$t("view.manager.tip5",{name:e.params.name})))]),t("h2",[e._v(" "+e._s(e.$t("view.manager.totalCount",{count:e.itemLength}))),t("br"),e._v(" "+e._s(e.$t("view.manager.tip6"))),t("br"),e._v(" "+e._s(e.$t("view.manager.tip7",{name:e.params.type}))+" ")])])]),t("el-form",{ref:"delConfirmForm",staticClass:"paas-del-validate-form",attrs:{model:e.formData,rules:e.formDataRule}},[t("el-form-item",{attrs:{prop:"appName"}},[t("el-input",{staticClass:"input",attrs:{autocomplete:"off"},model:{value:e.formData.appName,callback:function(t){e.$set(e.formData,"appName",t)},expression:"formData.appName"}})],1)],1)],1),t("div",{staticClass:"dialog-footer-del",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{props:""},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},f=[],u=(a(7130),a(98316),a(84859)),d={name:"del-custom-apply",data:function(){var e=this,t=function(t,a,n){""===a?n(new Error(u.Ay.t("view.manager.tip8",{name:e.params.type}))):a!==e.params.name?n(new Error(u.Ay.t("view.manager.tip9"))):n()};return{visible:!1,itemLength:0,params:{},formData:{appName:""},formDataRule:{appName:[{validator:t,trigger:"blur"}]}}},methods:{open:function(e){var t=this;return(0,o.A)((0,i.A)().mark((function a(){var n,r,o;return(0,i.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.formData.appName="",n=e.appId,r=e.bizId,o=e.type,t.params=e,"应用"!==o&&o!==t.$t("common.base.application")){a.next=8;break}return a.next=6,t.checkAppState(n);case 6:a.next=10;break;case 8:return a.next=10,t.checkIsFormFiledState(r);case 10:t.$nextTick((function(){var e;null===(e=t.$refs["delConfirmForm"])||void 0===e||e.clearValidate()}));case 11:case"end":return a.stop()}}),a)})))()},onClose:function(){this.visible=!1},submit:function(){var e=this;this.$refs["delConfirmForm"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.params.type===e.$t("common.base.application")?e.fetchDelApp(e.params):e.fetchDelForm(e.params)}))},checkAppState:function(e){var t=this;return(0,o.A)((0,i.A)().mark((function a(){var n;return(0,i.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,m.A7({appId:e})["catch"]((function(e){console.error(e),t.$message.error(t.$t("view.manager.errorOccurred"))}));case 2:if(n=a.sent,"undefined"===typeof n||!n.success){a.next=11;break}if(0!==n.data){a.next=9;break}return a.next=7,t.fetchDelApp(t.params);case 7:a.next=11;break;case 9:t.itemLength=n.data,t.visible=!0;case 11:case"end":return a.stop()}}),a)})))()},fetchDelApp:function(e){var t=this;return(0,o.A)((0,i.A)().mark((function a(){var n,r;return(0,i.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return n=e.appId,a.prev=1,a.next=4,m.A5({appId:n});case 4:r=a.sent,r.success&&(t.$message({type:"success",message:"".concat(t.$t("common.base.deleteSuccess"),"!")}),setTimeout((function(){t.visible=!1,t.$emit("goBack")}),500)),a.next=12;break;case 8:a.prev=8,a.t0=a["catch"](1),console.error(a.t0),t.$message.error(t.$t("view.manager.errorOccurred"));case 12:case"end":return a.stop()}}),a,null,[[1,8]])})))()},checkIsFormFiledState:function(e){var t=this;return(0,o.A)((0,i.A)().mark((function a(){var n;return(0,i.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,m.Qr({templateBizId:e})["catch"]((function(e){console.error(e),t.$message.error(t.$t("view.manager.errorOccurred"))}));case 2:if(n=a.sent,"undefined"===typeof n||!n.success){a.next=11;break}if(0!==n.data){a.next=9;break}return a.next=7,t.fetchDelForm(t.params);case 7:a.next=11;break;case 9:t.itemLength=n.data,t.visible=!0;case 11:case"end":return a.stop()}}),a)})))()},fetchDelForm:function(e){var t=this;return(0,o.A)((0,i.A)().mark((function a(){var n,r;return(0,i.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return n=e.bizId,a.prev=1,a.next=4,m.UJ({templateBizId:n});case 4:r=a.sent,r.success&&(t.$message({type:"success",message:"".concat(t.$t("common.base.deleteSuccess"),"!")}),setTimeout((function(){t.visible=!1;var e=t.$parent,a=e.getAppInforn,n=e.appId;a({appId:n})}),500)),a.next=12;break;case 8:a.prev=8,a.t0=a["catch"](1),console.error(a.t0),t.$message.error(t.$t("view.manager.errorOccurred"));case 12:case"end":return a.stop()}}),a,null,[[1,8]])})))()}}},v=d,h=a(49100),g=(0,h.A)(v,l,f,!1,null,null,null),b=g.exports,w=a(64055),I=a(50651),y=a(88445),$={name:"manager",components:{DelAppModalConfirm:b},data:function(){return{nameLanguage:{},appIcons:["icon-creditcard-fill","icon-appstore-fill","icon-fund-fill1","icon-contacts-fill","icon-shopping-fill","icon-tags-fill","icon-shop-fill","icon-golden-fill","icon-trophy-fill","icon-car-fill","icon-piechart-circle-fil1","icon-container-fill","icon-batchfolding-fill","icon-idcard-fill","icon-detail-fill1","icon-reconciliation-fill","icon-file-text-fill"],loading:!0,appIcon:"icon-creditcard-fill",appId:"",appName:"",fromTemplates:[],mLeftItemHoverIdx:-1,reAppNamePrevVal:"",reNamePrevVal:"",showEditIcon:!1,mainPopoverList:[{name:u.Ay.t("common.base.rename"),type:"rename"},{name:u.Ay.t("view.manager.modifyIcon"),type:"icon"},{name:u.Ay.t("common.base.delete"),type:"delete"}],popoverList:[{name:u.Ay.t("common.otherPageTitle.editForm"),type:"edit"},{name:u.Ay.t("common.base.rename"),type:"rename"},{name:u.Ay.t("common.base.delete"),type:"delete"},{name:u.Ay.t("common.base.copy"),type:"copy"}]}},computed:(0,s.A)((0,s.A)({},(0,I.aH)(["user"])),{},{isOpenMultiLanguage:function(){return(0,w.g9)()||!1},popoverListSift:function(){return this.newCreateGray?this.popoverList:this.popoverList.filter((function(e){return"copy"!==e.type}))},templateImg:function(){return{PlainFormImg:p,FlowFormImg:c}},checkedIsShowReNamePopover:function(){var e=this.$refs;for(var t in e)if(t.includes("mLeftNamePopoverRef")&&Array.isArray(e[t])&&e[t][0].showPopper)return!0;return!1},newCanvasGray:function(){var e;return null===(e=this.user.grayAuth)||void 0===e?void 0:e.includes(y.l.NewFlowDesign)},newCreateGray:function(){var e;return null===(e=this.user.grayAuth)||void 0===e?void 0:e.includes(y.l.shbPaasCreates)}}),mounted:function(){var e=this.$route.query,t=e.appId,a=e.appName;this.appName=a||"",this.appId=t,this.getAppInforn({appId:this.appId}),this.clearMLeftItemHoverIdx()},beforeDestroy:function(){this.removeAddEventListener()},methods:{handleSettingFormNameLang:function(e){var t,a=this,n=void 0!==e?null===(t=this.fromTemplates[e])||void 0===t?void 0:t.displayNameLanguage:this.nameLanguage,r=void 0!==e?this.fromTemplates[e].displayName:this.appName,i=(0,w.Tc)()||this.$i18n.locale,o=(0,s.A)({},n);n&&Object.values(n).filter((function(e){return e})).length||(o[this.$i18n.locale]=r,o[i]=r),this.$fast.languageSetting.show({title:this.$t("common.base.languageSetting"),type:"input",languageDefaultValueObj:o}).then((function(t){void 0!==e?(a.fromTemplates[e].displayNameLanguage=t,a.fromTemplates[e].displayName=t[a.$i18n.locale],a.handleFormItemRename(e,a.fromTemplates[e])):(a.nameLanguage=t,a.appName=t[a.$i18n.locale])}))},handleShowSettingListPopover:function(e){this.fromTemplates=this.fromTemplates.map((function(t,a){return t.isShowSetting=e===a,t}))},sortListDragEndEventHandler:function(e){this.fromTemplates=e},handleDelForm:function(e,t){var a,n=this,r={name:e,type:this.$t("common.base.form"),bizId:t};null===(a=n.$refs.delAppModalConfirmRef)||void 0===a||a.open(r)},handleCopyForm:function(e,t){var a=this;return(0,o.A)((0,i.A)().mark((function e(){var n,r,o,s,p,c,l,f,u,d;return(0,i.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=a.fromTemplates.find((function(e){return e.bizId===t}))||{},r=n.displayName,o=void 0===r?"":r,s=n.displayNameLanguage,p=void 0===s?{}:s,c=[],Object.keys(p).map((function(e){if(p[e].length>95)switch(e){case"zh":c.push(a.$t("common.base.Chinese"));break;case"en":c.push(a.$t("common.base.English"));break;case"ko":c.push(a.$t("common.base.Korea"));break;case"ja":c.push(a.$t("common.base.Japanese"));break}})),!(c.length||o.length>95)){e.next=5;break}return e.abrupt("return",a.$message.error(a.$t("view.manager.copyFormNameLength",{language:c.join("、")})));case 5:return e.prev=5,l={formTemplateId:t},e.next=9,m.gQ(l);case 9:f=e.sent,u=f.code,f.data,d=f.message,0===u?(a.$message.success(a.$t("view.manager.copyForm")),a.getAppInforn({appId:a.appId})):a.$message.warning(d),e.next=17;break;case 14:e.prev=14,e.t0=e["catch"](5),console.log(e.t0);case 17:case"end":return e.stop()}}),e,null,[[5,14]])})))()},handleDelApp:function(){var e,t=this,a={name:this.appName,type:this.$t("common.base.application"),appId:this.appId};null===(e=t.$refs.delAppModalConfirmRef)||void 0===e||e.open(a)},goBack:function(){var e=window.frameElement.dataset.id;this.$platform.closeTab(e),this.$platform.openTab({id:"menuSetting",title:this.$t("common.pageTitle.pageFoundationMenuSetting"),url:"/foundation/menuSetting",reload:!0,close:!0})},onChangeIcon:function(e){this.appIcon=e,this.$refs.iconPopover.doClose()},formatAppName:function(e){return e.length>9?"".concat(e.slice(0,9),"..."):e},gotoDesigne:function(e,t,a){var n;1==t?this.$router.push("/designer/form?formId=".concat(e,"&appId=").concat(this.appId,"&formType=plain")):this.$router.push("/designer/form?formId=".concat(e,"&appId=").concat(this.appId,"&formType=flow&newCanvas=").concat(null!==(n=(null===a||void 0===a?void 0:a.newCanvas)&&this.newCanvasGray)&&void 0!==n&&n))},createForm:function(e){this.$router.push("/designer/form?appId=".concat(this.appId,"&formType=").concat(e))},save:function(){if(""===this.appName)return this.$message.warning(this.$t("view.manager.tip1"));if(this.appName.length>100)return this.$message.warning(this.$t("view.designer.tip.tip6"));if(!/^[a-zA-Z0-9\u4e00-\u9fa5\s]+$/.test(this.appName))return this.$message.warning(this.$t("view.manager.tip2"));var e={appId:this.appId,name:this.appName,nameLanguage:this.nameLanguage,icon:this.appIcon,templateList:this.fromTemplates.map((function(e,t){var a={bizId:e.bizId};return a.orderId=t,a}))};this.updateAppInforn(e)},getAppInforn:function(e){var t=this;m.QQ(e).then((function(e){var a=e.code,n=e.data;e.message;0===a?(t.loading=!1,t.appName=n.name||"",t.nameLanguage=n.nameLanguage||(0,s.A)({},(0,w.T2)()),Array.isArray(n.fromTemplateSimple)&&n.fromTemplateSimple.length>0&&Reflect.has(n.fromTemplateSimple[0],"orderId")&&(n.fromTemplateSimple.sort((function(e,t){return e.orderId-t.orderId})),n.fromTemplateSimple=n.fromTemplateSimple.map((function(e){return e.isShowSetting=!1,e}))),t.fromTemplates=(null===n||void 0===n?void 0:n.fromTemplateSimple)||[],t.appIcon=n.icon||"icon-creditcard-fill"):t.loading=!1}))},updateAppInforn:function(e){var t=this;m.f(e).then((function(e){var a=e.code,n=(e.data,e.message);0==a?t.$message.success(t.$t("common.base.saveSuccess")):t.$message.warning(n)}))},popoverListLeave:function(e){var t=this.$refs["mLeftNamePopoverRef".concat(e)][0];t.showPopper||(this.mLeftItemHoverIdx=-1)},popoverListEnter:function(e){this.mLeftItemHoverIdx=e,this.setTimeoutInc&&clearTimeout(this.setTimeoutInc)},popoverItemClick:function(e,t,a){if("item"===a){this.$refs["mLeftItemPopoverRef".concat(t)][0].doClose();var n=e.target.dataset,r=n.type,i=n.value,o=n.formid,s=n.formtype;switch(r){case"rename":this.isOpenMultiLanguage?this.handleSettingFormNameLang(t):(this.$refs["mLeftNamePopoverRef".concat(t)][0].doShow(),this.reNamePrevVal=i);break;case"edit":this.gotoDesigne(o,s);break;case"delete":this.handleDelForm(i,o);break;case"copy":this.handleCopyForm(i,o);break}}else{var p=e.target.dataset,c=p.type;p.value,p.formid,p.formtype;switch(c){case"rename":this.isOpenMultiLanguage?this.handleSettingFormNameLang():(this.$refs.reAppNameRef.doShow(),this.reAppNamePrevVal=this.appName);break;case"icon":this.$refs.iconPopover.doShow();break;case"delete":this.handleDelApp();break}}},handleCancelRenamePopover:function(e){this.$refs["mLeftNamePopoverRef".concat(e)][0].doClose(),this.mLeftItemHoverIdx=-1},enterMLeftItem:function(e){this.checkedIsShowReNamePopover||(this.handleCloseAllLeftItemPopoverRef(),this.setTimeoutInc&&clearTimeout(this.setTimeoutInc),this.mLeftItemHoverIdx=e)},leaveMLeftItem:function(e){var t=this;this.checkedIsShowReNamePopover||(this.setTimeoutInc=setTimeout((function(){t.handleCloseAllLeftItemPopoverRef(),t.mLeftItemHoverIdx=-1}),150))},handleFormItemRename:function(e,t){var a=this;return(0,o.A)((0,i.A)().mark((function n(){var r,o,s,p,c,l,f,u,d;return(0,i.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r=t.bizId,o=t.appId,s=t.isCommonTemplate,p=t.displayName,c=a.isOpenMultiLanguage?p:a.reNamePrevVal,c){n.next=4;break}return n.abrupt("return",a.$message.warning(a.$t("common.placeholder.inputSomething",{data1:a.$t("view.designer.formName")})));case 4:if(!(c.length>100)){n.next=6;break}return n.abrupt("return",a.$message.warning(a.$t("view.manager.tip3")));case 6:return l={bizId:r,displayName:a.isOpenMultiLanguage?p:a.reNamePrevVal,displayNameLanguage:a.fromTemplates[e].displayNameLanguage,appId:o,isCommonTemplate:s},n.next=9,m.DZ(l);case 9:f=n.sent,u=f.code,f.data,d=f.message,0===u?(a.$message.success(a.$t("common.base.tip.editSuccess")),a.getAppInforn({appId:o}),a.$refs["mLeftNamePopoverRef".concat(e)][0].doClose()):a.$message.warning(d);case 12:case"end":return n.stop()}}),n)})))()},handleConfirmAppName:function(){return this.reAppNamePrevVal?this.reAppNamePrevVal.length>100?this.$message.warning(this.$t("view.designer.tip.tip6")):(this.appName=this.reAppNamePrevVal,void this.handleCancelAppRename()):this.$message.warning(this.$t("view.manager.tip1"))},handleCancelAppRename:function(){this.$refs.reAppNameRef.doClose()},bodyAddEventListenerEvent:function(){var e=this.fromTemplates.every((function(e){return!e.isShowSetting}));e||-1===this.mLeftItemHoverIdx||(this.mLeftItemHoverIdx=-1,this.handleCloseAllLeftItemPopoverRef())},clearMLeftItemHoverIdx:function(){document.body.addEventListener("click",this.bodyAddEventListenerEvent)},removeAddEventListener:function(){document.body.removeEventListener("click",this.bodyAddEventListenerEvent)},handleCloseAllLeftItemPopoverRef:function(){this.fromTemplates=this.fromTemplates.map((function(e){return e.isShowSetting=!1,e}))}}},C=$,k=(0,h.A)(C,n,r,!1,null,"d70cd8fa",null),A=k.exports,N=A}}]);