"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[176,203],{8265:function(e,t,a){a.d(t,{A:function(){return Yn}});var n=a(18885),o=a(42881),i=a(35730),r=a(71357),s=a(62361),l=(a(67880),a(2286),a(44807),a(3923),a(80793),a(48152),a(35256),a(21484),a(89716),a(94e3),a(7509),a(16961),a(54615),a(7354),a(89370),a(32807),a(24929),a(19944),a(55650),a(75069),a(21633),a(69594),a(13262),a(68735),a(74526)),c=a(93526),u=a(69396),d=a(60524),m=a(97022),f=a(80602),p={name:"project-task-detail-mixin",data:function(){return{projectTaskId:""}},computed:{currentNodeIsApproveNode:function(){var e;return(null===(e=this.currFlow)||void 0===e?void 0:e.nodeType)===f.A.APPROVE_NODE},projectTask:function(){return{projectTaskId:this.projectTaskId,state:this.flowPermiss.status,currentNodeIsApproveNode:this.currentNodeIsApproveNode}}}},v=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"flow-record-container"},[e.logList.length>0?[t("div",{class:["btn-group",e.isNotLastLog?"mb_16":null,e.isNoCanSeeFlow?"btn-noseeflow":null]},[e.isNoCanSeeFlow?e._e():t("div",{staticClass:"workflow-btn-box"},[e.isNotLastLog?e._e():[t("i",{staticClass:"iconfont icon-question"}),t("span",{staticClass:"txt"},[e._v(e._s(e.$t("view.template.detail.nextNode")))])],t("el-button",{class:[e.isNotLastLog?null:"ml_12"],attrs:{type:"text"},on:{click:e.openFlowDialog}},[e._v(e._s(e.$t("view.template.detail.viewFlowChart")))])],2),t("el-button",{staticClass:"detail-btn",attrs:{type:"text"},on:{click:e.openLogDialog}},[e._v(e._s(e.$t("view.template.detail.viewDetail")))])],1),e.isNotLastLog||e.isNoCanSeeFlow?e._e():t("div",{staticClass:"top-line"})]:e._e(),t("base-timeline",{class:{"parallel-timeline":e.currLogList.length>1},attrs:{data:e.logList,"record-render":e.renderRecord,"head-render":e.renderHeader}}),t("flow-log-dailog",{ref:"flowLogDialog",attrs:{data:e.allLogList}}),t("urge-dialog",{ref:"urgeDialog",on:{success:e.fetchProcessLog}}),t("work-flow-dialog",{ref:"workFlowDialog",attrs:{"log-list":e.allLogList}})],2)},h=[],g=(a(62838),a(46622),a(36700),a(13560),a(33438),a(42925),a(67259),a(33656),a(48649)),b=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.record.label8"),show:e.visible,width:"380px"},on:{"update:show":function(t){e.visible=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.handleUrge}},[e._v(" "+e._s(e.$t("view.template.detail.record.label8"))+" ")])]},proxy:!0}])},[t("div",{staticClass:"urge-modal-box"},[t("el-radio",{attrs:{label:1},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v(e._s(e.$t("view.template.detail.systemInfo")))]),t("el-radio",{attrs:{label:2},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("DING")])],1)])},w=[],y=a(87512),A=a(87),I={name:"urge-dialog",data:function(){return{pending:!1,visible:!1,radio:1,nodeInstanceId:null}},computed:{},methods:{open:function(){this.radio=2,this.visible=!0},urge:function(){var e=this;this.pending=!0;var t=this.$route.query.formContentId;u.urgeProcess({formContentId:t,nodeInstanceId:this.nodeInstanceId}).then((function(t){t.success?(e.$message.success(e.$t("view.template.detail.urgeSuccess")),e.$emit("success"),e.visible=!1):e.$platform.alert(t.message)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.log(e)}))},handleUrge:function(){1===this.radio?this.urge():(this.visible=!1,this.handleDingMsg())},handleDingMsg:function(){var e=this,t=this.$parent.logList.filter((function(t){return t.nodeInstanceId&&t.nodeInstanceId===e.nodeInstanceId})),a=[];t.length>0&&(a=t[0].userIds.map((function(e){return e.staffId})));var n=this.$parent.templateId,o=this.$parent.processId,i=this.$parent.paasFormValueList,r=i.filter((function(e){return e.fieldName===y.E.SerialNumber})).map((function(e){return JSON.parse(e.value)})),s=this.$route.query.formContentId,l=(0,A.zO)(window);l.send_link_ding_paas_message(a,r,n,s,o)}}},T=I,k=a(49100),C=(0,k.A)(T,b,w,!1,null,"011f105d",null),S=C.exports,N=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{visible:e.visible,fullscreen:"",modal:!1},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"flow-log"},[t("div",{staticClass:"flow-log-header"},[t("h3",[e._v(e._s(e.$t("view.template.detail.flowDetail")))])]),t("div",{staticClass:"flow-log-content"},[t("h4",[e._v(" "+e._s(e.$t("view.template.detail.dailyRecord"))+" "),t("el-button",{attrs:{type:"text",disabled:e.pending},on:{click:e.exportLog}},[t("i",{staticClass:"iconfont icon-daochu"}),e._v(e._s(e.$t("common.base.export"))+" ")])],1),t("el-table",{attrs:{"header-row-class-name":"common-list-table-header__v2",data:e.data}},[t("el-table-column",{attrs:{width:"60",label:e.$t("common.base.SN")},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.data.length-a.$index))])]}}])}),e._l(e.columns,(function(a){return t("el-table-column",{key:a.prop,attrs:{label:a.label,prop:a.prop,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"header",fn:function(a){return["usedTime"===a.column.property?t("span",[e._v(" "+e._s(a.column.label)+" "),t("span",{attrs:{slot:"label"},slot:"label"},[t("el-tooltip",{attrs:{content:e.$t("view.designer.workFlow.tip51"),placement:"top"}},[t("i",{staticClass:"el-icon-question"})])],1)]):t("span",[e._v(" "+e._s(a.column.label)+" ")])]}},{key:"default",fn:function(n){return["startTime"===a.prop||"completeTime"===a.prop?[e._v(" "+e._s(e._f("fmt_datetime")(n.row[a.prop]))+" ")]:"userIds"===a.prop?[e.isOpenData?[e.autoSubmitCode.includes(n.row.operateCode)?[e._v(" system ")]:n.row.isCurrent?void 0:e._l(n.row.taskLogVOList,(function(e){var a,n,o;return t("open-data",{key:null===e||void 0===e||null===(a=e.user)||void 0===a?void 0:a.userId,attrs:{type:"userName",openid:null===e||void 0===e||null===(n=e.user)||void 0===n?void 0:n.staffId,name:null===e||void 0===e||null===(o=e.user)||void 0===o?void 0:o.userName}})}))]:[e._v(" "+e._s(e.handleUser(n.row))+" ")]]:"opearte"===a.prop?[e._v(" "+e._s(e.handleOpearte(n.row))+" ")]:[e._v(" "+e._s(n.row[a.prop])+" ")]]}}],null,!0)})}))],2)],1)])])},_=[],L=(a(53417),a(8200),a(36886),a(56831),a(4118),a(5981),a(63074),a(39724),a(19055)),x=a(84859),D={name:"flow-log-dailog",props:{data:{type:Array,default:function(){return[]}}},data:function(){return{pending:!1,visible:!1,columns:[{label:x.Ay.t("view.template.detail.nodeName"),prop:"nodeName"},{label:x.Ay.t("view.template.detail.receiveTime"),prop:"startTime"},{label:x.Ay.t("common.base.completeTime"),prop:"completeTime"},{label:x.Ay.t("view.template.detail.usedTime"),prop:"usedTime"},{label:x.Ay.t("common.base.operator"),prop:"userIds"},{label:x.Ay.t("common.base.operation"),prop:"opearte"}]}},computed:{formName:function(){var e;return(null===(e=this.$parent)||void 0===e||null===(e=e.$parent)||void 0===e?void 0:e.templateName)||""},isOpenData:function(){return this.$platform.isOpenData},autoSubmitCode:function(){return L.A.AUTO_SUBMIT.code||""}},methods:{open:function(){this.visible=!0},handleUser:function(e){var t=e.operateCode,a=e.isCurrent,n=e.taskLogVOList,o=void 0===n?[]:n;if(this.autoSubmitCode.includes(t))return"system";if(!a){var r=o.map((function(e){var t;return null===e||void 0===e||null===(t=e.user)||void 0===t?void 0:t.userName}));return(0,i.A)(new Set(r)).join(",")}},handleOpearte:function(e){var t=e.operate,a=e.operateCode,n=e.isCurrent;if(!n){var o=L.A.AUTO_SUBMIT,i=o.code,r=void 0===i?"":i,s=o.name;if(r.includes(a))return s;var l={"自动通过":x.Ay.t("buttons.autoSubmit"),"提交":x.Ay.t("buttons.submit"),"暂存":x.Ay.t("buttons.temporarilySave"),"转交":x.Ay.t("buttons.forward"),"退回":x.Ay.t("buttons.back"),"取消":x.Ay.t("buttons.cancel"),"同意":x.Ay.t("buttons.agree"),"不同意":x.Ay.t("buttons.refuse"),"抄送自动通过":x.Ay.t("buttons.ccAutoSubmit")};return l[t]||t}},exportLog:function(){var e=this;this.pending=!0;var t=this.$parent.formName,a=void 0===t?"":t,n=this.$route.query.processId;u.exportLog({processorInstanceId:n,formName:a}).then((function(t){e.$platform.alert(t.message),t.succ&&window.parent&&(window.parent.showExportList(),window.parent.exportPopoverToggle(!0))}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.log(e)}))}}},$=D,F=(0,k.A)($,N,_,!1,null,"fca97f20",null),B=F.exports,P=a(94912),E=a(56214),O=a(23043),R=a(25623),j=a(74367),z=a(50651),M=a(16295),V=a(18104),U={CURRENT:"icon-daishenpi",SUCCESS:"icon-yitongguo",ROLLBACK:"icon-yibohui",CANCEL:"icon-butongyi",END:"icon-jieshu"},W={name:"flow-record",props:{logList:{type:Array,default:function(){return[]}},paasFormValueList:{type:Array,default:function(){return[]}},templateId:{type:[String,Number],default:null},processId:{type:[String,Number],default:null},currLogList:{type:Array,default:function(){return[]}},formName:{type:String,default:""},displayNameLanguageArr:{type:Array,default:function(){return[]}}},computed:(0,r.A)((0,r.A)({},(0,z.aH)("user",["loginUser"])),{},{allowDing:function(){return R.A.inDingTalk},isParallelNode:function(){return this.currLogList.length>1},allLogList:function(){if(!this.isParallelNode)return this.logList;var e=this.logList.slice(1);return this.currLogList.concat(e)},isNotLastLog:function(){var e;return this.allLogList.length>0&&(null===(e=this.allLogList[0])||void 0===e?void 0:e.nodeType)===f.A.END_NODE},isNoCanSeeFlow:function(){var e;return this.allLogList.length>0&&(null===(e=this.allLogList[0])||void 0===e?void 0:e.nodeType)===f.A.START_NODE},isOpenData:function(){return this.$platform.isOpenData}}),methods:{getRemarkMessage:function(e,t){var a=L.A.AGREE,n=L.A.REFUSE,o=L.A.FORWARD;if(e==o.code)return t;if([a.code,n.code].includes(e))return"".concat(x.Ay.t("view.template.detail.approveSuggestion"),"：").concat(t);var i=L.A.getKeyByCode(e,"name")||"";return"".concat(x.Ay.t("view.template.detail.sthSuggestion",{data:i}),"：").concat(t)},openFlowDialog:function(){this.$refs.workFlowDialog.open()},openLogDialog:function(){this.$refs.flowLogDialog.open()},matchField:function(e){var t=/修改字段：(.+)/,a=e.match(t);if(a){var n=a[1].split(",").map((function(e){return e.trim()})).filter(Boolean);return{associatedFields:n}}},globalLanguage:function(e){var t=this.matchField(e),a=t.associatedFields,n=this.hanldeGlobalLanguage(a,e);return n},hanldeGlobalLanguage:function(e,t){var a=this,n=e.map((function(e){var t,n=a.displayNameLanguageArr.find((function(t){var a;return(null===(a=t.displayNameLanguage)||void 0===a?void 0:a["zh"])===e}));if(n)return null===n||void 0===n||null===(t=n.displayNameLanguage)||void 0===t?void 0:t[x.Ay.locale]})).filter(Boolean).join(",");return"".concat((0,x.t)("common.record.product.record15.text2"),"：").concat(n||t)},openUrgeDialog:function(e){if(this.$refs.urgeDialog.nodeInstanceId=e,!this.allowDing)return this.$refs.urgeDialog.urge();this.$refs.urgeDialog.open()},fetchProcessLog:function(){this.$emit("fetchProcessLog")},renderUserHeadDom:function(e,t){var a=e||{},n=a.userHead,o=a.userId;return n||(n=j),(0,g.h)("div",{class:"user-head",directives:[{name:"user",value:o}]},[(0,g.h)("img",{attrs:{src:n}}),t&&(0,g.h)("span",{class:"icon"},[(0,g.h)("i",{class:["iconfont",t]})])])},renderFlowLogAttachedDom:function(e){if(Array.isArray(e)&&e.length)return(0,g.h)("div",{class:"base-file__preview process-history-record-attached"},[e.map((function(t){if(null!==t&&void 0!==t&&t.id)return(0,g.h)("base-file-item",{attrs:{source:e,file:t,readonly:!0},key:t.id})}))])},renderRemarkDom:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(Array.isArray(e)&&e.length)return(0,g.h)("ul",{class:["base-remark__list",a?"padding-0":null]},[e.map((function(e){if(1===e.source)return(0,g.h)("li",[(0,g.h)("div",{class:"base_remark__title"},[(0,g.h)("strong",{class:"user-card-triggle",directives:[{name:"user",value:e.createUser.userId}]},[t.isOpenData&&e.createUser.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:e.createUser.staffId}}):e.createUser.displayName])," ",x.Ay.t("view.template.detail.record.addRemark"),"：",e.showInOwn?(0,g.h)("span",{class:"private"},[" ",(0,g.h)("i",{class:"iconfont icon-account1"})," ",x.Ay.t("common.option.commentAction[0]")," "]):null,e.isNoticeOriginator?(0,g.h)("span",["(",x.Ay.t("view.template.detail.record.label1"),")"]):null,e.cusRemarksNotice?(0,g.h)("span",["(",x.Ay.t("common.option.commentAction[3]"),")"]):null]),(0,g.h)("biz-comment-html",{class:"base_remark__content",attrs:{html:"".concat((0,M.X_)(e.content)),remarkItem:e}}),t.renderFlowLogAttachedDom(e.attachments),(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(2===e.source)return(0,g.h)("div",{class:"base_remark__title"},[(0,g.h)("strong",{class:"user-card-triggle"},[t.isOpenData&&e.createUser.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:e.createUser.staffId}}):e.createUser.displayName]),"  ",x.Ay.t("view.template.detail.record.label2"),e.content,(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(3===e.source)return(0,g.h)("div",{class:"base_remark__title"},[x.Ay.t("view.template.detail.record.label3"),e.content,(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(4===e.source)return(0,g.h)("div",{class:"base_remark__title"},[x.Ay.t("view.template.detail.record.label4"),e.content,(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(8===e.source)return(0,g.h)("div",{class:"base_remark__title"},[x.Ay.t("view.template.detail.record.materialDelivery"),e.content,(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(5===e.source)return(0,g.h)("div",{class:"base_remark__title"},[x.Ay.t("view.template.detail.record.label5"),e.content,(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(6===e.source)return(0,g.h)("div",{class:"base_remark__title"},[(0,g.h)("strong",{class:"user-card-triggle",directives:[{name:"user",value:e.createUser.userId}]},[t.isOpenData&&e.createUser.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:e.createUser.staffId}}):e.createUser.displayName]),x.Ay.t("view.template.detail.record.editForm"),"：",(0,g.h)("div",{class:"base_remark__content"},[e.content]),(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]);if(7===e.source){var a=e.content.split("添加了节点审批人："),n=JSON.parse(a[0]),o=[];return JSON.parse(a[1]).map((function(e){o.push({displayName:e.displayName,staffId:e.staffId})})),(0,g.h)("div",{class:"base_remark__title"},[(0,g.h)("strong",{class:"user-card-triggle",directives:[{name:"user",value:e.createUser.userId}]},[t.isOpenData&&e.createUser.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:e.createUser.staffId}}):e.createUser.displayName]),x.Ay.t("view.countersign"),"：",(0,g.h)("div",{class:"base_remark__content"},[t.isOpenData&&n.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:n.staffId}}):n.displayName,x.Ay.t("view.addCountersignUser"),"：",t.isOpenData&&o.length?o.map((function(e,t){o.length,(0,g.h)("open-data",{attrs:{type:"userName",openid:e.staffId}})})):o.map((function(e,t){return(0,g.h)("span",[t+1===o.length?e.displayName:e.displayName+"，"])}))]),(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])])}return[9,10,11,12].includes(e.source)?(0,g.h)("div",{class:"base_remark__title"},[(0,g.h)("strong",{class:"user-card-triggle"},[t.isOpenData&&e.createUser.staffId?(0,g.h)("open-data",{attrs:{type:"userName",openid:e.createUser.staffId}}):e.createUser.displayName]),(0,g.h)("span",{domProps:{innerHTML:e.content}}),(0,g.h)("div",{class:"base-remark__time"},[(0,V.Yf)(e.createTime)])]):void 0}))])},renderHistoryFlowLogDom:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.taskLogVOList,n=void 0===a?[]:a,o=t.operateCode,i=(t.processLogRemarkVOs,t.nodeType),r=L.A.AUTO_SUBMIT,s=r.code,l=void 0===s?"":s,c=r.name;return l.includes(o)?c:n.map((function(t,a){var n=t.approveResult,o=t.approveMessage,r=t.user,s=void 0===r?{}:r,l=t.attached,c=void 0===l?[]:l,u=t.sign,d=s||{},m=(d.userHead,d.userName),f=d.type,p=d.staffId,v={"自动通过":x.Ay.t("buttons.autoSubmit"),"提交":x.Ay.t("buttons.submit"),"暂存":x.Ay.t("buttons.temporarilySave"),"转交":x.Ay.t("buttons.forward"),"退回":x.Ay.t("buttons.back"),"取消":x.Ay.t("buttons.cancel"),"同意":x.Ay.t("buttons.agree"),"不同意":x.Ay.t("buttons.refuse"),"抄送自动通过":x.Ay.t("buttons.ccAutoSubmit")},h=L.A.getKeyByCode(t.operateCode,"value"),b=e.getRemarkMessage(t.operateCode,o);return(0,g.h)("div",{class:"user-wrapper"},[(0,g.h)("div",{class:"user-wrapper-top"},[e.renderUserHeadDom(s),(0,g.h)("div",{class:"user-info"},[(0,g.h)("div",{class:"user-info-left"},[(0,g.h)("span",{class:"user-name"},[p&&e.isOpenData?(0,g.h)("open-data",{attrs:{type:"userName",openid:p}}):m]),n&&(0,g.h)("span",{class:["user-approve-status",h]},["（",2==f?x.Ay.t("view.template.detail.record.outsideResult",{result:v[n]||n}):v[n]||n,"）"])]),(0,g.h)("div",{class:"user-info-right"},[(0,O.Yq)(t.completeTime,"YYYY-MM-DD HH:mm:ss")])])]),o&&(0,g.h)("div",{class:"user-wrapper-bottom",domProps:{innerHTML:b}}),"approve-node"===i&&u&&(0,g.h)("div",{class:"user-wrapper-bottom approve-sign"},[(0,g.h)("span",[x.Ay.t("common.base.approveSign"),"："]),(0,g.h)("el-image",{style:"width: 100px; height: auto",attrs:{src:u,"preview-src-list":[u]}})]),e.renderFlowLogAttachedDom(c)])}))},renderFlowManagersDom:function(e){var t=this;return(0,g.h)("div",{class:"user-wrapper-bottom user-list"},[e.map((function(e){e.userHead;var a=e.userName,n=e.staffId;return(0,g.h)("div",{class:"user-list-item"},[t.renderUserHeadDom(e),(0,g.h)("span",[n&&t.isOpenData?(0,g.h)("open-data",{attrs:{type:"userName",openid:n}}):a])])}))])},renderCurFlowLogDom:function(e){var t=e.nodeType,a=e.userIds,n=void 0===a?[]:a,o=e.taskLogVOList,i=void 0===o?[]:o,r=e.processLogRemarkVOs,s=void 0===r?[]:r;if(n.length){var l=t==f.A.APPROVE_NODE,c=this.loginUser||{},u=c.userId,d=n.findIndex((function(e){return e.userId==u}));d=d>-1?d:0;var m=n[d]||{},p=(m.userHead,m.userName),v=m.staffId,h=l?x.Ay.t("common.base.approve"):x.Ay.t("view.template.detail.record.handle"),b=n.length>1,w=b?x.Ay.t("common.paas.view.template.detail.record.label7",{userName:p,num:n.length,action:h}):x.Ay.t("common.paas.view.template.detail.record.label6",{userName:p,action:h});return(0,g.h)("div",{class:"user-wrapper"},[(0,g.h)("div",{class:"user-wrapper-top"},[this.renderUserHeadDom(n[d],U.CURRENT),this.isOpenData&&v?(0,g.h)("div",{attrs:{className:"user-info"}},[b?(0,g.h)("span",["待",(0,g.h)("open-data",{attrs:{type:"userName",openid:v}}),"等",n.length,"人",h]):(0,g.h)("span",["待",(0,g.h)("open-data",{attrs:{type:"userName",openid:v}}),h])]):(0,g.h)("div",{attrs:{className:"user-info"}},[w]),b&&(0,g.h)("div",{class:["load-more-btn",e.collapse&&"collapse-up"],on:{click:function(){e.collapse=!e.collapse}}},[(0,g.h)("i",{class:"iconfont icon-more"})])]),e.collapse&&this.renderFlowManagersDom(n),i.length>0&&(0,g.h)("div",{class:"process-history-record-wrapper"},[this.renderHistoryFlowLogDom(e)]),this.renderRemarkDom(s)])}},renderNodeNameAndStautsDom:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.nodeName,n=t.isCurrent,o=t.isDefault,i=t.operateCode,r=t.nodeInstanceId,s=L.A.getKeyByCode(i,"name"),l=L.A.getKeyByCode(i,"value"),c=n?(0,g.h)("i",{class:"remind-btn",on:{click:function(t){return e.openUrgeDialog(r)}}},[r?x.Ay.t("view.template.detail.record.label8"):""]):x.Ay.t("view.template.detail.record.label9",{data:s}),u=this.isParallelNode&&n&&!o;return(0,g.h)("div",{class:"process-record-top"},[(0,g.h)("strong",{class:"process-name"},[a]),(0,g.h)("span",{class:["process-status",l]},[c]),u&&(0,g.h)("el-button",{class:"log-view-btn",attrs:{type:"text"},on:{click:function(){e.$emit("updateFlowNode",t)}}},[x.Ay.t("view.template.detail.record.clickView")])])},renderMultiCurFlowLogDom:function(e){return(0,g.h)("base-timeline",{attrs:{data:this.currLogList,"record-render":this.renderRecord,"head-render":this.renderHeader}})},renderRecord:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.isMulti,n=t.isDefault,o=t.isCurrent,i=t.nodeType,r=t.nodeName,s=t.processLogRemarkVOs,l=void 0===s?[]:s;if(a)return e("div",{class:"parallel-content"},[e("div",{class:"parallel-border"}),this.renderMultiCurFlowLogDom(t)]);var c,u=o?this.renderCurFlowLogDom(t):e("div",[this.renderHistoryFlowLogDom(t),l.length>0&&this.renderRemarkDom(l)]);return i==f.A.END_NODE?e("div",{class:"process-record-end"},[e("div",{class:"process-record-end__text"},[e("strong",{class:"".concat(f.A.END_NODE)},[r])]),this.renderRemarkDom(null!==(c=null===t||void 0===t?void 0:t.processLogRemarkVOs)&&void 0!==c?c:[],!0)]):e("div",{class:["process-record-wrap",{active:n}]},[this.renderNodeNameAndStautsDom(t),e("div",{class:"process-record-content"},[u])])},headerIcon:function(e){var t=L.A.getKeyByCode(e.operateCode,"value");if(e.isCurrent)return U.CURRENT;var a=[L.A.REFUSE.value,L.A.ROLLBACK.value];return a.indexOf(t)>-1?U.ROLLBACK:t==L.A.CANCEL.value?U.CANCEL:e.nodeType==f.A.END_NODE?U.END:U.SUCCESS},renderHeader:function(e,t){var a=this.headerIcon(t),n=["iconfont",a];if(t.isMulti)return e("div",{class:["base-timeline-head","parallel-node-head"]});var o=t.nodeType==f.A.START_NODE&&!t.isCurrent;return o?e("div",{class:["base-timeline-head","start-node-head"]}):e("div",{class:"base-timeline-head"},[e("i",{class:n})])}},components:(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},B.name,B),S.name,S),"CommentHtml",P.A),E["default"].name,E["default"])},H=W,q=H,K=(0,k.A)(q,v,h,!1,null,"45a711cb",null),G=K.exports,J=a(52340),Y=a(92367),Q=a(90089),X=function(){var e=this,t=e._self._c;return t("div",{staticClass:"satisfaction-data"},[t("el-table",{attrs:{"header-row-class-name":"common-list-table-header__v2",data:e.satisfactionData,"highlight-current-row":!1,border:""}},e._l(e.columns,(function(a,n){return t("el-table-column",{key:"".concat(a.fieldName,"_").concat(n),attrs:{label:a.displayName,prop:a.fieldName,"min-width":a.minWidth||"120px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(n){return["sendUserList"===a.fieldName?[e._v(" "+e._s(n.row[a.fieldName]&&n.row[a.fieldName].join("、"))+" ")]:"sendType"===a.fieldName?[e._v(" "+e._s(e.getSendType(n.row))+" ")]:"evaluateState"===a.fieldName?[t("div",{class:["evaluate-state","evaluate-state-".concat(n.row[a.fieldName])]},[e._v(" "+e._s(n.row[a.fieldName]?e.$t("common.base.evaluated"):e.$t("common.base.notEvaluated"))+" ")])]:"action"===a.fieldName&&n.row["evaluateState"]?[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return e.preview(n.row)}}},[e._v(e._s(e.$t("common.base.view")))])]:[e._v(" "+e._s(n.row[a.fieldName])+" ")]]}}],null,!0)})})),1),t("base-modal",{attrs:{title:e.$t("view.template.detail.viewQuestionnaireDetail"),width:"700px",show:e.visible},on:{"update:show":function(t){e.visible=t},close:e.close}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.showLoading,expression:"showLoading"}],staticClass:"satisfaction-detail"},[t("iframe",{attrs:{id:"iframepage",src:e.iframeUrl,height:"350px",width:"100%"}})]),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.close}},[e._v(e._s(e.$t("common.base.close")))])],1)])],1)},Z=[],ee=a(19447),te={name:"satisfaction-data",props:{satisfactionData:{type:Array,default:function(){return[]}}},data:function(){return{visible:!1,showLoading:!1,iframeUrl:"",columns:[{displayName:x.Ay.t("view.template.detail.questionnaireScheme"),fieldName:"satisfactionName",minWidth:"150px"},{displayName:x.Ay.t("view.template.detail.sendNode"),fieldName:"nodeName"},{displayName:x.Ay.t("view.template.detail.sendUserList"),fieldName:"sendUserList"},{displayName:x.Ay.t("view.template.detail.sendType"),fieldName:"sendType"},{displayName:x.Ay.t("view.template.detail.questionnaireState"),fieldName:"evaluateState"},{displayName:x.Ay.t("view.template.detail.csatSatisfaction"),fieldName:"csat"},{displayName:x.Ay.t("view.template.detail.npsEvaluate"),fieldName:"nps"},{displayName:x.Ay.t("common.base.operation"),fieldName:"action",minWidth:"80px"}]}},mounted:function(){var e=this;window.addEventListener("message",(function(t){var a=t.data;e.showLoading=a.showLoading}))},methods:{preview:function(e){this.visible=!0,this.showLoading=!0,this.iframeUrl="/pcoperation/satisfaction/detail?type=PaaS&dataId=".concat(e.triggerPointDataId,"&typeId=")},close:function(){this.visible=!1,this.showLoading=!1,this.iframeUrl=""},getSendType:function(e){return ee.A.getNamesByValue(e.sendType)}}},ae=te,ne=(0,k.A)(ae,X,Z,!1,null,"ee0f2e34",null),oe=ne.exports,ie=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.sendPreview"),show:e.visible,width:"800px"},on:{"update:show":function(t){e.visible=t},close:e.close}},[t("div",{staticClass:"satisfaction-preview"},[t("div",{staticClass:"satisfaction-preview-left"},[t("div",{staticClass:"satisfaction-preview-left-title"},[t("label",[e._v(e._s(e.$t("view.template.detail.questionnaireScheme"))+"：")]),t("span",[e._v(e._s(e.selected.satisfactionName))])]),t("div",{staticClass:"satisfaction-preview-left-preview"},[t("div",{staticClass:"form-design-main"},[e.visible?t("div",{staticClass:"form-design-center"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"form-design-phone"},[t("iframe",{attrs:{id:"iframepage",src:e.iframeUrl,height:"100%",width:"100%"}})])]):e._e()])])]),t("div",{staticClass:"satisfaction-preview-right"},[t("div",{ref:"satisfactionTargetParent",staticClass:"satisfaction-preview-right-top",class:!e.collapse&&"max-height"},[t("label",[e._v(e._s(e.$t("view.template.detail.sendTo"))+"：")]),e.receivers.length?t("div",{ref:"satisfactionTarget",staticClass:"satisfaction-target"},[e._l(e.receivers,(function(a){return[t("el-tooltip",{key:a.userId,staticClass:"item",attrs:{effect:"dark",placement:"top",disabled:!a.phone&&!a.email}},[t("div",{attrs:{slot:"content"},slot:"content"},[a.phone?t("div",[e._v(e._s(e.$t("view.template.detail.phoneNo"))+"："+e._s(a.phone))]):e._e(),a.email?t("div",[e._v(e._s(e.$t("common.form.type.email"))+"："+e._s(a.email))]):e._e()]),t("div",{staticClass:"satisfaction-target-user"},[t("img",{attrs:{src:a.head}}),t("span",[e._v(e._s(a.userName))])])])]}))],2):t("div",{staticClass:"tips"},[e._v(e._s(e.$t("view.template.detail.notConfigured")))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.showMoreButton,expression:"showMoreButton"}],staticClass:"more-btn pointer",on:{click:e.toggle}},[e._v(" "+e._s(e.collapse?e.$t("common.base.collapse"):e.$t("common.base.more"))+" "),t("i",{staticClass:"iconfont",class:e.collapse?"icon-Icon_up":"icon-fdn-select"})]),t("div",{staticClass:"satisfaction-preview-right-bottom"},[t("label",[e._v(e._s(e.$t("view.template.detail.sendType"))+"：")]),e.sendType.length?t("div",{staticClass:"satisfaction-send-type"},e._l(e.sendType,(function(a){return t("span",{key:a.value},[e._v(e._s(a.name))])})),0):t("div",{staticClass:"tips"},[e._v(e._s(e.$t("view.template.detail.notConfigured")))])])])]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:e.close}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.send")))])],1)])},re=[],se={name:"satisfaction-preview-dialog",props:{satisfactionList:{type:Array,default:function(){return[]}},nodeName:{type:String,default:""},nodeId:{type:String,default:""}},data:function(){return{visible:!1,loading:!1,pending:!1,collapse:!1,showMoreButton:!1,selected:{}}},computed:{contentBizId:function(){return this.$route.query.formContentId},processId:function(){return this.$route.query.processId},receivers:function(){var e=this.selected.receivers,t=void 0===e?[]:e;return t.map((function(e){return e.head=e.head||j,e}))},sendType:function(){var e,t=(null===(e=this.selected)||void 0===e?void 0:e.sendRule)||{},a=t.sendType,n=void 0===a?[]:a;return[ee.A.DX,ee.A.EMAIL,ee.A.SYSTEM].filter((function(e){return n.includes(e.value)}))}},created:function(){var e=this;window.addEventListener("message",(function(t){var a=t.data;e.loading=a.showLoading}))},methods:{openDialog:function(){this.selected=this.satisfactionList[0]||{},this.visible=!0},close:function(){this.visible=!1,this.selected={}},submit:function(){var e=this;this.pending=!0,m.sj({contentBizId:this.contentBizId,processInstanceId:this.processId,returnVisitBizId:this.selected.bizId,curNodeName:this.nodeName,nodeInstanceId:this.nodeId}).then((function(t){if(!t.success)return e.$message.error(t.message);e.$message.success(e.$t("common.base.sendSuccess")),e.$emit("success"),e.close()}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("submit err",e)}))},toggle:function(){this.$refs.satisfactionTargetParent.scrollTop=0,this.collapse=!this.collapse}},watch:{selected:function(e){var t=this;this.loading=!0,this.iframeUrl="/pcoperation/task/evaluate?id=".concat(e.satisfactionId,"&isReviewed=0&isEvaluated=0&isShowPhone=true"),this.$nextTick((function(){var e;t.collapse=!1,t.showMoreButton=(null===(e=t.$refs)||void 0===e||null===(e=e.satisfactionTarget)||void 0===e?void 0:e.scrollHeight)>160}))}}},le=se,ce=(0,k.A)(le,ie,re,!1,null,"7dfd8a8a",null),ue=ce.exports,de=function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-info-name"},e._l(e.cards,(function(a,n){return t("span",{key:n,class:{active:e.module==a.module},on:{click:function(t){e.module=a.module}}},[e._v(e._s(e.getModuleName(a)))])})),0),t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.loading,expression:"loading",modifiers:{lock:!0}}],staticClass:"card-info-list"},[t("div",{staticClass:"btn-group"},[t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:e.openCreateDialog}},[e._v(e._s(e.$t("common.base.add2")))])],1),t("card-table",{attrs:{data:e.list,fields:e.fields,module:e.module,loading:e.loading},on:{delete:e.handleDelete}})],1),t("card-create-dialog",{ref:"cardCreateDialog",attrs:{"content-id":e.contentBizId,module:e.module,fields:e.fields,value:e.list},on:{success:e.fetchCardData}})],1)},me=[],fe=function(){var e=this,t=e._self._c;return t("el-table",{ref:"cardTable",attrs:{"header-row-class-name":"common-list-table-header__v2",data:e.list,border:""},on:{select:e.handleSelection,"select-all":e.handleSelection}},[e.selectable?t("el-table-column",{attrs:{type:"selection",align:"center",width:"40"}}):e._e(),e._l(e.fields,(function(a){return t("el-table-column",{key:a.fieldName,attrs:{label:a.displayName,prop:a.fieldName,"show-overflow-tooltip":"","min-width":a.minWidth||"128px"},scopedSlots:e._u([{key:"default",fn:function(n){return[e.isLinkField(a.fieldName)?[t("a",{staticClass:"view-detail-btn",on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.jump(n.row.id)}}},[e._v(" "+e._s(n.row[a.fieldName])+" ")])]:[e._v(" "+e._s(n.row[a.fieldName])+" ")]]}}],null,!0)})})),e.selectable?e._e():t("el-table-column",{attrs:{label:e.$t("common.base.operation")},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return e.handleDelete(a.row.id)}}},[e._v(e._s(e.$t("common.base.delete")))])]}}],null,!1,4211472972)}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.loading,expression:"!loading"}],attrs:{slot:"empty"},slot:"empty"},[e._v(e._s(e.$t("common.base.noData")))])],2)},pe=[],ve=(a(87313),a(14126),a(58878)),he=a(46274),ge=a(49175);function be(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a={id:t.id};return e.forEach((function(e){var n=e.fieldName,o=t[n];n===y.pb.Tags?a[n]=Array.isArray(o)?o.filter((function(e){return e&&e.tagName})).map((function(e){return e.tagName})).join("，"):"":n===y.pb.Manager||n===y.pb.Synergies?a[n]=(0,he.ry)(o):n===y.pb.CustomerAddress?a[n]=ve.A.prettyAddress(o):a[n]=o})),a}function we(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a={id:t.id};return e.forEach((function(e){var n=e.fieldName,o=t[n];n===y.su.Customer?a[n]=(0,he.ry)(o):n===y.su.CatalogId?a[n]=(null===o||void 0===o?void 0:o.pathName)||(null===t||void 0===t?void 0:t.type)||"":n===y.su.QualityStatus?a[n]=ge.C3[t.qualityStatus]:n===y.su.QualityStartTime||y.su.QualityEndTime?a[n]=(0,V.Z0)(o):a[n]=o})),a}function ye(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a={id:t.id};return e.forEach((function(e){var n=e.fieldName,o=t[n];n===y.Dv.Customer||n===y.Dv.Linkman?a[n]=(0,he.ry)(o):n===y.Dv.Product?a[n]=Array.isArray(o)?o.map((function(e){return e.name})).join("，"):"":n===y.Dv.Address?a[n]=ve.A.prettyAddress(o):a[n]=o})),a}function Ae(e,t){var a={id:t.id};return e.forEach((function(e){var n=e.fieldName,o=t[n];n===y.s0.Customer||n===y.s0.Linkman?a[n]=(0,he.ry)(o):n===y.s0.Product?a[n]=Array.isArray(o)?o.map((function(e){return e.name})).join("，"):"":n===y.s0.Address?a[n]=ve.A.prettyAddress(o):a[n]=o})),a}function Ie(e,t,a){return e===y.dz.Customer?be(t,a):e===y.dz.Product?we(t,a):e===y.dz.Event?ye(t,a):e===y.dz.Task?Ae(t,a):void 0}var Te,ke,Ce=a(92935),Se=a.n(Ce),Ne={name:"card-table",props:{data:{type:Array,default:function(){return[]}},fields:{type:Array,default:function(){return[]}},selectable:{type:Boolean,default:!1},value:{type:Array,default:function(){return[]}},module:{type:String,default:""},loading:{type:Boolean,default:!1}},data:function(){return{multipleSelection:[]}},computed:{list:function(){var e=this;return this.data.map((function(t){return Ie(e.module,e.fields,t)}))}},methods:{isLinkField:function(e){return this.module===y.dz.Event?e===y.Dv.EventNo:this.module===y.dz.Task?e===y.s0.TaskNo:this.module===y.dz.Customer?e===y.pb.SerialNumber:this.module===y.dz.Product?e===y.su.Name:void 0},handleSelection:function(e){var t=this,a=[];a=this.multipleSelection.filter((function(e){return t.list.every((function(t){return t.id!==e.id}))})),a=Se().uniqWith([].concat((0,i.A)(a),(0,i.A)(e)),Se().isEqual),this.multipleSelection=a},matchSelected:function(){var e=this;this.$refs.cardTable.clearSelection();var t=this.list.filter((function(t){return e.multipleSelection.some((function(e){return e.id===t.id}))}))||[];t.forEach((function(t){e.$refs.cardTable.toggleRowSelection(t,!0)}))},jump:function(e){return this.module===y.dz.Customer?this.openTab("customer_view_".concat(e),this.$t("common.pageTitle.pageCustomerView"),"/customer/view/".concat(e,"?noHistory=1")):this.module===y.dz.Product?this.openTab("product_view_".concat(e),this.$t("common.pageTitle.pageProductView"),"/customer/product/view/".concat(e,"?noHistory=1")):this.module===y.dz.Event?this.openTab("event_view_".concat(e),this.$t("common.pageTitle.pageEventView"),"/event/view/".concat(e)):this.module===y.dz.Task?this.openTab("task_view_".concat(e),this.$t("common.pageTitle.pageTaskView"),"/task/view/".concat(e)):void 0},openTab:function(e,t,a){var n,o=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");o&&this.$platform.openTab({id:e,title:t,url:a,fromId:o,close:!0,reload:!0})},handleDelete:function(e){this.$emit("delete",e)}},watch:{value:function(e,t){this.multipleSelection=e||[]}}},_e=Ne,Le=(0,k.A)(_e,fe,pe,!1,null,"36a99c1e",null),xe=Le.exports,De=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-create-dialog",attrs:{title:e.$t("common.base.addModule",{module:e.moduleName}),show:e.visible,width:"800px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.loading,expression:"loading",modifiers:{lock:!0}}]},[t("div",{staticClass:"search-panel"},[t("el-input",{attrs:{placeholder:e.$t("view.template.detail.placeholder2",{data:e.moduleName})},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.jump(1)}},model:{value:e.params.keyword,callback:function(t){e.$set(e.params,"keyword",t)},expression:"params.keyword"}}),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.jump(1)}}},[e._v(e._s(e.$t("common.base.search")))])],1),t("card-table",{ref:"cardTable",attrs:{data:e.data,fields:e.fields,value:e.value,module:e.module,loading:e.loading,selectable:""}}),t("el-pagination",{attrs:{background:"",total:e.totalItems,"page-sizes":e.defaultTableData.defaultPageSizes,"page-size":e.params.pageSize,"current-page":e.params.pageNum,layout:e.defaultTableData.defaultLayout},on:{"current-change":e.jump,"size-change":e.handleSizeChange}})],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.close")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.save")))])],1)])},$e=[],Fe=a(78528),Be=a(79556),Pe={name:"card-create-dialog",props:{contentId:{type:String,default:""},module:{type:String,default:""},fields:{type:Array,default:function(){return[]}},value:{type:Array,default:function(){return[]}}},data:function(){return{defaultTableData:Be.o,loading:!1,pending:!1,visible:!1,data:[],totalItems:0,params:this.buildParams()}},computed:{moduleName:function(){return Fe.A[this.module]}},methods:{buildParams:function(){return{keyword:"",pageSize:10,pageNum:1,module:this.module}},open:function(){this.data=[],this.totalItems=0,this.params=this.buildParams(),this.onSearch(),this.pending=!1,this.visible=!0},onSearch:function(){var e=this;this.loading=!0,c.gE(this.params).then((function(t){var a=t.success,n=t.message,o=t.data;if(!a)return e.$message.error(n);if(o&&o.list){var i=o.total,r=o.pageNum,s=o.list,l=void 0===s?[]:s;e.data=l,e.totalItems=i,e.params.pageNum=r,e.$nextTick((function(){e.$refs.cardTable.matchSelected()}))}else e.data=[],e.totalItems=0,e.params.pageNum=1}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error(e)}))},submit:function(){var e=this,t=this.$refs.cardTable.multipleSelection,a=void 0===t?[]:t,n=a.map((function(t){return{contentBizId:e.contentId,relatedId:t.id,module:e.module}}));this.pending=!0,c.CX(n).then((function(t){t.success?(e.$message.success(e.$t("common.base.saveSuccess")),e.$emit("success"),e.visible=!1):e.$message.error(t.message)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error(e)}))},jump:function(e){this.params.pageNum=e,this.onSearch()},handleSizeChange:function(e){this.params.pageNum=1,this.params.pageSize=e,this.onSearch()}},components:(0,s.A)({},xe.name,xe)},Ee=Pe,Oe=(0,k.A)(Ee,De,$e,!1,null,"b4e35cf4",null),Re=Oe.exports,je={name:"card-info",props:{cards:{type:Array,default:function(){return[]}}},data:function(){return{loading:!1,module:"",list:[]}},computed:{contentBizId:function(){return this.$route.query.formContentId},fields:function(){var e=this,t=this.cards.find((function(t){return t.module==e.module}));return(null===t||void 0===t?void 0:t.cardFieldVoList)||[]}},mounted:function(){this.module=this.cards[0].module},methods:{getModuleName:function(e){return Fe.A[e.module]},fetchCardData:function(){var e=this;this.list=[],this.loading=!0,c.gE({contentBizId:this.contentBizId,module:this.module}).then((function(t){var a=t.success,n=t.message,o=t.data;if(!a)return e.$message.error(n);e.list=(null===o||void 0===o?void 0:o.list)||[]}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error(e)}))},openCreateDialog:function(){this.$refs.cardCreateDialog.open()},handleDelete:function(e){var t=this;c.lA({contentBizId:this.contentBizId,module:this.module,relatedId:e}).then((function(e){e.success?(t.fetchCardData(),t.$message.success(t.$t("common.base.deleteSuccess"))):t.$message.error(e.message)}))["catch"]((function(e){return console.error(e)}))}},watch:{module:function(){this.fetchCardData()}},components:(0,s.A)((0,s.A)({},xe.name,xe),Re.name,Re)},ze=je,Me=(0,k.A)(ze,de,me,!1,null,null,null),Ve=Me.exports,Ue=function(){var e=this,t=e._self._c;return t("div",{staticClass:"card-info"},[t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.loading,expression:"loading",modifiers:{lock:!0}}],staticClass:"card-info-list"},[e.loading?e._e():t("connector-card-warper",{key:e.ConnectorCardWarperKey,attrs:{card:e.currentCard,"biz-id":e.formContentId,"from-biz-no":e.connectorCardData.fromBizNo,"show-create-button":e.connectorCardData.showCreateButton,"show-relation-button":e.connectorCardData.showRelationButton,"show-edit-button":e.connectorCardData.showEditButton,"show-delete-button":e.connectorCardData.showDeleteButton,"show-export-button":e.connectorCardData.showExportButton,visible:e.activeId===e.currentCard.bizId}})],1),t("card-pass-create-or-edit-dialog",{ref:"cardPassCreateDialog",on:{refresh:e.handleRefreshDataList}}),t("card-paas-detail-dialog",{ref:"CardPaasDetailDialog"}),t("connector-add-data-dialog",{ref:"ConnectorAddDataDialog",on:{success:e.onConnectAddDataSuccessHandler}})],1)},We=[],He=a(84943),qe=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-create-dialog",attrs:{title:e.title,show:e.visible,width:"800px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"card-create-dialog__box"},[e.visible?t("template-edit-view",{ref:"paasEditForm",staticClass:"edit-view",attrs:{"is-inline-self-data":e.isInlineSelfData,"is-inline-self":""},on:{buttonList:e.handleSetButtonList}}):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},e._l(e.buttonList,(function(a){return t("el-button",{key:a.value,attrs:{type:a.theme,disabled:e.pending},on:{click:function(t){return e.submit(a)}}},[e._v(" "+e._s(a.name)+" ")])})),1)])},Ke=[],Ge=a(18799),Je={name:"card-pass-create-or-edit-dialog",components:(0,s.A)({},Ge["default"].name,Ge["default"]),data:function(){return{visible:!1,pending:!1,title:x.Ay.t("common.base.add"),isInlineSelfData:{},isEdit:!1,buttonList:[]}},methods:{handleSetButtonList:function(e){this.buttonList=e},handleOpenModal:function(e){var t=this;this.buttonList=[];var a=e.cardBizId,i=void 0===a?"":a,s=e.appId,l=e.targetFormTemplateId,c=e.bizId,u=e.config,d=void 0===u?"":u,m=(e.processorInstanceId,e.isDetail),f=void 0!==m&&m,p=(e.isStore,e.customStatus,"string"===typeof d&&d.length>0&&JSON.parse(d)),v=p.connectorInfo,h=void 0===v?{}:v;this.isInlineSelfData={appId:s,templateId:l,contentBizId:c},this.title=c?f?this.$t("common.base.detail"):this.$t("common.base.edit"):this.$t("common.base.add"),this.isEdit=Boolean(c),this.visible=!0,this.$nextTick((0,o.A)((0,n.A)().mark((function e(){return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$refs.paasEditForm.isConnectorFrame=!0,t.$refs.paasEditForm.connectorMessageData=(0,r.A)({cardBizId:i,fromBizId:t.$route.query.formContentId,sourceOperate:5,fromBizTypeId:t.$route.query.formId,isCreate:!c,isEdit:Boolean(c),isDetail:f},h);case 2:case"end":return e.stop()}}),e)}))))},submit:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.pending=!0,a.next=4,t.$refs.paasEditForm.submit(e);case 4:t.pending=!1,t.$message.success(t.$t("common.base.saveSuccess")),setTimeout((function(){t.$emit("refresh"),t.visible=!1}),200),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),t.pending=!1;case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()}}},Ye=Je,Qe=(0,k.A)(Ye,qe,Ke,!1,null,"4f38ebf0",null),Xe=Qe.exports,Ze=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"card-detail-dialog",attrs:{title:e.$t("common.base.detail"),show:e.visible,width:"1200px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"card-detail-dialog__box"},[e.visible?t("template-detail-view",{ref:"paasDetailForm",staticClass:"detail-view",attrs:{"is-inline-self-data":e.isInlineSelfData,"is-inline-self":""}}):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.close")))])],1)])},et=[],tt={name:"card-paas-detail-dialog",components:{"template-detail-view":function(){return Promise.all([a.e(452),a.e(267)]).then(a.bind(a,70452))}},data:function(){return{pending:!1,visible:!1,isInlineSelfData:{}}},methods:{handleOpenModal:function(e){var t=this,a=(e.appId,e.targetFormTemplateId),n=e.bizId,o=(e.config,e.formValue),i=void 0===o?{}:o,r=e.processorInstanceId,s=void 0===r?"":r;this.isInlineSelfData={serialNumber:(null===i||void 0===i?void 0:i.serialNumber)||"",templateId:a,contentBizId:n,formContentId:n,processId:s},this.$nextTick((function(){t.visible=!0}))}}},at=tt,nt=(0,k.A)(at,Ze,et,!1,null,"58cfd2c7",null),ot=nt.exports,it=a(17319),rt=a.n(it),st="PaasIframeDialogComponent",lt="PaasIframeDialog",ct="paas-connector-data",ut={name:"connector-add-data-dialog",props:{appId:{type:String,default:""},formId:{type:String,default:""},formContentId:{type:String,default:""},processorInstanceId:{type:String,default:""},isDetail:{type:Boolean,default:!1}},data:function(){return{visible:!1,loading:!1,connectorStorageData:{},PaasIframeDialogId:lt,title:"",url:""}},computed:{attributes:function(){return this.getAttributes()},iframeUrl:function(){return this.url}},watch:{iframeUrl:function(){this.$nextTick((function(){var e=document.getElementById(lt);e&&e.contentWindow&&e.contentWindow.location.reload(!0)}))}},mounted:function(){this.onWindowMessageHandler()},methods:{getAttributes:function(){var e=this;return{ref:st,class:"connector-add-data-dialog",props:{show:this.visible,title:this.title},on:{closed:function(){e.onCloseWindowHandler()},"update:show":function(){e.onCloseWindowHandler()}},directives:[{name:"loading",value:this.loading}]}},getConnectDataToSessionStorage:function(){var e=sessionStorage.getItem(ct)||"";try{return JSON.parse(e)}catch(t){return{}}},hideLoading:function(){this.loading=!1},hideDialog:function(){this.visible=!1},onPassIframeLoadHandler:function(){this.hideLoading()},onWindowMessageHandler:function(){var e=this;window.addEventListener("message",(function(t){var a=t.data;if(null!==a&&void 0!==a&&a.close&&(console.log(a,e.onWindowMessageHandler.name,e.onWindowMessageHandler.name),e.onCloseWindowHandler(),e.onSuccessHandler()),null!==a&&void 0!==a&&a.refresh&&console.log(a,e.onWindowMessageHandler.name,e.onWindowMessageHandler.name),null!==a&&void 0!==a&&a.ready){var n;console.log(a,e.onWindowMessageHandler.name,e.onWindowMessageHandler.name);var o=document.getElementById(lt),i=e.getConnectDataToSessionStorage(),s=(null===i||void 0===i?void 0:i.toBizTypeId)==(null===(n=e.messageData)||void 0===n?void 0:n.toBizTypeId);console.log(" window.addEventListener ~ isSamePage",s),o&&o.contentWindow&&s&&o.contentWindow.postMessage({task:{},connector:(0,r.A)({},e.messageData)},"*"),e.hideLoading()}}))},onSuccessHandler:function(){this.$emit("success")},onCloseWindowHandler:function(){this.hideDialog(),this.hideLoading()},open:function(e,t,a){var n=this;this.title=e,this.messageData=a,this.url=t,this.showLoading(),this.$nextTick((function(){n.showDialog()}))},showDialog:function(){this.visible=!0},showLoading:function(){this.loading=!0}},render:function(){var e=this.getAttributes();return(0,g.h)("base-modal",rt()([{},e]),[(0,g.h)("div",{class:"paas-iframe-dialog-container"},[this.visible&&(0,g.h)("iframe",{attrs:{frameborder:"0",width:"100%",height:"100%",id:lt,src:this.iframeUrl},on:{load:this.onPassIframeLoadHandler}})])])}},dt=ut,mt=(0,k.A)(dt,Te,ke,!1,null,null,null),ft=mt.exports,pt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"connector-card-container"},[t("connector-module-connector-card",{attrs:{card:e.card,"biz-id":e.bizId,visible:e.visible,"from-biz-no":e.fromBizNo,"show-create-button":e.showCreateButton,"show-relation-button":e.showRelationButton,"show-edit-button":e.showEditButton,"show-delete-button":e.showDeleteButton,"show-export-button":e.showExportButton}})],1)},vt=[],ht=a(25202),gt={name:"connector-card-warper",components:(0,s.A)({},ht.dc.name,ht.dc),props:{bizId:{type:String,default:""},card:{type:Object,default:function(){return{}}},fromBizNo:{type:String,default:""},showCreateButton:{type:Boolean,default:!1},showRelationButton:{type:Boolean,default:!1},showEditButton:{type:Boolean,default:!1},showDeleteButton:{type:Boolean,default:!1},showExportButton:{type:Boolean,default:!1},visible:{type:Boolean,default:!1}}},bt=gt,wt=(0,k.A)(bt,pt,vt,!1,null,null,null),yt=wt.exports,At=a(50224),It={name:"addition-card",props:{cards:{type:Array,default:function(){return[]}},serialNumberForConnector:{type:String,default:""},nodeTemplateIdForConnector:{type:String,default:""},nodeInstanceIdForConnector:{type:String,default:""},processIdForConnector:{type:String,default:""},formContentId:{type:String,default:""},extraData:{type:Object,default:function(){return{}}}},components:(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},Xe.name,Xe),yt.name,yt),ot.name,ot),ft.name,ft),data:function(){return{loading:!1,activeId:"",iframeSrc:"/shb/home/<USER>/connector/card/table",list:[],approveAuth:!1,currentCard:{},connectorCardData:{},ConnectorCardWarperKey:(0,At.uR)()}},computed:{contentBizId:function(){return this.$route.query.formContentId},fields:function(){var e;return(null===(e=this.extraData)||void 0===e?void 0:e.cardFieldVoList)||[]},baseIframeUrl:function(){return"/shb/home/<USER>/connector/card/table"},connectorInfo:function(){var e,t=this.extraData.config,a={};"string"===typeof t&&t.length>0&&(a=(null===(e=JSON.parse(t))||void 0===e?void 0:e.connectorInfo)||{});return a}},watch:{serialNumberForConnector:function(){this.initialize()}},mounted:function(){this.initialize(),this.listenedMessage()},methods:{initialize:function(){this.handleTabClick(this.extraData)},handleTabClick:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){var o,i,r,s;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.currentCard=e,t.loading=!0,o=e.bizId,i=e.config,r=e.appId,s=e.targetFormTemplateId,t.activeId=o,a.next=6,t.fetchAuthList(s,r);case 6:t.handleSetIframeSrc({config:i,isReload:!0,cb:function(){return t.loading=!1}});case 7:case"end":return a.stop()}}),a)})))()},fetchAuthList:function(e,t){var a=this;return(0,o.A)((0,n.A)().mark((function o(){var i,r;return(0,n.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,l.dV)({templateBizId:e,formContentId:a.contentBizId,appId:t});case 2:i=n.sent,i.success&&(a.approveAuth=Boolean(null===i||void 0===i||null===(r=i.data)||void 0===r?void 0:r.approveAuth)||!1);case 4:case"end":return n.stop()}}),o)})))()},handleSetIframeSrc:function(e){var t=e.config,a=(e.isReload,e.cb),n="string"===typeof t&&t.length>0&&JSON.parse(t),o=n.connectorInfo,i=void 0===o?{}:o,r={showEditButton:!0,showRelationButton:!0,showDeleteButton:!0,showCreateButton:(null===i||void 0===i?void 0:i.toBizType)!==He.U.Paas||this.approveAuth,fromBizId:this.contentBizId,fromBizNo:this.serialNumberForConnector,processId:this.processIdForConnector},s=Object.assign({},i,r);for(var l in s)this.$set(this.connectorCardData,l,s[l]);this.ConnectorCardWarperKey=(0,At.uR)(),a()},orderBizIdFindCardItem:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.activeId;return this.cards.find((function(t){return t.bizId===e}))||{}},onDeleteCardItemHandler:function(e){var t=this,a=e.config,n="string"===typeof a&&a.length>0&&JSON.parse(a),o=n.connectorInfo,i=void 0===o?{}:o,r=[e.bizId],s={bizIdList:r,toBizType:null===i||void 0===i?void 0:i.toBizType,toBizTypeId:null===i||void 0===i?void 0:i.toBizTypeId};c.Bg(s).then((function(){t.handleRefreshDataList()}))},handleRefreshDataList:function(){var e=this;setTimeout((function(){e.$refs.cardInfoIframe.contentWindow.postMessage({refresh:!0},"*")}),300)},listenedMessage:function(){var e=this,t=this;window.addEventListener("message",(function(a){var n=a.data,o=n.row,i=void 0===o?{}:o,s=t.extraData,l=(0,r.A)((0,r.A)({},s),i),c={add:function(){var t=null===n||void 0===n?void 0:n.currentPaasDialogUrl,a=(null===n||void 0===n?void 0:n.currentPaasDialogTitle)||"新建表单";e.$refs.ConnectorAddDataDialog.open(a,t,n)},edit:function(){var e;null===(e=t.$refs.cardPassCreateDialog)||void 0===e||e.handleOpenModal(l)},detail:function(){var a,n,o=l.isStore,i=l.customStatus,s=void 0===i?[]:i;o||s&&s.includes(e.$t("common.base.draft"))?null===(a=t.$refs.cardPassCreateDialog)||void 0===a||a.handleOpenModal((0,r.A)((0,r.A)({},l),{},{isDetail:!0})):null===(n=t.$refs.CardPaasDetailDialog)||void 0===n||n.handleOpenModal(l)},delete:function(){t.$confirm(e.$t("view.template.detail.tip4"),e.$t("common.base.toast"),{confirmButtonText:e.$t("common.base.makeSure"),cancelButtonText:e.$t("common.base.cancel"),type:"warning"}).then((function(){t.onDeleteCardItemHandler(l)}))}};Object.keys(c).forEach((function(e){n[e]&&c[e]()}))}))},handleSetIframeStyle:function(){var e=this.$refs.cardInfoIframe.contentWindow.document;e.querySelector("html").style.backgroundColor="#fff"},iframeReload:function(){this.$refs.cardInfoIframe.contentWindow.document.location.reload()},openCreateDialog:function(){this.$refs.cardCreateDialog.open()},onConnectAddDataSuccessHandler:function(){this.handleRefreshDataList()}}},Tt=It,kt=(0,k.A)(Tt,Ue,We,!1,null,null,null),Ct=kt.exports,St=a(71523),Nt=function(){var e=this,t=e._self._c;return t("div",{ref:"bbxBaseLayoutContainer",staticClass:"bbx-base-tile-tab-bar-box"},[t("div",{staticClass:"bbx-base-tab-list",attrs:{id:"bbx-base-tile-tab-list"}},[e._l(e.visibleTabs,(function(a,n){return t("div",{directives:[{name:"show",rawName:"v-show",value:a.tabShow||a.show,expression:"item.tabShow || item.show"}],key:a.tabName||a.key,ref:"listItem",refInFor:!0,staticClass:"bbx-base-tab-list-item",class:[e.nowItem===a.tabName||e.nowItem===a.key?"is-select":""],on:{click:function(t){return e.changeItem(a,n)}}},[t("span",{staticClass:"overHideCon-1"},[e._v(e._s(a.tabLabel||a.name))])])})),t("el-dropdown",{directives:[{name:"show",rawName:"v-show",value:e.showDropdown,expression:"showDropdown"}],attrs:{placement:"top-start",trigger:"click","hide-on-click":!1},on:{command:e.handleCommand}},[t("span",{class:["layout-dropdown-link",e.dropdownActive&&"is-active"]},[t("span",{staticClass:"layout-link-text"},[e._v(e._s(e.dropdownActive))]),t("span",{staticClass:"layout-link-bg"},[t("i",{staticClass:"el-icon-caret-bottom el-icon--right"})])]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",[t("div",{staticClass:"search"},[t("el-input",{attrs:{placeholder:"搜索页签","suffix-icon":"el-icon-search"},on:{input:e.searchTab},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1)]),t("div",{staticClass:"layout-dropdown-content"},[e.dropdownTabs.length?e._l(e.dropdownTabs,(function(a){return t("el-dropdown-item",{key:a.tabName||a.key,staticClass:"layout-dropdown-content__item",attrs:{command:a}},[e._v(" "+e._s(a.tabLabel||a.name)+" ")])})):t("div",{staticClass:"layout-dropdown-empty"},[e._v(" 暂无数据 ")])],2)],1)],1)],2),e.isFormMultiRow?t("div",{staticClass:"bbx-base-tab-bar-layout-icon",on:{click:e.openLayoutModal}},[t("i",{staticClass:"iconfont icon-buju"})]):e._e()])},_t=[],Lt=(a(64162),a(61725)),xt="formWithMultipleColumns",Dt={name:"title-layout-tab-bar",props:{barList:{type:Array,default:function(){return[]}},nowItem:{type:[String,null],default:null},trackConfig:{type:Object,default:function(){return{}}},module:{type:String,default:""},structure:{type:Number,default:1}},watch:{barList:{handler:function(e){var t=this;e.length&&1===this.structure?setTimeout((function(){return t.initCalculateTabs()}),10):this.visibleTabs=e||[]},deep:!0,immediate:!0},nowItem:function(e){this.checkNowItem(e)}},data:function(){return{keyword:"",isSearch:!1,dropdownActiveLabel:"",dropdownActiveValue:"",visibleTabs:[],dropdownTabs:[],storeVisibleTabs:[],storeDropdownTabs:[],containerWidth:0,totalTabWidth:0,tabPaddingWidth:48,dropdownIconWidth:28,dropdownActiveLabelWidth:16,isFontSize:"14px Arial",canvasContext:null}},computed:{isFormMultiRow:function(){return!0},dropdownActive:function(){return this.dropdownActiveLabel||""},showDropdown:function(){var e;return 1===this.structure&&(null===(e=this.dropdownTabs)||void 0===e?void 0:e.length)||this.isSearch}},methods:{openLayoutModal:function(){this.$emit("openLayoutModal")},changeItem:function(e,t){1===this.structure&&(this.dropdownActiveLabel="",this.visibleTabs=this.storeVisibleTabs,this.dropdownTabs=this.storeDropdownTabs),this.$emit("changeItem",e),this.$refs.listItem[t].scrollIntoView({behavior:"instant"})},searchTab:function(e){this.isSearch=!0,e||(this.dropdownTabs=this.storeDropdownTabs),this.dropdownTabs=this.dropdownTabs.filter((function(t){return t.tabLabel.includes(e)}))},handleCommand:function(e){e&&(this.dropdownActiveLabel=e.tabLabel||"",this.dropdownActiveValue=e.tabName,this.$emit("changeItem",e),this.calculateVisibleTabs())},init:function(){this.totalTabWidth=0,this.dropdownActiveLabelWidth=this.tabPaddingWidth,this.visibleTabs=[],this.dropdownTabs=[]},checkNowItem:function(e){this.barList.map((function(t){return t["tabName"]===e&&(t["tabShow"]=!0),t}))},measureTextWidth:function(e,t){this.canvasContext.font=t;var a=this.canvasContext.measureText(e);return Number(a.width.toFixed(2))+this.tabPaddingWidth},calculateVisibleTabs:function(e){var t=this;this.init(),this.dropdownActiveLabel&&(this.dropdownActiveLabelWidth+=this.measureTextWidth(this.dropdownActiveLabel,this.isFontSize)),this.totalTabWidth=this.tabPaddingWidth+this.dropdownIconWidth+this.dropdownActiveLabelWidth,this.barList.forEach((function(a){t.totalTabWidth+=t.measureTextWidth(a.tabLabel||a.name,t.isFontSize);var n=t.dropdownTabs.findIndex((function(e){return e.tabName===t.nowItem}));e&&n>-1&&(t.dropdownActiveLabel=t.dropdownTabs[n].tabLabel||t.dropdownTabs[n].name,t.totalTabWidth+=t.measureTextWidth(t.dropdownTabs[n].tabLabel,t.isFontSize)),t.visibleTabs.findIndex((function(e){return e.tabName===t.dropdownActiveValue}))>-1&&(t.dropdownActiveLabel=""),t.totalTabWidth<t.containerWidth?t.visibleTabs.push(a):t.dropdownTabs.push(a)})),this.keyword&&(this.dropdownTabs=this.dropdownTabs.filter((function(e){return e.tabLabel.includes(t.keyword)}))),this.storeVisibleTabs.length&&this.storeDropdownTabs.length&&!e||(this.storeVisibleTabs=(0,Ce.cloneDeep)(this.visibleTabs),this.storeDropdownTabs=(0,Ce.cloneDeep)(this.dropdownTabs))},initCalculateTabs:function(e){var t;this.containerWidth=(null===(t=this.$refs)||void 0===t||null===(t=t.bbxBaseLayoutContainer)||void 0===t?void 0:t.offsetWidth)||0,this.calculateVisibleTabs(e)}},mounted:function(){var e=this;this.$nextTick((function(){var t=document.createElement("canvas");e.canvasContext=t.getContext("2d"),1===e.structure&&(e.initCalculateTabs(),window.addEventListener("resize",(0,Ce.debounce)((function(){return e.initCalculateTabs("resize")}),200))),e.isFormMultiRow&&setTimeout((function(){var e=(0,A.zO)(window);(0,Lt.Tn)(null===e||void 0===e?void 0:e.activeGuide)&&(null===e||void 0===e||e.activeGuide(xt))}),2e3)}))},beforeDestroy:function(){window.removeEventListener("resize",this.initCalculateTabs)}},$t=Dt,Ft=(0,k.A)($t,Nt,_t,!1,null,"7a6692cf",null),Bt=Ft.exports,Pt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"amount-settlement-list common-list-container__v2"},[t("div",{staticClass:"amount-settlement-list-header"},[t("div",{staticClass:"total-amount"},[t("span",[e._v(e._s(e.$t("common.smartSettlement.amountSettleDisplayNames.total"))+": "+e._s(e.settlementAmountTotal))]),t("span",[e._v(e._s(e.$t("common.smartSettlement.amountSettleDisplayNames.actualTotal"))+": "+e._s(e.actualAmountTotal))])])]),t("div",{staticClass:"amount-settlement-list-wrap"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tableComponentRef",staticClass:"bbx-normal-list-box common-list-table__v2",attrs:{"header-row-class-name":"common-list-table-header__v2",data:e.dataList,stripe:"",border:""}},[e._l(e.columns,(function(a){return[t("el-table-column",{key:a.fieldName,attrs:{label:a.displayName,prop:a.fieldName,"min-width":a.width||160,"show-overflow-tooltip":a.showTooltip||!1},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["settlementAmount"===a.fieldName?[e._v(" "+e._s(3==o.settlementStatus?o.afterAdjustAmount:o.settlementAmount)+" ")]:"settlementStatus"===a.fieldName?[e._v(" "+e._s(e.getStateText(o[a.fieldName]))+" ")]:"settlementFormNo"===a.fieldName?[t("span",{class:e.viewSettleFormAuth?"view-detail-btn":"",on:{click:function(t){return e.openDetail(o)}}},[e._v(e._s(o[a.fieldName]))])]:[e._v(" "+e._s(o[a.fieldName]))]]}}],null,!0)})]})),t("template",{slot:"empty"},[t("no-data-view",{directives:[{name:"show",rawName:"v-show",value:!e.loading,expression:"!loading"}]})],1)],2)],1)])},Et=[],Ot=(a(20592),a(22229));function Rt(e){return Ot.A.get("/api/voice/outside/settlement/pool/getSettlementPoolByModule",e)}var jt={name:"amount-settlement-list",props:{module:{type:String,default:""},moduleSourceId:{type:String,default:""}},data:function(){return{loading:!1,dataList:[],settlementAmountTotal:0,actualAmountTotal:0,columns:[{fieldName:"settlementItemName",displayName:(0,x.t)("common.smartSettlement.standardDisplayNames.settleItem")},{fieldName:"settlementAmount",displayName:(0,x.t)("common.smartSettlement.amountSettleDisplayNames.amount")},{fieldName:"settlementStatus",displayName:(0,x.t)("common.smartSettlement.amountSettleDisplayNames.state")},{fieldName:"settlementFormNo",displayName:(0,x.t)("common.smartSettlement.amountSettleDisplayNames.serialNumber")}],viewSettleFormAuth:!1}},methods:{getViewAuth:function(){var e=this;(0,m.hZ)({templateBizId:"c41a2071-7624-4b2a-b30a-c18ba1273ba3"}).then((function(t){var a,n=(null===t||void 0===t||null===(a=t.data)||void 0===a||null===(a=a.find((function(e){return 1==e.operateType})))||void 0===a?void 0:a.authority)||[];if(n.length){var o,i=getRootWindowInitData(),r=(null===i||void 0===i||null===(o=i.user)||void 0===o?void 0:o.roles)||[];n.forEach((function(t){r.forEach((function(a){t.id==a.id&&(e.viewSettleFormAuth=!0)}))}))}}))},search:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a,o,i,r,s,l;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,r={},e.module&&(r.module=e.module),e.moduleSourceId&&(r.moduleSourceId=e.moduleSourceId),t.next=7,Rt(r);case 7:s=t.sent,l=s.data,e.dataList=null!==(a=null===l||void 0===l?void 0:l.settlePoolList)&&void 0!==a?a:[],e.settlementAmountTotal=null!==(o=null===l||void 0===l?void 0:l.settlementAmountTotal)&&void 0!==o?o:0,e.actualAmountTotal=null!==(i=null===l||void 0===l?void 0:l.actualAmountTotal)&&void 0!==i?i:0,t.next=17;break;case 14:t.prev=14,t.t0=t["catch"](0),console.error(t.t0);case 17:return t.prev=17,e.loading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,null,[[0,14,17,20]])})))()},getStateText:function(e){return 0==e?(0,x.t)("common.smartSettlement.amountSettleStateText[0]"):1==e?(0,x.t)("common.smartSettlement.amountSettleStateText[1]"):2==e?(0,x.t)("common.smartSettlement.amountSettleStateText[2]"):3==e?(0,x.t)("common.smartSettlement.amountSettleStateText[3]"):4==e?(0,x.t)("common.smartSettlement.amountSettleStateText[4]"):5==e?(0,x.t)("common.smartSettlement.amountSettleStateText[5]"):""},openDetail:function(e){var t;if(this.viewSettleFormAuth){var a="/paas/#/template/detail?formContentId=".concat(e.settlementFormId,"&noHistory=1"),n=null===(t=window.frameElement)||void 0===t?void 0:t.getAttribute("id");if(!n)return this.$router.push(a);var o=this.$t("view.template.detail.formDetail");this.$platform.openTab({id:e.settlementFormNo,title:o,close:!0,reload:!0,fromId:n,url:a})}}},mounted:function(){this.getViewAuth(),this.search()}},zt=jt,Mt=(0,k.A)(zt,Pt,Et,!1,null,"d5517586",null),Vt=Mt.exports,Ut=function(){var e=this,t=e._self._c;return t("form-builder",{ref:"form",attrs:{mode:"base",value:e.value,"customer-tags":e.customerTags,fields:e.fields,"is-edit-state":!0,"is-edit":"","form-editing-mode":"edit","form-cell-count":e.formCellCount,"find-builder-that":e.builderThat},on:{getDeleteFiles:e.getDeleteFiles,update:e.update},scopedSlots:e._u([{key:"serialNumber",fn:function(e){var a=e.field,n=e.value;return[t("div",{staticClass:"form-view-row"},[t("form-serial-number-view",{attrs:{value:n,field:a}})],1)]}},{key:"CREATE_USER",fn:function(a){var n=a.field,o=a.value;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[e.isOpenData&&o.staffId?t("open-data",{attrs:{type:"userName",openid:o.staffId}}):[e._v(" "+e._s(o.displayName)+" ")]],2)])]}},{key:"CURRENT_NODE",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[e._v(e._s(e.flowPermiss.currentNodeName))])])]}},{key:"WFLOW_STATUS",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[e._v(e._s(e.flowPermiss.customStatus||e.stateText))])])]}},{key:"sourceTemplateName",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[e._v(e._s(e.initValue.connectorInfo&&e.initValue.connectorInfo.sourceTemplateName))])])]}},{key:"sourceBizNo",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[e.initValue.connectorInfo?t("a",{staticClass:"view-detail-btn",attrs:{href:"javascript:;"},on:{click:function(t){return e.openSourceBizNoTab(e.initValue.connectorInfo)}}},[e._v(" "+e._s(e.initValue.connectorInfo.sourceBizNo)+" ")]):e._e()])])]}}])})},Wt=[],Ht=a(20462),qt=a(95743),Kt={name:"detail-form",props:{fields:{type:Array,default:function(){return[]}},value:{type:Object,default:function(){return{}}},initValue:{type:Object,default:function(){return{}}},customerTags:{type:Array,default:function(){return[]}},formCellCount:{type:Number,default:1},authData:{type:Object,default:function(){return{}}},paasTagInfoVos:{type:Array,default:function(){return[]}},isOpenData:{type:Boolean,default:function(){return!1}},flowPermiss:{type:Object,default:function(){return{}}},stateText:{type:String,default:function(){return""}},formLazyLoad:{type:Boolean,default:function(){return!1}}},methods:{getDeleteFiles:function(e){this.$emit("getDeleteFiles",e)},update:function(e){this.$emit("update",e)},onTagHandler:function(){this.$emit("onTagHandler")},openSourceBizNoTab:function(e){var t=this,a=e.sourceBizType,n=e.sourceBizId,o=e.sourceBizTypeId,i="",r="";switch(a){case"TASK":i="PageTaskView";break;case"EVENT":i="PageEventView";break;case"CUSTOMER":i="PageCustomerView";break;case"PRODUCT":i="PageProductView";break;case"CONTRACT":i="PageContractView",r="&contractTemplateId==".concat(n);break;case"SMART_PLAN":i="PageSmartPlanDetail",r="planId=".concat(o);break;case"INDENT_ADDITIONAL":i="PagePurchaaseOrderManageView",r="id=".concat(n);break;default:break}"PAAS"!=a?this.openTab(i,n,r):l.nL(n).then((function(e){var a,i,r="/template/detail?formId=".concat(o,"&formContentId=").concat(n);r+="&processId=".concat(null!==(a=e.data)&&void 0!==a&&a.processorInstanceId?null===(i=e.data)||void 0===i?void 0:i.processorInstanceId:""),t.jump(r,"detail_view_".concat(n),t.$t("view.template.detail.formDetail"))}))},openTab:function(e,t){var a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=null===(a=window)||void 0===a||null===(a=a.frameElement)||void 0===a?void 0:a.getAttribute("id");(0,Ht.iL)({type:qt.Z[e],key:t,params:n,fromId:o})},jump:function(e,t,a){var n,o=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");if(!o)return this.$router.push(e);this.$platform.openTab({id:"".concat(t,"_").concat(this.templateId),title:a,close:!0,reload:!0,fromId:o,url:"/paas/#".concat(e,"&noHistory=1")})},builderThat:function(){return this}}},Gt=Kt,Jt=(0,k.A)(Gt,Ut,Wt,!1,null,null,null),Yt=Jt.exports,Qt=a(42440),Xt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pro-task-detail"},[t("div",{staticClass:"task-title-top"},[t("div",{staticClass:"title-top-left"},[t("div",{staticClass:"title-name"},[e._v(e._s(e.detailData.projectTaskName))]),t("el-progress",{attrs:{percentage:e.detailData.taskProgress}})],1),t("div",{staticClass:"title-top-right"},[e.isShowEdit?t("el-button",{attrs:{type:"text"},on:{click:e.updateProgress}},[e._v(" "+e._s(e.$t("common.projectManage.detail.taskDetail.text3"))+" ")]):e._e(),e.isShowEdit?t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editTaskDetail()}}},[t("i",{staticClass:"el-icon-edit"}),e._v(e._s(e.$t("common.projectManage.detail.editTask"))+" ")]):e._e()],1)]),t("update-progress-dialog",{ref:"UpdateProgressDialogRef",attrs:{"can-finish":e.canFinish,"task-progress":e.detailData.taskProgress},on:{submit:e.handelProgressSubmit}}),t("div",{staticClass:"tab-wrap"},[t("el-tabs",{attrs:{type:"type-card"},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:e.$t("common.projectManage.detail.tab.workLog"),name:"record"}},[t("div",{staticClass:"tab-item"},[e.pageInit?t("projectTypeWorkLog",{attrs:{"project-id":e.detailData.projectId,"template-id":e.detailData.projectTypeId,"task-id":e.detailData.id,"btn-auth":e.btnAuth,"is-task-cancel-state":e.isTaskCancelState}}):e._e()],1)]),t("el-tab-pane",{attrs:{label:e.$t("common.projectManage.detail.taskDetail.title"),name:"task"}},[t("div",{staticClass:"tab-item"},[t("form-view",{attrs:{fields:e.fields,value:e.detailData},scopedSlots:e._u([{key:"taskStatus",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[t("span",{staticClass:"status-tag",style:e.stateBGC(e.detailData.taskStatus)},[e._v(" "+e._s(e.formatStatus(e.detailData.taskStatus))+" ")])])])]}},{key:"taskType",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[t("span",[e._v(e._s(e.taskTypeMap[e.detailData.taskType]||"")+"/"+e._s(e.detailData.taskFormName))])])])]}},{key:"preTaskInfoList",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},e._l(e.detailData.preTaskInfoList,(function(a,n){return t("span",{key:n,staticClass:"view-detail-btn",on:{click:function(t){return e.openPreTask(a)}}},[e._v(" "+e._s(a.name)+" "),e.detailData.preTaskInfoList.length-1!==n?t("i",[e._v("，")]):e._e()])})),0)])]}},{key:"projectNumber",fn:function(a){var n=a.field;return[t("div",{staticClass:"form-view-row"},[t("label",[e._v(e._s(n.displayName))]),t("div",{staticClass:"form-view-row-content"},[t("span",{staticClass:"view-detail-btn",on:{click:function(t){return e.openProjectViewTab(e.detailData)}}},[e._v(" "+e._s(e.detailData.projectNumber)+" ")])])])]}}])})],1)])],1)],1),t("create-project-task",{ref:"CreateProjectTaskModal",attrs:{"project-id":e.detailData.projectId,"template-id":e.detailData.projectTypeId},on:{updateList:e.getTaskData}})],1)},Zt=[],ea=a(74118),ta=a(70072);function aa(e){if(!e)return"";var t="";switch(e){case"DRAFT":t=(0,x.t)("common.base.draft");break;case"NO_START":t=(0,x.t)("common.base.notStart");break;case"IN_PROGRESS":t=(0,x.t)("common.base.processing");break;case"COMPLETE":t=(0,x.t)("common.base.usualStatus.finish");break;case"CANCEL":t=(0,x.t)("common.base.usualStatus.canceled");break;default:break}return t}function na(e){var t={DRAFT:"#D9D9D9",NO_START:"#FF4D4F",IN_PROGRESS:"#FAAD14",COMPLETE:(0,ta.getThemeColor)(),CANCEL:"#D9D9D9"},a={"background-color":t[e],color:""};return["DRAFT","CANCEL"].includes(e)&&(a.color="#595959"),a}var oa={COMMON_TASK:(0,x.t)("common.projectManage.commonTask"),WORK_TASK:(0,x.t)("common.projectManage.task"),PAAS_TASK:(0,x.t)("common.projectManage.paas")},ia=function(){return[{fieldName:"taskNumber",displayName:(0,x.t)("common.projectManage.detail.projectTaskId"),width:"150px",formType:"text",setting:{},isSystem:1,isSearch:1,tableName:"mission"},{fieldName:"projectTaskName",displayName:(0,x.t)("common.fields.taskName.displayName"),width:"150px",formType:"text",setting:{},isSystem:1,isSearch:1,tableName:"mission"},{fieldName:"taskType",displayName:(0,x.t)("common.projectManage.taskTypeText"),width:"150px",formType:"select",setting:{},isSystem:1,isSearch:0,tableName:"mission"},{fieldName:"taskStatus",displayName:(0,x.t)("common.projectManage.detail.taskDetail.label1"),formType:"select",isSystem:1,isSearch:1,tableName:"mission"},{fieldName:"preTaskInfoList",displayName:(0,x.t)("common.projectManage.detail.taskDetail.label3"),formType:"select",setting:{},isSystem:1,isSearch:0,tableName:"mission"},{fieldName:"projectTaskDesc",displayName:(0,x.t)("common.projectManage.edit.label4"),width:"150px",formType:"text",setting:{},isSystem:1,isSearch:1,tableName:"mission"},{fieldName:"planStartTime",displayName:(0,x.t)("common.form.type.planStartTime"),formType:"date",setting:{},isSystem:1,isSearch:0,tableName:"mission"},{fieldName:"planEndTime",displayName:(0,x.t)("common.projectManage.endTime"),formType:"date",setting:{},isSystem:1,isSearch:0,tableName:"mission"},{fieldName:"projectNumber",displayName:(0,x.t)("common.projectManage.detail.taskDetail.label5"),formType:"select",setting:{},isSystem:1,isSearch:0,tableName:"mission"},{fieldName:"projectName",displayName:(0,x.t)("common.projectManage.detail.taskDetail.label6"),formType:"select",setting:{},isSystem:1,isSearch:1,tableName:"mission"}]},ra=function(){return[{displayName:(0,x.t)("common.base.column.createPerson"),fieldName:"createUserName",formType:"user",isSystem:1},{displayName:(0,x.t)("common.base.column.createTime"),fieldName:"createTime",formType:"datetime",isSystem:1},{displayName:(0,x.t)("common.base.column.updateTime"),fieldName:"updateTime",formType:"datetime",isSystem:1}]},sa=a(37195),la=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("base-modal",{staticClass:"progress-dialog",attrs:{show:e.serviceDialog,title:e.$t("common.projectManage.detail.taskDetail.text3"),width:"520px"},on:{"update:show":function(t){e.serviceDialog=t},close:e.handleClose}},[t("div",{staticClass:"progress-content"},[t("div",{staticClass:"progress-content-top"},[t("el-slider",{attrs:{"show-tooltip":!1},model:{value:e.form.progress,callback:function(t){e.$set(e.form,"progress",t)},expression:"form.progress"}}),t("el-input",{attrs:{type:"number",min:0,max:100},on:{input:e.handelInput},model:{value:e.form.progress,callback:function(t){e.$set(e.form,"progress",e._n(t))},expression:"form.progress"}}),t("span",[e._v("%")])],1),t("div",{staticClass:"progress-content-bottom"},[t("label",[e._v(e._s(e.$t("common.base.updateDescription")))]),t("el-input",{attrs:{type:"textarea",placeholder:e.$t("common.projectManage.detail.taskDetail.placeholder1"),maxlength:"500","show-word-limit":""},model:{value:e.form.desc,callback:function(t){e.$set(e.form,"desc",t)},expression:"form.desc"}})],1)]),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleClose}},[e._v(e._s(e.$t("common.base.close")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v(e._s(e.$t("common.base.save")))])],1)])},ca=[],ua=a(69749),da=(0,g.defineComponent)({name:"update-progress-dialog",props:{taskProgress:{type:Number,default:0},canFinish:{type:Boolean,default:!0}},emits:["submit"],setup:function(e,t){var a=t.emit,i=(0,g.reactive)({serviceDialog:!1,form:{progress:0,desc:""}}),s=function(){i.form.progress=e.taskProgress||0,i.form.desc="",i.serviceDialog=!0},l=function(){i.serviceDialog=!1},c=function(){var t=(0,o.A)((0,n.A)().mark((function t(){return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(100!==i.form.progress){t.next=5;break}if(e.canFinish){t.next=3;break}return t.abrupt("return",(0,Ht.oR)((0,x.t)("common.projectManage.editTip5"),"error"));case 3:return ua.MessageBox.confirm((0,x.t)("common.projectManage.detail.taskDetail.tip1"),(0,x.t)("common.base.tip.changePwd.title"),{type:"warning"}).then((function(){a("submit",i.form)}))["catch"]((function(e){})),t.abrupt("return");case 5:a("submit",i.form);case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),u=function(e){i.form.progress=Math.floor(e)};return(0,r.A)((0,r.A)({},(0,g.toRefs)(i)),{},{openDialog:s,handleSubmit:c,handleClose:l,handelInput:u})}}),ma=da,fa=(0,k.A)(ma,la,ca,!1,null,"063bcce0",null),pa=fa.exports,va=function(){var e=this,t=e._self._c;return t("div",{staticClass:"project-type-work-log"},[e.isShowOperation?t("el-button",{staticClass:"mb_12",attrs:{type:"primary",size:"mini"},on:{click:e.handleClickCreate}},[e._v(e._s(e.$t("common.projectManage.detail.workLog.title1")))]):e._e(),t("el-table",{directives:[{name:"table-style",rawName:"v-table-style"},{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{data:e.dataList,"max-height":"500","header-row-class-name":"common-list-table-header__v2",border:""}},[t("template",{slot:"empty"},[t("BaseListForNoData",{directives:[{name:"show",rawName:"v-show",value:!e.listLoading,expression:"!listLoading"}],attrs:{"notice-msg":e.$t("common.base.tip.noData")}})],1),e._l(e.columns,(function(a,n){return t("el-table-column",{key:"".concat(a.displayName,"_").concat(n),attrs:{prop:a.fieldName,label:a.displayName,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return["createUserName"===a.fieldName?[e._v(" "+e._s(t.row.createUserName)+" ")]:"createTime"===a.fieldName||"updateTime"===a.fieldName?[e._v(" "+e._s(e._f("fmt_datetime")(t.row[a.fieldName]))+" ")]:[e._v(" "+e._s(e._f("fmt_form_field")(t.row[a.fieldName],a))+" ")]]}}],null,!0)})})),t("el-table-column",{attrs:{fixed:"right",label:e.$t("common.base.table.col.operator"),width:"160"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{staticClass:"operation-button edit",on:{click:function(t){return e.handleViewWorkLog(a.row)}}},[e._v(e._s(e.$t("common.base.view")))]),a.row.createUserId===e.loginUserId&&e.isShowOperation?[t("span",{staticClass:"operation-button edit ml_12",on:{click:function(t){return e.handleEditWorkLog(a.row)}}},[e._v(e._s(e.$t("common.base.edit")))]),t("span",{staticClass:"operation-button ml_12 delete",on:{click:function(t){return e.handleDeleteWorkLog(a.row)}}},[e._v(e._s(e.$t("common.base.delete")))])]:e._e()]}}])})],2),e.totalItems?t("div",{staticClass:"project-type-work-log-footer"},[t("div",{staticClass:"total-count"},[t("i18n",{attrs:{path:"common.base.table.totalCount"}},[t("span",{attrs:{place:"count"}},[e._v(e._s(e.totalItems))])])],1),t("el-pagination",{attrs:{background:"","page-size":e.paginationInfo.pageSize,"current-page":e.paginationInfo.pageNum,layout:"prev, pager, next",total:e.totalItems},on:{"current-change":e.handleCurrentChange}})],1):e._e(),t("projectTypeWorkLogDialog",{ref:"workLogDialogRef",attrs:{fields:e.allField,form:e.form,"dialog-title-type":e.dialogTitleType},on:{submitWorkLog:e.submitWorkLog,updateWorkLog:e.updateWorkLog}}),t("projectTypeWorkLogView",{ref:"workLogViewRef",attrs:{fields:e.allField,form:e.form}})],1)},ha=[],ga=a(98609),ba=a.n(ga),wa=a(80906),ya=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"project-type-work-log-dialog",attrs:{show:e.visible,title:e.dialogTitle,width:"600px"},on:{"update:show":function(t){e.visible=t},closed:e.closeDialog}},[e.fields.length?t("form-builder",{directives:[{name:"loading",rawName:"v-loading",value:e.formLoading,expression:"formLoading"}],ref:"workLogFormRef",attrs:{fields:e.fields,value:e.form,mode:"projectTypeWorkLogDialog","form-editing-mode":e.formEditingMode},on:{update:e.update,getDeleteFiles:e.getDeleteFiles}}):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.closeDialog}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",loading:e.showLoading},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.submit")))])],1)],1)},Aa=[],Ia={props:{fields:{type:Array,default:function(){return[]}},form:{type:Object,default:function(){return{}}},dialogTitleType:{type:String,default:""}},emits:["submitWorkLog","updateWorkLog"],setup:function(e,t){var a=t.emit,n=(0,g.ref)(),o=(0,g.reactive)({visible:!1,showLoading:!1,formLoading:!1,needServerDeleFiles:[]}),s=(0,g.computed)((function(){var t={new:x.Ay.t("common.projectManage.detail.workLog.title1"),edit:x.Ay.t("common.projectManage.detail.workLog.title2")};return t[e.dialogTitleType]})),l=(0,g.computed)((function(){return"edit"===e.dialogTitleType?"edit":"create"})),c=function(){o.visible=!0},u=function(){o.visible=!1},d=function(e){a("updateWorkLog",e)},m=function(){try{n.value.validate(!1).then((function(e){if(!e)return!1;o.showLoading=!0,a("submitWorkLog")}))}catch(e){}},f=function(e){o.needServerDeleFiles=[].concat((0,i.A)(o.needServerDeleFiles),(0,i.A)(e))};return(0,r.A)((0,r.A)({workLogFormRef:n,dialogTitle:s},(0,g.toRefs)(o)),{},{update:d,submit:m,openDialog:c,closeDialog:u,formEditingMode:l,getDeleteFiles:f})}},Ta=Ia,ka=(0,k.A)(Ta,ya,Aa,!1,null,"3f2ea6b2",null),Ca=ka.exports,Sa=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"project-type-work-log-dialog",attrs:{show:e.visible,title:e.dialogTitle,width:"600px"},on:{"update:show":function(t){e.visible=t},closed:e.closeDialog}},[t("div",{staticClass:"project-type-waork-log-view"},[e.fields.length?t("form-view",{attrs:{fields:e.fields,value:e.form}}):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])},Na=[],_a={name:"projectTypeViewWorkLog",props:{fields:{type:Array,default:function(){return[]}},form:{type:Object,default:function(){return{}}}},setup:function(){var e=(0,g.reactive)({visible:!1}),t=x.Ay.t("common.projectManage.detail.workLog.title3"),a=function(){e.visible=!0},n=function(){e.visible=!1};return(0,r.A)((0,r.A)({dialogTitle:t},(0,g.toRefs)(e)),{},{openDialog:a,closeDialog:n})}},La=_a,xa=(0,k.A)(La,Sa,Na,!1,null,"8af18d0c",null),Da=xa.exports,$a={name:"project-type-work-log",props:{templateId:{type:[String,Number],default:""},projectId:{type:[String,Number],default:""},taskId:{type:[String,Number],default:""},btnAuth:{type:Object,default:function(){return{}}},isTaskCancelState:{type:Boolean,default:!1}},components:{projectTypeWorkLogDialog:Ca,projectTypeWorkLogView:Da},setup:function(e){var t=(0,g.ref)(),a=(0,g.ref)(),s=(0,g.reactive)({listLoading:!1,columns:[],allField:[],form:{},totalItems:0,dataList:[],paginationInfo:{pageSize:10,pageNum:1},dialogTitleType:"new",isShowBtn:!1}),l=(0,g.computed)((function(){var t;return s.isShowBtn&&(null===e||void 0===e||null===(t=e.btnAuth)||void 0===t?void 0:t.isProjectMember)&&!e.isTaskCancelState})),c=(0,g.computed)((function(){var e,t=(0,A.zO)(window);return(null===t||void 0===t||null===(e=t.loginUser)||void 0===e?void 0:e.userId)||localStorage.getItem("loginUserId")})),u=function(){var t=(0,o.A)((0,n.A)().mark((function t(){var a,o,r,l;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return s.listLoading=!0,t.prev=1,t.next=4,(0,ea.Ul)({tableName:"projectWorkLog",templateId:e.templateId});case 4:if(a=t.sent,o=a.success,r=a.data,l=a.message,o){t.next=8;break}return t.abrupt("return",(0,Ht.oR)(l,"error"));case 8:s.columns=[].concat((0,i.A)(r||[]),(0,i.A)(ra())).filter((function(e){return"attachment"!==e.formType&&"separator"!==e.formType&&"info"!==e.formType&&"autograph"!==e.formType})),t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](1),console.log("error => fetchWorkLogFields",t.t0);case 14:return t.prev=14,s.listLoading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,null,[[1,11,14,17]])})));return function(){return t.apply(this,arguments)}}(),d=function(){var a=(0,o.A)((0,n.A)().mark((function a(){var o,i,l,c;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return"view"!==s.dialogTitleType&&(t.value.formLoading=!0),s.allField=[],a.prev=2,a.next=5,(0,ea.Ul)({tableName:"projectWorkLog",templateId:e.templateId});case 5:if(o=a.sent,i=o.success,l=o.data,c=o.message,i){a.next=9;break}return a.abrupt("return",(0,Ht.oR)(c,"error"));case 9:s.allField=(l||[]).map((function(e){return["remainTime","costTime"].includes(e.formType)&&(e.formType="number"),(0,r.A)((0,r.A)({},e),{},{maxlength:"workContent"==e.fieldName?500:""})})),"new"==s.dialogTitleType&&(s.form=(0,wa.n_)(l,{})),a.next=16;break;case 13:a.prev=13,a.t0=a["catch"](2),console.log("error => fetchWorkLogFieldsDialog",a.t0);case 16:return a.prev=16,"view"!==s.dialogTitleType&&(t.value.formLoading=!1),a.finish(16);case 19:case"end":return a.stop()}}),a,null,[[2,13,16,19]])})));return function(){return a.apply(this,arguments)}}(),m=function(){(0,ea.Bx)({templateId:e.templateId}).then((function(e){var t=e.success,a=e.data,n=e.message;if(!t)return(0,Ht.oR)(n,"error");s.isShowBtn=null===a||void 0===a?void 0:a.status}))},f=function(){var t=(0,o.A)((0,n.A)().mark((function t(){var a,o,i,l,c,u,d,m;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return s.listLoading=!0,t.prev=1,t.next=4,(0,ea.XR)((0,r.A)({projectId:e.projectId,taskId:e.taskId},s.paginationInfo));case 4:if(a=t.sent,o=a.data,i=a.message,l=a.success,l){t.next=8;break}return t.abrupt("return",(0,Ht.oR)(i,"error"));case 8:c=o.list,u=void 0===c?[]:c,d=o.total,m=void 0===d?0:d,s.dataList=null===u||void 0===u?void 0:u.map((function(e){var t;return(0,r.A)((0,r.A)({},e),null!==(t=null===e||void 0===e?void 0:e.attribute)&&void 0!==t?t:{})})),s.totalItems=m,t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](1),console.warn("projectTypeWorkLog fetchTableList =>",t.t0);case 16:return t.prev=16,s.listLoading=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,null,[[1,13,16,19]])})));return function(){return t.apply(this,arguments)}}(),p=function(){t.value&&(t.value.openDialog(),s.dialogTitleType="new",d())},v=function(e){s.form=(0,r.A)((0,r.A)({},e),e.attribute),t.value.openDialog(),s.dialogTitleType="edit",d()},h=function(e){s.form=(0,r.A)((0,r.A)({},e),e.attribute),a.value.openDialog(),s.dialogTitleType="view",d()},b=function(e){var t=e.id;ua.MessageBox.confirm(x.Ay.t("common.projectManage.detail.workLog.deleteLogTip"),x.Ay.t("common.base.toast"),{confirmButtonText:x.Ay.t("common.base.makeSure"),cancelButtonText:x.Ay.t("common.base.cancel"),type:"warning",beforeClose:function(e,a,n){"confirm"===e?(a.confirmButtonLoading=!0,(0,ea.a1)({recordId:t}).then((function(e){if(a.confirmButtonLoading=!1,!e.success)return(0,Ht.oR)(e.message,"error");(0,Ht.oR)(x.Ay.t("common.base.tip.deleteSuccess"),"success"),n(),f()}))):(a.confirmButtonLoading=!1,n())}})},w=function(e){s.paginationInfo.pageNum=e,f()},y=function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t||{}),o=n.fieldName,i=void 0===o?"":o;n.displayName;(0,g.set)(s.form,i,a)},I=function(e){y(e)},T=function(){var t={projectId:e.projectId,taskId:e.taskId,attribute:{}};return(0,wa.rO)(s.allField).forEach((function(e){var a=e.fieldName,n=e.isSystem,o=(e.formType,s.form[a]);n?t[a]="workDate"===a?ba()(o).valueOf():o:t.attribute[a]=o})),t},k=function(){var e=(0,o.A)((0,n.A)().mark((function e(){var a,o,i,l,c,u,d;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a=T(),o="","edit"===s.dialogTitleType?o=(0,ea.AR)((0,r.A)((0,r.A)({},a),{},{id:null===(i=s.form)||void 0===i?void 0:i.id,deleteFiles:(null===(l=t.value)||void 0===l?void 0:l.needServerDeleFiles)||[]})):"new"===s.dialogTitleType&&(o=(0,ea.TF)((0,r.A)({},a))),e.next=6,o;case 6:if(c=e.sent,u=c.success,d=c.message,u){e.next=10;break}return e.abrupt("return",(0,Ht.oR)(d,"error"));case 10:(0,Ht.oR)(x.Ay.t("common.base.tip.createSuccess2"),"success"),f(),t.value.closeDialog(),e.next=18;break;case 15:e.prev=15,e.t0=e["catch"](0),console.warn("submitWorkLog fetchTableList =>",e.t0);case 18:return e.prev=18,console.log("wuwuwu"),t.value.showLoading=!1,e.finish(18);case 22:case"end":return e.stop()}}),e,null,[[0,15,18,22]])})));return function(){return e.apply(this,arguments)}}();return(0,g.onMounted)((function(){m(),f(),u()})),(0,r.A)((0,r.A)({workLogDialogRef:t,workLogViewRef:a},(0,g.toRefs)(s)),{},{handleViewWorkLog:h,handleClickCreate:p,handleEditWorkLog:v,handleDeleteWorkLog:b,handleCurrentChange:w,submitWorkLog:k,updateWorkLog:I,isShowOperation:l,loginUserId:c})}},Fa=$a,Ba=(0,k.A)(Fa,va,ha,!1,null,"797a8f48",null),Pa=Ba.exports,Ea=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"create-project-task-dialog",attrs:{title:e.title,width:"676px",show:e.isShow,"mask-closeable":!1},on:{"update:show":function(t){e.isShow=t}}},[t("div",{staticClass:"add-form task-form"},[t("el-form",{ref:"taskForm",attrs:{"label-position":"top",model:e.form,rules:e.rules}},[t("el-form-item",{attrs:{label:e.$t("common.projectManage.detail.projectTaskId"),prop:"number"}},[t("div",{staticClass:"default-no"},[e._v(e._s(e.form.taskNumber))])]),t("el-form-item",{attrs:{label:e.$t("common.fields.taskName.displayName"),prop:"projectTaskName"}},[t("el-input",{attrs:{maxlength:"20",placeholder:e.$t("common.projectManage.edit.placeHolder1"),"show-word-limit":""},model:{value:e.form.projectTaskName,callback:function(t){e.$set(e.form,"projectTaskName","string"===typeof t?t.trim():t)},expression:"form.projectTaskName"}})],1),t("el-form-item",{attrs:{label:e.$t("common.projectManage.taskTypeText"),prop:"taskFormId"}},[t("el-cascader",{ref:"cascaderType",staticClass:"task-cascader",attrs:{props:e.taskProps,options:e.typeOptions,disabled:"add"!==e.actionType},model:{value:e.form.taskFormId,callback:function(t){e.$set(e.form,"taskFormId",t)},expression:"form.taskFormId"}})],1),t("el-form-item",{attrs:{label:e.$t("common.projectManage.detail.taskDetail.label3"),prop:"preTaskId"}},[t("el-select",{staticClass:"w-100",attrs:{multiple:"",filterable:"",clearable:"","multiple-limit":10,placeholder:e.$t("common.placeholder.select")},model:{value:e.form.preTaskId,callback:function(t){e.$set(e.form,"preTaskId",t)},expression:"form.preTaskId"}},e._l(e.preTaskOption,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:e.$t("common.form.type.planStartTime"),prop:"projectTaskStartTimeField"}},[t("el-select",{staticClass:"w-100",attrs:{filterable:"",clearable:"",disabled:"",placeholder:e.$t("common.projectManage.timeFieldPlace")},model:{value:e.form.projectTaskStartTimeField,callback:function(t){e.$set(e.form,"projectTaskStartTimeField",t)},expression:"form.projectTaskStartTimeField"}},e._l(e.paasTimeFields,(function(e){return t("el-option",{key:e.fieldName,attrs:{label:e.displayName,value:e.fieldName}})})),1)],1),t("el-form-item",{attrs:{label:e.$t("common.projectManage.endTime"),prop:"projectTaskEndTimeField"}},[t("el-select",{staticClass:"w-100",attrs:{filterable:"",clearable:"",disabled:"",placeholder:e.$t("common.projectManage.timeFieldPlace")},model:{value:e.form.projectTaskEndTimeField,callback:function(t){e.$set(e.form,"projectTaskEndTimeField",t)},expression:"form.projectTaskEndTimeField"}},e._l(e.paasTimeFields,(function(e){return t("el-option",{key:e.fieldName,attrs:{label:e.displayName,value:e.fieldName}})})),1)],1),t("el-form-item",{attrs:{label:e.$t("common.projectManage.edit.label4"),prop:"projectTaskDesc"}},[t("el-input",{attrs:{type:"textarea",maxlength:"500",placeholder:e.$t("common.projectManage.edit.placeHolder4"),"show-word-limit":""},model:{value:e.form.projectTaskDesc,callback:function(t){e.$set(e.form,"projectTaskDesc",t)},expression:"form.projectTaskDesc"}})],1)],1)],1),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isShow=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{disabled:e.pending,type:"primary"},on:{click:e.goCreateForm}},[e._v(e._s(e.$t("common.base.save")))]),t("el-button",{attrs:{disabled:e.pending,type:"primary"},on:{click:function(t){return e.goCreateForm("taskExtend")}}},[e._v(e._s(e.$t("common.base.nextStep")))])],1)])},Oa=[],Ra=(a(22665),{name:"create-project-task",data:function(){return{title:(0,x.t)("common.projectManage.detail.editTask"),form:{projectTaskName:"",projectTaskDesc:"",taskType:"",taskFormId:"",preTaskId:[],projectTaskStartTimeField:"",projectTaskEndTimeField:""},rules:{projectTaskName:[{required:!0,message:this.$t("common.projectManage.edit.placeHolder1"),trigger:"blur"},{max:20,message:this.$t("common.projectManage.edit.ruleMess1"),trigger:"blur"}],taskFormId:[{required:!0,message:this.$t("common.projectManage.edit.placeHolder2"),trigger:"change"}]},taskProps:{multiple:!1,value:"id",label:"name",emitPath:!1,children:"formList"},pending:!1,isShow:!1,actionType:"edit",typeOptions:[],preTaskOption:[],paasTimeFields:[]}},props:{templateId:{type:[String,Number],default:""},projectId:{type:[String,Number],default:""}},computed:{editPAASAuth:function(){return!0}},methods:{queryTimeFields:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a,o,i,r;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.paasTimeFields=[],t.next=4,(0,l.QA)({templateBizId:e.form.taskFormId});case 4:if(o=t.sent,i=o.success,r=o.data,i){t.next=9;break}return t.abrupt("return");case 9:e.paasTimeFields=null===r||void 0===r||null===(a=r.paasFormFieldVOList)||void 0===a?void 0:a.filter((function(e){return["date"].includes(e.formType)})),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](0),console.error(t.t0);case 15:case"end":return t.stop()}}),t,null,[[0,12]])})))()},openDialog:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.getTaskTypeData();case 3:return e.preTaskInfoList=(e.preTaskInfoList||[]).map((function(e){return Object.freeze((0,r.A)({label:e.name,value:e.id},e))})),t.$set(t,"form",Se().cloneDeep(e)),t.getPreTaskOption(),t.$refs["taskForm"].resetFields(),a.next=9,t.queryTimeFields();case 9:t.isShow=!0,a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](0),console.error(a.t0);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},goCreateForm:function(e){var t=this;this.pending=!0,this.$refs["taskForm"].validate(function(){var a=(0,o.A)((0,n.A)().mark((function a(o,i){return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.form.projectTaskStartTimeField||!t.form.projectTaskEndTimeField||t.form.projectTaskStartTimeField!==t.form.projectTaskEndTimeField){a.next=2;break}return a.abrupt("return",t.$message.error(t.$t("common.projectManage.settingPassTimeTips")));case 2:if(!o){a.next=9;break}if(t.editPAASAuth){a.next=5;break}return a.abrupt("return",t.$message.error(t.$t("common.projectManage.paasEditAuth")));case 5:return a.next=7,t.saveTaskForm(e);case 7:a.next=13;break;case 9:return t.pending=!1,t.scrollError("is-error"),console.log("error submit!!"),a.abrupt("return",!1);case 13:case"end":return a.stop()}}),a)})));return function(e,t){return a.apply(this,arguments)}}())},getTaskTypeData:function(){var e=this,t={projectTypeId:this.templateId};(0,ea.WX)(t).then((function(t){if(!t.success)return e.$message.error(t.message);e.typeOptions=(t.data||[]).map((function(e,t){return Object.freeze((0,r.A)({id:t},e))}))}))},getPreTaskOption:function(){var e=this,t={projectId:this.projectId,id:this.form.id||"",pageNo:1,pageSize:500};(0,ea.eU)(t).then((function(t){var a=t.data,n=t.message,o=t.success;if(!o)return e.$message.error(n);e.preTaskOption=(null===a||void 0===a?void 0:a.list)||[]}))},saveTaskForm:function(e){var t=this;this.pending=!0,(0,ea.vd)(this.form).then((function(a){if(t.pending=!1,!a.success)return t.$message.error(a.message);if(t.$message.success(t.$t("common.base.saveSuccess")),t.isShow=!1,t.$emit("updateList"),"taskExtend"===e){var n=t.form,o=n.taskFormId,i=void 0===o?"":o,r=n.taskBizId,s=void 0===r?"":r,l=n.taskBizNumber,c=void 0===l?"":l;(0,ea.UJ)({formTemplateBizId:i,formContentId:s}).then((function(e){var a,n,o=null!==(a=null===e||void 0===e?void 0:e.data)&&void 0!==a?a:{},r=o.appId,l=void 0===r?"":r,u=o.nodeInstanceId,d=void 0===u?"":u,m="/paas/#/template/edit?formId=".concat(i,"&appId=").concat(l,"&noHistory=1&formContentId=").concat(s,"&onlyEdit=true");d&&(m="".concat(m,"&nodeInstanceId=").concat(d));var f=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");t.$platform.openTab({url:m,title:"".concat(t.$t("common.otherPageTitle.editForm")).concat(c),id:"edit_view_".concat(c),fromId:f})}))}}))},scrollError:function(e){this.$nextTick((function(){var t=document.getElementsByClassName(e);t[0].scrollIntoView({block:"center",behavior:"smooth"})}))}}}),ja=Ra,za=(0,k.A)(ja,Ea,Oa,!1,null,"26b51522",null),Ma=za.exports,Va=a(21975),Ua={name:"task-project-info",props:{projectTask:{type:Object,default:{projectTaskId:"",state:"",currentNodeIsApproveNode:!1}}},data:function(){return{activeName:"record",formatStatus:aa,stateBGC:na,taskTypeMap:oa,fields:ia(),btnAuth:{},detailData:{},pageInit:!1}},computed:{isShowEdit:function(){var e,t,a,n,o;return(null===(e=this.projectTask)||void 0===e?void 0:e.state)!==Va.A.DRAFT.value&&!(null!==(t=this.projectTask)&&void 0!==t&&t.currentNodeIsApproveNode)&&this.detailData.hasEditAuth&&("NO_START"===(null===(a=this.detailData)||void 0===a?void 0:a.taskStatus)||"IN_PROGRESS"===(null===(n=this.detailData)||void 0===n?void 0:n.taskStatus))&&"CANCEL"!==(null===(o=this.detailData)||void 0===o?void 0:o.taskStatus)},isTaskCancelState:function(){var e;return"CANCEL"===(null===(e=this.detailData)||void 0===e?void 0:e.taskStatus)},canFinish:function(){var e;return(null===(e=this.projectTask)||void 0===e?void 0:e.state)===Va.A.FINISHED.value}},components:(0,s.A)((0,s.A)((0,s.A)({},pa.name,pa),"projectTypeWorkLog",Pa),"CreateProjectTask",Ma),mounted:function(){this.getTaskData()},methods:{editTaskDetail:function(){var e,t=Se().cloneDeep(this.detailData);t.preTaskId=(t.preTaskInfoList||[]).map((function(e){return e.id})),null===(e=this.$refs.CreateProjectTaskModal)||void 0===e||e.openDialog(t)},getTaskData:function(){var e,t=this;(0,ea._I)({id:null===(e=this.projectTask)||void 0===e?void 0:e.projectTaskId}).then(function(){var e=(0,o.A)((0,n.A)().mark((function e(a){return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a.success){e.next=2;break}return e.abrupt("return",t.$message.error(a.message));case 2:t.detailData=a.data||{},t.pageInit=!0,t.getBtnAuth();case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},openPreTask:function(e){var t,a=this,n=null===(t=window.frameElement)||void 0===t?void 0:t.getAttribute("id");if("WORK_TASK"===e.taskType)e.taskBizId?(0,Ht.iL)({type:sa.Z.PageTaskView,key:e.taskBizId,params:"noHistory=1",reload:!0,fromId:n}):this.$message.error((0,x.t)("common.projectManage.editTip2"));else if("PAAS_TASK"===e.taskType){e.taskFormName;var o=e.taskBizId,i=void 0===o?"":o,r=e.taskFormId,s=void 0===r?"":r;i?(0,ea.UJ)({formTemplateBizId:s,formContentId:i}).then((function(e){var t,o=null!==(t=null===e||void 0===e?void 0:e.data)&&void 0!==t?t:{},r=o.appId,l=void 0===r?"":r,c=o.processId,u=void 0===c?"":c,d="/paas/#/template/detail?formId=".concat(s,"&formContentId=").concat(i,"&appId=").concat(l);u&&(d="".concat(d,"&processId=").concat(u)),a.$platform.openTab({id:"detail_view_".concat(s),title:a.$t("common.pageTitle.otherPageTitle.jobNotificationItem.title1"),close:!0,reload:!0,fromId:n,url:d})})):this.$message.error((0,x.t)("common.projectManage.editTip4"))}else(0,Ht.iL)({type:sa.Z.PageTaskManageView,params:"id=".concat(e.id,"&projectId=").concat(this.detailData.projectId),reload:!0,key:e.id,fromId:n})},getBtnAuth:function(){var e=this;(0,ea.m3)({projectId:this.detailData.projectId}).then((function(t){if(!t.success)return e.$message.error(t.message);e.btnAuth=t.data}))},updateProgress:function(){var e;null===(e=this.$refs.UpdateProgressDialogRef)||void 0===e||e.openDialog()},handelProgressSubmit:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){var o,i,s;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=(0,r.A)((0,r.A)({},e),{},{id:t.detailData.id,projectId:t.detailData.projectId}),a.next=4,(0,ea.R6)(i);case 4:if(s=a.sent,s.success){a.next=7;break}return a.abrupt("return",t.$message.error(s.message));case 7:null===(o=t.$refs.UpdateProgressDialogRef)||void 0===o||o.handleClose(),(0,Ht.oR)((0,x.t)("common.base.tip.updateSuccess"),"success"),t.getTaskData(),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](0),console.error(a.t0);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},openProjectViewTab:function(){var e,t=null===(e=window.frameElement)||void 0===e?void 0:e.getAttribute("id");(0,Ht.iL)({type:sa.Z.PageProjectManageView,params:"templateId=".concat(this.detailData.projectTypeId,"&id=").concat(this.detailData.projectId),reload:!0,fromId:t})}}},Wa=Ua,Ha=(0,k.A)(Wa,Xt,Zt,!1,null,"8bcfaf34",null),qa=Ha.exports,Ka=a(56268),Ga=a(16828),Ja=function(){var e,t=this,a=t._self._c;return a("div",{staticClass:"group-position",class:[t.isRetraction&&"retraction",!(null!==(e=t.groupFields)&&void 0!==e&&e.length)&&"hidden",t.isFixed&&"isFixed"],on:{mouseenter:function(e){t.isRetraction=!1},mouseleave:t.handleRetraction}},[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.isRetraction,expression:"!isRetraction"}],staticClass:"content"},[a("div",{staticClass:"title mar-b-8"},[a("div",{staticClass:"content"},[t._v(t._s(t.$t("common.form.type.group")))]),a("div",{staticClass:"user-card-triggle",on:{click:t.handleFixed}},[t.isFixed?a("i",{staticClass:"iconfont icon-pushpin-fill"}):a("i",{staticClass:"iconfont icon-pushpin"})])]),a("el-menu",{staticClass:"group-menu",attrs:{"default-active":t.activeIndex},on:{select:t.handleSelect}},t._l(t.groupFields,(function(e){return a("div",{key:e.fieldName},["separator"===e.formType&&e.fieldName?a("el-menu-item",{key:e.fieldName,attrs:{index:e.fieldName}},[a("div",{staticClass:"group-item"},[t._v(t._s(e.displayName))])]):t._e()],1)})),0)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isRetraction,expression:"isRetraction"}],staticClass:"content content-retraction"},t._l(t.groupFields,(function(e){return a("div",{key:e.fieldName,staticClass:"item",class:t.activeIndex===e.fieldName&&"is-active"},["separator"===e.formType?a("div",{staticClass:"item-content"}):t._e()])})),0)])},Ya=[],Qa=(a(71620),{name:"group-position",props:{fields:{type:Array,default:function(){return[]}},formRef:{type:Object,default:function(){return{}}}},mounted:function(){var e;this.activeIndex=(null===(e=this.groupFields[0])||void 0===e?void 0:e.fieldName)||""},watch:{formRef:{handler:function(e,t){var a,n,o=null===t||void 0===t||null===(a=t.$refs)||void 0===a||null===(a=a.form)||void 0===a?void 0:a.$el;o&&o.removeEventListener("scroll",this.scrollHandler);var i,r,s=null===e||void 0===e||null===(n=e.$refs)||void 0===n||null===(n=n.form)||void 0===n?void 0:n.$el;s&&(this.scrollHandler=(0,Ce.throttle)(this.handleScroll,100),s.addEventListener("scroll",this.scrollHandler),null!==(i=this.formRef)&&void 0!==i&&null!==(i=i.$refs)&&void 0!==i&&null!==(i=i.form)&&void 0!==i&&i.$el&&null!==(r=this.groupFields)&&void 0!==r&&r.length&&this.formRef.$refs.form.$el.style.setProperty("padding-right","150px","important"))},immediate:!0}},computed:{groupFields:function(){var e;return(null===(e=this.fields)||void 0===e?void 0:e.filter((function(e){return"separator"===e.formType&&e.fieldName})))||[]}},data:function(){return{isFixed:!0,isRetraction:!1,activeIndex:""}},methods:{handleFixed:function(){var e,t,a=null===(e=this.formRef)||void 0===e||null===(e=e.$refs)||void 0===e||null===(e=e.form)||void 0===e?void 0:e.$el;a&&null!==(t=this.groupFields)&&void 0!==t&&t.length&&(this.isFixed=!this.isFixed,!this.isFixed&&(this.isRetraction=!0),a.style.setProperty("padding-right",this.isFixed?"150px":"64px","important"))},handleSelect:function(e){var t,a,n=this;this.activeIndex=e;var o=(null===(t=Array.from((null===(a=this.formRef.$refs)||void 0===a||null===(a=a.form)||void 0===a||null===(a=a.$el)||void 0===a?void 0:a.children)||[]))||void 0===t?void 0:t.filter((function(e){var t;return null===(t=e.children[0])||void 0===t||null===(t=t.className)||void 0===t?void 0:t.includes("section-title")})))||[];this.groupFields.forEach((function(e,t){var a;e.fieldName===n.activeIndex&&(null===(a=n.formRef.$refs)||void 0===a||null===(a=a.form)||void 0===a||a.$el.scrollTo({top:o[t].offsetTop-30,behavior:"smooth",block:"start"}))}))},handleRetraction:function(){this.isFixed||(this.isRetraction=!0)},handleScroll:function(){for(var e,t,a=null===(e=this.formRef)||void 0===e||null===(e=e.$refs)||void 0===e||null===(e=e.form)||void 0===e?void 0:e.$el,n=(null===(t=Array.from(a.children||[]))||void 0===t?void 0:t.filter((function(e){var t;return null===(t=e.children[0])||void 0===t||null===(t=t.className)||void 0===t?void 0:t.includes("section-title")})))||[],o=a.scrollTop,i=n.length-1;i>=0;i--){var r=n[i],s=r.offsetTop;if(s-50<=o){var l=this.groupFields[i];l&&this.activeIndex!==l.fieldName&&(this.activeIndex=l.fieldName);break}}}}}),Xa=Qa,Za=(0,k.A)(Xa,Ja,Ya,!1,null,"085a2814",null),en=Za.exports,tn=a(92648),an=a(78670),nn=a(66500),on=a(56582),rn=a(13465),sn=a(99376),ln=a(63590),cn=a(92060),un=a(51668),dn=a(94848),mn=(a(62197),a(88445)),fn=a(23559),pn=a(42998),vn=a(39723),hn=a(89546),gn=a(16029),bn=a(9138),wn=(a(78831),a(736)),yn=a(46202),An=a(57670),In=a(38715),Tn=a(48956),kn=a(50422),Cn=a(46995),Sn=a(72964),Nn=a(78485),_n=(0,g.defineComponent)({name:An.qq.IntelligentTagsTaggingView,props:{show:{type:Boolean,default:function(){return!0}},from:{type:String,default:function(){return"PAAS"}},tagsMainDataList:{type:Array,default:function(){return[]}},bizData:{type:Object,default:function(){return{}}},updateTagsMainDataList:{type:Function},showType:{type:String,default:function(){return"text"}},showTaggingButton:{type:Boolean,default:function(){return!0}}},setup:function(e,t){var a=t.expose,i=(0,g.toRefs)(e),l=i.show,c=i.from,u=i.tagsMainDataList,d=i.bizData,m=i.updateTagsMainDataList,f=i.showType,p=i.showTaggingButton,v=(0,Sn.rd)(),h=v.query,b=(0,g.computed)((function(){return"icon"===f.value?{normalShowType:"icon"}:{normalShowType:"text"}})),w=(0,g.computed)((function(){return"icon"===f.value?{showText:!1}:{showText:!0}})),y=(0,g.ref)([]),A=(0,Cn.P)((0,Tn.wY)(c.value,h.formId,An.Ns.Detail)),I=A.taggingBindAttr,T=A.taggingBindOn,k=A.taggingCheckedTags,C=(0,g.computed)((function(){return Object.assign(I.value,{placement:"bottom",showGroupIcon:!1})})),S=(0,g.computed)((function(){return(0,kn.Xk)(c.value,"bizIdKey")})),N=(0,g.computed)((function(){return(0,kn.Xk)(c.value,"bizNoKey")})),_=function(e){return e.map((function(e){return e.labelId}))},L=function(){var e=(0,o.A)((0,n.A)().mark((function e(){var t,a,o,i,r,s=arguments;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:u.value,t.length){e.next=3;break}return e.abrupt("return",y.value=[]);case 3:return e.prev=3,e.next=6,(0,Nn.DZ)(_(t));case 6:a=e.sent,o=a.data,i=void 0===o?[]:o,r=a.success,r&&(y.value=(0,In.fR)(i,t),k.value=(0,Ce.cloneDeep)(y.value)),e.next=16;break;case 13:e.prev=13,e.t0=e["catch"](3),console.error("[ getTagListFromIds error ]",e.t0);case 16:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(){return e.apply(this,arguments)}}(),x=function(){return[(0,s.A)((0,s.A)({},S.value,d.value[S.value]),N.value,d.value[N.value])]},D=function(e){L(e),(0,Ce.isFunction)(m.value)&&m.value(e)},$=function(e){y.value=y.value.map((function(t){var a=t.labelId||t.id||"";return a===e.id&&(t.name=e.name,t.logoColor=e.logoColor),t}))},F=[(0,g.watchEffect)((function(){L()}))];return(0,g.onBeforeUnmount)((function(){F.forEach((function(e){return e()}))})),a({getBizObjArrayData:x,refreshData:D}),function(){return l.value?(0,g.h)("div",{class:"biz-intelligent-tags__tagging-view"},[(0,g.h)("div",{class:"biz-process-intelligent-tags"},[(0,g.h)(wn.A,{attrs:{type:"detail",tagsList:y.value,config:b.value}})]),(0,g.h)("div",{class:["biz-process-intelligent-tag-button",0===y.value.length?"biz-process-intelligent-tag-button__plain":null]},[p.value?(0,g.h)(yn.A,{ref:"taggingCompRef",attrs:{tagsList:y.value},on:(0,r.A)({detailViewLabel:$},T.value),props:(0,r.A)({},(0,r.A)((0,r.A)({},C.value),{},{value:y.value,showText:w.value.showText,buttonText:"标签"}))}):null])]):null}}}),Ln=a(50152),xn=a(14389),Dn=(0,g.defineComponent)({components:{IntelligentTagsTaggingView:_n},setup:function(){var e=(0,Sn.rd)(),t=e.query,a=function(e,t){var a=(0,Tn.wY)(e,t);xn.A.commit("".concat(An.Do.Component,"/").concat(Ln.nv),a)};return(0,g.onBeforeMount)((function(){a(An.Vt.PAAS,t.formId||xn.A.state.common.detailPageParams.formTemplateId)})),(0,Cn.F)(An.Vt.PAAS)}}),$n=a(57818);a(62830);function Fn(e){return Ot.A.post("/api/voice/outside/container/button/getButtonByModule",e)}var Bn=a(70362);function Pn(){return En.apply(this,arguments)}function En(){return En=(0,o.A)((0,n.A)().mark((function e(){return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,ta.getAllGrayInfo)().then((function(e){return(null===e||void 0===e?void 0:e.SYSTEM_BUTTON_CODE)||!1}))["catch"]((function(e){console.log(e)})));case 1:case"end":return e.stop()}}),e)}))),En.apply(this,arguments)}var On=function(){var e=(0,o.A)((0,n.A)().mark((function e(t,a){var o,i,l,c,u,d,m=arguments;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=m.length>2&&void 0!==m[2]?m[2]:{},i=m.length>3&&void 0!==m[3]?m[3]:"",l=!1,e.prev=3,e.next=6,Pn();case 6:l=e.sent,e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](3),console.error(e.t0,"getCarryBtnGray is Error");case 12:if(l){e.next=14;break}return e.abrupt("return",Promise.reject("getCarryBtnGray is false"));case 14:if(c=(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},Bn.qy.Customer,{module:Bn.qy.Customer,moduleId:1,isEdit:!1}),Bn.qy.Product,{module:Bn.qy.Product,moduleId:2,isEdit:!1}),Bn.qy.TASK,{module:Bn.qy.TASK,moduleId:i,isEdit:!1}),Bn.qy.PAASSYSTEM,{module:Bn.qy.PAASSYSTEM,moduleId:i,isEdit:!1}),u=(0,s.A)((0,s.A)((0,s.A)({},Bn.cS.MobileDetail,"mobile"),Bn.cS.PcDetail,"detail"),Bn.cS.PcList,"list"),c[t]){e.next=18;break}return e.abrupt("return",console.error("mode is Miss"));case 18:return d=(0,r.A)((0,r.A)({},c[t]),o),e.abrupt("return",Fn(d).then((function(e){if(0===e.status){var t=e.data.filter((function(e){var t;return u[a]&&!0===(null===(t=e.show)||void 0===t?void 0:t[u[a]])}));return t}on.Ay.notification({type:"error",title:(0,x.t)("common.base.fail"),message:e.message})})));case 20:case"end":return e.stop()}}),e,null,[[3,9]])})));return function(t,a){return e.apply(this,arguments)}}(),Rn=(0,g.reactive)({stashData:{}});function jn(e,t){return zn.apply(this,arguments)}function zn(){return zn=(0,o.A)((0,n.A)().mark((function e(t,a){var o,i,r,s,l;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,a){e.next=3;break}return e.abrupt("return",Promise.resolve());case 3:return o=["http"],i=[Ot.A],t&&(r=Object.keys(t)||[],r.forEach((function(e){o.push(e),i.push(t[e])}))),o.push(a),s=(0,$n.A)(Function,o),e.next=10,s.apply(void 0,i);case 10:return l=e.sent,s=null,e.abrupt("return",l);case 15:return e.prev=15,e.t0=e["catch"](0),console.error(e.t0,a,"<<<<<< makeJsCodeFnc is Error"),e.abrupt("return",!0);case 19:case"end":return e.stop()}}),e,null,[[0,15]])}))),zn.apply(this,arguments)}var Mn,Vn=function(){function e(){return t.apply(this,arguments)}function t(){return t=(0,o.A)((0,n.A)().mark((function e(){var t,a=arguments;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=[],e.prev=1,e.next=4,On.apply(void 0,a);case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),console.error(e.t0,"getBtnJsCodeStashData is Error");case 10:console.log(t,"<<<<btnArr"),t.forEach((function(e){Rn.stashData=(0,r.A)((0,r.A)({},Rn.stashData),{},(0,s.A)({},e.buttonKey,e))}));case 12:case"end":return e.stop()}}),e,null,[[1,7]])}))),t.apply(this,arguments)}function a(){}function i(e){var t;return Reflect.has(Rn.stashData,e)?null===(t=Rn.stashData[e])||void 0===t||null===(t=t.event)||void 0===t?void 0:t[0].codeContent:null}return{getBtnJsCodeStashData:e,setBtnJsCodeStashData:a,makeJsCodeFnc:jn,getBtnJsCodeValueInStashData:i}},Un=a(51618),Wn=(0,ta.useFormTimezone)(),Hn=(Wn.disposeFormViewTime,Wn.disposeFormViewTimeV2),qn=(Wn.disposeFormSubmitTime,Wn.disposeFormSubmitTimeV2),Kn=Vn(),Gn=Kn.getBtnJsCodeValueInStashData,Jn=Kn.getBtnJsCodeStashData,Yn={name:"template-detail-view",mixins:[p,Dn],provide:function(){return{isEdit:!1,displayNameLanguageArr:this.displayNameLanguageArr}},props:{isInlineSelf:{type:Boolean,default:function(){return!1}},isInlineSelfData:{type:Object,default:function(){return{}}}},components:(Mn={},(0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)(Mn,G.name,G),J.A.name,J.A),Q.A.name,Q.A),Y.A.name,Y.A),oe.name,oe),ue.name,ue),Ve.name,Ve),Ct.name,Ct),Vt.name,Vt),St.A.name,St.A),(0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)(Mn,Bt.name,Bt),Yt.name,Yt),Qt.A.name,Qt.A),Ka.F.name,Ka.F),Ga.A.name,Ga.A),"TaskProjectInfo",qa),"GroupPosition",en)),data:function(){return{init:!1,pending:!1,loading:!0,popoverVisible:!1,isDelete:!1,isFlowForm:!1,collapseDirection:"",templateName:"",fields:[],systemFields:[],customFields:[],value:{},shareInfo:{},currLogList:[],logList:[],satisfactionData:[],cardList:[],additionCardList:[],flowPermiss:{},operaButton:{},commentPending:!1,paasFormValueList:[],originPaasFormValueList:[],rightContentTabName:"",baseCollapseComponentKey:(0,vn.u)(),paasTagInfoVos:[],tagIdList:[],customerTags:[],printTemplateList:[],printTemplateListNew:[],showPrintTemplateBtn:!1,initValue:{},printBtnState:!1,formTemplateId:"",createdUserId:"",isTaskFrame:!1,leftResizeObserver:null,approveSignConfig:{},formCellCount:1,needServerDeleFiles:[],oneLayoutActiveTab:{component:"detail-form",key:"detail"},baseLayout:2,pageButtonList:[],pageButtonLoading:!1,ButtonSetDetailForShowPositionEnum:Bn.cS,backButton:[],bizId:"",loadingRefuse:!0,displayNameLanguageArr:[],listPageIsTableStyle:!0,settlementPoolByModuleCheck:!1}},computed:(0,r.A)((0,r.A)({},(0,z.aH)(["user","common"])),{},{serialNumber:function(){return this.isInlineSelf?this.isInlineSelfData.serialNumber:this.$route.query.serialNumber},appId:function(){var e;return this.isInlineSelf?null===(e=this.isInlineSelfData)||void 0===e?void 0:e.appId:this.$route.query.appId},filteredButtons:function(){return this.init&&this.flowPermiss.buttons?this.flowPermiss.buttons.filter((function(e){return![1,4,6,7].includes(e.code)})):[]},templateId:function(){return this.isInlineSelf?this.isInlineSelfData.templateId:this.$route.query.formId||this.common.detailPageParams.formTemplateId},formContentId:function(){return this.isInlineSelf?this.isInlineSelfData.formContentId:this.$route.query.formContentId},processId:function(){return this.isInlineSelf?this.isInlineSelfData.processId:this.$route.query.processId||this.common.detailPageParams.processorInstanceId},isSettlementForm:function(){return"c41a2071-7624-4b2a-b30a-c18ba1273ba3"==this.templateId},isSettlementChangeForm:function(){return"2c6a85de-35d3-4681-9dbe-3c378cd12b83"==this.templateId},stateColor:function(){return Va.A.getColorForFlow(this.flowPermiss.status)},stateText:function(){return Va.A.getName(this.flowPermiss.status)},currFlow:function(){return this.currLogList.find((function(e){return e.isDefault}))||{}},isParallelNode:function(){return this.currLogList.length>1},firstLogItemData:function(){var e=this.logList[0];if(e){var t=(null===e||void 0===e?void 0:e.nodeType)===f.A.END_NODE,a=(null===e||void 0===e?void 0:e.nodeType)===f.A.START_NODE;return{firstLogItem:e,isEndNode:t,isStartNode:a}}return{firstLogItem:{},isEndNode:!0,isStartNode:!1}},endNodeLog:function(){return this.logList.find((function(e){return e.nodeType==f.A.END_NODE}))||{}},currentNodeLog:function(){return this.currLogList.length?this.currFlow:this.endNodeLog},satisfactionList:function(){var e;return(null===(e=this.currentNodeLog)||void 0===e?void 0:e.returnVisitSettingVOS)||[]},isShowAdditionCard:function(){return!0},isNimblePaasPrintGray:function(){var e,t;return null!==(e=null===(t=this.user)||void 0===t||null===(t=t.grayAuth)||void 0===t?void 0:t.includes(mn.l.NimblePaasPrint))&&void 0!==e&&e},isContainerGray:function(){return(0,gn.pX)()||!1},tabs:function(){var e=[];return this.isShowAdditionCard&&(e=(this.additionCardList||[]).map((function(e){return{key:"add_".concat(e.bizId),name:e.name,component:Ct.name,show:!0,isAdditionTab:!0,extraData:e}}))),[{key:"task-pro-tab",name:this.$t("common.projectManage.detail.taskDetail.title"),component:qa.name,show:!!this.projectTaskId},{key:"flow-record",name:this.$t("view.template.detail.flowRecord"),component:G.name,show:this.isFlowForm},{key:"satisfaction-data",name:this.$t("view.template.detail.questionnaireData"),component:oe.name,show:this.isFlowForm&&this.satisfactionData.length>0},{key:"card-info",name:this.$t("view.template.detail.relatedComponents"),component:Ve.name,show:this.cardList.length>0},{key:"amount-settlement-list",name:this.$t("common.smartSettlement.amountSettle"),component:Vt.name,show:this.hasSettlePoolAuth&&this.settlementPoolByModuleCheck}].concat((0,i.A)(e)).filter((function(e){return e.show}))},isOpenData:function(){return this.$platform.isOpenData},auth:function(){var e,t=(0,A.zO)(window);return(null===t||void 0===t||null===(e=t.loginUser)||void 0===e?void 0:e.auth)||{}},hasSettlePoolAuth:function(){return 3==this.auth.INTELLIGENT_SETTLE_POOL_VIEW},serialNumberForConnector:function(){var e;return(null===(e=this.value)||void 0===e?void 0:e.serialNumber)||""},nodeTemplateIdForConnector:function(){var e;return(null===(e=this.currentNodeLog)||void 0===e?void 0:e.nodeTemplateId)||""},nodeInstanceIdForConnector:function(){var e;return(null===(e=this.currentNodeLog)||void 0===e?void 0:e.nodeInstanceId)||""},processIdForConnector:function(){return this.$route.query.processId},authData:function(){return this.user.authData||{}},isShowEdit:function(){return Va.A.OFFED.value!==this.flowPermiss.status},isShowMoreOperation:function(){return this.pageButtonList.length},currentNodeIsStartNode:function(){var e;return(null===(e=this.currFlow)||void 0===e?void 0:e.nodeType)===f.A.START_NODE},customerFieldName:function(){var e=(this.fields||[]).find((function(e){return"customer"===e.formType}))||{};return(null===e||void 0===e?void 0:e.fieldName)||""},isExternalCreate:function(){var e;return 2===(null===(e=this.value["CREATE_USER"])||void 0===e?void 0:e.type)},isFormCreator:function(){var e,t=null===(e=this.user)||void 0===e||null===(e=e.loginUser)||void 0===e?void 0:e.userId;return this.createdUserId===t},formMultiRowGrayOpened:function(){return(0,gn.Nh)()},baseLayoutTabBarList:function(){return(0,tn.kh)()},layoutTabBarList:function(){return 1===this.baseLayout?this.baseLayoutTabBarList.concat(this.tabs):this.baseLayoutTabBarList},getOneLayoutComponentAttr:function(){return tn.vU.call(this)},getOneLayoutComponentAttrForForm:function(){return tn.vU.call(this,"detail")},hasCustomerField:function(){var e,t;return null!==(e=null===(t=this.fields)||void 0===t?void 0:t.some((function(e){return"customer"===e.formType})))&&void 0!==e&&e},statePaused:function(){var e;return null===(e=this.flowPermiss)||void 0===e?void 0:e.pauseFlag},editOperatorValue:function(){return this.isShowEdit&&this.authData.editAuth},btnJsCodeStashData:function(){return Rn},notShowMoreBaseButton:function(){var e;return 0===this.filteredButtons.length&&!("{}"!==JSON.stringify(this.authData)&&this.isFormCreator)&&0===this.satisfactionList.length&&!this.shareInfo.isOpen&&!("{}"!==JSON.stringify(this.authData)&&this.isShowEdit&&null!==(e=this.authData)&&void 0!==e&&e.editAuth)},printTemplateAllList:function(){return this.printTemplateList.map((function(e){return e.isOld=!0,e.templateName=e.name,e})).concat(this.printTemplateListNew)}}),created:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.processId){t.next=3;break}return t.next=3,e.fetchQueryParamsFromContentId(e.formContentId);case 3:e.getPageModeForStorage();case 4:case"end":return t.stop()}}),t)})))()},mounted:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.initLayoutData(),t.next=3,e.initData();case 3:e.setSettlementPoolByModuleCheck(),e.rerenderBaseCollapseComponentHandler(),window.addEventListener("message",function(){var t=(0,o.A)((0,n.A)().mark((function t(a){var o,i;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=a.data,i=void 0===o?{}:o,"shb.system.refresh"!==(null===i||void 0===i?void 0:i.action)){t.next=5;break}return t.next=4,e.initData();case 4:e.rerenderBaseCollapseComponentHandler();case 5:"shb.frame.activatedPage"===(null===i||void 0===i?void 0:i.action)&&e.rerenderBaseCollapseComponent();case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),!1);case 6:case"end":return t.stop()}}),t)})))()},beforeDestroy:function(){this.leftResizeObserver&&this.formMultiRowGrayOpened&&this.leftResizeObserver.unobserve(this.$refs.form.$el)},watch:{tabs:function(e){if(e.length)return this.rightContentTabName=e[0].key}},methods:(0,r.A)((0,r.A)((0,r.A)({getBtnJsCodeValueInStashData:Gn,getBtnJsCodeStashData:Jn},(0,z.i0)("user",["fetchAuthData"])),(0,z.i0)("common",["fetchQueryParamsFromContentId"])),{},{goCopyForm:function(){var e,t=null===(e=window.frameElement)||void 0===e?void 0:e.getAttribute("fromid"),a="/template/edit?formId=".concat(this.templateId,"&appId=").concat(this.appId,"&formContentId=").concat(this.formContentId,"&onlyEdit=false&isCopy=1&fromId=").concat(t);this.jump(a,"create_view",this.$t("common.otherPageTitle.createNewForm"))},handelPageButtonClick:function(e,t){var a=this;(0,ln.Yi)(e,t,{fields:this.fields,multipleSelection:t,js_vm:this},(function(){a.pageButtonLoading=!0}),null,(function(){a.pageButtonLoading=!1}))},hanldeBtnList:function(e){"trigger"===e.event[0].type?(0,ln.Yi)(e,[this.initValue.bizId],{},null,null,this.handleTrigger):"linker"===e.event[0].type?(0,ln.Yi)(e,[this.initValue.bizId]):this.handelPageButtonClick(e,[this.initValue.bizId])},jump:function(e,t,a){var n,o=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");if(!o)return this.$router.push(e);this.$platform.openTab({id:"".concat(t,"_").concat(this.templateId),title:a,close:!0,reload:!0,fromId:o,url:"/paas/#".concat(e,"&noHistory=1")})},handleTrigger:function(){location.reload()},initData:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.initialize();case 2:return e.fetchNodes(),t.next=5,e.fetchProcessLog();case 5:e.fetchShareInfo(),e.fetchIsShowPrintBtn(),e.fetchEvaluates(),e.fetchCardInfo(),e.fetchAdditionCardList(),e.fetchAuthList(),e.fetchPrintTemplateListNew();case 12:case"end":return t.stop()}}),t)})))()},update:function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;this.$set(this.value,n,a)},buildFormValueParams:function(){var e=(0,bn.lR)(this.customFields,!0),t=this.operaButton.enName==L.A.TEMPORARILY_SAVE.value?1:0,a=(0,fn.Oj)(this.customFields,this.value),n=qn(this.customFields,a);return{formValueList:(0,tn.nO)(e,n),contentBizId:this.formContentId,templateUUId:this.templateId,version:this.version,deletefiles:this.needServerDeleFiles,isStore:t}},initialize:function(e){var t=this,a=[this.fetchFields(e),this.fetchFormData()];this.loading=!0,this.init=!1;try{this.getBtnJsCodeStashData(Bn.qy.PAASSYSTEM,Bn.cS.PcDetail,{},this.templateId)}catch(i){console.error("getBtnJsCodeStashData is Error >>>",i)}return Promise.all(a).then(function(){var a=(0,o.A)((0,n.A)().mark((function a(o){var i,s,l,c,u,d,m,f,p;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(c=o[1]||{},t.version=c.version,console.log("initValue",c),u=wa.n_(t.customFields,(0,tn.Sq)((null===c||void 0===c?void 0:c.paasFormValueList)||[],t.customFields)),d=(0,tn.bI)(t.systemFields,c),t.createdUserId=(null===c||void 0===c||null===(i=c.createUser)||void 0===i?void 0:i.userId)||"",u=Hn(t.customFields,u),t.value=(0,r.A)((0,r.A)((0,r.A)({},u),d),{},{templateBizId:t.templateId||t.formTemplateId,contentId:c.id}),!t.isFlowForm){a.next=11;break}return a.next=11,t.fetchFlowBtnList(e);case 11:t.initValue=(0,r.A)((0,r.A)({},c),{},{serialNumber:(null===(s=t.value)||void 0===s?void 0:s.serialNumber)||""}),t.originPaasFormValueList=c.paasFormValueList,t.paasTagInfoVos=c.paasTagInfoVos||[],t.init=!0,m=u[y.E.SerialNumber]||"",f="".concat(t.templateName||""," ").concat(m),!t.isInlineSelf&&null!==(l=window)&&void 0!==l&&null!==(l=l.frameElement)&&void 0!==l&&l.id&&parent.setFrameTabTitle({id:window.frameElement.id,title:f||t.$t("view.template.detail.formDetail")}),t.loading=!1,t.$nextTick((function(){return t.scrollIntoFirstRequireDom()})),p=t.value[null===t||void 0===t?void 0:t.customerFieldName]||[],t.fetchCustomerTags(p[0]);case 22:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}())["catch"]((function(e){t.loading=!1,console.log("template-detail-view initialize error: ",e)}))},fetchFields:function(e){var t=this;return l.Tw({contentBizId:this.formContentId,nodeInstanceId:e,excludeConnectOption:!0},!0).then((function(e){var a=e.data,n=e.success,o=e.message;if(n){var r,s=a||{},l=s.isContainWf,c=s.paasFormFieldVOList,u=void 0===c?[]:c,d=s.systemFormFieldVOList,m=void 0===d?[]:d;return t.displayNameLanguageArr=[],u=u.map((function(e){if(["productAndSparepart"].includes(e.formType)&&e.subFormFieldList.map((function(e){var t;"sparepart"===e.formType&&null!==(t=e.setting)&&void 0!==t&&null!==(t=t.subFormFieldList)&&void 0!==t&&t.length&&(e.subFormFieldList=e.setting.subFormFieldList)})),e.displayNameLanguage){var a={displayName:e.displayName,displayNameLanguage:e.displayNameLanguage};t.displayNameLanguageArr.push(a)}return e})),m=[{displayName:"",formType:"separator"}].concat((0,i.A)(m)),t.fields=(0,bn.lR)([].concat((0,i.A)(u),(0,i.A)(m)).map((function(e){return e.revisable=l&&e.revisable||0,e.disabled=0===e.revisable,e})).filter((function(e){return wa.Pm(e,!0)})),!0),t.systemFields=m,t.customFields=u,t.isShowAdditionCard&&(t.fields=t.fields.concat(hn.D)),t.templateName=(null===a||void 0===a||null===(r=a.templateNameLanguage)||void 0===r?void 0:r[t.$i18n.locale])||a.templateName,t.isFlowForm=1==l,a}t.$message.warning(o)}))["catch"]((function(e){return console.error("detail fetchFields error",e)}))},fetchFormData:function(){var e=this;return l.Ev({contentBizId:this.formContentId}).then((function(t){var a,n=t.data,o=void 0===n?{}:n;return e.isDelete=1==(null===o||void 0===o?void 0:o.isDelete),e.paasFormValueList=o.paasFormValueList||[],e.formTemplateId=o.templateBizId,e.projectTaskId=null!==(a=null===o||void 0===o?void 0:o.extendInfo)&&void 0!==a?a:"",o}))["catch"]((function(e){return console.error("detail fetchFormData error",e)}))},fetchCustomerTags:function(e){var t=this,a=(null===e||void 0===e?void 0:e.id)||"";a.length&&(0,d.RS)(e).then((function(e){e.success&&(t.customerTags=e.data)}))},fetchFlowBtnList:function(e){var t=this;return l.y5({contentBizId:this.formContentId,nodeInstanceId:e},!0).then((function(e){if(e.success){var a=t.flowPermiss=(null===e||void 0===e?void 0:e.data)||{},n=a.buttons,o=void 0===n?[]:n,i=a.approveSignConfig,r=o.sort((function(e,t){return e.sort-t.sort})).map((function(e){var t=L.A.getBtnByCode(e.code);return e.theme=t.theme||"",e}));t.$set(t.flowPermiss,"buttons",r),t.approveSignConfig=i,t.value["WFLOW_STATUS"]=t.flowPermiss.customStatus||t.stateText}}))["catch"]((function(e){return console.error("err",e)}))},fetchNodes:function(){var e=this;return this.loadingRefuse=!1,l.ML({nodeInstanceId:this.flowPermiss.currentNodeInstanceId}).then((function(t){var a=t.success,n=t.data;if(a){var o=n.map((function(e){return{name:e.name,bizId:e.bizId}}));e.backButton=o}}))["finally"]((function(){e.loadingRefuse=!0}))},fetchShareInfo:function(){var e=this;l.td({formTemplateId:this.templateId||this.formTemplateId,formContentId:this.formContentId}).then((function(t){t.success&&(e.shareInfo=t.data||{})}))["catch"]((function(e){return console.error("err",e)}))},fetchProcessLog:function(){var e=this;if(this.processId)return l.O0({contentBizId:this.formContentId},!0).then((function(t){if(t.success){var a=t.data||{},n=a.createUser,o=a.createTime,r=a.nodeLogList,s=void 0===r?[]:r;s.forEach((function(e){if(e.collapse=!1,e.nodeType==f.A.START_NODE){var t=L.A.SUBMIT,a=t.name,i=t.code;e.operate=a,e.operateCode=i,e.userIds=[n],e.taskLogVOList=e.isCurrent?[]:[{approveResult:a,completeTime:o,user:n}]}})),e.currLogList=s.filter((function(e){return e.isCurrent}));var l=s.filter((function(e){return!e.isCurrent}));e.isParallelNode&&(s=[{isCurrent:!0,isMulti:!0}].concat((0,i.A)(l))),e.logList=s}}))["catch"]((function(e){return console.error(e)}))},fetchEvaluates:function(){var e=this;this.processId&&l.Pp({formContentId:this.formContentId}).then((function(t){t.success&&(e.satisfactionData=t.data||[])}))["catch"]((function(e){console.error("template-detail-view fetchEvaluates error: ",e)}))},fetchCardInfo:function(){var e=this;c.h_({formTemplateBizId:this.templateId||this.formTemplateId}).then((function(t){t.success&&(e.cardList=(t.data||[]).filter((function(e){return 1==e.enable})))}))["catch"]((function(e){console.error("template-detail-view fetchCardInfo error: ",e)}))},fetchAdditionCardList:function(){var e=this;c.HV({formTemplateId:this.templateId||this.formTemplateId}).then((function(t){var a=t.success,n=t.message,o=t.data,i=void 0===o?[]:o;a?e.additionCardList=i:e.$message.error(n)}))["finally"]((function(){e.loading=!1}))["catch"]((function(e){return console.error("satisfaction setting initialize",e)}))},submit:function(e,t){var a=this;return(0,o.A)((0,n.A)().mark((function o(){var i,r,s,l,c,d,m,f,p,v,h,g,b,w,y,A,I;return(0,n.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if([4,7].includes(e.code)&&a.backButton.length>0?a.bizId=t:a.bizId="",r=a.operaButton=e,s=r.enName,l=r.showAttached,c=r.showRemark,d=a.getBtnJsCodeValueInStashData("paasFlowBeforeSubmit"),!d){n.next=10;break}return m=a,n.next=7,jn({that:m,operaButton:e},d);case 7:if(f=n.sent,f){n.next=10;break}return n.abrupt("return");case 10:if(p={flowTaskId:a.flowPermiss.currentaskId,nodeButtonName:a.operaButton.enName,approveResult:a.operaButton.code},a.bizId&&(p.backNodeId=a.bizId),null===(i=a.flowPermiss)||void 0===i||!i.pauseFlag){n.next=14;break}return n.abrupt("return",a.fetchRecoverFlowTask());case 14:if(s!==L.A.BACKTOME.value||l||c){n.next=16;break}return n.abrupt("return",a.backToMeApi(p));case 16:if(s!=L.A.COUNTERSIGN.value){n.next=19;break}return a.formCountersign(),n.abrupt("return");case 19:if(s!=L.A.AGREE.value&&s!=L.A.SUBMIT.value){n.next=50;break}return n.next=22,a.$refs.detailFrom.$refs.form.validate(!1);case 22:return v=n.sent,a.scrollIntoFirstRequireDom(),n.next=26,a.$refs.detailFrom.$refs.form.validateAsyncFetch();case 26:if(v||"detail"===a.oneLayoutActiveTab.key||a.$message.warning("view.template.detail.validateTip"),v){n.next=29;break}return n.abrupt("return");case 29:return n.prev=29,a.pending=!0,n.next=33,(0,u.getNodeStockSetting)({flowTaskId:a.flowPermiss.currentaskId});case 33:if(h=n.sent,g=(null===h||void 0===h?void 0:h.data)||{},b=g.openStockFieldNameList,w=void 0===b?[]:b,a.pending=!1,!w.length){n.next=44;break}if(y=(0,pn.lY)(a.fields,a.value,w),!y.length){n.next=44;break}return n.next=41,a.$confirm(a.$t("view.template.detail.tip1",{message:y.join("；")}))["catch"]((function(){}));case 41:if(A=n.sent,A){n.next=44;break}return n.abrupt("return");case 44:n.next=50;break;case 46:n.prev=46,n.t0=n["catch"](29),a.pending=!1,console.log("template-detail-view getNodeStockSetting error",n.t0);case 50:if(s!=L.A.FORWARD.value){n.next=52;break}return n.abrupt("return",a.openDialog(s));case 52:if(I=[L.A.AGREE.value,L.A.REFUSE.value,L.A.ROLLBACK.value,L.A.CANCEL.value,L.A.BACKTOME.value,L.A.PAUSE.value],!I.includes(s)||!l&&!c){n.next=55;break}return n.abrupt("return",a.openDialog(s));case 55:if(!a.currentNodeIsStartNode){n.next=60;break}if(s!==L.A.TEMPORARILY_SAVE.value){n.next=59;break}return a.pending=!0,n.abrupt("return",Ge["default"].methods.saveFormContent.call(a,a.buildFormValueParams(),!0));case 59:return n.abrupt("return",a.startFlowContent());case 60:a.finishedFlowContent(p);case 61:case"end":return n.stop()}}),o,null,[[29,46]])})))()},updateBtn:function(e,t){this.submit(e,t)},openBackNode:function(e){if(this.backButton.length>1)this.$refs.backNodeDialogRef.openDialog(e);else if(1===this.backButton.length){var t;this.submit(e,null===(t=this.backButton[0])||void 0===t?void 0:t.bizId)}else this.submit(e)},startFlowContent:function(){var e=this;this.pending=!0;var t={appId:this.appId,formTemplateId:this.templateId||this.formTemplateId,formContentId:this.contentBizId,formContentEditForm:this.buildFormValueParams(),isNeedAuth:this.isTaskFrame?0:1};l.Ub(t).then((function(t){var a=t.success,n=t.message,o=t.code;a?(e.successCallbackFn(),e.$message.success(e.$t("common.base.tip.operationSuccess"))):(e.$message.warning(n),e.pending=!1,3002===o&&e.initialize())}))["catch"]((function(e){console.log("template-edit-view startFlowContent error",e)}))["finally"]((function(){return e.pending=!1}))},formCountersign:function(){var e=this;this.pending=!0;var t={title:this.$t("countersign.pleaseSelectCountersign"),showDelete:!1,max:5,isTag:!0,showSp:!0,selectedUsers:[]},a=(0,A.zO)(window);a.$fast.select.multi.user(t).then((function(t){if(0==t.status){var a,n;if(null===(a=t.data)||void 0===a||!a.users.length)return e.$message.warning(e.$t("countersign.notSelectCountersign"));var o=[];null===(n=t.data)||void 0===n||n.users.map((function(e){o.push(e.userId)})),l.OO({paasFlowNodeInstanceId:e.nodeInstanceIdForConnector,processorInstanceId:e.processId,userIdList:o}).then((function(t){var a=t.code,n=t.message;if(0==a)e.$message.success(e.$t("common.base.tip.operationSuccess")),e.successCallbackFn();else{if(a>=3e3&&a<4e3)return e.$notify({title:e.$t("common.base.toast"),type:"warning",message:n}),void setTimeout((function(){e.successCallbackFn()}),500);e.$message.warning(n)}}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){console.error("template-detail-view finishedFlowContent error: ",e)}))}}))["catch"]((function(e){return console.error(e)})),this.pending=!1},finishedFlowContent:function(e){var t=this;this.pending=!0;var a=this.buildFormValueParams();l.tx({formContentEditForm:a,finishTaskForm:e,version:this.version},!0).then((function(e){var a=e.code,n=e.message;if(0==a)t.$message.success(t.$t("common.base.tip.operationSuccess")),t.successCallbackFn();else{if(a>=3e3&&a<4e3)return t.$notify({title:t.$t("common.base.toast"),type:"warning",message:n}),void setTimeout((function(){t.successCallbackFn()}),500);t.$message.warning(n)}}))["finally"]((function(){t.pending=!1}))["catch"]((function(e){console.error("template-detail-view finishedFlowContent error: ",e)}))},backToMeApi:function(e){var t=this;this.pending=!0,l.T2((0,r.A)({},e),!0).then((function(e){var a=e.code,n=e.message;if(0==a)t.$message.success(t.$t("common.base.tip.operationSuccess")),t.successCallbackFn();else{if(a>=3e3&&a<4e3)return t.$notify({title:t.$t("common.base.toast"),type:"warning",message:n}),void setTimeout((function(){t.successCallbackFn()}),500);t.$message.warning(n)}}))["finally"]((function(){t.pending=!1}))["catch"]((function(e){console.error("template-detail-view finishedFlowContent error: ",e)}))},updateFlowNode:function(e){var t=e.nodeInstanceId;this.popoverVisible=!1,this.currFlow.nodeInstanceId!=t&&(this.initialize(t),this.currLogList.forEach((function(e){e.nodeInstanceId==t?e.isDefault=!0:e.isDefault=!1})))},successCallbackFn:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.initialize();case 2:return e.fetchNodes(),t.next=5,e.fetchProcessLog();case 5:e.fetchIsShowPrintBtn(),e.fetchPrintTemplateListNew(),window.parent.postMessage({refresh:!0},"*"),e.projectTaskId&&(null===(a=e.$refs)||void 0===a||null===(a=a["task-project-info"])||void 0===a||null===(a=a[0])||void 0===a||a.getTaskData()),e.reloadTab();case 10:case"end":return t.stop()}}),t)})))()},openDialog:function(e){switch(e){case L.A.AGREE.value:case L.A.REFUSE.value:case L.A.ROLLBACK.value:case L.A.CANCEL.value:case L.A.BACKTOME.value:return this.$refs.approvalDialog.openDialog(this.bizId);case L.A.FORWARD.value:return this.$refs.transferDialog.openDialog();case"share":return this.$refs.shareLinkDialog.openDialog();case"satisfaction":return this.$refs.satisfactionDialog.openDialog();case L.A.PAUSE.value:return this.$refs.pauseDialog.openDialog()}},expand:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.collapseDirection)return this.collapseDirection="";this.collapse=e},scrollIntoFirstRequireDom:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a,o,i,r,s,l;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=function(e,t,a){var n=e.scrollTop,o=performance.now(),i=function(e){return e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1},r=function(){var s=performance.now(),l=s-o,c=Math.min(l/a,1);e.scrollTop=i(c)*(t-n)+n,c<1&&requestAnimationFrame(r)};requestAnimationFrame(r)},t.prev=1,i=e.$refs.detailFrom.$refs.form.$children.filter((function(e){return!["form-view","form-info"].includes(e.$el.className)})),r=[],s=(0,n.A)().mark((function e(t){var a,o;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=i[t],e.next=3,new Promise((function(e,n){i[t].$nextTick((function(){if(a.$attrs["data-empty"])return e(!0);(0,Lt.Tn)(null===a||void 0===a?void 0:a.valueFn)?an.Ay.validate(null===a||void 0===a?void 0:a.valueFn(),null===a||void 0===a?void 0:a.field).then((function(t){return e(t)}))["catch"]((function(e){return console.error(e)})):e(!1)}))}));case 3:o=e.sent,o&&r.push(a.$el);case 5:case"end":return e.stop()}}),e)})),l=0;case 6:if(!(l<i.length)){t.next=11;break}return t.delegateYield(s(l),"t0",8);case 8:l++,t.next=6;break;case 11:null!==(o=r[0])&&void 0!==o&&o.offsetTop&&a(document.querySelector(".form-builder-column"),r[0].offsetTop-44,800),t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](1),console.error(t.t1);case 17:case"end":return t.stop()}}),t,null,[[1,14]])})))()},createRemark:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){var o,i,s,l,c,d,m,f,p;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.commentPending=!0,s=t.user.loginUser,l=s.tenantId,c=void 0===l?"":l,d=s.displayName,m="tenantId=".concat(c,"&&bizId=").concat(t.formContentId,"&&sendUserName=").concat(d,"&&receivers=").concat(e.receivers,"&&templateId=").concat(t.templateId||t.formTemplateId,"&&templateName=").concat(t.templateName,"&&bizNo=").concat(t.value["serialNumber"],"&&processId=").concat(t.processId),m+="&&content=".concat((0,ta.cutAtTextContent)(e.originalContent)),f=(0,r.A)((0,r.A)({},e),{},{processInstanceId:t.processId,nodeInstanceBizId:null===(o=t.currLogList.find((function(e){return e.isDefault})))||void 0===o?void 0:o.nodeInstanceId,remindUserId:e.cusRemarksNotice?null===(i=t.value[un.A.CREATE_USER.value])||void 0===i?void 0:i.userId:null,isNoticeOriginator:e.toCustomer,templateName:t.templateName,templateId:t.templateId||t.formTemplateId,cusRemarksNotice:e.cusRemarksNoticeNew}),delete f.cusRemarksNoticeNew,a.next=9,(0,u.fetchLogRemark)(m,f)["finally"]((function(){return t.commentPending=!1}));case 9:p=a.sent,p.success&&(t.$message.success(t.$t("view.template.detail.addRemarkSuccess")),t.fetchProcessLog(),t.$refs.comment.reset());case 11:case"end":return a.stop()}}),a)})))()},fetchIsShowPrintBtn:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a,o,i,r,s,c,u,d,m;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,r=e.firstLogItemData,s=r.firstLogItem,c=r.isEndNode,u=c?(null===s||void 0===s?void 0:s.nodeTemplateId)||"":(null===(a=e.currLogList.find((function(e){return e.isDefault})))||void 0===a?void 0:a.nodeTemplateId)||"",d={formTemplateId:e.templateId||e.formTemplateId,nodeTemplateId:u},t.next=6,Promise.all([l.CO(d),l.EB(d)]);case 6:m=t.sent,e.printTemplateList=((null===m||void 0===m||null===(o=m[1])||void 0===o?void 0:o.data)||[]).filter((function(e){return e.status}))||[],e.showPrintTemplateBtn=e.printTemplateList.length>0||(null===m||void 0===m||null===(i=m[0])||void 0===i?void 0:i.data),t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](0),console.log("[fetch template error]",t.t0);case 14:case"end":return t.stop()}}),t,null,[[0,11]])})))()},fetchPrintTemplateListNew:function(){var e=this,t="";t=this.logList[0]&&"end-node"==this.logList[0].nodeType?this.logList[0].nodeTemplateId:this.currFlow.nodeTemplateId,(0,m.Aw)({module:"PAAS",bizId:this.templateId||this.formTemplateId,enable:1,nodeTemplateIds:t?[t]:[]}).then((function(t){e.printTemplateListNew=(null===t||void 0===t?void 0:t.result)||[]}))},rerenderBaseCollapseComponentHandler:function(){var e=this;this.rerenderBaseCollapseComponent(),this.$nextTick((function(){e.rerenderBaseCollapseComponent()}));var t=[300,1e3,2e3,3e3,5e3];t.forEach((function(t,a){setTimeout((function(){e.rerenderBaseCollapseComponent()}),t)}))},rerenderBaseCollapseComponent:function(){this.$refs.BaseCollapseComponent.calcWidth()},fetchAuthList:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.fetchAuthData({templateBizId:e.templateId||e.formTemplateId,formContentId:e.formContentId,appId:e.appId});case 1:case"end":return t.stop()}}),t)})))()},handleToEdit:function(){var e,t,a,n=(null===(e=this.currLogList.find((function(e){return e.isDefault})))||void 0===e?void 0:e.nodeInstanceId)||(null===(t=this.logList.find((function(e){return(null===e||void 0===e?void 0:e.nodeType)===f.A.nodeType||2===(null===e||void 0===e?void 0:e.nodeTypeNum)})))||void 0===t?void 0:t.nodeInstanceId),o="/template/edit?formId=".concat(this.templateId||this.formTemplateId,"&appId=").concat(this.appId||"","&noHistory=1&formContentId=").concat(this.formContentId,"&nodeInstanceId=").concat(n,"&onlyEdit=true"),i=this.value[y.E.SerialNumber]||"",r=null===(a=window.frameElement)||void 0===a?void 0:a.getAttribute("id"),s=(0,ta.parseUrlSearch)(window.location.href.split("?")[1]);if(null!==s&&void 0!==s&&s.noNeedWindow){var l={action:"PaaS.frame.edit.loaded",data:o};window.parent.postMessage(l,"*")}if(!r)return this.$router.push(o);try{on.Ay.closeTab("edit_view_".concat(i||this.formContentId))}catch(c){console.error("[closeTab error ]",c)}this.$platform.openTab({id:"edit_view_".concat(i||this.formContentId),title:this.$t("common.otherPageTitle.editForm")+i,close:!0,reload:!0,fromId:r,url:"/paas/#".concat(o)})},onTagHandler:function(){this.$refs.onTagDialog.visible=!0,this.tagIdList=this.paasTagInfoVos.map((function(e){return e.tagId}))},saveOnTag:function(){var e=this;setTimeout((function(){e.initialize()}),500)},handlePrint:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){var o,i;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=(null===e||void 0===e||null===(o=e.setting)||void 0===o?void 0:o.isSupportCustomPrint)||!1,!i){a.next=3;break}return a.abrupt("return",t.handleCustomPrint());case 3:null!==e&&void 0!==e&&e.isOld?t.handlePrintOld():t.handlePrintNew(e);case 4:case"end":return a.stop()}}),a)})))()},handleCustomPrint:function(){null===(e=window.frameElement)||void 0===e||e.getAttribute("id");var e,t,a=null===(t=this.currLogList.find((function(e){return e.isDefault})))||void 0===t?void 0:t.nodeInstanceId,n="?formId=".concat(this.templateId||this.formTemplateId,"&nodeInstanceId=").concat(a,"&formContentId=").concat(this.formContentId,"&processId=").concat(this.processId);R.A.openHelp("/paas/#/template/customPrint?params=".concat(n))},handlePrintOld:function(){var e,t,a,n=this.printTemplateList[0],o=this.firstLogItemData,i=o.firstLogItem,r=o.isEndNode,s=r?i.nodeInstanceId:null!==(e=null===(t=this.currLogList.find((function(e){return e.isDefault})))||void 0===t?void 0:t.nodeInstanceId)&&void 0!==e?e:"",l=(null===(a=window.frameElement)||void 0===a?void 0:a.getAttribute("id"))||"",c="?formId=".concat(this.templateId||this.formTemplateId,"&nodeInstanceId=").concat(s,"&formContentId=").concat(this.formContentId,"&processId=").concat(this.processId,"&printBizId=").concat(n.bizId);try{on.Ay.closeTab("paas_print_view_".concat(n.bizId))}catch(u){console.info(u)}nn.A.openTab({id:"paas_print_view_".concat(n.bizId),title:"".concat(this.templateName).concat(this.value[y.E.SerialNumber],"-").concat(n.name),close:!0,reload:!0,fromId:l,url:["9d546dc5-f7f8-3371-3df7-bb8767d71e74","633edfc5-a5cc-6a6f-a8d2-8fa53c25e4d5","20eca07e-4fca-8ce5-2170-f9290f7bf01c"].includes(n.bizId)?"/template/print_fixed".concat(c):"/template/print".concat(c)})},handlePrintNew:function(e){var t=this;return(0,o.A)((0,n.A)().mark((function a(){var s,l,c,u,d,f,p,v,h;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return l=e.id,t.printBtnState=!0,c=(0,tn.bI)(t.systemFields,t.initValue),u=(0,r.A)((0,r.A)({},(0,tn.Sq)((null===(s=t.initValue)||void 0===s?void 0:s.paasFormValueList)||[])),(0,r.A)((0,r.A)({},c),{},{CURRENT_NODE:c.CURRENT_NODE||t.flowPermiss.currentNodeName,WFLOW_STATUS:c.WFLOW_STATUS||t.stateText})),a.prev=4,d=function(e){var t=[];return e.forEach((function(e){wa.hL(e)&&(t=[].concat((0,i.A)(t),(0,i.A)(e.subFormFieldList||[])))})),e.concat(t)},a.next=8,Promise.all(d(t.customFields).map(function(){var e=(0,o.A)((0,n.A)().mark((function e(a){var i,r;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a.formType!=y.pb.CustomerAddress){e.next=6;break}return e.next=3,(0,sn.Qn)((u[a.fieldName]||[])[0]);case 3:u[a.fieldName]=e.sent,e.next=19;break;case 6:if(a.formType!=y.E.Address){e.next=18;break}if(!a.isChildForm){e.next=13;break}return i=u[a.parentFieldName]||[],e.next=11,Promise.all(i.map(function(){var e=(0,o.A)((0,n.A)().mark((function e(t){var o;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.t0=(0,n.A)().keys(t);case 1:if((e.t1=e.t0()).done){e.next=9;break}if(o=e.t1.value,a.fieldName!=o){e.next=7;break}return e.next=6,(0,sn.ij)(t[o]);case 6:t[o]=e.sent;case 7:e.next=1;break;case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 11:e.next=16;break;case 13:return e.next=15,(0,sn.ij)(u[a.fieldName]);case 15:u[a.fieldName]=e.sent;case 16:e.next=19;break;case 18:[y.E.MaterialOrder,y.E.SubForm].includes(a.formType)&&(null===u||void 0===u||null===(r=u[a.fieldName])||void 0===r||r.forEach((function(e,a){t.$set(e,"materialIndex",a+1)})));case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 8:a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](4),console.error("getAddressValue error:",a.t0);case 13:return a.t1=rn.vt,a.next=16,t.getHtmlContent(l);case 16:return a.t2=a.sent,a.t3=u,a.t4=[].concat((0,i.A)(t.customFields),(0,i.A)(t.systemFields)),a.t5=t.logList,a.t6=(0,a.t1)(a.t2,a.t3,a.t4,a.t5),a.t7=(null===e||void 0===e?void 0:e.setting)||{},f={type:"pdf",content:a.t6,setting:a.t7},a.next=25,(0,m.Wf)(f)["catch"]((function(e){return console.log(e)}))["finally"]((function(){return t.printBtnState=!1}));case 25:if(p=a.sent,v=p.success,h=p.result,v&&h){a.next=30;break}return a.abrupt("return");case 30:R.A.openHelp(h);case 31:case"end":return a.stop()}}),a,null,[[4,10]])})))()},getHtmlContent:function(e){return(0,o.A)((0,n.A)().mark((function t(){var a,o,i,r;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={id:e},t.next=3,(0,m.om)(a)["catch"]((function(e){return console.log(e)}));case 3:if(o=t.sent,i=o.success,r=o.result,i&&r){t.next=8;break}return t.abrupt("return","");case 8:return t.abrupt("return",r.content);case 9:case"end":return t.stop()}}),t)})))()},openSourceBizNoTab:function(e){var t=this,a=e.sourceBizType,n=e.sourceBizId,o=e.sourceBizTypeId,i="",r="";switch(a){case"TASK":i="PageTaskView";break;case"EVENT":i="PageEventView";break;case"CUSTOMER":i="PageCustomerView";break;case"PRODUCT":i="PageProductView";break;case"SMART_PLAN":i="PageSmartPlanDetail";break;case"INDENT_ADDITIONAL":i="PagePurchaaseOrderManageView",r="id=".concat(n);break;default:break}"PAAS"!=a?"SMART_PLAN"==a?(r="planId=".concat(o),this.openTab(i,o,r)):this.openTab(i,n,r):l.nL(n).then((function(e){var a,i,r="/template/detail?formId=".concat(o,"&formContentId=").concat(n);r+="&processId=".concat(null!==(a=e.data)&&void 0!==a&&a.processorInstanceId?null===(i=e.data)||void 0===i?void 0:i.processorInstanceId:""),t.openPaasTab(r,"detail_view_".concat(n),t.$t("view.template.detail.formDetail"))}))},openTab:function(e,t){var a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=null===(a=window)||void 0===a||null===(a=a.frameElement)||void 0===a?void 0:a.getAttribute("id");(0,Ht.iL)({type:qt.Z[e],key:t,params:n,fromId:o})},initObserverLeftPanelDom:function(){var e=this,t=new ResizeObserver((function(t){if(t){var a=t[0].contentRect.width;a>1300&&(e.formCellCount=4),a>900&&a<=1300&&(e.formCellCount=3),a<=900&&(e.formCellCount=2),e.init=!0}}));t.observe(this.$refs.form.$el),this.leftResizeObserver=t},openPaasTab:function(e,t,a){var n,o=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");if(!o)return this.$router.push(e);this.$platform.openTab({id:"".concat(t,"_").concat(this.templateId),title:a,close:!0,reload:!0,fromId:o,url:"/paas/#".concat(e,"&noHistory=1")})},reloadTab:function(){var e,t=null===(e=window)||void 0===e||null===(e=e.frameElement)||void 0===e?void 0:e.getAttribute("fromid");this.$platform.refreshTab(t)},getDeleteFiles:function(e){this.needServerDeleFiles=[].concat((0,i.A)(this.needServerDeleFiles),(0,i.A)(e))},tabBarChangeItem:function(e){2===this.baseLayout&&"detail"===e.key||(this.rightContentTabName=e.key,this.oneLayoutActiveTab=e)},openBaseLayoutModal:function(){this.$refs.bizLayoutModal.open()},changeTaskDetailLayout:function(e,t){this.baseLayout=e,this.formCellCount=t,this.oneLayoutActiveTab={component:"detail-form",key:"detail"},this.rightContentTabName="flow-record"},initLayoutData:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a,o,i;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=(0,ta.useStateSystemViewLayout)(),o=a.getSystemViewLayout,t.next=3,o();case 3:i=t.sent,e.baseLayout=i.baseLayout||2,e.formCellCount=i.formCellCount||1;case 6:case"end":return t.stop()}}),t)})))()},fetchRecoverFlowTask:function(){var e=arguments,t=this;return(0,o.A)((0,n.A)().mark((function a(){var i,r,s;return(0,n.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i=e.length>0&&void 0!==e[0]?e[0]:{},r=i.formContentId,s=i.flowTaskId,t.$confirm("是否继续流程?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((0,o.A)((0,n.A)().mark((function e(){var a,o,i;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.pending=!0,e.next=4,l.uk({formContentId:t.formContentId||r,flowTaskId:(null===(a=t.flowPermiss)||void 0===a?void 0:a.currentaskId)||s,remark:"",attached:[]})["finally"]((function(){return t.pending=!1}));case 4:return o=e.sent,i=o.success,i&&t.successCallbackFn(),e.abrupt("return",i);case 10:e.prev=10,e.t0=e["catch"](0),console.error("[fetchRecoverFlowTask error]",e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])}))));case 2:case"end":return a.stop()}}),a)})))()},checkedSomeButtonIsDisabled:function(e){var t;return(null===(t=this.flowPermiss)||void 0===t?void 0:t.pauseFlag)&&e.enName!==L.A.PAUSE.value},builderThat:function(){return this},changePage:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"next";try{e=JSON.parse(sessionStorage.getItem(this.$route.query.JumpKey)||"[]")}catch(b){console.log(b)}for(var a=0;a<e.length;a++){var n;if((null===(n=e[a])||void 0===n?void 0:n.bizId)===this.formContentId){if("next"===t)if(e[a+1]){var o=e[a+1],i=o.bizId,r=void 0===i?"":i,s=o.appId,l=void 0===s?"":s,c=o.templateBizId,u=void 0===c?"":c;this.openPage(r,l,u,this.$route.query.JumpKey)}else this.$message.error(this.$t("common.base.tip.noNextPage"));else if(e[a-1]){var d=e[a-1],m=d.bizId,f=void 0===m?"":m,p=d.appId,v=void 0===p?"":p,h=d.templateBizId,g=void 0===h?"":h;this.openPage(f,v,g,this.$route.query.JumpKey)}else this.$message.error(this.$t("common.base.tip.noLastPage"));break}}},openPage:function(e,t,a,n){var o,i,r=this;l.nL(e).then((function(s){var l;o="/template/detail?formId=".concat(a,"&formContentId=").concat(e,"&appId=").concat(t,"&JumpKey=").concat(n);var c=(null===(l=s.data)||void 0===l?void 0:l.processorInstanceId)||r.processId;c&&(o+="&processId=".concat(c)),i=r.$t("view.template.detail.formDetail"),r.$platform.closeTab("detail_view_".concat(r.formContentId,"_").concat(r.templateId)),r.jump(o,"detail_view_".concat(e),i)}))},getPageModeForStorage:function(){var e=this;return(0,o.A)((0,n.A)().mark((function t(){var a;return(0,n.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,cn["if"])("PaaS-Board-Mode-".concat(e.templateId),"");case 2:return a=t.sent,a&&a.type!==dn.aI.LIST&&(e.listPageIsTableStyle=!1),t.abrupt("return",a);case 5:case"end":return t.stop()}}),t)})))()},setSettlementPoolByModuleCheck:function(){var e=this;(0,Un.po)({module:"paas",moduleSourceId:this.formContentId}).then((function(t){0===t.status&&(e.settlementPoolByModuleCheck=t.data)}))}})}},16828:function(e,t,a){a.d(t,{A:function(){return p}});var n=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("base-modal",{staticClass:"card-create-dialog",attrs:{title:e.t("view.designer.workFlow.label15"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"base-modal-body"},[t("el-form",{ref:"formRef",attrs:{model:e.form,rules:e.rules,"label-position":"top","label-width":"80px"}},[t("el-form-item",{attrs:{label:e.t("view.designer.workFlow.tip60"),prop:"nodeId"}},[t("el-select",{model:{value:e.form.nodeId,callback:function(t){e.$set(e.form,"nodeId",t)},expression:"form.nodeId"}},[e._l(e.backBtn,(function(e,a){return[t("el-option",{key:a,attrs:{label:e.name,value:e.bizId}})]}))],2)],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v(e._s(e.t("common.base.save")))])],1)])},o=[],i=a(18885),r=a(42881),s=a(48649),l=a(92935),c=a(84859),u=(0,s.defineComponent)({name:"back-node-dialog",props:{backButton:{type:Array,defalut:function(){return[]}}},setup:function(e,t){t.attrs,t.slots;var a=t.emit,n=t.expose,o=(0,s.ref)(!1),u=(0,s.ref)([]),d=(0,s.ref)(null),m=(0,s.ref)({nodeId:""}),f=(0,s.ref)({}),p=(0,s.ref)({nodeId:[{required:!0,message:(0,c.t)("view.designer.workFlow.tip59"),trigger:"blur"}]}),v=function(t){o.value=!0,f.value=t,u.value=(0,l.cloneDeep)(e.backButton),m.value.nodeId="",(0,s.nextTick)((function(){d.value.resetFields()}))},h=function(){var e=(0,r.A)((0,i.A)().mark((function e(){return(0,i.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,d.value.validate();case 3:a("update",f.value,m.value.nodeId),o.value=!1,e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();return n({openDialog:v}),{visible:o,rules:p,form:m,backBtn:u,formRef:d,openDialog:v,t:c.t,submit:h}}}),d=u,m=a(49100),f=(0,m.A)(d,n,o,!1,null,"2b19aa6c",null),p=f.exports},21975:function(e,t,a){var n=a(52275),o=a(26183),i=a(54232),r=a(58091),s=a(62361),l=(a(67880),a(86651)),c=a(84859),u=function(e){function t(){return(0,n.A)(this,t),(0,i.A)(this,t)}return(0,r.A)(t,e),(0,o.A)(t,null,[{key:"getBgColor",value:function(e,a){var n,o=t,i="";for(var r in o)if(n=o[r],n.value==e){i=n.bgColor||"";break}return i||console.warn("Caused: FlowStateEnum getBgColor got the value is empty"),i="number"===typeof a?"rgba(".concat(i,", ").concat(a,")"):"rgb(".concat(i,")"),i}},{key:"getColor",value:function(e){var a,n=t;for(var o in n)if(a=n[o],a.value==e)return a.color||"";return""}},{key:"getName",value:function(e){var a,n=t;for(var o in n)if(a=n[o],a.value==e)return a.name;return""}},{key:"getColorForFlow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{color:t.getColor(e),bgColor:t.getBgColor(e)}}}])}(l.A);(0,s.A)(u,"PROCESSING",{bgColor:"250, 174, 20",color:"#fff",name:c.Ay.t("common.base.processing"),value:1}),(0,s.A)(u,"FINISHED",{bgColor:"103, 194, 58",color:"#fff",name:c.Ay.t("common.base.usualStatus.finish"),value:2}),(0,s.A)(u,"OFFED",{bgColor:"140, 140, 140",color:"#fff",name:c.Ay.t("common.task.type.offed"),value:3}),(0,s.A)(u,"DRAFT",{bgColor:"140, 140, 140",color:"#fff",name:c.Ay.t("common.base.draft"),value:4}),(0,s.A)(u,"REFUSED",{bgColor:"245, 108, 108",color:"#fff",name:c.Ay.t("common.task.type.refused"),value:5}),t.A=u},39723:function(e,t){function a(){for(var e="",t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",a=0,n=t.length;a<n;a+=1){var o=t[a],i=16*Math.random()|0,r="x"===o?i:"y"===o?3&i|8:o;e+=r.toString(16)}return e}t.u=void 0,t.u=a},42440:function(e,t,a){a.d(t,{A:function(){return d}});var n=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.sthSuggestion",{data:e.$t("buttons.pause")}),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("form-builder",{ref:"formBulider",attrs:{value:e.form,fields:e.fields,"form-editing-mode":"create"},on:{update:e.update}}):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("buttons.submit"))+" ")])],1)],1)},o=[],i=(a(2286),a(75069),a(74526)),r=a(80906),s={name:"pause-dialog",props:{config:{type:Object,default:function(){return{}}},taskId:{type:String,default:""},buildFormParams:{type:Function}},data:function(){return{visible:!1,pending:!1,form:{}}},computed:{fields:function(){var e=this.config,t=(e.showAttached,e.checkAttached,e.showRemark),a=e.checkRemark;return[{fieldName:"remark",displayName:this.$t("view.template.detail.sthSuggestion",{data:this.$t("buttons.pause")}),formType:"textarea",isNull:a?0:1,show:t}].filter((function(e){return e.show}))}},methods:{update:function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;this.$set(this.form,n,a)},openDialog:function(){this.form=r.n_(this.fields,{}),this.visible=!0},buildParams:function(){var e=this.form,t=e.forwardUserId,a=void 0===t?{}:t,n=e.attached,o=void 0===n?[]:n,i=e.remark;return a=a.userId,{attached:o,forwardUserId:a,remark:i,flowTaskId:this.taskId,approveResult:this.config.code,nodeButtonName:this.config.enName}},submit:function(){var e=this;this.$refs.formBulider.validate(!1).then((function(t){if(!t)return Promise.reject("validate fail.");e.pending=!0;var a=e.buildParams(),n=e.buildFormParams();i.cQ({flowTaskId:a.flowTaskId,remark:a.remark,attached:a.attached,formContentId:n.contentBizId}).then((function(t){t.success?(e.$message.success(e.$t("buttons.pause")+e.$t("common.base.success")),e.visible=!1,e.$emit("success")):e.$platform.alert(t.message)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.log(e)}))}))["catch"]((function(t){e.pending=!1}))}}},l=s,c=a(49100),u=(0,c.A)(l,n,o,!1,null,"7a2b01bd",null),d=u.exports},46995:function(e,t,a){a.d(t,{P:function(){return P},F:function(){return E}});var n=a(37801),o=a(71357),i=(a(3923),a(35256),a(16961),a(89370),a(32807),a(75069),a(15645),a(13262),a(48649)),r=a(81798),s=a(18885),l=a(42881),c=(a(67880),a(87313),a(2286),a(62838),a(80793),a(46622),a(36700),a(21484),a(8326),a(89716),a(27408),a(14126),a(54615),a(88747),a(33438),a(55650),a(39789),a(62830),a(21633),a(69594),a(68735),a(98878),a(73385),a(82324),a(54303),a(14389)),u=a(57670);a(7509),a(19944),a(22229);var d=a(87),m=function(){var e=(0,i.ref)({tableShowType:"icon"}),t=function(t){e.value.tableShowType=t?"text":"icon"},a=function(e,a){var n;t(e),null===(n=(0,d.zO)(window))||void 0===n||n.changeLabelView({labelTableShow:e,linksLabel:a})},n=function(){try{var e;return null===(e=(0,d.zO)(window))||void 0===e?void 0:e.getLabelInfo()}catch(t){return void console.warn(t)}},o=function(){try{var e,t;return null!==(e=null===(t=n())||void 0===t?void 0:t.labelTableShow)&&void 0!==e?e:0}catch(a){return void console.warn(a)}},r=function(){try{var e,t;return null!==(e=null===(t=n())||void 0===t?void 0:t.linksLabel)&&void 0!==e?e:0}catch(a){return void console.warn(a)}},s=function(t){e.value.tableShowType=t},l=function(){e.value.tableShowType=1==o()?"text":"icon"};return(0,i.onBeforeMount)((function(){l()})),{config:e,changeTableShowType:s,getLabelShowInLocal:n,changeLabelShowInLocal:a,changeLabelShowTable:t,getLabelTableShow:o,getLinksLabelShow:r}},f=a(50422),p=a(92935),v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=(0,i.getCurrentInstance)(),a=t.proxy,n=(0,i.ref)([{value:u.EO.AllTags}]),d=(0,i.ref)(!1),v=(0,i.ref)({selectValue:[{value:u.EO.AllTags}],linkTag:!1}),h=(0,i.ref)({}),g=(0,i.ref)(),b=m(),w=b.config,y=b.changeLabelShowInLocal,A=b.getLabelTableShow,I=b.getLinksLabelShow,T=b.changeLabelShowTable,k=(0,i.ref)(!1),C=(0,i.computed)((function(){return v.value.linkTag})),S=(0,i.computed)((function(){return c.A.getters.initComponentParams})),N=(0,i.computed)((function(){return(0,f.Xk)(S.value.bizType,"searchFunKey")})),_=(0,i.computed)((function(){return c.A.state.tags.tagsGroupList})),L=(0,i.computed)((function(){var e=(0,i.unref)(n).map((function(e){return e.value}));return e.length&&![u.EO.AllTags].some((function(t){return e.includes(t)}))})),x=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];d.value=e},D=function(e){var t=e.id,o={value:t},i=n.value.findIndex((function(e){return e.value===t}));if(i>-1)n.value.splice(i,1);else{var r=[u.EO.NoTags,u.EO.AllTags];r.includes(t)?n.value=[o]:n.value=n.value.concat([o]).reduce((function(e,t){return r.includes(t.value)||e.push(t),e}),[])}v.value.selectValue=n.value,(0,f.cv)((0,p.get)(a,N.value),a),U()},$=function(e){var t;k.value=e,y(Number(k.value),Number((null===(t=v.value)||void 0===t?void 0:t.linkTag)||0))},F=function(e){v.value=(0,o.A)((0,o.A)({},v.value),{},{linkTag:e}),y(Number(k.value),Number(e||0))},B=function(e){e.size&&(n.value=n.value.filter((function(t){return!e.has(t.value)})))},P=function(){v.value={selectValue:[{value:u.EO.AllTags}],linkTag:C.value},n.value=[{value:u.EO.AllTags}]},E=function(){var e=v.value.selectValue.map((function(e){return e.value})),t={},a=function(t){return!!e.length&&e.every((function(e){return e===t}))};return t=a(u.EO.NoTags)?{labelIds:null,labelExists:!1}:a(u.EO.AllTags)?{labelIds:null,labelExists:null}:{labelIds:e,labelExists:null},{labelQuery:t}},O=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a,n;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=(0,o.A)((0,o.A)({},(0,p.cloneDeep)(S.value)),t),e.next=3,c.A.dispatch("".concat(u.Do.Tags,"/initTagsGroupList"),{params:a,commit:!1});case 3:return n=e.sent,e.abrupt("return",n);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),R=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a,n;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=(0,o.A)((0,o.A)({},(0,p.cloneDeep)(S.value)),t),Reflect.has(a,"keyword")&&Reflect.deleteProperty(a,"keyword"),e.next=4,c.A.dispatch("".concat(u.Do.Tags,"/initTagsGroupListIds"),{params:a,commit:!1});case 4:return n=e.sent,e.abrupt("return",n);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),j=function(){try{var e;null===a||void 0===a||null===(e=a.$refs)||void 0===e||null===(e=e.taggingLabelPanelRef)||void 0===e||e.deleteTagFetch()}catch(t){console.warn(t)}},z=function(){h.value={show:d.value,value:n.value,ref:"taggingLabelPanelRef",tagsGroupList:_.value,remoteFetchFun:O,remoteFetchIdsFun:R,showLinkTagsSwitchValue:v.value.linkTag,showLabelSettingSwitchValue:k.value},g.value={filterItemClick:D,linkTagsSwitchChange:F,close:function(){return x(!1)},emptyLinkClick:function(){return(0,f.vD)()},filterNoExistItemOfClick:B,labelSettingSwitchChange:$},e&&(g.value.remoteFetchFun=e);try{k.value=Boolean(A()),v.value.linkTag=Boolean(I())}catch(t){console.error(t)}},M=function(){if(V.value&&sessionStorage.getItem(V.value)){var e=JSON.parse(sessionStorage.getItem(V.value));Array.isArray(e)?n.value=e:"object"!==(0,r.A)(e)||Array.isArray(e)||null===e||(n.value=[{value:e.labelId}],v.value.selectValue=[{value:e.labelId}]),x(!0),(0,f.cv)((0,p.get)(a,N.value),a),U()}},V=(0,i.computed)((function(){var e=window.location.hash,t=new URLSearchParams(e.substring(e.indexOf("?")+1)),a=t.get("formId");return a})),U=function(){var e,t;sessionStorage.getItem(null===(e=S.value)||void 0===e?void 0:e.appId)&&sessionStorage.removeItem(null===(t=S.value)||void 0===t?void 0:t.appId)};(0,i.watchEffect)((function(){h.value.tagsGroupList=_.value,h.value.value=n.value,h.value.show=d.value,h.value.showLinkTagsSwitchValue=v.value.linkTag,h.value.showLabelSettingSwitchValue=k.value}));var W=function(e){var t=e.data,a=t.action,n=t.data;"shb.frame.activatedPage"==a&&M(),"changeAllLabelView"==a&&(k.value=Boolean(null===n||void 0===n?void 0:n.labelTableShow),v.value.linkTag=Boolean(null===n||void 0===n?void 0:n.linksLabel),T(null===n||void 0===n?void 0:n.labelTableShow),v.value.linkTag=Boolean(null===n||void 0===n?void 0:n.linksLabel))};return(0,i.onMounted)((function(){window.addEventListener("message",W)})),(0,i.onBeforeUnmount)((function(){window.removeEventListener("message",W)})),(0,i.onBeforeMount)((function(){z()})),{labelConfigTable:w.value,filterTagPanelShow:d,showTagOperatorButtonDot:L,filterTagsPanelBindAttr:h,filterTagsPanelBindOn:g,showLinkIntelligentTags:C,changeIntelligentTagsFilterPanelShow:x,builderIntelligentTagsSearchParams:E,resetIntelligentTagsSearchParams:P,getSessionFn:M,deleteTagFetch:j}},h=a(35730),g=a(83559),b=(a(24929),a(78485)),w=a(48956),y=a(69749),A=a(38715),I=a(84859),T=a(26183),k=a(52275),C=a(62361),S=(0,T.A)((function e(t,a,n,o){(0,k.A)(this,e),(0,C.A)(this,"success",!1),(0,C.A)(this,"code",1),(0,C.A)(this,"message",""),(0,C.A)(this,"result",void 0),(0,C.A)(this,"data",void 0),this.success=n,this.code=t,this.message=a,this.result=o,this.data=o}));(0,C.A)(S,"SUCCESS",0),(0,C.A)(S,"FAIL",1);var N=S,_=a(56582);function L(e,t){var a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=(0,i.ref)(new N(N.FAIL,"",!1,null)),r=(0,i.ref)(!1),s=(0,i.computed)((function(){return n.value.success})),l=(0,i.computed)((function(){return n.value.message})),c=function(i){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return r.value=!0,e((0,o.A)((0,o.A)({},i),t)).then((function(e){var t=Boolean((null===e||void 0===e?void 0:e.success)||(null===e||void 0===e?void 0:e.succ)||200==(null===e||void 0===e?void 0:e.status));n.value.message=(null===e||void 0===e?void 0:e.message)||"",t?(n.value.code=N.SUCCESS,n.value.success=!0,n.value.result=(null===e||void 0===e?void 0:e.result)||(null===e||void 0===e?void 0:e.data)):(n.value.code=N.FAIL,n.value.success=!1,s&&_.Ay.alert(n.value.message))}))["catch"]((function(e){n.value.code=N.FAIL,n.value.success=!1,n.value.result=null,n.value.message="系统错误"}))["finally"]((function(){r.value=!1}))};return[n,c,r,s,l]}var x=L,D=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a=(0,i.getCurrentInstance)(),r=a.proxy,d=(0,i.ref)([]),m=(0,i.ref)([]),v=(0,i.ref)(),T=(0,i.ref)({}),k=(0,i.ref)([]),C=(0,i.ref)([]),S=x(b.u3,void 0,!1),N=(0,n.A)(S,5),_=(N[0],N[1]),L=N[2],D=N[3],$=N[4],F=x(b.iL,void 0),B=(0,n.A)(F,5),P=B[0],E=B[1],O=B[2],R=B[3],j=B[4],z=x(b.B8,void 0),M=(0,n.A)(z,5),V=(M[0],M[1]),U=M[2],W=M[3],H=M[4],q=x(b.VS,void 0),K=(0,n.A)(q,5),G=(K[0],K[1]),J=K[2],Y=K[3],Q=K[4],X=(0,i.computed)((function(){return e||c.A.getters.initComponentParams})),Z=(0,i.computed)((function(){return r.$refs.taggingCompRef})),ee=(0,i.computed)((function(){return(0,f.Xk)(X.value.bizType,"searchFunKey")})),te=(0,i.computed)((function(){return(0,p.get)(r,(0,f.Xk)(X.value.bizType,"multiBizDataKeyArray"))})),ae=(0,i.computed)((function(){return X.value.from===u.Ns.Detail})),ne=function(){return new Promise(function(){var e=(0,l.A)((0,s.A)().mark((function e(t,a){var n,o,i,r,l;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!te.value||0!==te.value.length){e.next=3;break}return y.MessageBox.confirm((0,I.t)("common.base.intelligentTag.checkSelectTagTip"),(0,I.t)("common.base.toast"),{showCancelButton:!1,confirmButtonText:(0,I.t)("common.base.confirm")}),e.abrupt("return",t(!1));case 3:return d.value=[],n=(0,w.pH)({appId:X.value.appId,bizType:X.value.bizType,bizObjsArray:te.value,checkedTags:[]}),e.prev=5,e.next=8,(0,b.Op)({appId:n.appId,bizObjIds:n.bizObjs.map((function(e){return e.bizId}))});case 8:o=e.sent,i=o.success,r=o.data,l=void 0===r?[]:r,i&&(k.value=l),e.next=18;break;case 15:e.prev=15,e.t0=e["catch"](5),console.error("[ getIntelligentTagForBizIds error ]",e.t0);case 18:return C.value=[],e.abrupt("return",t(!0));case 20:case"end":return e.stop()}}),e,null,[[5,15]])})));return function(t,a){return e.apply(this,arguments)}}())},oe=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a,n,o;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=ae.value?r.getBizObjArrayData():te.value,n=d.value.filter((function(e){return e.enabled||Reflect.has(e,"personalLabel")})),o=(0,w.pH)({appId:X.value.appId||c.A.state.common.detailPageParams.formTemplateId,bizType:X.value.bizType,bizObjsArray:a,checkedTags:n,setMethod:t}),ae.value||(o.fromList=!0,o.addLabels=o.labels,o.addPersonalLabels=o.personalLabels,o.delLabels=C.value.filter((function(e){return"0"==(null===e||void 0===e?void 0:e.personalLabel)})).map((function(e){return{labelId:e.id,name:e.name}})),o.delPersonalLabels=C.value.filter((function(e){return"1"==(null===e||void 0===e?void 0:e.personalLabel)})).map((function(e){return{labelId:e.id,name:e.name}})),(0,f.$L)(o,["labels","setMethod","personalLabels"])),e.next=6,_(o);case 6:D.value?(y.Message.success((0,I.t)("common.base.saveSuccess")),Z.value&&(re(),Z.value.hidePopover(),ae.value||(d.value=[],T.value.value=[],setTimeout((function(){(0,f.cv)((0,p.get)(r,ee.value),r);try{var e;null===(e=r.$refs)||void 0===e||null===(e=e.taggingLabelPanelRef)||void 0===e||e.plainRestFetch()}catch(t){console.error("useTagging: ",t)}}),500))),ae.value&&r.refreshData((0,A.__)(d.value))):y.Message.warning($.value);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ie=function(){var e,t=(0,g.A)(m.value);try{var a=function(){var t=e.value;"add"===t.status&&(d.value=d.value.filter((function(e){return e.id!==t.id}))),"delete"===t.status&&(d.value.some((function(e){return e.id===t.id}))||d.value.push(t))};for(t.s();!(e=t.n()).done;)a()}catch(n){t.e(n)}finally{t.f()}re()},re=function(){m.value=[]},se=function(e){oe(e)},le=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,E({name:t.labelName,logoColor:t.color,description:""});case 2:R.value?(y.Message.success("新建成功"),T.value.localGroupLabelList={id:P.value.result,name:t.labelName,logoColor:t.color,status:"add"},null===(a=Z.value)||void 0===a||a.handleBack()):y.Message.warning(j.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ce=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,V({id:t.id,name:t.labelName,logoColor:t.color,description:""});case 2:W.value?(y.Message.success("编辑成功"),T.value.localGroupLabelList={id:t.id,name:t.labelName,logoColor:t.color,status:"edit"},null===(a=Z.value)||void 0===a||a.handleBack()):y.Message.warning(H.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ue=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,G({ids:[t]});case 2:Y.value?(y.Message.success("删除成功"),T.value.localGroupLabelList={id:t,status:"delete"},null===(a=Z.value)||void 0===a||a.handleBack()):y.Message.warning(Q.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),de=function(){(0,f.vD)()},me=function(){},fe=function(e,t){if(Array.isArray(t))d.value=t;else if(e)d.value.push(t),pe(t,"add");else{var a=d.value.findIndex((function(e){return t.id===e.id}));a>-1&&d.value.splice(a,1),pe(t,"delete")}},pe=function(e,t){var a=m.value.findIndex((function(t){return t.id===e.id}));if(a>-1){var n=m.value[a];(null===n||void 0===n?void 0:n.status)!==t&&m.value.splice(a,1)}else m.value.push((0,o.A)((0,o.A)({},e),{},{status:t}))},ve=function(){var e={list:u.$z.Increment,detail:u.$z.Cover};return X.value.from&&Reflect.get(e,X.value.from)||1},he=function(){var e=(0,l.A)((0,s.A)().mark((function e(t){var a,n;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=(0,o.A)((0,o.A)({},(0,p.cloneDeep)(X.value)),{},{appId:X.value.appId||c.A.state.common.detailPageParams.formTemplateId},t),e.next=3,c.A.dispatch("".concat(u.Do.Tags,"/initTagsGroupList"),{params:a,commit:!1});case 3:return n=e.sent,n.list=(0,f.AG)(n.list),e.abrupt("return",n);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ge=function(e){if(e.id){var t=k.value.findIndex((function(t){return t.id===e.id}));if(t>-1){var a,n=k.value.splice(t,1);(a=C.value).push.apply(a,(0,h.A)(n))}}},be=function(){v.value={save:oe,hidePopover:ie,manageTagsClick:de,change:fe,clear:ge,radioChange:me,personalSave:se,personalLabelAdd:le,personalLabelEdit:ce,personalLabelDelete:ue},T.value={value:ae.value?d.value:k.value,disabled:L.value,openPopoverHook:ae.value?void 0:ne,ref:"taggingCompRef",remoteFetchFun:he,operatorType:ve(),remoteSearch:!0,localGroupLabelList:{}},t&&(T.value.remoteFetchFun=t)},we=[(0,i.watchEffect)((function(){T.value.disabled=L.value||O.value||J.value||U.value,T.value.value=ae.value?d.value:k.value}))];return(0,i.onBeforeMount)((function(){be()})),(0,i.onBeforeUnmount)((function(){we.forEach((function(e){return e()}))})),{checkedTags:d,taggingBindAttr:T,taggingBindOn:v}},$=a(72964),F=a(75895),B=a(70136),P=function(e){var t=(0,i.computed)((function(){return e||c.A.getters.initComponentParams})),a=D(e),n=a.taggingBindAttr,r=a.taggingBindOn,s=a.checkedTags;if(t.value.from===u.Ns.Detail)return{taggingCheckedTags:s,taggingBindAttr:n,taggingBindOn:r};var l=v();return(0,o.A)((0,o.A)({},l),{},{taggingCheckedTags:s,taggingBindAttr:n,taggingBindOn:r})},E=function(e){var t=(0,$.EQ)(),a=(0,n.A)(t,1),o=a[0],r=(0,i.ref)(e),s=(0,i.computed)((function(){return r.value?Reflect.get(F.i,r.value):new B.k})),l=(0,i.computed)((function(){return s.value.singleBizDataKeyArray})),c=(0,i.computed)((function(){return s.value.key})),u=(0,i.computed)((function(){return o[c.value]})),d=(0,i.computed)((function(){return(0,p.get)(o,s.value.deleteKey)})),m=((0,i.computed)((function(){return(0,p.get)(o,"editOperatorValue")})),(0,i.computed)((function(){var e=s.value.refreshKeyArr;return null!==e&&void 0!==e&&e.length?null===e||void 0===e?void 0:e.map((function(e){return(0,p.get)(o,e)})):[]}))),f=(0,i.computed)((function(){return(0,p.isObject)(o[c.value])&&(0,p.get)(o[c.value],l.value)||[]})),v=function(e){r.value=e},h=function(e){var t;null!==(t=m.value)&&void 0!==t&&t.length&&m.value.forEach((function(e){return(0,p.isFunction)(e)&&e.call(o)}))},g=(0,i.computed)((function(){return{show:!o.loading,from:r.value,tagsMainDataList:f.value,bizData:u.value,updateTagsMainDataList:h,showTaggingButton:!d.value}}));return{tagsSingleComponentAttrs:g,moduleValueMainKey:c,tagsMainDataList:f,updateIntelligentTagsModule:v,updateTagsMainDataList:h}}},52340:function(e,t,a){a.d(t,{A:function(){return d}});var n=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.transferSuggestion"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("form-builder",{ref:"formBulider",attrs:{value:e.form,fields:e.fields,"form-editing-mode":"create"},on:{update:e.update}}):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("view.template.detail.transfer"))+" ")])],1)],1)},o=[],i=(a(2286),a(75069),a(74526)),r=a(80906),s={name:"tranfer-dialog",props:{config:{type:Object,default:function(){return{}}},taskId:{type:String,default:""},buildFormParams:{type:Function}},data:function(){return{visible:!1,pending:!1,form:{}}},computed:{fields:function(){var e=this.config,t=e.showAttached,a=e.checkAttached,n=e.showRemark,o=e.checkRemark;return[{fieldName:"forwardUserId",displayName:this.$t("view.template.detail.transferUser"),formType:"user",placeHolder:this.$t("common.placeholder.selectSomething",{0:this.$t("view.template.detail.transferUser")}),isNull:0,show:!0},{fieldName:"approveMessage",displayName:this.$t("view.template.detail.transferSuggestion"),formType:"textarea",isNull:o?0:1,show:n},{fieldName:"attached",displayName:this.$t("common.base.attachment"),formType:"attachment",isNull:a?0:1,show:t}].filter((function(e){return e.show}))}},methods:{update:function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;this.$set(this.form,n,a)},openDialog:function(){this.form=r.n_(this.fields,{}),this.visible=!0},buildParams:function(){var e=this.form,t=e.forwardUserId,a=void 0===t?{}:t,n=e.attached,o=void 0===n?[]:n,i=e.approveMessage;return a=a.userId,{attached:o,forwardUserId:a,approveMessage:i,flowTaskId:this.taskId,approveResult:this.config.code,nodeButtonName:this.config.enName}},submit:function(){var e=this;this.$refs.formBulider.validate(!1).then((function(t){if(!t)return Promise.reject("validate fail.");e.pending=!0,i.tx({formContentEditForm:e.buildFormParams(),finishTaskForm:e.buildParams()},!0).then((function(t){t.success?(e.$message.success(e.$t("view.template.detail.transferSuccess")),e.visible=!1,e.$emit("success")):e.$platform.alert(t.message)}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.log(e)}))}))["catch"]((function(t){e.pending=!1}))}}},l=s,c=a(49100),u=(0,c.A)(l,n,o,!1,null,"3106b532",null),d=u.exports},71523:function(e,t,a){a.d(t,{A:function(){return u}});var n=function(){var e=this,t=e._self._c;return t("base-modal",{staticClass:"tag-setting-modal",attrs:{title:e.$t("view.designer.rule.tagSetting.handleTag"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("el-form",{ref:"form",staticClass:"tag-setting-form",attrs:{model:e.form,"label-position":"left"}},[t("el-form-item",{attrs:{label:e.$t("view.designer.rule.tagSetting.selectTag")}},[t("el-select",{attrs:{multiple:""},model:{value:e.form.tags,callback:function(t){e.$set(e.form,"tags",t)},expression:"form.tags"}},e._l(e.tagListOptions,(function(e,a){return t("el-option",{key:a,attrs:{label:e.tagName,value:e.tagId}})})),1)],1)],1):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("buttons.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("buttons.save")))])],1)],1)},o=[],i=(a(35256),a(21484),a(16961),a(32807),a(75069),a(3402)),r={name:"on-tag-dialog",props:{bizIds:{type:Array,default:function(){return[]}},tagIdList:{type:Array,default:function(){return[]}},templateBizId:{type:String,default:""},appId:{type:String,default:""}},data:function(){return{visible:!1,pending:!1,form:{tags:[]},tagListOptions:[]}},watch:{visible:function(e,t){e&&(this.form.tags=this.tagIdList,this.getSelectListReq())}},methods:{submit:function(){this.saveOnTag()},saveOnTag:function(){var e=this,t=[],a=[];this.form.tags.map((function(e){a.push({tagId:e})})),this.bizIds.map((function(n){t.push({bizId:n,formTemplateId:e.templateBizId,appId:e.appId,paasTagFormContentEntityList:a})})),this.pending=!0,(0,i.a_)(t).then((function(t){t.code;var a=t.message,n=t.success;t.data;n?(e.$message.success(e.$t("common.base.saveSuccess")),e.visible=!1,e.$emit("saveOnTag")):e.$message.warning(a)}))["catch"]((function(e){console.log("err",e)}))["finally"]((function(){e.pending=!1}))},getSelectListReq:function(){var e=this,t={appId:this.appId,formTemplateId:this.templateBizId};(0,i.TO)(t).then((function(t){t.code;var a=t.data,n=void 0===a?[]:a,o=t.success;o&&(e.tagListOptions=n)}))["catch"]((function(e){console.log("err",e)}))}}},s=r,l=a(49100),c=(0,l.A)(s,n,o,!1,null,"7966b9f1",null),u=c.exports},78831:function(e,t,a){a.d(t,{A:function(){return h}});var n=a(71357),o=a(18885),i=a(42881),r=a(35730),s=(a(67880),a(36700),a(35256),a(21484),a(8326),a(16961),a(32807),a(88747),a(75069),a(48649)),l=a(57670),c=(l.qq.IntelligentTagsList,l.qq.IntelligentTagsTaggingRules,l.qq.IntelligentTagsUsage,l.qq.IntelligentTagsLogs,[{exportAlias:"intelligentLabel",fieldName:"intelligentLabel",displayName:"智能标签",isSystem:1}]),u=a(50152),d=a(14389),m=a(46995),f=a(72964),p=a(48956),v=a(92935),h=(0,s.defineComponent)({setup:function(){var e=(0,m.P)(),t=(0,f.rd)(),a=t.query,h=(0,s.ref)(c),g=function(e,t){var a=(0,p.wY)(e,t);d.A.commit("".concat(l.Do.Component,"/").concat(u.nv),a)},b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"labelList";return(0,v.isPlainObject)(e)?Array.isArray(e[t])?e[t]:[]:Array.isArray(e)?e.reduce((function(e,a){return a[t]&&Array.isArray(a[t])&&e.push.apply(e,(0,r.A)(a[t])),e}),[]):[]},w=function(e,t){return e.map((function(e){return e[t]}))};return(0,s.onBeforeMount)((0,i.A)((0,o.A)().mark((function t(){return(0,o.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:g(l.Vt.PAAS,a.formId),null===e||void 0===e||e.getSessionFn();case 2:case"end":return t.stop()}}),t)})))),(0,n.A)((0,n.A)({},e),{},{intelligentTagsExportFields:h,initIntelligentTagsParams:g,getLinkInitIntelligentTagsList:b,getLinkInitIntelligentTagsListShowTextArray:w})}})},90089:function(e,t,a){a.d(t,{A:function(){return m}});var n=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.shareLink"),show:e.visible,width:"480px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"share-modal-top"},[t("p",{staticClass:"tips"},[e._v(e._s(e.$t("view.template.detail.tip2")))]),t("el-input",{attrs:{disabled:""},model:{value:e.outerUrl,callback:function(t){e.outerUrl=t},expression:"outerUrl"}},[t("el-button",{attrs:{slot:"append"},on:{click:e.copyText},slot:"append"},[e._v(e._s(e.$t("common.base.copy")))])],1),e.shareData.pwd?t("div",{staticClass:"share-modal-top-password align-items-center"},[t("div",{staticClass:"tips"},[e._v(e._s(e.$t("common.base.password"))+"："+e._s(e.shareData.pwd))]),t("el-button",{attrs:{type:"text",disabled:e.pending},on:{click:e.reset}},[e._v(" "+e._s(e.$t("common.base.resetPwd"))+" ")])],1):e._e()],1),t("div",{staticClass:"share-modal-bottom"},[t("div",{ref:"qrcode",staticClass:"share-modal-bottom-left",attrs:{id:"qrcode"}}),t("div",{staticClass:"share-modal-bottom-right"},[t("p",{staticClass:"tips"},[e._v(e._s(e.$t("view.template.detail.byMobilePhoneScan")))]),t("el-button",{attrs:{type:"text"},on:{click:e.downloadQrcode}},[e._v(e._s(e.$t("common.base.download")))])],1)]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.close")))])],1)])},o=[],i=(a(75069),a(74526)),r=a(20548),s=a.n(r),l={name:"share-link-dialog",props:{shareData:{type:Object,default:function(){return{}}},formContentId:{type:String,default:""}},data:function(){return{visible:!1,pending:!1}},computed:{outerUrl:function(){var e;return(null===(e=this.shareData)||void 0===e?void 0:e.url)||""}},methods:{openDialog:function(){this.createQrcode(),this.visible=!0},reset:function(){var e=this;this.pending=!0,i.zq({formContentBizId:this.formContentId}).then((function(t){var a=t.success,n=t.message,o=t.data;if(!a)return e.$message.error(n);e.$message.success(e.$t("common.base.resetSuccess")),e.shareData.pwd=o||""}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.error("reset err",e)}))},createQrcode:function(){this.$refs.qrcode.innerHTML="";new(s())("qrcode",{width:100,height:100,text:this.outerUrl})},downloadQrcode:function(){var e=document.getElementById("qrcode"),t=e.getElementsByTagName("canvas"),a=document.createElement("a");a.href=t[0].toDataURL("image/png"),a.download=this.$t("view.template.detail.formQrCode"),a.click()},copyText:function(){var e=document.createElement("input");e.value=this.outerUrl,document.body.appendChild(e),e.select(),document.execCommand("Copy"),document.body.removeChild(e),this.$message.success(this.$t("view.template.detail.tip3"))}}},c=l,u=a(49100),d=(0,u.A)(c,n,o,!1,null,"46e5d300",null),m=d.exports},92367:function(e,t,a){a.d(t,{A:function(){return h}});var n=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.title,show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("form-builder",{ref:"formBulider",attrs:{value:e.form,fields:e.fields,"form-editing-mode":"create"},on:{update:e.update}}):e._e(),e.isApproveButtonCode&&e.enableNewSign?t("div",{staticClass:"approve-sign"},[t("div",{staticClass:"sign-label"},[t("span",{staticClass:"label-require"},[e._v(e._s(e.$t("common.base.approveSign")))]),t("span",{staticClass:"form-item-required"},[e._v("*")])]),e.enableLastSign&&e.approveSignConfig.lastSignData?t("div",{},[t("img",{attrs:{src:e.approveSignConfig.lastSignData}})]):t("div",{staticClass:"unsigned"},[t("div",[e._v(e._s(e.$t("common.base.approveSignTip1")))]),t("div",[e._v(e._s(e.$t("common.base.approveSignTip2")))])])]):e._e(),e.isProjectCancelBtn?[t("div",{staticClass:"tag-mess"},[t("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.$t("common.projectManage.cancelText2")))])],1)]:e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.submit")))])],1)],2)},o=[],i=a(18885),r=a(71357),s=a(42881),l=(a(67880),a(2286),a(80793),a(75069),a(74526)),c=a(80906),u=a(84859),d=a(19055),m={name:"approva-dialog",props:{config:{type:Object,default:function(){return{}}},taskId:{type:String,default:""},buildFormParams:{type:Function},approveSignConfig:{type:Object,default:function(){return{}}},projectTaskId:{type:String,default:""}},data:function(){return{visible:!1,pending:!1,form:{},backNodeId:""}},computed:{isProjectCancelBtn:function(){var e;return(null===(e=this.config)||void 0===e?void 0:e.enName)===d.A.CANCEL.value&&!!this.projectTaskId},title:function(){var e,t=d.A.AGREE,a=d.A.REFUSE;return[t.value,a.value].includes(this.config.enName)?this.$t("view.template.detail.approveSuggestion"):this.$t("view.template.detail.sthSuggestion",{data:(null===(e=this.config)||void 0===e||null===(e=e.nameLanguage)||void 0===e?void 0:e[u.Ay.locale])||this.config.cnName})},fields:function(){var e=this.config,t=e.showAttached,a=e.checkAttached,n=e.showRemark,o=e.checkRemark;return[{fieldName:"approveMessage",displayName:this.title,formType:"textarea",isNull:o?0:1,show:n},{fieldName:"attached",displayName:this.$t("common.base.attachment"),formType:"attachment",isNull:a?0:1,show:t}].filter((function(e){return e.show}))},isApproveButtonCode:function(){return 6===this.config.code||7===this.config.code},enableNewSign:function(){return this.approveSignConfig.newSign},enableLastSign:function(){return this.approveSignConfig.lastSign}},methods:{update:function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;this.$set(this.form,n,a)},openDialog:function(e){this.backNodeId=e,this.form=c.n_(this.fields,{}),this.visible=!0},buildParams:function(){var e=this.form,t=e.forwardUserId,a=void 0===t?{}:t,n=e.attached,o=void 0===n?[]:n,i=e.approveMessage,r=this.backNodeId;console.log("backNodeId",r);var s=this.isApproveButtonCode&&this.enableNewSign&&this.enableLastSign?this.approveSignConfig.lastSignData:null;a=a.userId;var l={attached:o,forwardUserId:a,approveMessage:i,sign:s,flowTaskId:this.taskId,approveResult:this.config.code,nodeButtonName:this.config.enName};return r&&(l.backNodeId=r),l},submit:function(){var e=this,t=d.A.AGREE,a=d.A.REFUSE,n=d.A.BACKTOME,o="";o=[t.value,a.value].includes(this.config.enName)?this.$t("common.base.approveSuccess"):"".concat(d.A.getName(this.config.enName)).concat(this.$t("common.base.messageStatus.success")),this.$refs.formBulider.validate(!1).then(function(){var t=(0,s.A)((0,i.A)().mark((function t(a){var s,c,u,d,m,f,p,v,h;return(0,i.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a){t.next=2;break}return t.abrupt("return",Promise.reject("validate fail."));case 2:if(!e.isProjectCancelBtn){t.next=9;break}return s=e.$t("common.projectManage.cancelTip2"),t.next=6,e.$platform.confirm(s);case 6:if(c=t.sent,c){t.next=9;break}return t.abrupt("return");case 9:if(u=e.isApproveButtonCode,d=e.enableNewSign&&!e.enableLastSign,m=e.enableNewSign&&e.enableLastSign&&!e.approveSignConfig.lastSignData,f=u&&(d||m),!f){t.next=15;break}return t.abrupt("return",e.$notify({title:e.$t("common.base.toast"),type:"warning",message:e.$t("common.base.approveSignTip2")}));case 15:e.pending=!0,p=e.buildFormParams(),v={formContentEditForm:p,finishTaskForm:e.buildParams(),version:p.version},h=l.tx,e.config.enName===n.value&&(h=l.T2,v=e.buildParams()),h((0,r.A)({},v),!0).then((function(t){if(t.success)e.$message.success({message:o,duration:2e3}),e.visible=!1,e.$emit("success");else{e.$notify({title:e.$t("common.base.toast"),type:"warning",message:t.message});var a=t.code;a>=3e3&&a<4e3&&setTimeout((function(){e.visible=!1,e.$emit("success")}),500)}}))["finally"]((function(){e.pending=!1}))["catch"]((function(e){return console.log(e)}));case 21:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())["catch"]((function(t){e.pending=!1}))}}},f=m,p=a(49100),v=(0,p.A)(f,n,o,!1,null,"7bde1122",null),h=v.exports}}]);