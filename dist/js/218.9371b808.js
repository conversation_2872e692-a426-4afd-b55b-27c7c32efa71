(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[218],{5390:function(e,t,n){"use strict";n.d(t,{nm:function(){return A},Ay:function(){return F}});n(80793),n(21633),n(33656);var i=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.designer.workFlow.setJudgeCondition"),show:e.visible,width:"750px"},on:{"update:show":function(t){e.visible=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("common.base.save"))+" ")])]},proxy:!0}])},[t("div",{staticClass:"condition-box"},[t("p",[e._v(" "+e._s(e.$t("view.designer.workFlow.label2"))+" ")]),e._l(e.conditionsList,(function(n,i){return t("div",{key:i,staticClass:"condition-box-content"},[t("div",{staticClass:"condition-box-content-item"},[0!==i?t("span",{staticClass:"condition-content-title"},[e._v(e._s(e.$t("view.designer.workFlow.or")))]):e._e(),t("div",{staticClass:"condition-box-content-main"},[t("div",{staticClass:"title-box"},[t("h1",[e._v(e._s(e.$t("view.designer.workFlow.conditionGroup")))]),0!==i?t("div",{staticClass:"tips",on:{click:function(t){return e.handleDelOrItem(i)}}},[t("i",{staticClass:"iconfont icon-delete"}),t("span",{staticClass:"txt"},[e._v(e._s(e.$t("view.designer.workFlow.deleteConditionGroup")))])]):e._e()]),t("ul",{staticClass:"condition-list"},e._l(n.subCondition,(function(o,a){return t("li",{key:a,staticClass:"condition-list-item"},[t("el-form",{ref:"formItem",refInFor:!0,staticClass:"form",attrs:{model:o,inline:""}},[e.isTaskMode?t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"content.tableName",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.designer.workFlow.taskFromFields")}),trigger:"change"}]}},[t("el-select",{staticClass:"filed-select",attrs:{size:"mini",placeholder:e.$t("common.base.pleaseSelect"),filterable:"",clearable:""},on:{change:function(t){return e.taskFormChange(t,i,a)}},model:{value:o.content.tableName,callback:function(t){e.$set(o.content,"tableName",t)},expression:"formItem.content.tableName"}},e._l(e.taskOptionsList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"content.fieldName",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.template.print.formFields")}),trigger:"change"}]}},[t("el-select",{staticClass:"filed-select",attrs:{size:"mini",placeholder:e.$t("view.template.print.formFields"),disabled:o.content.fieldName.includes("trigger"),filterable:"",clearable:""},on:{change:function(t){return e.formKeyChange(t,i,a)}},model:{value:o.content.fieldName,callback:function(t){e.$set(o.content,"fieldName",t)},expression:"formItem.content.fieldName"}},e._l(e.filterAfterFields(o.content.tableName),(function(e){return t("el-option",{key:e.id,attrs:{label:e.displayName,value:e.fieldName}})})),1)],1),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"expression",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.designer.workFlow.discriminator")}),trigger:"change"}]}},[t("el-select",{staticClass:"cond-select",attrs:{size:"mini",placeholder:e.$t("view.designer.workFlow.discriminator"),disabled:o.content.fieldName.includes("trigger")},model:{value:o.expression,callback:function(t){e.$set(o,"expression",t)},expression:"formItem.expression"}},e._l(e.getConditions(o.content.fieldName),(function(e){return t("el-option",{key:e.val,attrs:{label:e.name,value:e.val,disabled:e.disable}})})),1)],1),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"target",size:"mini",rules:[{required:!0,message:e.placeholderText(o.content.fieldName),trigger:["change","blur"]}]}},["out_targetIds"==o.content.fieldName?[t("el-select",{staticClass:"filed-select",staticStyle:{"margin-left":"1px"},attrs:{filterable:"","collapse-tags":"","value-key":"id",placeholder:e.$t("common.part.target")},model:{value:o.target,callback:function(t){e.$set(o,"target",t)},expression:"formItem.target"}},e._l(e.repertories,(function(e){return t("el-option",{key:e.id,attrs:{value:e,label:e.name,origin:e}})})),1)]:"text"!==e.getRenderComponentName(o.content.fieldName)?[t(e.getRenderComponentName(o.content.fieldName),{tag:"component",staticClass:"input-box",attrs:{value:o.target,field:e.orderFieldNameGetFieldItem(o.content.fieldName)},on:{input:function(t){return e.input(t,i,a,o.content.fieldName)}}})]:[t("el-input",{staticClass:"input",attrs:{size:"mini",disabled:""===o.expression,placeholder:e.$t("view.designer.workFlow.inputValue")},model:{value:o.target,callback:function(t){e.$set(o,"target","string"===typeof t?t.trim():t)},expression:"formItem.target"}})]],2)],1),t("el-button",{staticClass:"icon-btn",attrs:{icon:"iconfont icon-delete",disabled:e.disabledDeleteBtn(n.subCondition)},on:{click:function(t){return e.handleDelAndItem(i,a)}}})],1)})),0),t("el-button",{staticClass:"or-btn",attrs:{type:"primary",icon:"el-icon-plus",plain:""},on:{click:function(t){return e.handleAddAnd(i)}}},[e._v(" "+e._s(e.$t("view.designer.workFlow.andCondition"))+" ")])],1)])])})),t("el-button",{staticClass:"and-btn",attrs:{type:"primary",icon:"el-icon-plus",plain:""},on:{click:e.handleAddOrItem}},[e._v(" "+e._s(e.$t("view.designer.workFlow.orCondition"))+" ")])],2)])},o=[],a=n(37801),r=n(35730),l=n(81798),s=n(18885),c=n(42881),d=n(71357),u=(n(98316),n(67880),n(2286),n(44807),n(62838),n(3923),n(36700),n(35256),n(21484),n(27408),n(7509),n(16961),n(54615),n(7354),n(89370),n(32807),n(19944),n(33438),n(55650),n(75069),n(62830),n(93431),n(69594),n(13262),n(68735),n(56582),n(33921)),f=n(92935),m=n.n(f),p=n(84859),v=n(44309),h=n(12139),g=n(50651),b=n(87512),y=n(32759),w=n(87),k={fieldName:"",type:"text",setting:{}},A={subCondition:[{content:(0,d.A)({},k),expression:"",target:""}],conditionRelation:200},x={name:"condition-modal",inject:["flowData","mode"],data:function(){return{visible:!1,pending:!1,conditions:[{name:p.Ay.t("common.base.equal"),val:"==",disable:!1},{name:p.Ay.t("common.base.notEqual"),val:"!=",disable:!1},{name:p.Ay.t("common.base.greaterThan"),val:">",disable:!1},{name:p.Ay.t("view.designer.workFlow.greaterThanOrEqual"),val:">=",disable:!1},{name:p.Ay.t("common.base.lessThan"),val:"<",disable:!1},{name:p.Ay.t("view.designer.workFlow.lessThanOrEqual"),val:"<=",disable:!1}],conditionsList:[{subCondition:[{content:(0,d.A)({},k),expression:"",target:""}],conditionRelation:200}],setting:{},repertories:[],taskCustomNode:[]}},computed:(0,d.A)((0,d.A)({},(0,g.L8)({conditionOptionsListMap:"flowEdgeSupportConditionMap",flowEdgeSupportConditionFields:"flowEdgeSupportConditionFields"})),{},{taskFlowExtendGray:function(){var e,t=(0,w.zO)(window);return Boolean(null===(e=t.grayAuth)||void 0===e?void 0:e.taskFlowExtend)},cell:function(){return this.flowData.cell||{}},nodeId:function(){return this.flowData.cell.id},formTypesConditions:function(){return u.A.formTypesConditions},formComponents:function(){return u.A.formComponents},isTaskMode:function(){return this.mode===v.A.TASK},taskTypeId:function(){return this.$route.query.taskTypeId},taskFlowType:function(){return this.$route.query.taskFlowType},isCustomNode:function(){return"isCustomNode"===this.$route.query.isCustomNode},currentNodeId:function(){return this.$route.query.currentNodeId},isCommonNode:function(){return"1"==this.$route.query.isCommonNode},taskOptionsList:function(){var e=[{label:this.$t("view.designer.workFlow.task"),value:"task"}],t=[{label:this.$t("view.designer.workFlow.taskReceipt"),value:"task_receipt"},{label:this.$t("view.designer.workFlow.taskReceiptAmount"),value:"task_receipt_amount"}],n=[{label:this.$t("common.pageTitle.pageSparepartApply"),value:"sparePart"}],i=[{label:this.$t("common.pageTitle.pageEventView"),value:"event"},{label:this.$t("common.paas.event.eventReceipt"),value:"eventReceipt"}];return"sparePart"==this.$route.query.moduleType?[].concat(e,n):"eventModule"==this.$route.query.moduleType?i:this.isCommonNode?this.taskCustomNode:["create","allot","start"].includes(this.taskFlowType)||this.isCustomNode?e.concat(this.taskCustomNode):e.concat(this.taskCustomNode).concat(t)}}),watch:{nodeId:{handler:function(e){this.setting=this.cell.data},immediate:!0}},methods:{getTaskAllNode:function(){var e=this;return(0,c.A)((0,s.A)().mark((function t(){var n,i,o,a,r;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.taskCustomNode=[],e.taskFlowExtendGray){t.next=4;break}return t.abrupt("return");case 4:return t.next=6,(0,y.uL)({taskTypeId:e.taskTypeId});case 6:n=t.sent,i=n.result,o=null===i||void 0===i?void 0:i.findIndex((function(t){return t.id===e.currentNodeId})),e.isCommonNode?e.taskCustomNode.push({label:null===(a=i[o])||void 0===a?void 0:a.title,value:null===(r=i[o])||void 0===r?void 0:r.id}):null===i||void 0===i||i.forEach((function(t,n){["normal","accept","start"].includes(t.type)&&n<=o&&e.taskCustomNode.push({label:t.title,value:t.id})})),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](0),console.error(t.t0);case 15:case"end":return t.stop()}}),t,null,[[0,12]])})))()},checkIsShowDynamicComponent:function(e){return"text"!==this.getRenderComponentName(e.content.fieldName)&&!this.isNullOrNotNullExpression(e.expression)},getTargetValue:function(e,t,n){var i=this.orderFieldNameGetFieldItem(t,n),o={tag:{typeString:"[object Array]",defaultValue:[]},customer:{typeString:"[object Array]",defaultValue:[]},linkman:{typeString:"[object Object]",defaultValue:null}};try{(0,f.isString)(e)&&(e=JSON.parse(e))}catch(a){console.warn("[JSON.parse Error Value]",a)}return Reflect.has(o,i.formType)&&Object.prototype.toString.call(e)!==o[i.formType].typeString&&(e=o[i.formType].defaultValue),"between"!==n||i.originFormType!==b.E.Currency||Array.isArray(e)||(e=["",""]),e},handleExpressionChange:function(e,t){var n=this;this.isNullOrNotNullExpression(e)&&(t.target=""),this.$nextTick((function(){n.clearAllValidate()}))},isNullOrNotNullExpression:function(e){return["is_null","is_not_null"].includes(e)},disabledDeleteBtn:function(e){if(1===e.length&&1===this.conditionsList.length)return!0},placeholderText:function(e){var t=this.orderFieldNameGetFieldItem(e),n=["text","number","formula"];return t&&(n.includes(t.formType)||""===e)?this.$t("view.designer.workFlow.placeholder1"):this.$t("view.designer.workFlow.placeholder2")},getRenderComponentName:function(e,t){var n;if(null!==(n=this.conditionOptionsListMap[e])&&void 0!==n&&n.formType)return"between"===t?"form-condition-between":this.conditionOptionsListMap[e].formType;var i=this.orderFieldNameGetFieldItem(e);if(i){var o=this.orderFieldNameGetFieldItem(e).formType,a=this.formComponents;if(a[o])return a[o]}return"text"},input:function(e,t,n,i){"form-user"===this.getRenderComponentName(i)&&"{}"===JSON.stringify(e)?this.conditionsList[t].subCondition[n].target=[]:this.conditionsList[t].subCondition[n].target=e,this.clearAllValidate()},taskFormChange:function(e,t,n){var i=this;return(0,c.A)((0,s.A)().mark((function e(){return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.conditionsList[t].subCondition[n].content.fieldName="",i.formKeyChange("",t,n);case 2:case"end":return e.stop()}}),e)})))()},formKeyChange:function(e,t,n){var i=this;this.conditionsList[t].subCondition[n].expression="";var o=this.orderFieldNameGetFieldItem(e),a=o.formType,r=o.setting;u.A.supportFormTypes.includes(a)&&(this.conditionsList[t].subCondition[n].content.type=a,this.conditionsList[t].subCondition[n].content.setting=Object.assign({},r)),this.conditionsList[t].subCondition[n].target="user"===a?[]:"",this.$nextTick((function(){i.clearAllValidate()}))},filterAfterFields:function(e){if(!this.isTaskMode){var t=this.conditionOptionsListMap,n=[];for(var i in t)n.push(t[i].field);return n}var o=this.flowData.fields;return this.isTaskMode&&e&&(o=this.flowData.fields.filter((function(t){return t.tableName===e}))),o.filter((function(e){return u.A.supportFormTypes.includes(e.formType)}))},getConditions:function(e){var t=this.orderFieldNameGetFieldItem(e);if(t){var n,i=t.formType;return this.isTaskMode?this.formTypesConditions.get(i):(null===(n=this.conditionOptionsListMap[e])||void 0===n?void 0:n.expressionList)||[]}return[]},orderFieldNameGetFieldItem:function(e,t){if(e){var n,i;if(null!==(n=this.conditionOptionsListMap[e])&&void 0!==n&&n.field)return null===(i=this.conditionOptionsListMap[e])||void 0===i?void 0:i.field;var o=this.flowData.fields.filter((function(t){return t.fieldName===e}))[0];return"object"===(0,l.A)(o)&&"select"===o.formType&&(o.setting.selectType=1),o}return""},clearAllValidate:function(){this.$refs["formItem"].forEach((function(e){return e.clearValidate()}))},handleDelOrItem:function(e){this.conditionsList.splice(e,1)},show:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.getTaskAllNode(),this.visible=!0;var n=Object.keys(t).length>0?t:this.cell.data;if(Array.isArray(n.conditionObject)&&n.conditionObject.length>0){var i=n.conditionObject[0];if(Reflect.has(i,"subCondition")){if(Array.isArray(i.subCondition)&&i.subCondition.length>0){var o=i.subCondition[0];"string"===typeof o.content?this.conditionsList=[m().cloneDeep(A)]:this.conditionsList=m().cloneDeep(n.conditionObject).map((function(t){return Array.isArray(t.subCondition)&&t.subCondition.forEach((function(n,i){var o=e.flowEdgeSupportConditionFields.find((function(e){var t;return(null===e||void 0===e?void 0:e.fieldName)===(null===n||void 0===n||null===(t=n.content)||void 0===t?void 0:t.fieldName)}))||e.flowData.fields.find((function(e){var t;return(null===e||void 0===e?void 0:e.fieldName)===(null===n||void 0===n||null===(t=n.content)||void 0===t?void 0:t.fieldName)}));o||(t.subCondition[i]={content:(0,d.A)({},k),expression:"",target:""})})),t}))}}else switch(n.conditionRelation){case 100:var a=[];n.conditionObject.forEach((function(e){var t=m().cloneDeep(A);if("string"===typeof e.content&&e.content.includes("trigger")){var n=e.content;e.content=(0,d.A)({},k),e.content.fieldName=n}t.subCondition=[e],a.push(t)})),this.conditionsList=a;break;case 200:var l=m().cloneDeep(A);l.subCondition=(0,r.A)(n.conditionObject),this.conditionsList=[l];break}}else this.conditionsList=[m().cloneDeep(A)];"sparePart"==this.$route.query.moduleType&&this.fetchAllRepertory(),this.$nextTick((function(){e.clearAllValidate()}))},handleAddAnd:function(e){var t={content:(0,d.A)({},k),expression:"",target:""};return this.conditionsList[e].subCondition.push(t),t},handleDelAndItem:function(e,t){var n=this;this.conditionsList[e].subCondition.length>1||this.conditionsList[e].subCondition.length>1?this.conditionsList[e].subCondition.splice(t,1):this.conditionsList.splice(e,1),this.$nextTick((function(){return n.clearAllValidate()}))},handleAddOrItem:function(){this.conditionsList.push(m().cloneDeep(A))},validateForm:function(){var e=this.$refs["formItem"],t=[];return e.forEach((function(e){var n=new Promise((function(t,n){e.validate((function(e){return e&&t()}))}));t.push(n)})),t},submit:function(){var e=this,t=this.conditionsList;Promise.all(this.validateForm()).then((function(){var n=(0,d.A)((0,d.A)({},e.flowData.cell.data),{},{name:"",isCondition:!0,conditionRelation:100,conditionObject:(0,r.A)(t)});e.cell.setData(n,{overwrite:!0,deep:!0,silent:!0}),e.$emit("submit",(0,r.A)(t)),e.visible=!1}))},fetchAllRepertory:function(){var e=this;return(0,c.A)((0,s.A)().mark((function t(){var n;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,h.Yu();case 3:n=t.sent,e.repertories=(n||[]).map((function(e){return{id:e.id,name:e.name}})),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.log(t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},validatorBetween:function(e,t){var n=(0,a.A)(e,3),i=(n[0],n[1]),o=n[2];if("between"===t.expression&&Array.isArray(i)){if(!i[0])return o("请输入起始值");if(!i[1])return o("请输入结束值");if(Number(i[0])>=Number(i[1]))return o("介于判断条件，起始不得大于等于结束")}o()}}},C=x,_=n(49100),N=(0,_.A)(C,i,o,!1,null,null,null),F=N.exports},11592:function(e,t,n){"use strict";n.d(t,{i:function(){return o}});n(2286),n(35256),n(16961),n(54615),n(32807),n(75069);var i=n(80906),o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return e.map((function(e){return e.subFormFieldList=o(e.subFormFieldList,i.hL(e)),e})).filter((function(e){return!e.isHidden})).filter((function(e){var n=e.isSystem,i=e.setting,o=void 0===i?{}:i;return!t||!n||n&&(o.isShow||void 0==o.isShow)}))}},34117:function(e,t,n){"use strict";n.d(t,{A:function(){return Ee}});var i=n(17319),o=n.n(i),a=n(37801),r=n(62361),l=(n(33438),n(48649)),s=n(43667),c=n(51280),d=n(72964),u=n(71357),f=(n(80793),n(55650),n(21633),n(80602)),m=n(66681),p=n(64055);function v(e){var t=(0,l.computed)((function(){return e instanceof c.fh}));return{isOldFlowDesignNode:t}}var h=(0,l.defineComponent)({name:"node-config-name",props:{node:{type:[Object,c.bP,c.oH],default:function(){return{}}}},setup:function(e,t){var n=e.node,i=t.emit,s=(0,d.J0)(!1),c=(0,a.A)(s,2),h=c[0],g=c[1],b=(0,d.J0)(n.title),y=(0,a.A)(b,2),w=y[0],k=y[1],A=(0,d.EQ)(),x=(0,a.A)(A,1),C=x[0],_=(0,l.computed)((function(){return C.$i18n.locale})),N=v(n),F=N.isOldFlowDesignNode,S=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];g(e)},T=function(e){"Enter"===e.key&&S(!1)},I=function(e){F.value&&n.attr("text/textWrap/text",e,{ignoreHistory:!0})},L=function(e){var t=e;n.title=t,n.data.name=e,k(t),I(t)},D=function(){var e,t=n.title,i=n.data.attribute.nameLanguage,o=void 0===i?{}:i,a=C.$i18n.locale;0===Object.keys(o).length&&(o=(0,u.A)((0,u.A)({},(0,p.T2)()),{},(0,r.A)({},a,t))),C.$fast.languageSetting.show({title:C.$t("common.base.languageSetting"),type:"input",languageDefaultValueObj:(0,u.A)((0,u.A)({},o),{},(0,r.A)({},a,(null===(e=o)||void 0===e?void 0:e[a])||n.title))}).then((function(e){n.data.attribute["nameLanguage"]=e,n.title=e[a],n.data.name=e[a],F.value&&k(e[a]),I(e[a])}))},O=function(e){var t=e.target.value;if(!t){var i=m.A.getName(n.type);n.data.name=i,n.title=i,I(i),F.value&&k(i)}var o=n.data.attribute.nameLanguage,a=void 0===o?{}:o;n.data.attribute.nameLanguage=(0,u.A)((0,u.A)({},a),{},(0,r.A)({},_.value,n.title)),S(!1)};return function(){return(0,l.h)("div",{class:"node-edit-name__box"},[(0,l.h)("div",{class:"node-edit-name__main"},[h.value?(0,l.h)("div",{class:"node-edit-name__title"},[(0,l.h)("el-input",o()([{class:"node-edit-name__input",attrs:{value:w.value},nativeOn:{keydown:function(e){return T(e)}},on:{input:L,blur:O}},{directives:[{name:"input-filter",value:n.title,arg:"special-letter"}]}]),[(0,p.g9)()?(0,l.h)("i",{slot:"suffix",class:"iconfont icon-earth",on:{click:D}}):null])]):(0,l.h)("div",{class:"node-edit-name__content"},[n.type&&!n.type.includes(f.A.CONDITION)?(0,l.h)("div",{class:"icon el-icon-edit-outline",on:{click:function(){return S(!0)}}}):null,(0,l.h)("div",{class:"title"},[n?n.title:""])]),(0,l.h)("i",{class:"el-icon-close",on:{click:function(){return i("handleCloseDrawer")}}})])])}}}),g=(n(67880),n(35256),n(21484),n(8326),n(16961),n(32807),n(88747),n(75069),n(57309)),b=n(44309),y=n(67142),w=n(35730),k=(n(87313),n(2286),n(3923),n(89716),n(76119),n(14126),n(54615),n(89370),n(24929),n(13262),n(79690)),A=n(96732),x=n(23403),C=n(50653),_=n(24682),N=n(84859),F=n(92935),S=n(12986),T={initialize:S.A.methods.initialize,formFieldsHandler:S.A.methods.formFieldsHandler,setFieldAuth:S.A.methods.setFieldAuth},I=(0,l.defineComponent)({name:"node-config-form-auth",components:(0,r.A)({},C.A.name,C.A),setup:function(){var e=(0,l.inject)("node"),t=(0,l.inject)("flowData"),n=(0,d.J0)([]),i=(0,a.A)(n,2),s=i[0],c=i[1],u=(0,l.computed)((function(){var t;return(null===(t=e.data.attribute)||void 0===t?void 0:t.fieldAuthorityList)||[]})),m=(0,l.computed)((function(){return e.type==f.A.END_NODE})),p=(0,l.computed)((function(){return e.type==f.A.CARBON_COPY_NODE})),v=(0,l.computed)((function(){return m.value||p.value||!t.fields.length})),h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;return e.filter((function(e){return!C.e.includes(e.formType)})).every((function(e){return e[t]}))},g=function(){var e=(0,F.cloneDeep)(t.fields);T.formFields=e,T.fields=e,T.value=(0,l.unref)(u),T.fixedFields=k.i.map((function(e){return e.sysGroup=!0,e})),T.initialize();var n=_.A.methods.getGroupField.call(T,T.formFields,!1).map((function(e){return e.visibleCheckedAll=h(e.children,x.FW.Visible),e.revisableCheckedAll=h(e.children,x.FW.Revisable),e.requiredCheckedAll=h(e.children,x.FW.Required),e.expand=!0,e})),i=T.fixedFields;n.unshift({separator:{displayName:N.Ay.t("common.form.fieldGroupName.system2"),fieldName:"sysField"},children:i,visibleCheckedAll:h(i,x.FW.Visible),revisableCheckedAll:h(i,x.FW.Revisable),requiredCheckedAll:h(i,x.FW.Required),expand:!0}),c(n)},b=function(){var t=s.value.reduce((function(e,t){return e.push.apply(e,(0,w.A)(t.children)),e}),[]);e.data.attribute.fieldAuthorityList=A.A.packToData(t)},y=function(e,t,n){var i=s.value,o=i[t];o[n]=e;var a=(0,r.A)((0,r.A)((0,r.A)({},x.CQ.VisibleCheckedAll,(function(){return!0})),x.CQ.RevisableCheckedAll,(function(e){return!C.e.includes(e.formType)})),x.CQ.RequiredCheckedAll,(function(e){return!C.e.includes(e.formType)})),l=o.children.filter((function(e){return a[n](e)}));T.setFieldAuth(l,k.W[n],e),e||"visibleCheckedAll"!==n||(T.setFieldAuth(l,"revisable",!1),o.revisableCheckedAll=!1),e&&"revisableCheckedAll"===n&&(T.setFieldAuth(o.children,"visible",!0),o.visibleCheckedAll=!0),e&&n===x.CQ.RequiredCheckedAll&&(T.setFieldAuth(l,x.FW.Required,!0),o.visibleCheckedAll=!0),c(i),b()},S=function(e){var t=s.value,n=t[e];n.children.forEach((function(e){var t=e.subFormFieldList,n=void 0===t?[]:t;n.length&&(e.visible=n.some((function(e){return e.visible})),e.revisable=n.some((function(e){return e.revisable})))})),I(n),b()},I=function(e){e.visibleCheckedAll=h(e.children,x.FW.Visible),e.revisableCheckedAll=h(e.children,x.FW.Revisable),e.requiredCheckedAll=h(e.children,x.FW.Required)},L=function(e){return""!==e.separator||v.value?v.value||"sysField"===e.separator.fieldName:e.children.every((function(e){return C.e.includes(e.formType)}))},D=function(e,t){var n=s.value[e],i=document.getElementsByClassName("fields-auth-panel")[e];t&&(n.domHeight=i.offsetHeight+"px",i.style.height=i.offsetHeight+"px"),i.style.display="block",requestAnimationFrame((function(){i.style.height=n.expand?n.domHeight:"0px"})),s.value[e].expand=!s.value[e].expand},O=function(e,t){if(!s.value[t].expand&&e.target){var n=e.target;n.style.display="none"}};return g(),function(){return(0,l.h)("div",{class:"fields-auth-container__v2"},[(0,l.unref)(s).map((function(e,t){return(0,l.h)("div",{class:"fields-auth-group"},[(0,l.h)("div",{class:"fields-auth-group__top"},[(0,l.h)("h3",[e.separator.displayName||N.Ay.t("view.designer.workFlow.customInfo")]),(0,l.h)("span",{on:{click:function(){return D(t,e.expand)}}},[e.expand?N.Ay.t("common.base.collapse"):N.Ay.t("common.base.expand")])]),(0,l.h)("div",{class:"fields-auth-panel",on:{transitionend:function(e){return O(e,t)}}},[(0,l.h)("div",{class:"fields-auth-panel__title"},[(0,l.h)("span",{class:"fields-auth-panel__title-item"},[N.Ay.t("view.template.print.formFields")]),(0,l.h)("span",{class:"fields-auth-panel__title-item"},[(0,l.h)("el-checkbox",{attrs:{value:e.visibleCheckedAll},on:{change:function(e){return y(e,t,x.CQ.VisibleCheckedAll)}}},[N.Ay.t("common.base.canRead")])]),(0,l.h)("span",{class:"fields-auth-panel__title-item"},[(0,l.h)("el-checkbox",{attrs:{value:e.revisableCheckedAll,disabled:L(e)},on:{change:function(e){return y(e,t,x.CQ.RevisableCheckedAll)}}},[N.Ay.t("common.base.edit")])]),(0,l.h)("span",{class:"fields-auth-panel__title-item"},[(0,l.h)("el-checkbox",{attrs:{value:e.requiredCheckedAll,disabled:L(e)},on:{change:function(e){return y(e,t,x.CQ.RequiredCheckedAll)}}},[N.Ay.t("common.base.isRequire")])])]),(0,l.h)("custom-form-fields",o()([{attrs:{fields:e.children,disabled:L(e),flowDesignVersion:"v2",showRequired:!0}},{on:{updateCheckAuthField:function(){return S(t)}}}]))])])}))])}}}),L=(n(27408),n(55872),n(62830),n(51668)),D=n(13242),O='package cn.publink.shb.application.facade.controller.connector.outside;\n\nimport cn.publink.shb.application.service.common.service.ShbMatchRuleService;\nimport com.alibaba.fastjson.JSONObject;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\n\n/**\n * 执行条件判断的示例\n * 1.fromData为前置节点的业务数据，例如某工单表单数据，某客户表单数据\n * 数据结构为typeId:fieldName。例如某工单的类型typeId=bdf4d1c4-8d1f-458e-b4fc-24835499329e，fromData数据如下\n * {\n *   "bdf4d1c4-8d1f-458e-b4fc-24835499329e:taskNo": "TAQ125024030520",\n *   "bdf4d1c4-8d1f-458e-b4fc-24835499329e:customer": {\n *     "serialNumber": "zmjjkk",\n *     "name": "zmjjkk",\n *     "id": "9ea625a6-eb22-11ee-8999-00163f00409a"\n *   },\n *   "bdf4d1c4-8d1f-458e-b4fc-24835499329e:templateId": "bdf4d1c4-8d1f-458e-b4fc-24835499329e",\n *   "bdf4d1c4-8d1f-458e-b4fc-24835499329e:createTime": "2024-03-28 14:47:01"\n * }\n *\n * 2.最后返回true/false 表示条件匹配或不匹配\n * 3.可以通过@Autowired注入spring容器中的bean\n * 详细说明查看文档：https://www.yuque.com/shb/help2/ox8c8k8i3hp1g6do#N8WYr\n */\npublic class ShbMatchRuleDemo implements ShbMatchRuleService {\n\n  private final static Logger log = LoggerFactory.getLogger("ShbMatchRuleDemo");\n\n  @Override\n  public boolean match(JSONObject fromData) {\n    //编写判断条件的代码\n    return fromData.containsKey("bdf4d1c4-8d1f-458e-b4fc-24835499329e:taskNo");\n  }\n}\n',E='package cn.publink.shb.application.facade.controller.connector.outside;\n\nimport cn.publink.shb.application.service.common.service.ShbListCodeService;\nimport com.alibaba.fastjson.JSONObject;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\n\nimport java.util.List;\nimport java.util.ArrayList;\n\n/**\n * 执行条件判断的示例\n * 1.fromData为前置节点的业务数据\n * 数据结构为PaasFormContentVO。例如某表单类型，fromData数据如下\n * {\n *   "appId": "3e770c44-284e-4a3b-b5c9-8df4ed07dce1",\n *   "bizId": "8a3d977a-c963-4d50-8228-249ac6a24b89",\n *   "flowStatus": 1,\n *   "paasFormValueList": [{\n *     "fieldName": "serialNumber",\n *     "formContentBizId": "8a3d977a-c963-4d50-8228-249ac6a24b89",\n *     "templateBizId": "cae0c7fd-29d3-45d8-9c9c-56100df8603e",\n *     "value": ""THH24091903402""\n *   }],\n *   "templateBizId": "cae0c7fd-29d3-45d8-9c9c-56100df8603e",\n *   "templateName": "xx测试02",\n *   "tenantId": "85c4d9182a09b612adb5795xxxb736f",\n *   "version": 0\n * }\n *\n * 2.最后返回人员列表作为节点负责人\n * 3.可以通过@Autowired注入spring容器中的bean\n * 详细说明查看文档：https://www.yuque.com/shb/help2/ox8c8k8i3hp1g6do#N8WYr\n */\npublic class ShbUserListDemo implements ShbListCodeService {\n\n  private final static Logger log = LoggerFactory.getLogger("ShbUserListDemo");\n\n  @Override\n  public List<String> execute(JSONObject fromData) {\n    //编写判断条件的代码\n    \n    List<String> list = new ArrayList<>();\n    list.add("ce803da4-2d22-11ee-8999-00163fxxxx9a");\n    list.add("12ad84bf-fd19-11ec-8999-00163fxxxx9a");\n    return list;\n  }\n}',R=n(87),M=n(16029),j=n(32914),P=n(56268),$=function(){var e=this,t=e._self._c;return t("div",{staticClass:"action-app-codemirror"},[t("codemirror",{attrs:{value:e.value,options:e.cmOptions},on:{input:e.onCmCodeChange}})],1)},z=[],q=n(34219),B=(n(57968),n(73461),n(65172),{components:{codemirror:q.codemirror},props:{value:{type:String,default:function(){return""}}},data:function(){return{code:"",cmOptions:{tabSize:4,mode:"text/x-java",theme:"dracula",lineNumbers:!0,line:!0,highlightDifferences:!0}}},methods:{onCmCodeChange:function(e){this.$emit("change",e)}}}),V=B,U=n(49100),W=(0,U.A)(V,$,z,!1,null,"53a5dd86",null),G=W.exports,K=n(14389),H=n(69396),J=L.A.NODE_OWNER,Q=L.A.DEPT_MANAGER,Y=L.A.USER,X=(L.A.CUSTOMER,L.A.FORM_CUSTOMER),Z=L.A.FORM_LINK_MAN,ee=L.A.NODE_SUBMITTER,te=L.A.USER_PHOTO,ne=L.A.USER_EMAIL,ie=L.A.FORM_TAG,oe=L.A.FORM_TAG_SUPERVISOR,ae=L.A.FORM_SERVICE_PROVIDER,re=(0,l.defineComponent)({name:"node-config-candidate",components:(0,r.A)((0,r.A)((0,r.A)({},P.F.name,P.F),j.A.name,j.A),"actionCodemirror",G),setup:function(){var e,t,n,i=this,s=(0,l.inject)("node"),c=(0,l.inject)("flowData"),f=(0,l.inject)("moduleType"),m=(0,l.inject)("mode"),p=(0,D.s)(),h=p.visible,g=p.showDialog,b=p.hideDialog,w=v(s),k=w.isOldFlowDesignNode,A=(0,l.ref)(null),x=s.type?(0,y.m9)(s.type):"",C=(0,d.J0)((null===(e=s.data)||void 0===e||null===(e=e.attribute)||void 0===e?void 0:e.candidate)||[]),_=(0,a.A)(C,2),S=_[0],T=_[1],I=(0,l.ref)(!1),L=(0,R.zO)(window),O=(0,l.computed)((function(){var e;return null===L||void 0===L||null===(e=L.grayAuth)||void 0===e?void 0:e.PAAS_CODE})),j=(0,l.computed)((function(){return"start-node"!==s.type&&"project"!=f})),P=(0,l.computed)((function(){return(0,M.Bc)()})),$=(0,l.computed)((function(){return P.value&&"task"!==m})),z=(0,l.computed)((function(){return[J.name,Q.name,Y.name,X.name,Z.name,ee.name,ne.name,te.name,ie.name,oe.name,ae.name]})),q=(0,l.computed)((function(){return(0,u.A)({},H)})),B=(0,l.computed)({get:function(){var e;return k.value?S.value:(null===(e=s.data)||void 0===e||null===(e=e.attribute)||void 0===e?void 0:e.candidate)||[]},set:function(e){(0,F.isObject)(s.data.attribute)?s.data.attribute.candidate=e:s.data.attribute=(0,u.A)({candidate:e},s.data.attribute),V(s.data)}}),V=function(e){k.value&&s.setData(e,{overwrite:!0})},U=function(){k.value&&s.trigger("change:data",{current:s.data})},W=function(){if(A){var e=A.value.checked;e=e.map((function(e){var t=e.id,n=e.name,i=e.type,o=e.extend,a=e.staffId,r=void 0===a?"":a,l=e.rootType,s=void 0===l?"":l,c={id:t,name:n,type:i,staffId:r,rootType:s};return o&&(c.extend=o),c})),b(),B.value=e,k.value&&(T(e),U()),(0,y.FW)(s,[])}},re=function(e,t){B.value.splice(t,1),(0,y.FW)(s,[]),U()};(0,l.onMounted)((function(){var e;if(!Reflect.has((null===(e=s.data)||void 0===e?void 0:e.attribute)||{},"fieldAuthorityList")){var t=(0,y.Lj)(c.fields,s.shape);s.data.attribute=(0,F.merge)(s.data.attribute,t)}})),O.value||(s.data.convertType="person",s.data.code="");var le=(0,l.ref)((null===(t=s.data)||void 0===t?void 0:t.convertType)||"person"),se=(0,l.ref)((null===(n=s.data)||void 0===n?void 0:n.code)||""),ce=(0,l.computed)((function(){return"person"===le.value?"切换至代码格式":"切换至配置格式"})),de=function(){le.value="person"===le.value?"code":"person",s.data.convertType=le.value,"code"!==le.value||se.value||(se.value=E,s.data.code=E),(0,y.FW)(s,[]),U()},ue=function(e){se.value=e,s.data.code=e},fe=function(){var e,t,n;"code"!==(null===(e=s.data)||void 0===e?void 0:e.convertType)||null!==(t=s.data)&&void 0!==t&&t.code||(I.value=!0,H.getConditionCode({templateId:null===(n=K.A.getters)||void 0===n||null===(n=n.flowCurrentVersion)||void 0===n?void 0:n.bizId,codeLocation:"paasNodeApprover",locationId:s.id}).then((function(e){if(0==e.code){var t,n,i=(null===(t=e.data)||void 0===t?void 0:t.executeCode)||E;null!==(n=s.data)&&void 0!==n&&n.code||(s.data.code=i,se.value=i)}}))["catch"]((function(e){console.log(e)}))["finally"]((function(){I.value=!1})))};O.value&&fe();var me=function(){window.open("https://alidocs.dingtalk.com/i/nodes/ZgpG2NdyVXrAGPEBFOjYgLqw8MwvDqPk?cid=220602592%3A3936227323&corpId=ding8bbfee4c40974e1e35c2f4657eb6378f&doc_type=wiki_doc&iframeQuery=utm_medium%3Dim_card&utm_source=im&utm_medium=im_card&utm_scene=team_space&utm_source=im")},pe=function(){return(0,l.h)("div",[(0,l.h)("div",{class:"mar-t-8"},["创建一个 cn.publink.shb.application.service.common.service.ShbListCodeService 接口的实现类；重写execute方法，根据自己业务需要进行编写代码。返回人员Id 的 List 作为节点负责人"]),(0,l.h)("el-button",{attrs:{type:"text"},style:{fontSize:"14px"},on:{click:me}},["代码规范及使用帮助"]),(0,l.h)(G,{directives:[{name:"loading",value:I.value}],attrs:{value:se.value},on:{change:ue},class:"mar-t-12"})])},ve=function(){return(0,l.h)("div",[(0,l.h)("div",{class:"node-config-candidate__btn",on:{click:g}},[N.Ru.t("common.base.add2"),x]),(0,l.h)("ul",{class:"node-config-candidate__list"},[B.value.map((function(e,t){return(0,l.h)("li",{class:"node-config-candidate__list-item"},[(0,l.h)("span",{class:"name"},[e.name]),(0,l.h)("i",{class:"icon el-icon-close",on:{click:re.bind(i,e,t)}})])}))])])};return function(){return(0,l.h)("fragment",[(0,l.h)("div",{class:"node-config-candidate__box"},[(0,l.h)("div",{class:"node-config-candidate__title"},[(0,l.h)("div",{class:"title"},[x]),"start-node"!==s.type&&O.value&&(0,l.h)("div",{class:"switch",on:{click:de}},[(0,l.h)("i",{class:"iconfont icon-qiehuan1 mar-r-5"}),(0,l.h)("div",[ce.value])])]),"code"!==le.value?ve():pe()]),(0,l.h)("base-modal",o()([{class:"choose-user-modal",attrs:{title:N.Ru.t("view.designer.workFlow.selectSth",{sth:x}),show:h.value}},{on:(0,r.A)({},"update:show",(function(e){return e?g():b()}))},{attrs:{width:$.value?"800px":"640px"}}]),[h.value?(0,l.h)("config-contact",{ref:A,attrs:{title:x,fields:c.fields,value:B.value,"flow-api":q.value,"show-dynamic":j.value,showDynamicMenus:z.value,showSmartDispatch:$.value,showNewServiceProvider:!0}}):null,(0,l.h)("div",{slot:"footer",class:"dialog-footer"},[(0,l.h)("el-button",{on:{click:b}},[N.Ru.t("common.base.cancel")]),(0,l.h)("el-button",{attrs:{type:"primary"},on:{click:W}},[N.Ru.t("common.base.makeSure")])])])])}}}),le=(n(7130),n(98316),n(56582)),se=n(50224),ce=n(5261),de=n(4946),ue=n(40578),fe=(0,l.defineComponent)({name:"node-config-operate-auth",components:(0,r.A)((0,r.A)({},de.A.name,de.A),"AddCustomizeModal",ue.A),setup:function(){var e=(0,l.ref)(null),t=(0,l.inject)("node"),n=((0,l.inject)("flowData"),(0,l.inject)("mode")),i=(0,l.ref)({}),o=(0,l.ref)((0,se.uR)()),a=(0,l.ref)([]),r=(0,l.computed)((function(){return n===b.A.TASK})),s=function(e){a.value=e,t.data.attribute.buttonList=e},c=function(){if(e.value)try{f(),e.value.openDialog()}catch(t){console.log(t)}},d=function(e){var t=e.form,n=e.field,i=void 0===n?"":n,r=(0,y.sd)(t),l=(0,u.A)((0,u.A)({},r),{},{isOpen:!0,disabled:!1,show:!0,name:"custom",customize:!0,field:i,buttonId:(0,se.uR)()});"codeContent"===t.type?l.event=[{type:t.type,codeContent:t[t.type]}]:l.event=[{type:t.type,execute:[t[t.type]]}],a.value.push(l),s(a.value),o.value=(0,se.uR)()},f=function(){var e=a.value.filter((function(e){return e.customize})).length;if(e>=5)throw le.Ay.notification({type:"warning",title:(0,N.t)("common.base.messageStatus.warning"),message:"自定义按钮数量不能超过五个"}),new Error;var t={name:"代码块",formType:"flowJsCode",isSystem:0,isSearch:0,forceDelete:!1,isShowDisplayName:!0};i.value=new ce.A((0,u.A)((0,u.A)({},t),{},{formType:t.formType,displayName:t.name,isSystem:t.isSystem,setting:{automaticOperation:0,hidden:0,sync:0,codeBlockConfig:{automaticOperation:0,codeContent:"",resultAliasPath:[]}}}))};return(0,l.watchEffect)((function(){a.value=(0,y.bc)(t,r.value)})),function(){return(0,l.h)("div",{class:"node-config-operate__box"},[(0,l.h)("h4",{class:"workflow-item-title"},[N.Ru.t("view.designer.workFlow.customFunctionButtonType")]),(0,l.h)("custom-btn-setting",{key:o.value,attrs:{value:a.value,node:t,flowDesignVersion:"v2"},on:{update:s}}),(0,l.h)("el-button",{attrs:{size:"medium",icon:"el-icon-circle-plus-outline"},directives:[{name:"show",value:!r.value}],class:"customize-btn",on:{click:c}},[N.Ru.t("view.designer.workFlow.label13")]),(0,l.h)("add-customize-modal",{ref:e,on:{update:d},attrs:{node:t,newField:i.value}})])}}}),me=n(28890),pe=(0,l.defineComponent)({name:"node-config-adv",components:(0,r.A)({},me.A.name,me.A),setup:function(){var e=(0,l.inject)("node"),t=(0,l.inject)("flowData"),n=(0,F.isString)(t.antvJson)?JSON.parse(t.antvJson).cells||[]:t.antvJson.cells||[],i=(0,l.computed)((function(){return Reflect.has(e,"shape")?e:(0,u.A)((0,u.A)({},e),{},{shape:e.type})})),o=function(t){e.data.attribute.endShowFlowStatus=t.map((function(e){return(0,u.A)((0,u.A)({},e),{},{finishStatus:e.finishName})}))};return t.finishNodes=(0,y.UE)(n),function(){return(0,l.h)("advanced-setting",{attrs:{node:i.value,flowDesignVersion:"v2"},on:{toFinishName:o}})}}}),ve=(0,l.defineComponent)({name:"node-config-main",components:(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({},P.F.name,P.F),re.name,re),I.name,I),fe.name,fe),pe.name,pe),props:{node:{type:[Object,c.oH,c.bP],default:function(){return{}}}},setup:function(e){var t=e.node,n=(0,l.reactive)({index:0,component:"node-config-candidate"}),i=(0,l.inject)("mode"),o=(0,l.inject)("isModuleForApproval"),a=(0,l.computed)((function(){return t.type===f.A.CONVERGE_NODE})),r=(0,l.computed)((function(){return i===b.A.TASK})),s=(0,l.computed)((function(){return t.type===f.A.START_NODE})),c=(0,l.computed)((function(){return o&&t.type===f.A.END_NODE})),d=g.I4.reduce((function(e,i,a){var l=["approve-node","process-node","end-node"];switch(a){case 0:t.type!==f.A.END_NODE?e.push((0,u.A)((0,u.A)({},i),{},{name:t.type?"".concat(N.Ay.t("common.base.setting")).concat((0,y.m9)(t.type)):i.name})):n.component="node-config-form-auth";break;case 1:!r.value&&(!o||!s.value)&&e.push(i);break;case 2:t.type&&![f.A.END_NODE,f.A.CARBON_COPY_NODE].includes(t.type)&&e.push(i);break;case 3:t.type&&l.includes(t.type)&&e.push(i);break;default:}return e}),[]),m=function(e,t){n.index=t,n.component=e.component};return(0,l.provide)("node",t),function(){return(0,l.h)("div",{class:"config-panel-box"},[a.value||c.value?null:(0,l.h)("fragment",[(0,l.h)("div",{class:"setting-toggle"},[d.map((function(e,t){return(0,l.h)("div",{class:["setting-toggle-item",n.index===t?"active":null],on:{click:function(){return m(e,t)}}},[e.name])})),(0,l.h)("div",{class:"setting-toggle-item_bg",style:{width:"calc(100% / ".concat(d.length,")"),left:"calc(100% / ".concat(d.length," * ").concat(n.index)}})]),(0,l.h)(n.component,{key:"".concat(n.component).concat(t.id),props:{node:t}})])])}}}),he=n(5390),ge=(n(69594),n(68735),function(){var e=this,t=e._self._c;return t("div",{staticClass:"condition-list-box"},[t("ul",{staticClass:"condition-list-flow__design"},e._l(e.subCondition,(function(n,i){return t("li",{key:i,staticClass:"condition-list-item"},[e._t("header",null,{formItem:n,subCondition:e.subCondition,index:i}),t("el-form",{ref:"formItem",refInFor:!0,staticClass:"form",attrs:{model:n}},[e.isTaskMode?t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"content.tableName",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.designer.workFlow.taskFromFields")}),trigger:"change"}]}},[t("el-select",{staticClass:"filed-select",attrs:{placeholder:e.$t("common.base.pleaseSelect"),filterable:"",clearable:""},on:{change:function(t){return e.taskFormChange(t,e.parentIndex,i)}},model:{value:n.content.tableName,callback:function(t){e.$set(n.content,"tableName",t)},expression:"formItem.content.tableName"}},e._l(e.taskOptionsList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"content.fieldName",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.template.print.formFields")}),trigger:"change"}]}},[t("el-select",{staticClass:"filed-select",attrs:{placeholder:e.$t("view.template.print.formFields"),disabled:n.content.fieldName.includes("trigger"),filterable:"",clearable:""},on:{change:function(t){return e.formKeyChange(t,e.parentIndex,i)}},model:{value:n.content.fieldName,callback:function(t){e.$set(n.content,"fieldName",t)},expression:"formItem.content.fieldName"}},e._l(e.filterAfterFields(n.content.tableName),(function(e){return t("el-option",{key:e.id,attrs:{label:e.displayName,value:e.fieldName}})})),1)],1),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"expression",rules:[{required:!0,message:e.$t("common.placeholder.selectSomething",{0:e.$t("view.designer.workFlow.discriminator")}),trigger:"change"}]}},[t("el-select",{staticClass:"cond-select",attrs:{placeholder:e.$t("view.designer.workFlow.discriminator"),disabled:n.content.fieldName.includes("trigger")},on:{change:function(t){return e.handleExpressionChange(t,n)}},model:{value:n.expression,callback:function(t){e.$set(n,"expression",t)},expression:"formItem.expression"}},e._l(e.getConditions(n.content.fieldName),(function(e){return t("el-option",{key:e.val,attrs:{label:e.name,value:e.val,disabled:e.disable}})})),1)],1),t("el-form-item",{staticClass:"cus-form-item",attrs:{prop:"target",rules:[{required:!e.isNullOrNotNullExpression(n.expression),message:e.placeholderText(n.content.fieldName),trigger:["change","blur"]},{validator:function(){for(var t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];return e.validatorBetween(i,n)}}]}},[e.checkIsShowDynamicComponent(n)?[t(e.getRenderComponentName(n.content.fieldName,n.expression),e._b({key:n.content.fieldName,tag:"component",staticClass:"input-box",attrs:{value:e.getTargetValue(n.target,n.content.fieldName,n.expression),field:e.orderFieldNameGetFieldItem(n.content.fieldName,n.expression)},on:{input:function(t){return e.input(t,e.parentIndex,i,n.content.fieldName)}}},"component",e.getComponentProps(e.orderFieldNameGetFieldItem(n.content.fieldName,n.expression)),!1))]:"out_targetIds"==n.content.fieldName?[t("el-select",{staticClass:"filed-select",staticStyle:{"margin-left":"1px"},attrs:{filterable:"","collapse-tags":"","value-key":"id",placeholder:e.$t("common.part.target")},model:{value:n.target,callback:function(t){e.$set(n,"target",t)},expression:"formItem.target"}},e._l(e.repertories,(function(e){return t("el-option",{key:e.id,attrs:{value:e,label:e.name,origin:e}})})),1)]:[t("ConditionTargetComponent",{attrs:{"form-item":n}})]],2)],1)],2)})),0)])}),be=[],ye=(n(33656),(0,l.defineComponent)({name:"ConditionTargetComponent",props:{formItem:{type:Object,default:function(){return{}}}},setup:function(e){var t=(0,l.toRefs)(e),n=t.formItem,i=function(e){return he.Ay.methods.isNullOrNotNullExpression(e)},o={functional:!0,render:function(e){return e("el-input",{attrs:{value:n.value.target,disabled:""===n.value.expression,placeholder:(0,N.t)("view.designer.workFlow.inputValue")},class:"input",on:{input:function(e){return n.value.target=e.trim()}}})}};return function(){return i(n.value.expression)?null:(0,l.h)(o)}}})),we=n(80906),ke=(0,l.defineComponent)({name:"form-condition-between",props:{value:{type:Array,default:function(){return["",""]}},field:{type:Object,default:function(){return{}}},componentName:{type:String,default:function(){return"form-number"}},formFields:{type:Array,default:function(){return[new ce.A({formType:"number"}),new ce.A({formType:"number"})]}},placeholderArray:{type:Array,default:function(){return["",""]}}},setup:function(e,t){var n=t.emit,i=(0,l.toRefs)(e),o=i.formFields,r=i.placeholderArray,s=i.componentName,c=i.field,u=i.value,f=(0,d.J0)(u.value),m=(0,a.A)(f,1),p=m[0],v=function(e,t){p.value[t]=e,n("input",p.value)};return function(){return(0,l.h)("div",{class:"form-condition-between"},[(0,l.h)(s.value,{props:{field:o.value[0],value:u.value[0],placeholder:(0,we.dk)(c.value.formType,"")||r.value[0]},on:{input:function(e){return v(e,0)}}}),(0,l.h)("span",["-"]),(0,l.h)(s.value,{props:{field:o.value[1],value:u.value[1],placeholder:(0,we.dk)(c.value.formType,"")||r.value[1]},on:{input:function(e){return v(e,1)}}})])}}}),Ae=n(92042),xe=n(9710),Ce={name:"condition-form-item-list",components:(0,r.A)((0,r.A)((0,r.A)((0,r.A)({ConditionTargetComponent:ye},ke.name,ke),"form-tag",Ae.A.TagsSearch),"form-linkman",Ae.A.linkmanSearch),"form-customer",Ae.A.customerSearch),props:{parentIndex:{type:Number,default:function(){return 0}}},inject:["flowData","mode"],data:function(){return(0,u.A)({},he.Ay.data())},computed:(0,u.A)((0,u.A)({},he.Ay.computed),{},{subCondition:function(){var e;return(null===(e=this.conditionsList)||void 0===e||null===(e=e[this.parentIndex])||void 0===e?void 0:e.subCondition)||[]}}),methods:(0,u.A)((0,u.A)({},he.Ay.methods),{},{handlePushConditionListItem:function(e,t){this.conditionsList.push(t)},handleDelSubConditionListItem:function(){this.conditionsList.pop()},getComponentProps:function(e){return xe.gd[e.formType]?("customer"===e.formType&&(xe.gd[e.formType].multiple=!1),xe.gd[e.formType]):{}}})},_e=Ce,Ne=(0,U.A)(_e,ge,be,!1,null,"537bd6a0",null),Fe=Ne.exports,Se=n(69749),Te=(0,l.defineComponent)({name:"edge-config-condition-modal",components:(0,r.A)({},Fe.name,Fe),setup:function(e,t){var n=t.expose,i=t.emit,a=(0,l.inject)("node"),s=(0,D.s)(),c=s.visible,d=s.showDialog,f=s.hideDialog,m=v(a),p=m.isOldFlowDesignNode,h=(0,l.reactive)({subCondition:[],parentIndex:0,isEdit:!1}),g=(0,l.ref)(null),b=function(e,t){h.parentIndex=e,h.isEdit=t,d(),(0,l.nextTick)((function(){g.value&&g.value.show((0,u.A)({},a.data))}))},y=function(){g.value&&(Promise.all(g.value.validateForm()).then((function(){i("submit",{index:h.parentIndex,subCondition:g.value.subCondition})})),p.value)},w=function(){var e=(0,F.cloneDeep)(g.value.handleAddAnd(h.parentIndex));h.subCondition.push(e)},k=function(e){Se.MessageBox.confirm(N.Ay.t("view.designer.workFlow.tip1"),N.Ay.t("common.base.toast"),{confirmButtonText:N.Ay.t("common.base.confirm"),cancelButtonText:N.Ay.t("common.base.cancel"),type:"warning"}).then((function(t){h.subCondition.splice(e,1),g.value.handleDelAndItem(h.parentIndex,e)}))["catch"]()},A=function(){g.value.conditionsList=[],h.isEdit||i("handlePopLastItem"),f()};return n({show:b,handleCancel:A,hideDialog:f}),function(){return(0,l.h)("base-modal",o()([{class:"edge-config-condition__modal",attrs:{title:"".concat(h.isEdit?N.Ay.t("common.base.edit"):N.Ay.t("common.base.add")).concat(N.Ay.t("view.designer.workFlow.conditionGroup")),show:c.value}},{on:(0,r.A)({},"update:show",(function(e){return c.value=e}))},{attrs:{width:"500px"},on:{close:A}}]),[(0,l.h)("div",{class:"edge-config-condition__list"},[c.value?(0,l.h)("condition-form-item-list",{ref:g,attrs:{subCondition:h.subCondition,parentIndex:h.parentIndex},scopedSlots:{header:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.index,n=e.subCondition;return(0,l.h)("div",{class:"edge-config-condition__list-header"},[(0,l.h)("div",{class:"edge-config-condition__list-header-l"},[0===t?N.Ay.t("common.base.when"):N.Ay.t("view.designer.workFlow.and")," "]),(0,l.h)("div",{class:"edge-config-condition__list-header-r"},[(0,l.h)("el-button",{class:"icon-btn",attrs:{icon:"iconfont icon-delete",disabled:1===n.length},on:{click:function(){return k(t)}}})])])}}}):null,(0,l.h)("el-button",{class:"edge-condition-and__btn",on:{click:w},attrs:{plain:!0}},[(0,l.h)("i",{class:"el-icon-plus el-icon--left"}),N.Ay.t("common.base.add"),N.Ay.t("common.base.condition")])]),(0,l.h)("div",{slot:"footer",class:"dialog-footer"},[(0,l.h)("el-button",{on:{click:A}},[N.Ay.t("common.base.cancel")]),(0,l.h)("el-button",{attrs:{type:"primary"},on:{click:y}},[N.Ay.t("common.base.save")])])])}}}),Ie=n(19572),Le=n(16113),De=(0,l.defineComponent)({name:"edge-config-condition",components:(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},he.Ay.name,he.Ay),Te.name,Te),Ie.Ay.name,Ie.Ay),"actionCodemirror",G),setup:function(){var e=(0,R.zO)(window),t=(0,l.computed)((function(){var t;return null===e||void 0===e||null===(t=e.grayAuth)||void 0===t?void 0:t.PAAS_CODE})),n=function(){var e,n;((0,F.isEmpty)(o.cell)&&(o.cell=(0,F.cloneDeep)(i)),null!==(e=i.data)&&void 0!==e&&e.convertType&&t.value)||(i.data.convertType="mapping",i.data.code="",null!==(n=i.data.conditionObject)&&void 0!==n&&n.length||(i.data.isCondition=!1));i.data.conditionRelation="code"!==i.data.convertType?100:200;var a=i.data.conditionObject;i.data.conditionObject=a?(0,Ie.sz)(s?o.fields:K.A.getters.flowEdgeSupportConditionFields,a):[]},i=(0,l.inject)("node"),o=(0,l.inject)("flowData"),a=v(i),r=a.isOldFlowDesignNode,s=(0,l.inject)("isModuleForApproval");n();var c=(0,l.ref)(!1),d=(0,l.ref)(null),u=(0,l.ref)(i.data.conditionObject),f=(0,l.ref)(i.data.code),m=(0,l.ref)(i.data.convertType);(0,l.watch)((function(){return f.value}),(function(e){var t;"code"===(null===(t=i.data)||void 0===t?void 0:t.convertType)&&(i.data.isCondition=!!e)}));var p=function(){if(d.value){var e=(0,F.cloneDeep)(he.nm);u.value=[].concat((0,w.A)(u.value),[e]),(0,l.nextTick)((function(){i.data.conditionObject=u.value,d.value&&d.value.show(u.value.length-1,!1)}))}},h=function(e){d.value&&d.value.show(e,!0)},g=function(e,t){Se.MessageBox.confirm(N.Ay.t("view.designer.tip.delWarning",{text:N.Ay.t("view.designer.workFlow.conditionGroup")}),N.Ay.t("common.base.toast"),{confirmButtonText:N.Ay.t("common.base.confirm"),cancelButtonText:N.Ay.t("common.base.cancel"),type:"warning"}).then((function(e){if(u.value.splice(t,1),(0,y.FW)(i,o.fields),0===u.value.length&&r.value){i.data.isCondition=!1;var n=i.getAttrByPath("line/stroke");n!==Le.Zo&&i.setAttrByPath("line/stroke",Le.F3)}}))["catch"]()},b=function(e){var t=e.index,n=e.subCondition;u.value[t].subCondition=n,(0,y.FW)(i,o.fields),d.value.hideDialog(),i.data.isCondition=!0},k=function(e){f.value=e,i.data.code=e},A=function(e){m.value=e,i.data.convertType=e,"code"===e?(i.data.conditionRelation=200,f.value||(f.value=O,i.data.code=O),i.data.isCondition=!0):"mapping"===e&&(i.data.conditionRelation=100,u.value.length?i.data.isCondition=!0:i.data.isCondition=!1)},x=function(){var e,t,n;"code"!==(null===(e=i.data)||void 0===e?void 0:e.convertType)||null!==(t=i.data)&&void 0!==t&&t.code||(c.value=!0,H.getConditionCode({templateId:null===(n=K.A.getters)||void 0===n||null===(n=n.flowCurrentVersion)||void 0===n?void 0:n.bizId,codeLocation:"paasLine",locationId:i.id}).then((function(e){if(0==e.code){var t,n,o=(null===(t=e.data)||void 0===t?void 0:t.executeCode)||O;null!==(n=i.data)&&void 0!==n&&n.code||(i.data.code=o,f.value=o)}}))["catch"]((function(e){console.log(e)}))["finally"]((function(){c.value=!1})))};t.value&&x();var C=function(){window.open("https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/gre7yumc5sz4ewod/dklg7gkm64cya8l4/ox8c8k8i3hp1g6do.html")},_=function(){return(0,l.h)("div",[(0,l.h)("div",{class:"mar-t-8"},["创建一个 cn.publink.shb.application.service.common.service.ShbMatchRuleService 接口的实现类；重写match方法，根据自己业务需要进行编写代码。返回true表示条件满足，返回false表示条件不满足"]),(0,l.h)("el-button",{attrs:{type:"text"},style:{fontSize:"14px"},on:{click:C}},["代码规范及使用帮助"]),(0,l.h)(G,{directives:[{name:"loading",value:c.value}],attrs:{value:f.value},on:{change:k},class:"mar-t-12"})])},S=function(){return(0,l.h)("div",[(0,l.h)("condition-list",{attrs:{list:(0,l.unref)(u)},scopedSlots:{headerRight:function(e,t){return(0,l.h)("div",{class:"edge-condition-list__header-right"},[(0,l.h)("i",{class:"iconfont icon-setting",on:{click:function(){return h(t)}}}),(0,l.h)("i",{class:"iconfont icon-delete",on:{click:function(){return g(e,t)}}})])}}}),(0,l.h)("el-button",{class:"edge-condition-add__btn",on:{click:p},attrs:{plain:!0}},[(0,l.h)("i",{class:"el-icon-plus el-icon--left"}),"".concat(N.Ay.t("common.base.add")).concat(N.Ay.t("view.designer.workFlow.conditionGroup"))])])};return function(){return(0,l.h)("div",{class:"edge-config-condition__box"},[t.value&&(0,l.h)("el-radio-group",{attrs:{value:m.value},on:{input:A},class:"mar-b-12"},[(0,l.h)("el-radio-button",{attrs:{label:"mapping"}},["配置方式"]),(0,l.h)("el-radio-button",{attrs:{label:"code"}},["代码方式"])]),(0,l.h)("div",{class:"edge-condition-title"},[(0,l.h)("span",[N.Ay.t("view.designer.workFlow.conditionBranchEnter")])]),"mapping"===m.value?S():_(),(0,l.h)("edge-config-condition-modal",{ref:d,on:{handlePopLastItem:function(){return u.value.pop()},submit:b}})])}}}),Oe=(0,l.defineComponent)({name:"edge-config-main",components:(0,r.A)({},De.name,De),props:{node:{type:[Object,c.bP,c.oH],default:function(){return{}}}},setup:function(e){var t=e.node;return(0,l.provide)("node",t),function(){return(0,l.h)("div",{class:"config-panel-box"},[t.type&&t.type.includes(f.A.CONDITION)?(0,l.h)("edge-config-condition"):null])}}}),Ee=(0,l.defineComponent)({name:"node-config-panel-main",props:{node:{type:[Object,c.bP,c.oH],default:function(){return{}}},isSpecialNode:{type:Boolean,default:function(){return!1}},modal:{type:Boolean,default:function(){return!0}},zIndex:{type:Number,default:function(){return 998}},drawer:{type:Boolean,default:function(){return!0}},width:{type:Number,default:function(){return 500}}},components:(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},h.name,h),ve.name,ve),Oe.name,Oe),P.F.name,P.F),setup:function(e,t){var n=t.expose,i=t.emit,c=(0,d.J0)(!1),u=(0,a.A)(c,2),f=u[0],m=u[1],p=(0,l.toRefs)(e),v=p.node,h=p.isSpecialNode,g=p.modal,b=p.zIndex,y=p.drawer,w=p.width,k=function(){m(!0)},A=function(){m(!1)},x=function(e){f.value=e,i("updateVisible",e)},C={functional:!0,render:function(e){return e("fragment",[e("node-config-name",{attrs:{node:v.value},key:(0,s.A)(),on:{handleCloseDrawer:function(){return f.value=!1}}}),f.value?h.value?e("edge-config-main",{attrs:{node:v.value},key:v.value.id}):e("node-config-main",{attrs:{node:v.value},key:v.value.id}):null])}};return n({handleShow:k,handleHide:A}),function(){return y.value?(0,l.h)("el-drawer",o()([{attrs:{"custom-class":"node-config-drawer",visible:f.value,size:w.value,withHeader:!1,"z-index":b.value,modal:g.value,"close-on-press-escape":!1}},{on:(0,r.A)({},"update:visible",x)}]),[(0,l.h)(C)]):(0,l.h)("div",{class:"node-config-panel",style:{width:"".concat(w.value,"px"),right:f.value?"0":"-".concat(w.value,"px")}},[(0,l.h)(C)])}}})},49430:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});n(2286),n(44807),n(35256),n(21484),n(16961),n(54615),n(7354),n(32807),n(75069);var i=n(80602),o=n(84859);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.map((function(e){var n=(null===e||void 0===e?void 0:e.data)||{},a=n.name,r=n.attribute,l=[];if(e.shape!==i.A.EDGE){if(a||l.push(o.Ay.t("common.base.moduleIsNotNull",{module:o.Ay.t("view.template.detail.nodeName")})),e.shape===i.A.PROCEDD_NODE||e.shape===i.A.APPROVE_NODE||e.shape===i.A.CARBON_COPY_NODE){var s,c,d,u,f,m,p,v,h,g=(null===r||void 0===r||null===(s=r.flowSetting)||void 0===s?void 0:s.flowRule)||0,b=(null===r||void 0===r||null===(c=r.buttonList)||void 0===c||null===(c=c.find((function(e){return"countersign"===e.name})))||void 0===c?void 0:c.isOpen)||!1;2===g&&b&&l.push(o.Ay.t("view.designer.workFlow.tip38"));var y=(null===r||void 0===r?void 0:r.candidate)||[];("code"===(null===e||void 0===e||null===(d=e.data)||void 0===d?void 0:d.convertType)||y&&y.length)&&("code"!==(null===e||void 0===e||null===(u=e.data)||void 0===u?void 0:u.convertType)||null!==e&&void 0!==e&&null!==(f=e.data)&&void 0!==f&&f.code)||l.push(o.Ay.t("view.designer.workFlow.tip28"));var w=null!==(m=null===r||void 0===r?void 0:r.outStockSetting)&&void 0!==m?m:{};if(w.enable)if(w.bindFieldName){var k=t.find((function(e){return w.bindFieldName===e.fieldName}));k||(l.push(o.Ay.t("view.designer.workFlow.tip30")),w.bindFieldName="")}else l.push(o.Ay.t("view.designer.workFlow.tip29"));var A=null!==(p=null===r||void 0===r?void 0:r.inStockSetting)&&void 0!==p?p:{};if(A.enable)if(A.bindFieldName){var x=t.find((function(e){return A.bindFieldName===e.fieldName}));x||(l.push(o.Ay.t("view.designer.workFlow.tip32")),A.bindFieldName="")}else l.push(o.Ay.t("view.designer.workFlow.tip31"));var C=null!==(v=null===r||void 0===r?void 0:r.paySetting)&&void 0!==v?v:{};if(C.enable&&!C.bindFieldName&&l.push(o.Ay.t("view.designer.workFlow.tip33")),C.enable&&C.bindFieldName){var _=t.find((function(e){return C.bindFieldName===e.fieldName}));_||l.push(o.Ay.t("view.designer.workFlow.tip34"))}var N=null!==(h=null===r||void 0===r?void 0:r.memberPaySetting)&&void 0!==h?h:{};if(N.enable)if(N.bindFieldName){var F=t.find((function(e){return N.bindFieldName===e.fieldName}));F||(l.push(o.Ay.t("view.designer.workFlow.tip40")),N.bindFieldName="")}else l.push(o.Ay.t("view.designer.workFlow.tip39"))}return l.length>0?{message:l,title:a}:null}})).filter((function(e){return null!=e}))}},57968:function(e,t,n){(function(e){e(n(69113))})((function(e){"use strict";var t="CodeMirror-activeline",n="CodeMirror-activeline-background",i="CodeMirror-activeline-gutter";function o(e){for(var o=0;o<e.state.activeLines.length;o++)e.removeLineClass(e.state.activeLines[o],"wrap",t),e.removeLineClass(e.state.activeLines[o],"background",n),e.removeLineClass(e.state.activeLines[o],"gutter",i)}function a(e,t){if(e.length!=t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!=t[n])return!1;return!0}function r(e,r){for(var l=[],s=0;s<r.length;s++){var c=r[s],d=e.getOption("styleActiveLine");if("object"==typeof d&&d.nonEmpty?c.anchor.line==c.head.line:c.empty()){var u=e.getLineHandleVisualStart(c.head.line);l[l.length-1]!=u&&l.push(u)}}a(e.state.activeLines,l)||e.operation((function(){o(e);for(var a=0;a<l.length;a++)e.addLineClass(l[a],"wrap",t),e.addLineClass(l[a],"background",n),e.addLineClass(l[a],"gutter",i);e.state.activeLines=l}))}function l(e,t){r(e,t.ranges)}e.defineOption("styleActiveLine",!1,(function(t,n,i){var a=i!=e.Init&&i;n!=a&&(a&&(t.off("beforeSelectionChange",l),o(t),delete t.state.activeLines),n&&(t.state.activeLines=[],r(t,t.listSelections()),t.on("beforeSelectionChange",l)))}))}))},65172:function(e,t,n){(function(e){e(n(69113))})((function(e){"use strict";function t(e,t,n,i,o,a){this.indented=e,this.column=t,this.type=n,this.info=i,this.align=o,this.prev=a}function n(e,n,i,o){var a=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=i&&(a=e.context.indented),e.context=new t(a,n,i,o,null,e.context)}function i(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function o(e,t,n){return"variable"==t.prevToken||"type"==t.prevToken||(!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,n))||(!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0))}function a(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function r(e){for(var t={},n=e.split(" "),i=0;i<n.length;++i)t[n[i]]=!0;return t}function l(e,t){return"function"===typeof e?e(t):e.propertyIsEnumerable(t)}e.defineMode("clike",(function(r,s){var c,d,u=r.indentUnit,f=s.statementIndentUnit||u,m=s.dontAlignCalls,p=s.keywords||{},v=s.types||{},h=s.builtin||{},g=s.blockKeywords||{},b=s.defKeywords||{},y=s.atoms||{},w=s.hooks||{},k=s.multiLineStrings,A=!1!==s.indentStatements,x=!1!==s.indentSwitch,C=s.namespaceSeparator,_=s.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,N=s.numberStart||/[\d\.]/,F=s.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,S=s.isOperatorChar||/[+\-*&%=<>!?|\/]/,T=s.isIdentifierChar||/[\w\$_\xa1-\uffff]/,I=s.isReservedIdentifier||!1;function L(e,t){var n=e.next();if(w[n]){var i=w[n](e,t);if(!1!==i)return i}if('"'==n||"'"==n)return t.tokenize=D(n),t.tokenize(e,t);if(N.test(n)){if(e.backUp(1),e.match(F))return"number";e.next()}if(_.test(n))return c=n,null;if("/"==n){if(e.eat("*"))return t.tokenize=O,O(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(S.test(n)){while(!e.match(/^\/[\/*]/,!1)&&e.eat(S));return"operator"}if(e.eatWhile(T),C)while(e.match(C))e.eatWhile(T);var o=e.current();return l(p,o)?(l(g,o)&&(c="newstatement"),l(b,o)&&(d=!0),"keyword"):l(v,o)?"type":l(h,o)||I&&I(o)?(l(g,o)&&(c="newstatement"),"builtin"):l(y,o)?"atom":"variable"}function D(e){return function(t,n){var i,o=!1,a=!1;while(null!=(i=t.next())){if(i==e&&!o){a=!0;break}o=!o&&"\\"==i}return(a||!o&&!k)&&(n.tokenize=null),"string"}}function O(e,t){var n,i=!1;while(n=e.next()){if("/"==n&&i){t.tokenize=null;break}i="*"==n}return"comment"}function E(e,t){s.typeFirstDefinitions&&e.eol()&&a(t.context)&&(t.typeAtEndOfLine=o(e,t,e.pos))}return{startState:function(e){return{tokenize:null,context:new t((e||0)-u,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var r=t.context;if(e.sol()&&(null==r.align&&(r.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return E(e,t),null;c=d=null;var l=(t.tokenize||L)(e,t);if("comment"==l||"meta"==l)return l;if(null==r.align&&(r.align=!0),";"==c||":"==c||","==c&&e.match(/^\s*(?:\/\/.*)?$/,!1))while("statement"==t.context.type)i(t);else if("{"==c)n(t,e.column(),"}");else if("["==c)n(t,e.column(),"]");else if("("==c)n(t,e.column(),")");else if("}"==c){while("statement"==r.type)r=i(t);"}"==r.type&&(r=i(t));while("statement"==r.type)r=i(t)}else c==r.type?i(t):A&&(("}"==r.type||"top"==r.type)&&";"!=c||"statement"==r.type&&"newstatement"==c)&&n(t,e.column(),"statement",e.current());if("variable"==l&&("def"==t.prevToken||s.typeFirstDefinitions&&o(e,t,e.start)&&a(t.context)&&e.match(/^\s*\(/,!1))&&(l="def"),w.token){var u=w.token(e,t,l);void 0!==u&&(l=u)}return"def"==l&&!1===s.styleDefs&&(l="variable"),t.startOfLine=!1,t.prevToken=d?"def":l||c,E(e,t),l},indent:function(t,n){if(t.tokenize!=L&&null!=t.tokenize||t.typeAtEndOfLine&&a(t.context))return e.Pass;var i=t.context,o=n&&n.charAt(0),r=o==i.type;if("statement"==i.type&&"}"==o&&(i=i.prev),s.dontIndentStatements)while("statement"==i.type&&s.dontIndentStatements.test(i.info))i=i.prev;if(w.indent){var l=w.indent(t,i,n,u);if("number"==typeof l)return l}var c=i.prev&&"switch"==i.prev.info;if(s.allmanIndentation&&/[{(]/.test(o)){while("top"!=i.type&&"}"!=i.type)i=i.prev;return i.indented}return"statement"==i.type?i.indented+("{"==o?0:f):!i.align||m&&")"==i.type?")"!=i.type||r?i.indented+(r?0:u)+(r||!c||/^(?:case|default)\b/.test(n)?0:u):i.indented+f:i.column+(r?0:1)},electricInput:x?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}}));var s="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",c="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",d="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",u="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",f=r("int long char short double float unsigned signed void bool"),m=r("SEL instancetype id Class Protocol BOOL");function p(e){return l(f,e)||/.+_t$/.test(e)}function v(e){return p(e)||l(m,e)}var h="case do else for if switch while struct enum union",g="struct enum union";function b(e,t){if(!t.startOfLine)return!1;for(var n,i=null;n=e.peek();){if("\\"==n&&e.match(/^.$/)){i=b;break}if("/"==n&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=i,"meta"}function y(e,t){return"type"==t.prevToken&&"type"}function w(e){return!(!e||e.length<2)&&("_"==e[0]&&("_"==e[1]||e[1]!==e[1].toLowerCase()))}function k(e){return e.eatWhile(/[\w\.']/),"number"}function A(e,t){if(e.backUp(1),e.match(/^(?:R|u8R|uR|UR|LR)/)){var n=e.match(/^"([^\s\\()]{0,16})\(/);return!!n&&(t.cpp11RawStringDelim=n[1],t.tokenize=_,_(e,t))}return e.match(/^(?:u8|u|U|L)/)?!!e.match(/^["']/,!1)&&"string":(e.next(),!1)}function x(e){var t=/(\w+)::~?(\w+)$/.exec(e);return t&&t[1]==t[2]}function C(e,t){var n;while(null!=(n=e.next()))if('"'==n&&!e.eat('"')){t.tokenize=null;break}return"string"}function _(e,t){var n=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&"),i=e.match(new RegExp(".*?\\)"+n+'"'));return i?t.tokenize=null:e.skipToEnd(),"string"}function N(t,n){"string"==typeof t&&(t=[t]);var i=[];function o(e){if(e)for(var t in e)e.hasOwnProperty(t)&&i.push(t)}o(n.keywords),o(n.types),o(n.builtin),o(n.atoms),i.length&&(n.helperType=t[0],e.registerHelper("hintWords",t[0],i));for(var a=0;a<t.length;++a)e.defineMIME(t[a],n)}function F(e,t){var n=!1;while(!e.eol()){if(!n&&e.match('"""')){t.tokenize=null;break}n="\\"==e.next()&&!n}return"string"}function S(e){return function(t,n){var i;while(i=t.next()){if("*"==i&&t.eat("/")){if(1==e){n.tokenize=null;break}return n.tokenize=S(e-1),n.tokenize(t,n)}if("/"==i&&t.eat("*"))return n.tokenize=S(e+1),n.tokenize(t,n)}return"comment"}}function T(e){return function(t,n){var i,o=!1,a=!1;while(!t.eol()){if(!e&&!o&&t.match('"')){a=!0;break}if(e&&t.match('"""')){a=!0;break}i=t.next(),!o&&"$"==i&&t.match("{")&&t.skipTo("}"),o=!o&&"\\"==i&&!e}return!a&&e||(n.tokenize=null),"string"}}N(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:r(s),types:p,blockKeywords:r(h),defKeywords:r(g),typeFirstDefinitions:!0,atoms:r("NULL true false"),isReservedIdentifier:w,hooks:{"#":b,"*":y},modeProps:{fold:["brace","include"]}}),N(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:r(s+" "+c),types:p,blockKeywords:r(h+" class try catch"),defKeywords:r(g+" class namespace"),typeFirstDefinitions:!0,atoms:r("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:w,hooks:{"#":b,"*":y,u:A,U:A,L:A,R:A,0:k,1:k,2:k,3:k,4:k,5:k,6:k,7:k,8:k,9:k,token:function(e,t,n){if("variable"==n&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&x(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),N("text/x-java",{name:"clike",keywords:r("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:r("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:r("catch class do else finally for if switch try while"),defKeywords:r("class interface enum @interface"),typeFirstDefinitions:!0,atoms:r("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")},'"':function(e,t){return!!e.match(/""$/)&&(t.tokenize=F,t.tokenize(e,t))}},modeProps:{fold:["brace","import"]}}),N("text/x-csharp",{name:"clike",keywords:r("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in init interface internal is lock namespace new operator out override params private protected public readonly record ref required return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:r("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:r("catch class do else finally for foreach if struct switch try while"),defKeywords:r("class interface namespace record struct var"),typeFirstDefinitions:!0,atoms:r("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=C,C(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),N("text/x-scala",{name:"clike",keywords:r("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:r("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:r("catch class enum do else finally for forSome if match switch try while"),defKeywords:r("class enum def object package trait type val var"),atoms:r("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=F,t.tokenize(e,t))},"'":function(e){return e.match(/^(\\[^'\s]+|[^\\'])'/)?"string-2":(e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom")},"=":function(e,n){var i=n.context;return!("}"!=i.type||!i.align||!e.eat(">"))&&(n.context=new t(i.indented,i.column,i.type,i.info,null,i.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=S(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),N("text/x-kotlin",{name:"clike",keywords:r("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:r("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:r("catch class do else finally for if where try while enum"),defKeywords:r("class val var object interface fun"),atoms:r("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){return t.tokenize=T(e.match('""')),t.tokenize(e,t)},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=S(1),t.tokenize(e,t))},indent:function(e,t,n,i){var o=n&&n.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=n?"operator"==e.prevToken&&"}"!=n&&"}"!=e.context.type||"variable"==e.prevToken&&"."==o||("}"==e.prevToken||")"==e.prevToken)&&"."==o?2*i+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(n||"").charAt(0)?0:i):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),N(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:r("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:r("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:r("for while do if else struct"),builtin:r("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:r("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":b},modeProps:{fold:["brace","include"]}}),N("text/x-nesc",{name:"clike",keywords:r(s+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:p,blockKeywords:r(h),atoms:r("null true false"),hooks:{"#":b},modeProps:{fold:["brace","include"]}}),N("text/x-objectivec",{name:"clike",keywords:r(s+" "+d),types:v,builtin:r(u),blockKeywords:r(h+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:r(g+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:r("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:w,hooks:{"#":b,"*":y},modeProps:{fold:["brace","include"]}}),N("text/x-objectivec++",{name:"clike",keywords:r(s+" "+d+" "+c),types:v,builtin:r(u),blockKeywords:r(h+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:r(g+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:r("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:w,hooks:{"#":b,"*":y,u:A,U:A,L:A,R:A,0:k,1:k,2:k,3:k,4:k,5:k,6:k,7:k,8:k,9:k,token:function(e,t,n){if("variable"==n&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&x(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),N("text/x-squirrel",{name:"clike",keywords:r("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:p,blockKeywords:r("case catch class else for foreach if switch try while"),defKeywords:r("function local class"),typeFirstDefinitions:!0,atoms:r("true false null"),hooks:{"#":b},modeProps:{fold:["brace","include"]}});var I=null;function L(e){return function(t,n){var i,o=!1,a=!1;while(!t.eol()){if(!o&&t.match('"')&&("single"==e||t.match('""'))){a=!0;break}if(!o&&t.match("``")){I=L(e),a=!0;break}i=t.next(),o="single"==e&&!o&&"\\"==i}return a&&(n.tokenize=null),"string"}}N("text/x-ceylon",{name:"clike",keywords:r("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:r("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:r("class dynamic function interface module object package value"),builtin:r("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:r("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=L(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!I||!e.match("`"))&&(t.tokenize=I,I=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,n){if(("variable"==n||"type"==n)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}))}}]);