"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[799],{9138:function(e,t,n){n.d(t,{lR:function(){return m}});n(44807),n(80793),n(21484),n(8326),n(16961),n(7354),n(88747),n(75069),n(21633),n(67880);var r=n(87512),i=n(83804),o=n(84859);function a(e){var t=e.fieldName,n=e.isNull,a=e.setting,s=e.isSearch,c=e.revisable;return{displayName:o.Ay.t("common.base.logisticsCompany"),fieldName:"".concat(t,"_").concat(r.On.LogisticsCompany),formType:"logistics",id:(0,i.D)(),isSystem:0,disabled:0===c,setting:a,isSearch:s,isNull:n,revisable:c}}function s(e){var t=e.fieldName,n=e.isNull,a=e.setting,s=e.isSearch,c=e.revisable;return{displayName:o.Ay.t("common.base.logisticsNo"),fieldName:"".concat(t,"_").concat(r.On.LogisticsNo),formType:"logistics",id:(0,i.D)(),isSystem:0,disabled:0===c,setting:a,isSearch:s,isNull:n,revisable:c}}function c(e){var t=e.fieldName,n=e.isNull,a=e.setting,s=e.isSearch,c=e.revisable;return{displayName:o.Ay.t("common.base.afterPhone",{data1:4}),fieldName:"".concat(t,"_").concat(r.On.LogisticsPhone),formType:"logistics",id:(0,i.D)(),isSystem:0,disabled:0===c,setting:a,isSearch:s,isNull:n,revisable:c}}var u=n(23559),l=n(92935),d=n.n(l),f=n(80906);function m(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=d().cloneDeep(e);return n.reduce((function(e,n){if((0,u.a_)(n)&&!n.isHidden){var r=[a(n),s(n)];t&&r.push(c(n)),e.push.apply(e,r)}else!(0,f.hL)(n)||n.isHidden||["productAndSparepart"].includes(n.formType)||(n.subFormFieldList=m(n.subFormFieldList,t)),e.push(n);return e}),[])}},18495:function(e,t,n){n.d(t,{A0:function(){return o},m$:function(){return a},rD:function(){return i}});var r=n(22229);function i(e){return r.A.post("/api/application/outside/trigger/getTriggerByBizTypeInfo",e)}function o(e){return r.A.post("/api/voice/outside/container/button/updateButton",e)}function a(e){return r.A.post("/api/voice/outside/container/button/getButtonByModule",e)}},18799:function(e,t,n){n.r(t),n.d(t,{default:function(){return te}});var r=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"template-edit-view",class:{"task-iframe-view":e.isTaskFrame}},[t("div",{staticClass:"page-title"},[t("div",{staticClass:"template-name"},[e._v(e._s(e.formTabTitle))]),e.isDelete?t("div",{staticClass:"template-delete-status"},[e._v(e._s(e.$t("common.base.deleted")))]):e._e(),t("div",{staticClass:"page-title-right"},[e.isShowFormCellChange?t("biz-form-cell-change",{attrs:{count:e.formCellCount},on:{update:e.handleFormCellChange}}):e._e(),e.isDelete?e._e():t("div",{staticClass:"btn-group"},[e._l(e.buttonList,(function(n){return t("el-button",{key:n.value,attrs:{type:n.theme,disabled:e.pending},on:{click:function(t){return e.submit(n)}}},[e._v(" "+e._s(n.name)+" ")])})),e.isShowBackBtn?t("el-button",{attrs:{type:"plain-third"},on:{click:e.goBack}},[e._v(e._s(e.$t("common.base.cancel")))]):e._e()],2)],1)]),e.init?t("form-builder",{ref:"form",attrs:{mode:"base",value:e.value,fields:e.fields,"is-edit-state":!0,"is-dit":e.isEdit,"template-id":e.templateId,"form-cell-count":e.formCellCount,"form-editing-mode":e.formEditingMode,"find-builder-that":e.builderThat},on:{getDeleteFiles:e.getDeleteFiles,update:e.update}}):e._e()],1)},i=[],o=n(37801),a=n(18885),s=n(71357),c=n(42881),u=n(35730),l=(n(67880),n(87313),n(2286),n(3923),n(71620),n(80793),n(36700),n(35256),n(21484),n(8326),n(89716),n(76119),n(16961),n(14126),n(54615),n(89370),n(32807),n(88747),n(24929),n(11948),n(55650),n(75069),n(28244),n(42925),n(21633),n(69594),n(79526),n(75048),n(13262),n(68735),n(74526)),d=n(60651),f=n(74118),m={name:"ProjectTaskEditMixin",computed:{isFromProject:function(){var e;return"project"===(null===(e=this.$route.query)||void 0===e?void 0:e.from)},isProjectTaskDraft:function(){var e;return"draft"===(null===(e=this.$route.query)||void 0===e?void 0:e.actionType)},isProjectTaskAdd:function(){var e;return"add"===(null===(e=this.$route.query)||void 0===e?void 0:e.actionType)},isProjectNew:function(){return this.isFromProject&&(this.isProjectTaskDraft||this.isProjectTaskAdd)}},methods:{toFilterTemporatyBtn:function(){var e,t;this.isFromProject&&(this.buttonList=null!==(e=null===(t=this.buttonList)||void 0===t?void 0:t.filter((function(e){return"temporarilySave"!==e.value})))&&void 0!==e?e:[])},projectToTaskForm:function(e){var t=this;return(0,c.A)((0,a.A)().mark((function n(){var r,i,o,s,c,u,l;return(0,a.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r={},n.prev=1,o=t.$route.query,s=o.projectId,c=o.additionalId,c){n.next=5;break}return n.abrupt("return");case 5:return u={fromBizId:s,additionalId:c,useScene:"PROJECT_TASK_RULE",toBizType:"PAAS",fromBizType:"PROJECT_MANAGER"},n.next=8,(0,f.J8)(u);case 8:if(l=n.sent,l.success){n.next=11;break}return n.abrupt("return",t.$message.error(l.message));case 11:return n.abrupt("return",e(null!==(i=null===l||void 0===l?void 0:l.data)&&void 0!==i?i:{}));case 14:n.prev=14,n.t0=n["catch"](1),console.error(n.t0);case 17:return n.abrupt("return",r);case 18:case"end":return n.stop()}}),n,null,[[1,14]])})))()},projectTaskParamsFun:function(e){try{var t,n,r={},i=JSON.parse(localStorage.getItem("projectTaskCreate"));if(this.isProjectTaskDraft)r=(0,s.A)((0,s.A)({},e),{},{projectTask:{projectId:(null===(t=this.$route.query)||void 0===t?void 0:t.projectId)||"",id:i.id}});else r=(0,s.A)((0,s.A)({},e),{},{projectTask:(0,s.A)({projectId:(null===(n=this.$route.query)||void 0===n?void 0:n.projectId)||""},i)});return r}catch(o){return console.error(o),e}}}},p=n(48649),v=(n(7509),n(19944),n(67259),function(){var e=(0,p.ref)("im_linkage_session_local_"),t=function(e,t){sessionStorage.setItem(e,JSON.stringify(t))},n=function(e){return JSON.parse(sessionStorage.getItem(e))},r=function(e){sessionStorage.removeItem(e)};return{sessionLocalKey:e,setSessionLocal:t,getSessionLocal:n,clearSessionLocal:r}}),h=v(),g=h.setSessionLocal,b=h.getSessionLocal,y=h.sessionLocalKey,C=h.clearSessionLocal,S=(0,p.defineComponent)({name:"intelligentTagsCreateAndMixin",data:function(){return{currentInstance:null,IntelligentCurrentType:"",IntelligentCurrentLabelFromIm:[],sessionLocalKey:y}},methods:{setSessionLocal:g,getSessionLocal:b,clearSessionLocal:C,IntelligentInitFormLabelHandler:function(e){this.currentInstance=e,this.IntelligentCurrentType=this.$route.query.formId||""},IntelligentGetSessionHandler:function(){try{this.IntelligentCurrentLabelFromIm=this.getSessionLocal(this.sessionLocalKey+this.IntelligentCurrentType)||[],this.clearSessionLocal(this.sessionLocalKey+this.IntelligentCurrentType)}catch(e){console.error(e)}},executeValueChange:function(){var e;this.currentInstance&&(null===(e=this.currentInstance)||void 0===e||e.changeLabelFromSession())},handlerLabelFromFormBuilderWindow:function(e){var t=e.data,n=t.action;t.data;if("changeAllFormBuilderLabelValue"==n){if(!sessionStorage.hasOwnProperty(this.sessionLocalKey+this.IntelligentCurrentType))return;this.IntelligentGetSessionHandler(),this.executeValueChange()}}},mounted:function(){window.addEventListener("message",this.handlerLabelFromFormBuilderWindow)},destroyed:function(){window.removeEventListener("message",this.handlerLabelFromFormBuilderWindow)}}),I=n(9138),F=n(80906),A=n(23559),T=n(92648),k=n(7905),w=n(92935),B=n.n(w),L=n(70072),E=n(16029),x=n(19055),D=n(87512),P=n(84913),N=n(63590),O=n(58184),_=n(44916),$=function(){var e=this,t=e._self._c;e._self._setupProxy;return e.showCom?t("div",{staticClass:"flex-x bbx-form-cell-change-box"},e._l(e.cellGroup,(function(n,r){return t("div",{key:r,staticClass:"just-cur-point cell-item",class:e.count===n.value?"choosed-item":"",on:{click:function(t){return e.changeCell(n)}}},[t("i",{staticClass:"iconfont",class:n.icon})])})),0):e._e()},M=[],z=(n(33438),(0,p.defineComponent)({name:"FormCellChange",props:{count:{type:Number},disabled:{type:Boolean,default:!1}},setup:function(e,t){var n=t.emit,r=(0,p.ref)(!1),i=(0,p.computed)((function(){return!e.disabled&&r.value}));function o(){return s.apply(this,arguments)}function s(){return s=(0,c.A)((0,a.A)().mark((function e(){var t;return(0,a.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,L.getAllGrayInfo)();case 2:t=e.sent,t.FORM_MULTI_ROW?r.value=!0:n("update",1);case 4:case"end":return e.stop()}}),e)}))),s.apply(this,arguments)}function u(e,t){return{value:e,icon:t}}var l=[u(1,"icon-danlie"),u(2,"icon-shuanglie"),u(3,"icon-sanlie"),u(4,"icon-silie")];function d(t){if(!e.disabled&&r.value){var i=(0,L.useStateSystemFormBuilderCell)(),o=i.setSystemFormBuilderCell;o(t.value),n("update",t.value)}}function f(){window.addEventListener("message",(function(t){if(!e.disabled&&r.value){var i=t.data,o=i.action,a=i.number;"changeAllFormBuilderCell"==o&&n("update",a)}}))}function m(){return v.apply(this,arguments)}function v(){return v=(0,c.A)((0,a.A)().mark((function e(){var t,r,i;return(0,a.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=(0,L.useStateSystemFormBuilderCell)(),r=t.getSystemFormBuilderCell,e.next=3,r();case 3:i=e.sent,n("update",i);case 5:case"end":return e.stop()}}),e)}))),v.apply(this,arguments)}return f(),o(),m(),{cellGroup:l,changeCell:d,haveFormCellGray:r,showCom:i}}})),j=z,R=n(49100),V=(0,R.A)(j,$,M,!1,null,"24172b14",null),q=V.exports,U=n(94766),H=n(44236),Y=(0,L.useFormTimezone)(),G=Y.disposeFormViewTime,K=Y.disposeFormSubmitTime,J=(0,H.p)(),W=J.fieldIntLabel,X={name:"template-edit-view",mixins:[m,S],components:{BizFormCellChange:q},provide:function(){return{isEdit:this.isEdit,isFlowForm:this.isFlowForm}},props:{isInlineSelf:{type:Boolean,default:function(){return!1}},isInlineSelfData:{type:Object,default:function(){return{}}}},data:function(){return{init:!1,loading:!1,pending:!1,isDelete:!1,isTaskFrame:!1,fields:[],value:{},isFlowForm:!1,buttonList:[],isStoreContentBizId:"",taskMessage:{},isConnectorFrame:!1,connectorMessageData:{},isShowBackBtn:!0,operaButton:{},formCellCount:1,needServerDeleFiles:[],templateNameLanguage:{},templateName:"",initValue:{}}},computed:{isCopy:function(){return"1"===this.$route.query.isCopy},appId:function(){var e;return this.isInlineSelf?null===(e=this.isInlineSelfData)||void 0===e?void 0:e.appId:this.$route.query.appId},templateId:function(){var e;return this.isInlineSelf?null===(e=this.isInlineSelfData)||void 0===e?void 0:e.templateId:this.$route.query.formId},contentBizId:function(){var e;return this.isInlineSelf?null===(e=this.isInlineSelfData)||void 0===e?void 0:e.contentBizId:this.$route.query.formContentId||this.isStoreContentBizId},fieldNameMap:function(){return this.fields.reduce((function(e,t){return e[t.fieldName]=t,e}),{})},connectorCreateDataSessionKey:function(){var e=location.href.replace(location.origin,"");return"connector_to_PAAS_create_data_".concat(e)},isEdit:function(){return Boolean(this.contentBizId)},isOnlyEdit:function(){var e=this.$route.query.onlyEdit;return"boolean"===typeof e?e:"true"===this.$route.query.onlyEdit},nodeInstanceId:function(){return this.$route.query.nodeInstanceId},storageKey:function(){var e=this.contentBizId||"";return _.IB+"_"+e},isShowFormCellChange:function(){return!(this.isConnectorFrame||this.isTaskFrame)&&(0,E.Nh)()},formEditingMode:function(){return this.isEdit&&!this.isCopy?"edit":"create"},formTabTitle:function(){var e,t=(null===(e=this.templateNameLanguage)||void 0===e?void 0:e[this.$i18n.locale])||this.templateName,n="create"===this.formEditingMode?"common.base.createModule":"common.otherPageTitle.editForm";return["create","edit"].includes(this.formEditingMode)?this.$t(n,{data1:t}):this.$t("common.pageTitle.otherPageTitle.createNewForm")},connectorInfo:function(){return this.initValue.connectorInfo||{}},fromSource:function(){return this.$route.query.fromSource}},created:function(){var e=this;this.getConnectorCreateDataBySessionKey(),this.IntelligentInitFormLabelHandler(this),this.formCellCount=this.$platform.getMainAllFormBuilderCell(),window.addEventListener("message",(function(t){var n=t.data;n.task&&(e.isTaskFrame=!0,e.taskMessage=n.task),n.connector&&(e.isConnectorFrame=!0,e.connectorMessageData=n.connector)})),window.parent.postMessage({ready:!0},"*"),this.clearRateStorage()},mounted:function(){var e=this;this.initialize().then((function(){e.fetchConnectorInsertSelectCall().then((function(){var t=(0,P.X0)({fields:e.fields,isEdit:e.isEdit,templateId:e.templateId}),n=t.fetchBatchSubFormDefaultValueDataHandler;n(e),!e.isEdit&&e.$route.query.conversationNo&&(e.IntelligentGetSessionHandler(),e.changeLabelFromSession())}))["finally"]((function(){return e.loading=!1}))}))},beforeDestroy:function(){this.removeConnectorCreateSessionData(),this.clearRateStorage()},methods:{fieldIntLabel:W,hiddenFieldValueFilterHandler:A.Oj,disposeFormSubmitTime:K,packSaveForm:T.nO,getDeleteFiles:function(e){this.needServerDeleFiles=[].concat((0,u.A)(this.needServerDeleFiles),(0,u.A)(e))},changeLabelFromSession:function(){!this.isEdit&&this.$route.query.conversationNo&&this.$set(this.value,"intLabel",this.IntelligentCurrentLabelFromIm)},update:function(e){var t=e.field,n=e.newValue,r=(e.oldValue,t.fieldName);t.displayName;this.$set(this.value,r,n)},initialize:function(){var e=this,t=[this.fetchFields()];return this.contentBizId&&t.push(this.fetchFormData()),this.loading=!0,Promise.all(t).then(function(){var t=(0,c.A)((0,a.A)().mark((function t(n){var r,i,o,c,u,l,d,f,m,p;return(0,a.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=n[1]||{},o=i.id,c=i.isDelete,u=i.paasFormValueList,l=void 0===u?[]:u,d=n[1]||{},e.initValue=d,e.isDelete=1==c,f=F.n_(e.fields,(0,T.Sq)(l,e.fields),e.isCopy),f["intLabel"]=null!==(r=null===d||void 0===d?void 0:d.label)&&void 0!==r?r:{},f=G(e.fields,f),e.value=(0,s.A)({templateBizId:e.templateId,contentId:e.isCopy?"":o},f),e.isCopy&&(e.value.serialNumber=""),e.isTaskFrame&&!e.isConnectorFrame&&(m=(0,T._q)(e.fields,e.taskMessage),e.value=Object.assign(e.value,m)),t.prev=10,e.toFilterTemporatyBtn(),!e.isProjectNew){t.next=17;break}return t.next=15,e.projectToTaskForm(e.handlerConnector);case 15:p=t.sent,e.value=Object.assign(e.value,p);case 17:t.next=22;break;case 19:t.prev=19,t.t0=t["catch"](10),console.error(t.t0);case 22:e.init=!0,!e.isConnectorFrame&&(e.loading=!1),e.$emit("buttonList",e.buttonList);case 25:case"end":return t.stop()}}),t,null,[[10,19]])})));return function(e){return t.apply(this,arguments)}}())["catch"]((function(t){!e.isConnectorFrame&&(e.loading=!1)}))},fetchFields:function(){var e=this,t={templateBizId:this.templateId,excludeConnectOption:!0};return this.isOnlyEdit&&(t.contentBizId=this.contentBizId,t.nodeInstanceId=this.nodeInstanceId),l.Tw(t,this.isOnlyEdit).then((function(t){var n=t.data,r=t.success,i=t.message;if(r){var o=n||{},a=o.paasFormFieldVOList,c=void 0===a?[]:a,u=o.isContainWf,l=o.templateNameLanguage,d=o.templateName;e.templateNameLanguage=l||{},e.templateName=d||"";var f=c.map((function(e){return e.disabled=0===e.revisable,["productAndSparepart"].includes(e.formType)&&e.subFormFieldList.map((function(e){var t;"sparepart"===e.formType&&null!==(t=e.setting)&&void 0!==t&&null!==(t=t.subFormFieldList)&&void 0!==t&&t.length&&(e.subFormFieldList=e.setting.subFormFieldList)})),e})).filter((function(e){return F.Pm(e,!0)}));if(e.fields=(0,I.lR)(e.isOnlyEdit?e.resetFieldListCanEdit(f):f,!0),e.fields.unshift((0,s.A)({},e.fieldIntLabel())),e.isFlowForm=1==u,e.isFlowForm)return e.fetchFlowBtnList();e.buttonList=[x.A.SUBMIT,x.A.TEMPORARILY_SAVE].filter((function(t){return!e.isOnlyEdit||t.value===x.A.SUBMIT.value}))}else e.$message.warning(i)}))["catch"]((function(e){return console.error("fetchFields error",e)}))},handelPageButtonClick:function(e,t){var n=this;(0,N.Yi)(e,t,{fields:this.fields,multipleSelection:t,js_vm:this},(function(){n.pageButtonLoading=!0}),null,(function(){n.pageButtonLoading=!1}))},hanldeBtnList:function(e){"trigger"===e.event[0].type?(0,N.Yi)(e,[this.templateId],{},null,null,this.handleTrigger):"linker"===e.event[0].type?(0,N.Yi)(e,[this.templateId]):this.handelPageButtonClick(e,[this.templateId])},fetchFormData:function(){return l.Ev({contentBizId:this.contentBizId}).then((function(e){return(null===e||void 0===e?void 0:e.data)||{}}))["catch"]((function(e){return console.error("fetchFormData error",e)}))},fetchFlowBtnList:function(){var e=this;return l.y5({formTemplateId:this.templateId}).then((function(t){if(t.success){var n=(null===t||void 0===t?void 0:t.data)||{},r=n.buttons,i=void 0===r?[]:r;e.buttonList=i.map((function(e){if(30!==e.code){var t=x.A.getBtnByCode(e.code);return t.name=e.cnName,t}return e.name=e.cnName,e.theme="plain-third",e})).filter((function(t){return!e.isOnlyEdit||t.value===x.A.SUBMIT.value}))}}))["catch"]((function(e){return console.error("err",e)}))},handleTrigger:function(){location.reload()},submit:function(e){var t=this;return(0,c.A)((0,a.A)().mark((function n(){var r,i,o,s,c,u,l,d,f,m,p,v,h,g,b,y,C,S,I;return(0,a.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,t.operaButton=e,s=e.value===x.A.TEMPORARILY_SAVE.value?1:0,30!==e.code){n.next=5;break}return n.abrupt("return",t.hanldeBtnList(e));case 5:if(s){n.next=36;break}if(c=["materialOrder","serviceItem"],u=t.fields.filter((function(e){return c.includes(e.formType)})).map((function(e){return e.fieldName})),!u.length){n.next=12;break}if(l=t.checkExchangeRate(t.value,u),!l){n.next=12;break}return n.abrupt("return",(0,k.xE)(t.$t("common.currency.exchangeRateValidate")));case 12:if(t.isOnlyEdit){n.next=20;break}return n.next=15,t.$refs.form.validate(!1);case 15:return d=n.sent,n.next=18,t.scrollIntoFirstRequireDom();case 18:if(d){n.next=20;break}return n.abrupt("return",Promise.reject("validate error"));case 20:f=[!1],m=0,p=Array.from(t.$refs.form.fields);case 22:if(!(m<p.length)){n.next=34;break}if(h=p[m],null!==(v=h.setting)&&void 0!==v&&v.dataFillingMode){n.next=26;break}return n.abrupt("continue",31);case 26:return n.next=28,Promise.all((0,U.ox)(t.$refs.form,"form-builder").map((function(e){return["formula"].includes(e.fields[0].formType),e.validate()})));case 28:g=n.sent,b=g.every((function(e){return e})),b||f.push(!0);case 31:m++,n.next=22;break;case 34:if(!f.some((function(e){return e}))){n.next=36;break}return n.abrupt("return");case 36:return t.pending=!0,n.next=39,t.$refs.form.validateAsyncFetch();case 39:if(y=(0,A.Oj)(t.fields,t.value),C=K(t.fields,y),S={formValueList:(0,T.nO)(t.fields,C,t.isCopy),templateUUId:t.templateId,contentBizId:t.isCopy?"":t.contentBizId,isEdit:t.isOnlyEdit,paasNodeInstanceId:t.nodeInstanceId,isStore:s},Object.assign(S,(null===(r=t.value)||void 0===r?void 0:r.intLabel)||{}),t.isEdit&&!t.isCopy&&(S.deletefiles=t.needServerDeleFiles),!t.isConnectorFrame||null===(i=t.connectorMessageData)||void 0===i||!i.isCreate){n.next=48;break}if(!s){n.next=47;break}return n.abrupt("return",t.connectorSaveFormContent(S,s));case 47:return n.abrupt("return",t.startFlowContentForConnectorCard(S));case 48:if(!t.isCopy||B().isEmpty(t.connectorInfo)){n.next=50;break}return n.abrupt("return",t.startFlowContentForConnectorCard(S));case 50:if(!t.isConnectorFrame||null===(o=t.connectorMessageData)||void 0===o||!o.isEdit||s){n.next=54;break}if(null===(I=t.connectorMessageData)||void 0===I||!I.isDetail){n.next=53;break}return n.abrupt("return",t.startFlowContentForConnectorCard(S));case 53:return n.abrupt("return",t.saveFormContent(S,s));case 54:if(!t.isFlowForm||s||t.isOnlyEdit){n.next=56;break}return n.abrupt("return",t.startFlowContent(S));case 56:t.saveFormContent(S,s),n.next=62;break;case 59:n.prev=59,n.t0=n["catch"](0),console.error("err",n.t0);case 62:case"end":return n.stop()}}),n,null,[[0,59]])})))()},fetchUpdateSerialNumberValue:function(e){var t=this;l.pp({contentBizId:e}).then((function(e){var n=e.data,r=void 0===n?"":n;return t.value[D.E.SerialNumber]=r}))},saveFormContent:function(e,t){var n=this;this.isProjectNew&&(e=this.projectTaskParamsFun(e)),l.xd(e).then((function(e){var r=e.success,i=e.message,o=e.data;r?(t?(n.isStoreContentBizId=o,n.fetchUpdateSerialNumberValue(o),setTimeout((function(){return n.reloadTab()}),500)):!n.isInlineSelf&&setTimeout((function(){return n.goBack()}),500),!n.isInlineSelf&&n.$message.success(n.$t("common.base.saveSuccess"))):(n.$message.warning(i),n.pending=!1)}))["catch"]((function(e){console.log("template-edit-view saveFormContent error",e)}))["finally"]((function(){return n.pending=!1}))},connectorSaveFormContent:function(e,t){var n,r,i,o,a=this,s={sourceId:null===(n=this.connectorMessageData)||void 0===n?void 0:n.fromBizId,sourceTypeId:null===(r=this.connectorMessageData)||void 0===r?void 0:r.fromBizTypeId,connectorSourceOperate:null===(i=this.connectorMessageData)||void 0===i?void 0:i.connectorSourceOperate,fromBizNo:(null===(o=this.connectorMessageData)||void 0===o?void 0:o.fromBizNo)||"",formContentEditForm:e};l.xV(s).then((function(e){var t=e.success,n=e.message,r=e.data;t?(a.isStoreContentBizId=r,a.fetchUpdateSerialNumberValue(r),a.$message.success(a.$t("common.base.saveSuccess")),setTimeout((function(){a.goBack()}),500)):(a.$message.warning(n),a.pending=!1)}))["catch"]((function(e){console.log("template-edit-view saveFormContent error",e)}))["finally"]((function(){return a.pending=!1}))},startFlowContent:function(e){var t=this,n={appId:this.appId,formTemplateId:this.templateId,formContentId:this.isCopy?"":this.contentBizId,formContentEditForm:e,isNeedAuth:this.isTaskFrame?0:1};this.isProjectNew&&(n=this.projectTaskParamsFun(n)),l.Ub(n).then((function(e){var n=e.success,r=e.message,i=e.code;n?t.isInlineSelf||(setTimeout((function(){t.goBack()}),500),t.$message.success(t.$t("common.base.saveSuccess"))):(t.$message.warning(r),t.pending=!1,3002===i&&t.initialize())}))["catch"]((function(e){console.log("template-edit-view startFlowContent error",e),t.pending=!1}))},startFlowContentForConnectorCard:function(e){var t,n,r,i,o,a,c,u,d,f=this,m={sourceId:(null===(t=this.connectorInfo)||void 0===t?void 0:t.sourceBizId)||this.connectorMessageData.fromBizId,sourceTypeId:(null===(n=this.connectorInfo)||void 0===n?void 0:n.sourceBizTypeId)||this.connectorMessageData.fromBizTypeId,connectorSourceOperate:(null===(r=this.connectorInfo)||void 0===r?void 0:r.sourceBizType)||this.connectorMessageData.connectorSourceOperate,fromBizNo:(null===(i=this.connectorInfo)||void 0===i?void 0:i.sourceBizNo)||(null===(o=this.connectorMessageData)||void 0===o?void 0:o.fromBizNo)||"",cardBizId:(null===(a=this.connectorInfo)||void 0===a?void 0:a.cardBizId)||(null===(c=this.connectorMessageData)||void 0===c?void 0:c.cardBizId)||"",connectorParam:{processorInstanceId:(null===(u=this.connectorInfo)||void 0===u?void 0:u.processId)||(null===(d=this.connectorMessageData)||void 0===d?void 0:d.processId)||""}},p=(0,s.A)({appId:this.appId,formTemplateId:this.templateId,formContentId:this.contentBizId,formContentEditForm:e,isNeedAuth:this.isTaskFrame?0:1},m);return l._G(p).then((function(e){var t=e.success,n=e.message,r=e.code;t?(f.removeConnectorCreateSessionData(),f.isInlineSelf||(setTimeout((function(){f.goBack()}),500),f.$message.success(f.$t("common.base.saveSuccess")))):(f.$message.warning(n),f.pending=!1,3002===r&&f.initialize())}))["catch"]((function(e){console.log("template-edit-view startFlowContent error",e),f.pending=!1}))},reloadTab:function(){var e,t=null===(e=window)||void 0===e||null===(e=e.frameElement)||void 0===e?void 0:e.getAttribute("fromid");this.$platform.refreshTab(t)},goBack:function(){var e;if(this.removeConnectorCreateSessionData(),this.isTaskFrame)return window.parent.postMessage({close:!0},"*");var t=null===(e=window)||void 0===e||null===(e=e.frameElement)||void 0===e||null===(e=e.dataset)||void 0===e?void 0:e.id;if(t){var n,r=this.isCopy?this.$route.query.fromId:null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("fromid");!["connectControl"].includes(this.fromSource)&&this.$platform.refreshTab(r),this.$platform.closeTab(t),this.$platform.goOpenTab(r)}else this.$router.go(-1)},scrollIntoFirstRequireDom:function(){var e=this;return(0,c.A)((0,a.A)().mark((function t(){var n,r,i;return(0,a.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=e.$refs.form.$children.filter((function(e){return!["form-view","form-info"].includes(e.$el.className)})),r=0;case 2:if(!(r<n.length)){t.next=10;break}if(i=n[r],!(null!==i&&void 0!==i&&i.$el.classList.contains("err")||i.$attrs["data-empty"])){t.next=7;break}return i.$el.scrollIntoView({behavior:"smooth"}),t.abrupt("return",Promise.reject(!1));case 7:r++,t.next=2;break;case 10:case"end":return t.stop()}}),t)})))()},fetchConnectorInsertSelectCall:function(){var e=this;return(0,c.A)((0,a.A)().mark((function t(){var n,r,i,o,c,u,l,f,m;return(0,a.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isConnectorFrame&&null!==(n=e.connectorMessageData)&&void 0!==n&&n.isCreate){t.next=3;break}return console.warn("caused: is not create connector"),t.abrupt("return");case 3:if(o=(null===(r=window)||void 0===r||null===(r=r.frameElement)||void 0===r?void 0:r.getAttribute("id"))||"",c=(null===(i=window)||void 0===i||null===(i=i.frameElement)||void 0===i?void 0:i.className)||"",u=location.href.replace(location.origin,""),u=u.replaceAll("/","").replaceAll("-","").replaceAll("#","").replaceAll("?","").replaceAll("&","").replaceAll("=",""),l=o.includes(u)||c.includes(u),l){t.next=11;break}return console.warn("caused: is not connector frame window"),t.abrupt("return");case 11:if(f={fromBizType:e.connectorMessageData.fromBizType,fromBizTypeId:e.connectorMessageData.fromBizTypeId,fromBizId:e.connectorMessageData.fromBizId,toBizType:e.connectorMessageData.toBizType,toBizTypeId:e.connectorMessageData.toBizTypeId},m=Object.values(f).every(Boolean),m){t.next=16;break}return console.warn("connector 参数不完整"),t.abrupt("return");case 16:return e.$eventBus.$emit("connector-insert-fetch-value",!0),t.abrupt("return",(0,d.Lg)(f).then((function(t){var n=(null===t||void 0===t?void 0:t.data)||{},r=e.handlerConnector(n);e.value=(0,s.A)((0,s.A)({},e.value),r),e.$nextTick((function(){return e.$eventBus.$emit("connector-insert-fetch-value",!1)}))})));case 18:case"end":return t.stop()}}),t)})))()},handlerConnector:function(e){var t=this,n={};return Object.keys(e).forEach((function(r){var i=e[r],o=i,a=t.fieldNameMap[r];try{o=i&&JSON.parse(i)}catch(s){console.error("getConnectorInsertSelectCall 数据解析失败",s)}"customer"===(null===a||void 0===a?void 0:a.formType)&&(0,w.isPlainObject)(o)&&(o=[o]),null!==a&&void 0!==a&&a.formType&&F.hL(a)&&(0,w.isArray)(o)&&(o=o.map((function(e){var t=(null===e||void 0===e?void 0:e.subFormValueFormList)||[];return t.reduce((function(e,t){var n="";try{n=JSON.parse(t.value)}catch(r){n=t.value}return e[t.fieldName]=n,e}),{})}))),n[r]=o})),F.n_(this.fields,n,this.handleProcessConnectLogisticsFieldValue)},getConnectorCreateDataBySessionKey:function(){try{var e=sessionStorage.getItem(this.connectorCreateDataSessionKey);e&&(this.isConnectorFrame=!0,this.connectorMessageData=JSON.parse(e))}catch(t){console.error("getConnectorCreateDataBySessionKey error",t),this.isConnectorFrame=!1}},removeConnectorCreateSessionData:function(){sessionStorage.removeItem(this.connectorCreateDataSessionKey)},handleProcessConnectLogisticsFieldValue:function(e,t){var n=this,r=e.filter((function(e){return e.formType===D.E.Logistics}));return r.length>0&&r.forEach((function(e){var r=e.fieldName,i=r.split("_")[0];t[i]||!n.value[i]&&!n.value[r]||(t[i]=n.value[i]||n.value[r])})),t},resetFieldListCanEdit:function(e){var t=this;return e.map((function(e){if(e.disabled=!1,e.revisable=1,null!==e&&void 0!==e&&e.subFormFieldList&&Array.isArray(null===e||void 0===e?void 0:e.subFormFieldList)){var n=e.subFormFieldList;e.subFormFieldList=t.resetFieldListCanEdit(n)}return e}))},childShowAlert:B().debounce((function(){(0,k.xE)(this.$t("common.currency.queryException"))}),100),containWriteAlert:B().debounce((function(){(0,k.xE)(this.$t("common.currency.manulExchangeRateTip"))}),200),checkExchangeRate:function(e,t){var n=!1;return Object.entries(e).forEach((function(e){var r=(0,o.A)(e,2),i=r[0],a=r[1];t.includes(i)&&a.forEach((function(e){Object.entries(e).forEach((function(e){var t=(0,o.A)(e,2),r=t[0],i=t[1];r.includes(O.k.EXCHANGE_RATE)&&!i&&(n=!0)}))}))})),n},clearRateStorage:function(){sessionStorage.removeItem(this.storageKey)},handleFormCellChange:function(e){this.formCellCount=e},builderThat:function(){return this}}},Q=X,Z=Q,ee=(0,R.A)(Z,r,i,!1,null,"fe60e08e",null),te=ee.exports},63590:function(e,t,n){n.d(t,{GG:function(){return S},Yi:function(){return A},b2:function(){return I},eW:function(){return y},fT:function(){return b},iH:function(){return F},n3:function(){return C}});var r=n(71357),i=n(18885),o=n(42881),a=n(62361),s=(n(2286),n(44807),n(80793),n(48152),n(35256),n(21484),n(16961),n(54615),n(7354),n(32807),n(55650),n(75069),n(42925),n(944),n(21633),n(33656),n(84859)),c=n(65582),u=n(70362),l=n(70072),d=n(92935),f=n(18495),m=n(56582),p=n(34987),v=n(88376),h=n(78670),g=n(23403),b=5,y="primaryKey",C=function(){var e=function(e,t){return{label:e,value:t}},t=(0,a.A)((0,a.A)({},u.LY.Primary,"type-primary"),u.LY.Gray,"type-gray"),n=[e((0,s.t)("common.page.buttonSet.text6"),u.LY.Primary),e((0,s.t)("common.page.buttonSet.text7"),u.LY.Gray)],r=function(e,t,n){return{label:e,value:t,des:n,disabled:!1}},c=[r((0,s.t)("common.page.buttonSet.text8"),u.cS.PcList,"")],d=function(){var e=(0,o.A)((0,i.A)().mark((function e(t){var n,r,o,a,s,c,d,f,m;return(0,i.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n={pass:!0},e.prev=1,r=0;case 3:if(!(r<t.length)){e.next=19;break}if(d=t[r],!(0,l.isEmpty)(d.name)&&0!==d.name.trim().length&&null!==(o=d.position)&&void 0!==o&&o.length){e.next=8;break}return n={pass:!1,pathId:d.pathId,errorModule:u.tP.Base},e.abrupt("break",19);case 8:if(f=d.event.find((function(e){return e.type===u.Sd.Trigger})),0!==(null===f||void 0===f||null===(a=f.execute)||void 0===a?void 0:a.length)){e.next=12;break}return n={pass:!1,pathId:d.pathId,errorModule:u.tP.Event},e.abrupt("break",19);case 12:if(m=d.event.find((function(e){return e.type===u.Sd.Linker})),(!m||0!==(null===m||void 0===m||null===(s=m.execute)||void 0===s?void 0:s.length)&&h.WC.test(null===m||void 0===m?void 0:m.execute[0]))&&null!==(c=d.event[0])&&void 0!==c&&c.type){e.next=16;break}return n={pass:!1,pathId:d.pathId,errorModule:u.tP.Event},e.abrupt("break",19);case 16:r++,e.next=3;break;case 19:e.next=25;break;case 21:return e.prev=21,e.t0=e["catch"](1),console.error(e.t0,"<-----validateForButtonSet is Error"),e.abrupt("return",Promise.resolve({pass:!1}));case 25:return e.abrupt("return",Promise.resolve(n));case 26:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}();return{typeChooseArr:n,showPositionChooseArr:c,typeColorEnum:t,validateForButtonSet:d}},S=function(e){var t=(0,d.cloneDeep)(e);return t.map((function(e,t){var n,r=e.buttonId,i=e.show;r?(e["pathId"]=[r],e[y]=r):console.error("".concat(t," buttonId is Miss"));var o=[];return i.detail&&o.push(u.cS.PcDetail),i.list&&o.push(u.cS.PcList),i.mobile&&o.push(u.cS.MobileDetail),e["position"]=o,null===(n=e.event)||void 0===n||n.map((function(e){return e.execute||(e.execute=[]),e})),e}))},I=function(e){var t=(0,d.cloneDeep)(e);return t.map((function(e){var t=e.position;return delete e[y],delete e["pathId"],e["show"]={mobile:!(null===t||void 0===t||!t.includes(u.cS.MobileDetail)),detail:!(null===t||void 0===t||!t.includes(u.cS.PcDetail)),list:!(null===t||void 0===t||!t.includes(u.cS.PcList))},delete e["position"],e}))},F=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=(0,a.A)((0,a.A)((0,a.A)({},u.cS.MobileDetail,"mobile"),u.cS.PcDetail,"detail"),u.cS.PcList,"list"),i={module:"PAAS",moduleId:t,isEdit:!1};return(0,f.m$)(i).then((function(t){if(0===t.status){var i,o=(null===(i=t.data)||void 0===i?void 0:i.filter((function(t){var n;return r[e]&&!0===(null===(n=t.show)||void 0===n?void 0:n[r[e]])})))||[];n&&n(o)}else m.Ay.notification({type:"error",title:(0,s.t)("common.base.fail"),message:t.message})}))},A=function(){var e=(0,o.A)((0,i.A)().mark((function e(t){var n,o,a,c,d,f,h,b,y,C,S,I,F,A,k,w,B,L,E=arguments;return(0,i.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=E.length>1&&void 0!==E[1]?E[1]:null,o=E.length>2&&void 0!==E[2]?E[2]:{},a=E.length>3&&void 0!==E[3]?E[3]:null,c=E.length>4&&void 0!==E[4]?E[4]:null,d=E.length>5&&void 0!==E[5]?E[5]:null,f=t.event,h=void 0===f?[]:f,b=h.find((function(e){return e.type===u.Sd.Linker})),!b){e.next=14;break}if(S=null===(y=window.frameElement)||void 0===y?void 0:y.getAttribute("id"),I="".concat(null===(C=h[0])||void 0===C||null===(C=C.execute)||void 0===C?void 0:C[0]),I){e.next=12;break}return e.abrupt("return");case 12:return m.Ay.openTab({id:"".concat(t.buttonId),title:"".concat(t.name||t.cnName),close:!0,url:I,fromId:S}),e.abrupt("return");case 14:if(0!==(null===n||void 0===n?void 0:n.length)){e.next=16;break}return e.abrupt("return",m.Ay.alert((0,s.t)("common.modal.PLEASE_SELECT_DATA_MESSAGE")));case 16:if(F=h.find((function(e){return[u.Sd.Code,g.I3.CodeContent].includes(e.type)})),!F){e.next=29;break}return a&&a(),e.next=21,T(h[0].codeContent,n,(0,r.A)({},o));case 21:if(A=e.sent,d&&d(),k="errorMessage",!A){e.next=28;break}if(w=JSON.parse(A),!w.hasOwnProperty(k)){e.next=28;break}return e.abrupt("return",p.A.warning(w[k]));case 28:return e.abrupt("return");case 29:B=h.find((function(e){return e.type===u.Sd.Trigger})),L=[],n.length>0&&(L=n.map((function(e){return(0,l.isObject)(e)?e.bizId:e}))),a&&a(),(0,v.D$)({triggerId:B.execute.join("，"),bizIdList:L}).then((function(e){var t=0===e.status;m.Ay.notification({type:t?"success":"error",title:t?(0,s.t)("common.base.tip.operationSuccess"):(0,s.t)("common.base.tip.operationFail"),message:!t&&e.message}),c&&c()}))["finally"]((function(){d&&d()}));case 34:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();function T(e,t){return k.apply(this,arguments)}function k(){return k=(0,o.A)((0,i.A)().mark((function e(t,n){var o,a,s=arguments;return(0,i.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=s.length>2&&void 0!==s[2]?s[2]:{},a=c.A.getInstance((0,r.A)({},o)),e.next=4,a.executeCodeForListAndDetail(t,n,o);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)}))),k.apply(this,arguments)}},70362:function(e,t,n){n.d(t,{LY:function(){return i},Sd:function(){return o},cS:function(){return r},qy:function(){return s},tP:function(){return a},w1:function(){return c}});var r=(e=>(e.PcList="pcList",e.MobileDetail="mobileDetail",e.PcDetail="pcDetail",e))(r||{}),i=(e=>(e.Gray="plain-third",e.Primary="primary",e))(i||{}),o=(e=>(e.Trigger="trigger",e.Linker="linker",e.Code="code",e))(o||{}),a=(e=>(e.Base="base",e.Event="event",e))(a||{}),s=(e=>(e.Customer="CUSTOMER",e.Product="PRODUCT",e.TASK="TASK",e.B2BSHOP="B2B_SHOP",e.B2BSHOPSYSTEM="B2B_SHOP_SYSTEM",e.TOCSHOP="TOC_SHOP",e.TOCSHOPSYSTE="TOC_SHOP_SYSTEM",e.CLOUDWAREHOUSE="CLOUD_WAREHOUSE",e.CLOUDWAREHOUSESYSTEM="CLOUD_WAREHOUSE_SYSTEM",e.PAASSYSTEM="PAAS_SYSTEM",e.EVENT="EVENT",e.PROJECT="PROJECT",e.CONTRACT="CONTRACT",e))(s||{}),c=(e=>(e.Button="button",e.ButtonGroup="buttonGroup",e))(c||{})},74118:function(e,t,n){n.d(t,{AR:function(){return f},Bx:function(){return p},J8:function(){return o},R6:function(){return c},TF:function(){return d},UJ:function(){return b},Ul:function(){return u},WX:function(){return g},XR:function(){return l},_I:function(){return a},a1:function(){return m},eU:function(){return h},m3:function(){return s},vd:function(){return v}});var r=n(22229),i="/api/project/outside/project";function o(e){return r.A.post("/api/application/outside/call/insertSelectCallByAdditionalId",e)}function a(e){return r.A.post("".concat(i,"/task/detail"),e)}function s(e){return r.A.get("".concat(i,"/auth/button"),e)}function c(e){return r.A.post("".concat(i,"/task/updateProgress"),e)}function u(e){return r.A.post("/api/project/outside/field/list",e)}function l(e){return r.A.post("".concat(i,"/work/record/list"),e)}function d(e){return r.A.post("".concat(i,"/work/record/create"),e)}function f(e){return r.A.post("".concat(i,"/work/record/update"),e)}function m(e){return r.A.post("".concat(i,"/work/record/delete"),e,!1)}function p(e){return r.A.get("".concat(i,"/config/workRecord"),e)}function v(e){return r.A.post("".concat(i,"/task/updateTask"),e)}function h(e){return r.A.post("".concat(i,"/task/queryPreTaskList"),e)}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return r.A.post("".concat(i,"/config/task/queryAvailableTaskType"),e)}function b(e){return r.A.get("/api/paas/outside/pc/template/getNodeInstanceRelation",e)}}}]);