"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[419],{19055:function(e,t,n){var r=n(52275),i=n(26183),u=n(54232),a=n(58091),o=(n(98316),n(80793),n(75069),n(93431),n(21633),n(86651)),s=n(80602),c=n(84859),l=s.A.START_NODE,d=s.A.END_NODE,h=s.A.PROCEDD_NODE,A=s.A.APPROVE_NODE,g=function(e){function t(){var e;return(0,r.A)(this,t),e=(0,u.A)(this,t),e.SUBMIT={name:c.Ay.t("buttons.submit"),value:"submit",code:1,theme:"primary",shapes:[l,h]},e.TEMPORARILY_SAVE={name:c.Ay.t("buttons.temporarilySave"),value:"temporarilySave",code:2,theme:"plain-third",shapes:[l,h,A]},e.FORWARD={name:c.Ay.t("buttons.forward"),value:"forward",code:3,theme:"plain-third",shapes:[h,A]},e.ROLLBACK={name:c.Ay.t("buttons.rollback"),value:"rollback",code:4,theme:"plain-third",shapes:[h]},e.CANCEL={name:c.Ay.t("buttons.cancel"),value:"cancel",code:5,theme:"plain-third",shapes:[h,A]},e.AGREE={name:c.Ay.t("buttons.agree"),value:"agree",code:6,theme:"primary",shapes:[A]},e.REFUSE={name:c.Ay.t("buttons.refuse"),value:"refuse",code:7,theme:"danger",shapes:[A]},e.AUTO_SUBMIT={name:c.Ay.t("buttons.autoSubmit"),value:"autoSubmit",code:"0,8"},e.BACKTOME={name:c.Ay.t("buttons.backToMe"),value:"backToMe",code:10,theme:"danger",shapes:[h,A]},e.COUNTERSIGN={name:c.Ay.t("buttons.countersign"),value:"countersign",code:9,theme:"plain-third",shapes:[h,A]},e.PAUSE={name:c.Ay.t("buttons.pause"),value:"pause",code:11,theme:"plain-third",shapes:[h,A]},e.CUSTOMIZE={value:"custom",code:30,theme:"plain-third",shapes:[l,d,h,A]},e}return(0,a.A)(t,e),(0,i.A)(t,[{key:"getKeyByCode",value:function(e,t){for(var n in this){var r=this[n];if(r.code.toString().includes(e))return r[t]}}},{key:"getBtnByCode",value:function(e){for(var t in this){var n=this[t];if(n.code==e)return n}}}])}(o.A);t.A=new g},23403:function(e,t,n){var r,i,u;n.d(t,{CQ:function(){return i},FW:function(){return r},I3:function(){return u}}),function(e){e["Visible"]="visible",e["Revisable"]="revisable",e["Required"]="required"}(r||(r={})),function(e){e["VisibleCheckedAll"]="visibleCheckedAll",e["RevisableCheckedAll"]="revisableCheckedAll",e["RequiredCheckedAll"]="requiredCheckedAll"}(i||(i={})),function(e){e["Trigger"]="trigger",e["Linker"]="linker",e["CodeContent"]="codeContent"}(u||(u={}))},88376:function(e,t,n){n.d(t,{BI:function(){return u},D$:function(){return c},GK:function(){return l},GL:function(){return a},fG:function(){return s},oG:function(){return o}});n(67880);var r=n(22229),i="/api/application";function u(e){return r.A.post("".concat(i,"/outside/trigger/getTriggerInfoList"),e)}function a(e){return r.A.post("".concat(i,"/outside/trigger/updateTrigger"),e)}function o(e){return r.A.post("".concat(i,"/outside/trigger/deleteTrigger"),{triggerId:e})}function s(e){return r.A.post("".concat(i,"/outside/trigger/getTriggerFormInfo"),e)}function c(e){return r.A.post("".concat(i,"/outside/trigger/manualTrigger"),e)}function l(e){return r.A.get("".concat(i,"/outside/trigger/v3/getAllExecuteWayList"),e)}}}]);