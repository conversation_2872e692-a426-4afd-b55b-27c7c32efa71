"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[948],{16113:function(e,t,n){n.d(t,{F3:function(){return l},SF:function(){return c},Un:function(){return a},Vm:function(){return s},Ye:function(){return d},Zb:function(){return i},Zo:function(){return u}});n(35256),n(75069);var r=n(51280),o=["top","right","bottom","left"],a=220,i=80,c=100,s=32,u="#067BEF",l="#8C8C8C",d="#F759AB";o.map((function(e){return{id:"select-port-".concat(e),group:"".concat(e,"Select"),attrs:{fo:{magnet:"true"}},markup:[r.VK.getForeignObjectMarkup()]}})),o.map((function(e,t){return"select-port-".concat(e)}))},70628:function(e,t,n){n.r(t);var r=n(17319),o=n.n(r),a=n(18885),i=n(42881),c=n(37801),s=n(62361),u=(n(67880),n(80793),n(35256),n(13560),n(76119),n(7509),n(16961),n(32807),n(19944),n(55650),n(75069),n(21633),n(48649)),l=n(81616),d=n(44309),f=n(80602),p=n(72964),m=n(70688),v=n(56582),w=n(64055),g=n(69749),y=n(49430),h=n(67142),A=n(11592),b=n(84859),N=n(92935),k=n(69396),x=n(74526),C=n(46687),F=n(57309),J=n(34117);t["default"]=(0,u.defineComponent)({name:"work-flow-design-v2",components:(0,s.A)({},J.A.name,J.A),props:{mode:{type:String,default:d.A.PAAS}},setup:function(e,t){var n=e.mode,r=t.expose,s=(0,p.J0)([]),J=(0,c.A)(s,2),I=J[0],O=J[1],S=(0,u.reactive)({isEdit:!1,fields:[],antvJson:{},initJson:"",templateName:"",cell:{}}),D=(0,u.ref)({}),L=(0,u.ref)(null),E=(0,p.EQ)(),P=(0,c.A)(E,1),T=P[0],B=(0,p.rd)(),_=B.query,M=(0,m.M)(),q=M.loading,$=M.showLoading,j=M.hideLoading,z=(0,m.M)(),G=z.loading,V=z.showLoading,K=z.hideLoading,Q=(0,u.computed)((function(){return _.appId})),R=(0,u.computed)((function(){return _.formId})),X=((0,u.computed)((function(){return n===d.A.TASK})),(0,u.computed)((function(){return _.taskTypeId})),(0,u.computed)((function(){return _.taskFlowType})),(0,u.computed)((function(){return T&&T.$i18n.locale===w.As})),(0,u.computed)((function(){return T&&T.$i18n.locale})),(0,u.computed)((function(){return F.dq[0].list=F.dq[0].list.slice(0,3),F.dq}))),Z=(0,u.computed)((function(){if(D.value.type){var e=D.value.type.split("-")[0];return D.value.type&&F.Xd.includes(e)}return null})),U=function(e){var t=(0,N.nth)(e,-1),n=t.type&&t.type.includes(f.A.CONDITION)?b.Ay.t("view.designer.workFlow.branch"):b.Ay.t("common.base.node");return g.MessageBox.confirm(b.Ay.t("view.designer.tip.delWarning",{text:n}),b.Ay.t("common.base.toast"),{confirmButtonText:b.Ay.t("common.base.confirm"),cancelButtonText:b.Ay.t("common.base.cancel"),type:"warning"})},W=function(e){D.value=e,S.cell=e,L.value&&L.value.handleShow()},Y=function(e,t,n,r){var o={};return o.attribute=(0,h.Lj)(S.fields,r.type),(0,h.AG)({nodeList:e,node:t,nextData:n,type:r.type,genNodeParams:{data:o}})},H=function(e){var t,n="".concat(e.type,"-custom"),r=(null===(t=e.children)||void 0===t?void 0:t.length)+1,o=e.type===f.A.CONDITION?b.Ay.t("view.designer.workFlow.branch"):b.Ay.t("view.designer.workFlow.parallel");return(0,h.AG)({nodeList:[],node:e,nextData:{targetId:e.targetId},type:n,genNodeParams:{title:"".concat(o).concat(b.Ay.t("view.designer.workFlow.branch")).concat(r),placeholder:b.Ay.t("view.designer.workFlow.setCondition"),content:"",type:n,nodeList:[l.A.generateNewNode(f.A.PROCEDD_NODE,{data:{attribute:(0,h.Lj)(S.fields,f.A.PROCEDD_NODE),name:""}})]}})},ee=function(){var e=(0,i.A)((0,a.A)().mark((function e(){var t,n,r,o,i,c,s;return(0,a.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return $(),e.prev=1,e.next=4,te();case 4:return e.next=6,k.getProcess({appId:(0,u.unref)(Q),formTemplateId:(0,u.unref)(R)});case 6:if(n=e.sent,n.success){e.next=9;break}return e.abrupt("return",v.Ay.toast(n.message,"error"));case 9:S.isEdit=!(null===(t=n.data)||void 0===t||!t.bizId),r=n.data||{},o=r.attribute,i=r.name,c=JSON.parse(o||"{}"),S.antvJson=c,T&&(T.$parent.formName=i||c.data.name),s=(0,C.i5)(c.cells,S.fields),S.initJson=JSON.stringify(s),S.flowProcessList=s,O(s),j(),e.next=25;break;case 21:e.prev=21,e.t0=e["catch"](1),console.error("workflow initialize error: ",e.t0),j();case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(){return e.apply(this,arguments)}}(),te=function(){return x.QA({templateBizId:R.value}).then((function(e){var t=e.success,n=e.data;t&&(S.fields=(0,A.i)(n.paasFormFieldVOList),S.templateName=n.templateName)}))["catch"]((function(e){return console.log(e)}))},ne=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return new Promise((function(t,n){var r=(0,y.A)(e,S.fields);r.length>0&&((0,g.Notification)({type:"error",title:b.Ay.t("view.designer.workFlow.tip35"),duration:0,customClass:"base-notification",message:function(e){var t=r.map((function(t){var n=t.message.map((function(t){return e("p",["- ",t])}));return n.unshift(e("h3",[t.title])),n}));return e("div",[t])}(T.$createElement)}),n(!1)),t(!0)}))},re=function(e){return k.deploymentProcess(e).then((function(e){var t=e.success;t&&(S.initJson=JSON.stringify(I.value)),(0,g.Notification)({type:t?"success":"error",title:t?b.Ay.t("common.base.saveSuccess"):b.Ay.t("common.base.saveFail"),message:!t&&e.message})}))["finally"]((function(){K()}))["catch"]((function(e){console.error("err",e)}))},oe=function(e){return k.reDeploymentProcess(e).then((function(e){var t=e.success;t&&(S.initJson=JSON.stringify(I.value)),(0,g.Notification)({type:t?"success":"error",title:t?b.Ay.t("common.base.tip.edit2Success"):b.Ay.t("common.base.tip.editFail"),message:!t&&e.message})}))["finally"]((function(){K()}))["catch"]((function(e){console.error("err",e)}))},ae=function(){var e=(0,i.A)((0,a.A)().mark((function e(){var t,n,r;return(0,a.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=(0,C.Gm)(I.value),e.next=3,ne(t);case 3:if(V(),S.antvJson.cells=t,n=C.Xo.call(S,!1,S.antvJson,S.templateName),r={appId:Q.value,formTemplateId:R.value,antvJson:n,newCanvas:!0},!S.isEdit){e.next=9;break}return e.abrupt("return",oe(r));case 9:return e.next=11,re(r);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(){return JSON.stringify(I.value)!=S.initJson};return ee(),(0,u.provide)("flowData",S),(0,u.provide)("mode",n),r({save:ae,checkModified:ie}),function(){var e=(0,u.unref)(q);return(0,u.h)("div",o()([{class:"workflow-wrap"},{directives:[{name:"loading",value:(0,u.unref)(e)}]}]),[!e&&(0,u.h)("div",o()([{class:"workflow-wrap-main__v2"},{directives:[{name:"loading",value:G.value,modifiers:{lock:!0}}]}]),[(0,u.h)("flow-process-chart",{attrs:{data:I.value,beforeDeleteNode:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return U(t)},nodeTypeList:X.value,customCreateNode:Y,customCreateConditionBranchNode:H,vertical:!0},on:{"node-content-click":W}})]),(0,u.h)("node-config-panel-main",{attrs:{node:D.value,isSpecialNode:Z.value},ref:L})])}}})}}]);