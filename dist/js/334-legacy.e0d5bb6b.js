"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[334],{7953:function(t,e,i){i.r(e),i.d(e,{default:function(){return f}});var s=function(){var t=this,e=t._self._c;return e("base-modal",{staticClass:"choose-user-modal",attrs:{title:t.$t("view.designer.workFlow.selectSth",{sth:t.nodeCandidateTitle}),show:t.visible,width:"640px"},on:{"update:show":function(e){t.visible=e}}},[t.visible?e("config-contact",{ref:"configContact",attrs:{title:t.nodeCandidateTitle,fields:t.flowData.fields,value:t.candidates,"flow-api":t.flowApi,"show-tab-setting":t.showTabSetting,"show-dynamic":t.showTabSetting.showDynamic,"disable-values":t.disableValues,"is-dynamic-flow-design-choose":!1}}):t._e(),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.visible=!1}}},[t._v(t._s(t.$t("common.base.cancel")))]),e("el-button",{attrs:{type:"primary"},on:{click:t.chooseUser}},[t._v(t._s(t.$t("common.base.makeSure")))])],1)],1)},o=[],n=i(71357),a=i(62361),l=(i(35256),i(16961),i(32807),i(75069),i(32914)),c=i(69396),d={name:"choose-user-modal",components:(0,a.A)({},l.A.name,l.A),inject:["flowData"],data:function(){return{visible:!1,nodeCandidateTitle:"",candidates:[],disableValues:[],showTabSetting:{}}},computed:{flowApi:function(){return(0,n.A)({},c)}},methods:{chooseUser:function(){var t=this.$refs.configContact.checked.map((function(t){var e=t.id,i=t.name,s=t.type,o=t.extend,n={id:e,name:i,type:s};return o&&(n.extend=o),n}));this.$emit("chooseUser",t),this.hideModal()},showModal:function(t,e,i){var s=this;this.nodeCandidateTitle=t,this.candidates=e,this.showTabSetting=i,this.$nextTick((function(){s.visible=!0}))},hideModal:function(){this.visible=!1}}},h=d,r=i(49100),u=(0,r.A)(h,s,o,!1,null,"2a5fe908",null),f=u.exports}}]);