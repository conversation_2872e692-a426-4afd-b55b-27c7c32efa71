"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[313],{15902:function(e,t,n){var o=n(37801),r=(n(33438),n(48649)),i=n(72964);t.A=(0,r.defineComponent)({props:{defaultDataZoomValue:{type:Number,default:50}},setup:function(e,t){var n=t.emit,a=(0,i.J0)(e.defaultDataZoomValue),u=(0,o.A)(a,2),s=u[0],l=u[1],c=function(e){var t=e>s.value;n("changeZoom",e,t),l(e)},d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"add";if("add"===e){var t=s.value+=10,n=t%10;0!==n&&(t+=n>=5?10-n:-n),l(t)}else{var o=s.value-=10,r=o%10;0!==r&&(o+=r>=5?10-r:-r),l(o)}},p=function(){n("scrollToContent")};return function(){return(0,r.h)("div",{class:"graph-warp-action__bar"},[(0,r.h)("div",{class:"action-slider-box"},[(0,r.h)("div",{class:"action-slider-operator action-slider-operator__left",on:{click:function(){return d("reduce")}}},[(0,r.h)("i",{class:"el-icon-minus"})]),(0,r.h)("el-slider",{class:"action-slider-content",attrs:{value:s.value,min:10,max:100,step:1,"input-size":"mini"},on:{input:c}}),(0,r.h)("div",{class:"action-slider-operator action-slider-operator__right",on:{click:function(){return d("add")}}},[(0,r.h)("i",{class:"el-icon-plus"})])]),(0,r.h)("div",{class:"action-slider-zoom__data"},["".concat(s.value,"%")]),(0,r.h)("div",{class:"action-operator-item"},[(0,r.h)("i",{class:"iconfont icon-dingwei",on:{click:p}})]),(0,r.h)("div",{class:"action-operator-item"},[(0,r.h)("el-popover",{attrs:{placement:"top",trigger:"hover","popper-class":"graph-minimap__popover"}},[(0,r.h)("i",{slot:"reference",class:"iconfont icon-daohang"}),(0,r.h)("div",{class:"graph-minimap-box"})])])])}}})},16113:function(e,t,n){n.d(t,{F3:function(){return c},SF:function(){return u},Un:function(){return i},Vm:function(){return s},Ye:function(){return d},Zb:function(){return a},Zo:function(){return l}});n(35256),n(75069);var o=n(51280),r=["top","right","bottom","left"],i=220,a=80,u=100,s=32,l="#067BEF",c="#8C8C8C",d="#F759AB";r.map((function(e){return{id:"select-port-".concat(e),group:"".concat(e,"Select"),attrs:{fo:{magnet:"true"}},markup:[o.VK.getForeignObjectMarkup()]}})),r.map((function(e,t){return"select-port-".concat(e)}))},35564:function(e,t,n){var o=n(71357),r=n(51280),i=n(80602);class a extends r.bP{}(function(e){function t(e){const t=[],n=r.VK.getForeignObjectMarkup();return e?t.push({tagName:e,selector:"body"},n):t.push(n),t}e.config({view:"vue-shape-view",markup:t(),attrs:{body:{fill:"none",stroke:"none",refWidth:"100%",refHeight:"100%"},fo:{refWidth:"100%",refHeight:"100%"}},propHooks(e){if(null==e.markup){const n=e.primer;if(n){e.markup=t(n);let o={};switch(n){case"circle":o={refCx:"50%",refCy:"50%",refR:"50%"};break;case"ellipse":o={refCx:"50%",refCy:"50%",refRx:"50%",refRy:"50%"};break;default:break}e.attrs=r.D5.merge({},{body:Object.assign({refWidth:null,refHeight:null},o)},e.attrs||{})}}return e}}),r.bP.registry.register("vue-shape",e,!0)})(a||(a={}));var u=n(48649),s=!0,l=!1,c=u["default"];u["default"].util.warn;function d(e,t){var n,o={},r={config:u["default"].config,use:u["default"].use.bind(u["default"]),mixin:u["default"].mixin.bind(u["default"]),component:u["default"].component.bind(u["default"]),provide:function(e,t){return o[e]=t,this},directive:function(e,t){return t?(u["default"].directive(e,t),r):u["default"].directive(e)},mount:function(r,i){return n||(n=new u["default"](Object.assign({propsData:t},e,{provide:Object.assign(o,e.provide)})),n.$mount(r,i),n)},unmount:function(){n&&(n.$destroy(),n=void 0)}};return r}function p(e){return{setup(){throw new Error("[vue-demi] "+e+" is not supported in Vue 2. It's provided to avoid compiler errors.")}}}var h=p("Teleport");var f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const v={};function m(e){const{shape:t,component:n,inherit:o}=e,i=f(e,["shape","component","inherit"]);if(!t)throw new Error("should specify shape in config");v[t]={component:n},r.ke.registerNode(t,Object.assign({inherit:o||"vue-shape"},i),!0)}let g=!1;const _=(0,u.reactive)({});function b(e,t,n,o,r){g&&(_[e]=(0,u.markRaw)((0,u.defineComponent)({render:()=>(0,u.h)(h,{to:n},[(0,u.h)(t,{node:o,graph:r})]),provide:()=>({getNode:()=>o,getGraph:()=>r})})))}function y(e){g&&delete _[e]}function A(){return g}class N extends r.Yv{getComponentContainer(){return this.selectors&&this.selectors.foContent}confirmUpdate(e){const t=super.confirmUpdate(e);return this.handleAction(t,N.action,(()=>{this.renderVueComponent()}))}targetId(){return`${this.graph.view.cid}:${this.cell.id}`}renderVueComponent(){this.unmountVueComponent();const e=this.getComponentContainer(),t=this.cell,n=this.graph;if(e){const{component:o}=v[t.shape];if(o)if(s){const r=c;this.vm=new r({el:e,render(e){return e(o,{node:t,graph:n})},provide(){return{getNode:()=>t,getGraph:()=>n}}})}else l&&(A()?b(this.targetId(),o,e,t,n):(this.vm=d({render(){return(0,u.h)(o,{node:t,graph:n})},provide(){return{getNode:()=>t,getGraph:()=>n}}}),this.vm.mount(e)))}}unmountVueComponent(){const e=this.getComponentContainer();return this.vm&&(s&&this.vm.$destroy(),l&&this.vm.unmount(),this.vm=null),e&&(e.innerHTML=""),e}onMouseDown(e,t,n){const o=e.target,r=o.tagName.toLowerCase();if("input"===r){const e=o.getAttribute("type");if(null==e||["text","password","number","email","search","tel","url"].includes(e))return}super.onMouseDown(e,t,n)}unmount(){return A()&&y(this.targetId()),this.unmountVueComponent(),super.unmount(),this}}(function(e){e.action="vue",e.config({bootstrap:[e.action],actions:{component:e.action}}),r.Yv.registry.register("vue-shape-view",e,!0)})(N||(N={}));var O=n(37801),k=(n(67880),n(44807),n(80793),n(16961),n(7354),n(75069),n(21633),n(57309)),D=n(67142),F=n(72964),C=n(14389),E=n(92935),w=n.n(E),x=(0,u.defineComponent)({name:"GraphNormalNode",setup:function(){var e=(0,u.inject)("getGraph"),t=(0,u.inject)("getNode"),n=(0,u.ref)(""),o=(0,u.ref)(""),r=(0,F.J0)(null),a=(0,O.A)(r,2),s=a[0],l=a[1],c=(0,u.computed)((function(){return t()})),d=(0,u.computed)((function(){return e()})),p=(0,u.computed)((function(){var e;return c.value.shape===i.A.START_NODE||(null===(e=c.value.getData())||void 0===e?void 0:e.originShape)===i.A.START_NODE})),h=(0,u.computed)((function(){var e,t=(null===(e=c.value.getData())||void 0===e?void 0:e.originShape)||c.value.shape;return k.dq[0].list.find((function(e){return t.includes(e.type)}))})),f=(0,u.computed)((function(){var e;return(null===(e=s.value)||void 0===e?void 0:e.id)===c.value.id})),v=(0,u.computed)((function(){return c.value.shape!==i.A.CONVERGE_NODE})),m=(0,u.computed)((function(){return C.A.getters.flowDesignNodeCardStyle})),g=(0,u.computed)((function(){return[i.A.Done_Node,i.A.Doing_Node,i.A.UnDone_Node].includes(c.value.shape)})),_=function(){var e=(0,D.m9)(c.value.shape)||"",t=(0,D.FW)(c.value,[])||"";return t?"".concat(t&&t.includes(e)?"":"".concat(e,"：")).concat(t):""};return(0,u.onMounted)((function(){n.value=c.value.getAttrByPath("text/textWrap/text"),o.value=_(),c.value.on("change:data",(function(e){e.current;o.value=_()})),c.value.on("change:attrs",(function(e){var t=e.current;n.value=t.text.textWrap.text})),d.value.on("node:selected",(function(e){var t=e.node;l(t)})),d.value.on("node:unselected",(function(){l(null)}))})),function(){var e,t,r;return m.value===i.A.NORMAL_NODE_CARD?(0,u.h)("div",{class:["graph-normal-node",p.value?"graph-normal-node__start":null,f.value?"graph-normal-node__active":null,g.value?"graph-normal-node__view":null,"graph-normal-node__".concat(c.value.shape)]},[(0,u.h)("span",{class:"txt"},[" ",n.value])]):(0,u.h)("div",{class:["graph-normal-node__custom",f.value?"graph-normal-node__custom-active":null,"graph-normal-node__custom-".concat(c.value.shape)]},[(0,u.h)("div",{class:"graph-normal-node__custom-top"},[(0,u.h)("i",{class:p.value?"iconfont icon-faqi":null===(e=h.value)||void 0===e?void 0:e.icon,style:{color:p.value?"#51B1FF":null===(t=h.value)||void 0===t?void 0:t.color}}),(0,u.h)("span",[n.value])]),(0,u.h)("div",{class:"graph-normal-node__custom-content"},[(0,u.h)("span",{class:["node-content-text",w().isEmpty(o.value)?"placeholder":null]},[o.value.length||!v.value?o.value:"暂未设置".concat((0,D.m9)((null===(r=c.value.getData())||void 0===r?void 0:r.originShape)||c.value.shape))]),v.value&&o.value.length&&!g.value?(0,u.h)("i",{class:"el-icon-arrow-right"}):null])])}}}),P=(0,u.defineComponent)({name:"DndGroupItemNode",setup:function(){(0,u.inject)("getGraph");var e=(0,u.inject)("getNode"),t=(0,u.computed)((function(){return e()})),n=(0,u.computed)((function(){return t.value.shape})),o=(0,u.computed)((function(){return k.dq[0].list.find((function(e){return n.value.includes(e.type)}))})),r=(0,u.computed)((function(){return t.value.getAttrByPath("text/textWrap/text")}));return(0,u.onMounted)((function(){var t=e();t.on("change:data",(function(e){e.current}))})),function(){return(0,u.h)("div",{class:"graph-dnd-group__node"},[(0,u.h)("i",{class:o.value.icon}),(0,u.h)("span",[r.value])])}}}),R=n(16113),W=(r.ke.registerNode("node-flow",{inherit:"rect",width:100,height:32,attrs:{body:{stroke:"#D8D8D8",strokeWidth:1,fill:"#FFFFFF",rx:2,ry:2},fo:{refWidth:"100%",refHeight:"100%"},foBody:{xmlns:r.Jf.ns.xhtml,style:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"}},text:{fontSize:12,fill:"#595959",textWrap:{text:"",width:-10,ellipsis:!0,breakWord:!0}}},markup:[{tagName:"rect",selector:"body"},{tagName:"text",selector:"text"},{tagName:"foreignObject",selector:"fo",children:[{ns:r.Jf.ns.xhtml,tagName:"body",selector:"foBody",children:[{tagName:"div",selector:"edit-text"}]}]}],ports:{groups:{top:{position:"top",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},right:{position:"right",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},bottom:{position:"bottom",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},left:{position:"left",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}}},items:[{group:"top",id:"top"},{group:"right",id:"right"},{group:"bottom",id:"bottom"},{group:"left",id:"left"}]}}),function(e){r.ke.registerNode(i.A.END_NODE,{width:e.width,height:e.height,attrs:{body:{stroke:"#CBD6E2",fill:"#F5F8FA"}},inherit:"node-flow"})}),j=function(){var e={inherit:"vue-shape",width:R.Un,height:R.Zb,ports:{groups:{top:{position:"top",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},right:{position:"right",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},bottom:{position:"bottom",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},left:{position:"left",attrs:{circle:{r:3,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}}},items:[{group:"top",id:"top"},{group:"right",id:"right"},{group:"bottom",id:"bottom"},{group:"left",id:"left"}]},component:{components:{NormalNode:x},render:function(e){return e(x)}}};m((0,o.A)({shape:i.A.START_NODE},e)),m((0,o.A)({shape:i.A.PROCEDD_NODE},e)),m((0,o.A)({shape:i.A.APPROVE_NODE},e)),m((0,o.A)({shape:i.A.CONVERGE_NODE},e)),m((0,o.A)({shape:i.A.CARBON_COPY_NODE},e)),m((0,o.A)({shape:i.A.Done_Node},e)),m((0,o.A)({shape:i.A.Doing_Node},e)),m((0,o.A)({shape:i.A.UnDone_Node},e)),W({width:150,height:40})},S=function(){var e={inherit:"vue-shape",width:110,height:32,component:{components:{DndGroupItemNode:P},render:function(e){return e(P)}}};m((0,o.A)({shape:i.A.APPROVE_NODE_GROUP_ITEM_SHAPE},e)),m((0,o.A)({shape:i.A.PROCEDD_NODE_GROUP_ITEM_SHAPE},e)),m((0,o.A)({shape:i.A.CARBON_COPY_NODE_GROUP_ITEM_SHAPE},e)),m((0,o.A)({shape:i.A.CONVERGE_NODE_GROUP_ITEM_SHAPE},e))};S(),j()}}]);