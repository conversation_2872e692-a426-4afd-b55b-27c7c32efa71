"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[452],{70452:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});a(2286),a(80793),a(7509),a(16961),a(54615),a(19944),a(75069);var o=function(){var e,t=this,a=t._self._c;t._self._setupProxy;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:!t.isInlineSelf&&t.loading,expression:"!isInlineSelf && loading",modifiers:{fullscreen:!0,lock:!0}},{name:"loading",rawName:"v-loading.lock",value:t.isInlineSelf&&t.loading,expression:"isInlineSelf && loading",modifiers:{lock:!0}}],staticClass:"template-detail-view"},[a("div",{staticClass:"template-detail-header"},[a("div",{staticClass:"template-detail-header-title"},[t.isDelete?a("div",{staticClass:"template-delete-status"},[t._v(t._s(t.$t("common.base.deleted")))]):t._e(),t.templateName?a("el-popover",{attrs:{disabled:!t.isParallelNode},model:{value:t.popoverVisible,callback:function(e){t.popoverVisible=e},expression:"popoverVisible"}},[a("div",{staticClass:"font-title mr_12",attrs:{slot:"reference"},slot:"reference"},[t._v(" "+t._s(t.templateName)+" "),t.currFlow.nodeName?a("span",[t._v(" - "+t._s(t.currFlow.nodeName)+" "),t.isParallelNode?a("i",{staticClass:"iconfont icon-fdn-select"}):t._e()]):t._e()]),a("ul",{staticClass:"flow-nodes-menu"},t._l(t.currLogList,(function(e,o){return a("li",{key:o,class:{selected:t.currFlow.nodeInstanceId==e.nodeInstanceId},on:{click:function(a){return t.updateFlowNode(e)}}},[t._v(" "+t._s(t.templateName)+" - "+t._s(e.nodeName)+" ")])})),0)]):t._e(),t.flowPermiss.customStatus||t.stateText?a("div",{staticClass:"flow-state",style:{"background-color":t.stateColor.bgColor,"border-color":t.stateColor.bgColor,color:t.stateColor.color}},[t._v(" "+t._s(t.flowPermiss.customStatus||t.stateText)+" ")]):t._e(),t.statePaused?a("div",{staticClass:"flow-state ml_12",style:{"background-color":"#F56C6C","border-color":"#F56C6C",color:"#fff"}},[t._v(" "+t._s(t.$t("buttons.pause"))+t._s(t.$t("formType.middle"))+" ")]):t._e(),a("IntelligentTagsTaggingView",t._b({},"IntelligentTagsTaggingView",t.tagsSingleComponentAttrs,!1)),t.$route.query.JumpKey&&t.listPageIsTableStyle?a("div",{staticClass:"template-detail-tab"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.$t("common.base.lastOne"),placement:"top"}},[a("div",{staticClass:"tab tab-left",on:{click:function(e){return t.changePage("last")}}},[a("i",{staticClass:"iconfont icon-left"})])]),a("div",{staticClass:"border-mid"}),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.$t("common.base.nextOne"),placement:"top"}},[a("div",{staticClass:"tab tab-right",on:{click:function(e){return t.changePage("next")}}},[a("i",{staticClass:"iconfont icon-right"})])])],1):t._e()],1),t.isDelete?t._e():a("div",{staticClass:"template-detail-header-btn"},[t.init&&t.flowPermiss.buttons?[t._l(t.flowPermiss.buttons.filter((function(e){return[1,6].includes(e.code)})),(function(e){return a("el-button",{key:e.code,attrs:{type:e.theme,disabled:t.pending||t.checkedSomeButtonIsDisabled(e)},on:{click:function(a){return t.submit(e)}}},[t._v(" "+t._s(e.cnName)+" ")])})),t.loadingRefuse?t._l(t.flowPermiss.buttons.filter((function(e){return[4,7].includes(e.code)})),(function(e){return a("el-button",{key:e.code,attrs:{type:e.theme,disabled:t.pending||t.checkedSomeButtonIsDisabled(e)},on:{click:function(a){return t.openBackNode(e)}}},[t._v(" "+t._s(e.cnName)+" ")])})):t._e()]:t._e(),t.notShowMoreBaseButton?t._e():a("el-dropdown",{staticClass:"detail-more-dropdown",attrs:{trigger:"click"}},[a("el-button",{attrs:{type:"plain-third"}},[t._v(" "+t._s(t.$t("common.base.more"))+" "),a("i",{staticClass:"iconfont icon-fdn-select"})]),a("el-dropdown-menu",{staticClass:"detail-more-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[t._l(t.filteredButtons,(function(e,o){return a("el-dropdown-item",["custom"!==e.codeStr?a("div",{key:e.code,attrs:{disabled:t.pending||t.checkedSomeButtonIsDisabled(e)},on:{click:function(a){return t.submit(e)}}},[t._v(" "+t._s(e.cnName)+" ")]):a("div",{key:o,on:{click:function(a){return t.hanldeBtnList(e)}}},[t._v(" "+t._s(e.cnName)+" ")])])})),a("el-dropdown-item",["{}"!==JSON.stringify(t.authData)&&t.isShowEdit?a("div",{directives:[{name:"auth",rawName:"v-auth",value:t.authData.editAuth,expression:"authData.editAuth"}],attrs:{disabled:t.pending},on:{click:t.handleToEdit}},[t._v(t._s(t.$t("common.base.edit")))]):t._e()]),a("el-dropdown-item",["{}"===JSON.stringify(t.authData)||!t.isFormCreator||t.isSettlementForm||t.isSettlementChangeForm?t._e():a("div",{attrs:{disabled:t.pending},on:{click:t.goCopyForm}},[t._v(t._s(t.$t("common.base.copy")))])]),a("el-dropdown-item",[t.satisfactionList.length?a("div",{on:{click:function(e){return t.openDialog("satisfaction")}}},[t._v(t._s(t.$t("view.template.detail.sendSatisfactionQuestionnaire")))]):t._e()]),a("el-dropdown-item",[t.shareInfo.isOpen?a("div",{on:{click:function(e){return t.openDialog("share")}}},[t._v(t._s(t.$t("common.base.share")))]):t._e()])],2)],1),t.showPrintTemplateBtn?[t.printTemplateAllList.length?[a("el-dropdown",{attrs:{trigger:"click"}},[a("el-button",{staticClass:"print-btn",attrs:{type:"plain-third",icon:"iconfont icon-printer"}},[t._v(t._s(t.$t("common.base.print")))]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.printTemplateAllList,(function(e){return a("el-dropdown-item",{key:e.id},[a("el-button",{staticClass:"print-btn2",attrs:{loading:t.printBtnState,type:"text"},on:{click:function(a){return t.handlePrint(e)}}},[t._v(t._s(e.templateName))])],1)})),1)],1)]:t._e()]:t._e()],2)]),a("div",{class:["template-detail-main",t.formMultiRowGrayOpened?"template-detail-main__multi":null]},[a("base-collapse",{key:t.baseCollapseComponentKey,ref:"BaseCollapseComponent",class:{active:!t.collapseDirection},attrs:{"show-collapse":t.tabs.length>0&&2===t.baseLayout,resize:t.tabs.length>0&&2===t.baseLayout,direction:t.collapseDirection,"left-size":2,"min-scroll-width":320},on:{"update:direction":function(e){t.collapseDirection=e}}},[t.init?a("div",{staticClass:"template-detail-main__left",attrs:{slot:"left"},slot:"left"},[t.formMultiRowGrayOpened?a("title-layout-tab-bar",{attrs:{"bar-list":t.layoutTabBarList,"now-item":t.oneLayoutActiveTab.tabName||t.oneLayoutActiveTab.key},on:{changeItem:t.tabBarChangeItem,openLayoutModal:t.openBaseLayoutModal}}):t._e(),a("keep-alive",[a("div",{staticClass:"left-content"},[a("detail-form",t._g(t._b({directives:[{name:"show",rawName:"v-show",value:"detail"===t.oneLayoutActiveTab.key,expression:"oneLayoutActiveTab.key === 'detail'"}],ref:"detailFrom"},"detail-form",t.getOneLayoutComponentAttrForForm.props,!1),t.getOneLayoutComponentAttrForForm.on)),"detail"!==t.oneLayoutActiveTab.key?a(t.oneLayoutActiveTab.component,t._g(t._b({directives:[{name:"show",rawName:"v-show",value:!t.oneLayoutActiveTab.isAdditionTab||t.oneLayoutActiveTab.key===t.rightContentTabName,expression:"(!oneLayoutActiveTab.isAdditionTab || oneLayoutActiveTab.key === rightContentTabName)"}],key:t.oneLayoutActiveTab.key,ref:"detail"===t.oneLayoutActiveTab.key?"detailFrom":null,tag:"component"},"component",t.getOneLayoutComponentAttr.props,!1),t.getOneLayoutComponentAttr.on)):t._e(),a("GroupPosition",t._b({directives:[{name:"show",rawName:"v-show",value:"detail"===t.oneLayoutActiveTab.key,expression:"oneLayoutActiveTab.key === 'detail'"}],class:"detail"===t.oneLayoutActiveTab.key&&"group-position-active",attrs:{formRef:null===(e=t.$refs)||void 0===e?void 0:e.detailFrom}},"GroupPosition",t.getOneLayoutComponentAttrForForm.props,!1))],1)]),t.firstLogItemData.isEndNode||t.firstLogItemData.isStartNode||"flow-record"!==t.oneLayoutActiveTab.key?t._e():a("div",{staticClass:"customer-quick-comment"},[a("biz-comment",{ref:"comment",attrs:{placeholder:t.$t("view.template.detail.placeholder1"),"show-customer-action":!t.isExternalCreate,"has-customer-field":t.hasCustomerField,disabled:t.commentPending,autofocus:"","user-search-url":"/message/user/lists","search-params":{seeAllOrg:!0}},on:{submit:t.createRemark}})],1)],1):t._e(),a("div",{staticClass:"template-detail-main__right",attrs:{slot:"right"},slot:"right"},[a("el-tabs",{directives:[{name:"show",rawName:"v-show",value:"right"!=t.collapseDirection,expression:"collapseDirection != 'right'"}],class:["detail-tabs"],model:{value:t.rightContentTabName,callback:function(e){t.rightContentTabName=e},expression:"rightContentTabName"}},t._l(t.tabs,(function(e){return a("el-tab-pane",{key:e.key,attrs:{label:e.name,name:e.key}},[e.isAdditionTab&&e.key!==t.rightContentTabName?t._e():a(e.component,{ref:e.component,refInFor:!0,tag:"component",attrs:{"log-list":t.logList,"curr-log-list":t.currLogList,"form-name":t.templateName,"paas-form-value-list":t.paasFormValueList,"template-id":t.templateId,"process-id":t.processId,displayNameLanguageArr:t.displayNameLanguageArr,"satisfaction-data":t.satisfactionData,module:"paas",moduleSourceId:t.formContentId,"extra-data":e.extraData,cards:e.isAdditionTab?t.additionCardList:t.cardList,"serial-number-for-connector":t.serialNumberForConnector,"node-template-id-for-connector":t.nodeTemplateIdForConnector,"node-instance-id-for-connector":t.nodeInstanceIdForConnector,"process-id-for-connector":t.processIdForConnector,"form-content-id":t.formContentId,"project-task":t.projectTask},on:{updateFlowNode:t.updateFlowNode,fetchProcessLog:t.fetchProcessLog}})],1)})),1),a("div",{directives:[{name:"show",rawName:"v-show",value:"right"==t.collapseDirection,expression:"collapseDirection == 'right'"}],staticClass:"collapse-right",on:{click:t.expand}},[t._v(t._s(t.tabs[0]&&t.tabs[0].name))]),t.firstLogItemData.isEndNode||t.firstLogItemData.isStartNode||"flow-record"!==t.rightContentTabName?t._e():a("div",{staticClass:"customer-quick-comment"},[a("biz-comment",{directives:[{name:"show",rawName:"v-show",value:"right"!=t.collapseDirection,expression:"collapseDirection != 'right'"}],ref:"comment",attrs:{placeholder:t.$t("view.template.detail.placeholder1"),"show-customer-action":!t.isExternalCreate,"has-customer-field":t.hasCustomerField,disabled:t.commentPending,autofocus:"","user-search-url":"/message/user/lists","search-params":{seeAllOrg:!0}},on:{submit:t.createRemark}})],1)],1)])],1),a("tranfer-dialog",{ref:"transferDialog",attrs:{config:t.operaButton,"task-id":t.flowPermiss.currentaskId,"build-form-params":t.buildFormValueParams},on:{success:t.successCallbackFn}}),a("approva-dialog",{ref:"approvalDialog",attrs:{config:t.operaButton,"task-id":t.flowPermiss.currentaskId,"build-form-params":t.buildFormValueParams,"approve-sign-config":t.approveSignConfig,projectTaskId:t.projectTaskId},on:{success:t.successCallbackFn}}),a("share-link-dialog",{ref:"shareLinkDialog",attrs:{"share-data":t.shareInfo,"form-content-id":t.formContentId}}),a("satisfaction-preview-dialog",{ref:"satisfactionDialog",attrs:{"node-name":t.currentNodeLog.nodeName,"node-id":t.currentNodeLog.nodeInstanceId,"satisfaction-list":t.satisfactionList},on:{success:t.fetchProcessLog}}),t.backButton.length>0?a("back-node-dialog",{ref:"backNodeDialogRef",attrs:{backButton:t.backButton},on:{update:t.updateBtn}}):t._e(),a("on-tag-dialog",{ref:"onTagDialog",attrs:{"biz-ids":[t.formContentId],"tag-id-list":t.tagIdList,"template-biz-id":t.templateId,"app-id":t.appId},on:{saveOnTag:t.saveOnTag}}),a("biz-layout-modal",{ref:"bizLayoutModal",attrs:{"biz-layout-type":t.baseLayout,columns:t.formCellCount},on:{changeLayout:t.changeTaskDetailLayout}}),a("pause-dialog",{ref:"pauseDialog",attrs:{config:t.operaButton,"task-id":t.flowPermiss.currentaskId,"build-form-params":t.buildFormValueParams,"approve-sign-config":t.approveSignConfig},on:{success:t.successCallbackFn}})],1)},s=[],i=a(8265),n=i.A,r=n,l=a(49100),c=(0,l.A)(r,o,s,!1,null,"4fe262da",null),d=c.exports}}]);