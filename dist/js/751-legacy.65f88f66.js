"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[708,751],{19708:function(e,t,n){n.r(t),n.d(t,{default:function(){return ct}});var i=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{directives:[{name:"loading",rawName:"v-loading.lock",value:e.submitting,expression:"submitting",modifiers:{lock:!0}}],staticClass:"workflow-wrap"},[t("div",{staticClass:"workflow-wrap-main"},[t("div",{staticClass:"toolbar-box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.flowIsDesigning,expression:"flowIsDesigning"}],staticClass:"toolbar-box-acton"},[e.isReady?t("tool-bar",{attrs:{graph:e.graph},on:{setFinishNodeStatus:e.updateFinishNodeStatus}}):e._e(),t("div",{staticClass:"toolbar-stencil",attrs:{id:"stencil"}})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.flowIsDesigning,expression:"!flowIsDesigning"}],staticClass:"flow-version-tip"},[t("i",{staticClass:"el-icon-warning"}),t("span",[e._v("流程已发布，如需增删节点和连线，请"),t("em",{on:{click:e.handleCreateNewFlowVersion}},[e._v("添加新版本")])])]),t("div",{staticClass:"toolbar-box-right"},[t("GraphNodeActionBar",{on:{changeFlowNodeCardStyle:e.changeFlowNodeCardStyle}}),t("GraphVersionActionBar",{attrs:{"save-process":e.save},on:{handleVersionItemClick:e.handleVersionItemClick}})],1)]),t("div",{staticClass:"x6-graph",attrs:{id:"container"}})]),e.newPanel?[e.isReady?t("NodeConfigPanelMain",{ref:"nodeConfigPanelMainRef",attrs:{node:e.jsonCell,"is-special-node":e.isSpecialNode,drawer:!1},on:{updateVisible:e.handlePanelUpdateVisible}}):e._e()]:t("div",{class:["workflow-wrap-right",e.isLanguageIsZh?null:"workflow-wrap-right__multi",{active:e.showConfig}]},[e.isReady?t("config-panel",{attrs:{cell:e.cell}}):e._e()],1),t("div",{staticClass:"edge-tooltip-box"},[t("el-tooltip",{attrs:{value:e.isShowEdgeTooltip,"popper-class":"edge-tooltip",content:e.$t("view.designer.workFlow.clickSetCondition")}},[t("div")])],1),t("GraphWrapToolActionBar",{directives:[{name:"show",rawName:"v-show",value:e.isReady,expression:"isReady"}],on:{changeZoom:e.handleChangeGraphZoom,scrollToContent:e.handleGraphScrollToContent}})],2)},o=[],a=n(35730),s=n(18885),r=n(42881),l=n(62361),c=n(71357),d=(n(67880),n(2286),n(44807),n(62838),n(3923),n(80793),n(48152),n(35256),n(21484),n(8326),n(89716),n(76119),n(7509),n(16961),n(54615),n(7354),n(89370),n(32807),n(24929),n(19944),n(55650),n(75069),n(28244),n(62830),n(21633),n(69594),n(13262),n(68735),n(12096)),u=n(52275),h=n(26183),f=(n(27408),n(42925),n(79526),n(51280)),p=(n(35564),n(80602)),v=n(44309),m=n(61725),g=n(84859),w=n(64055),b=n(57309),A=n(67142),y=n(81616),C=n(16113),k=n(48649),N=n(92935),_=n.n(N),S=(0,k.defineComponent)({props:{styleText:{type:String,default:function(){return""}},isNativePaaS:{type:Boolean,default:function(){return!0}}},setup:function(e,t){var n=t.emit,i=(0,k.computed)((function(){var t=(0,N.clone)(b.dq[0].list);return t.splice(3,1),e.isNativePaaS&&t.splice(0,1),t})),o=function(e){n("addNode",e);var t=document.querySelector(".graph-add-node__popover-panel");t&&(t.style.cssText="left:0;top:0;display:none")};return function(){return(0,k.h)("div",{class:"graph-add-node__popover-panel",style:e.styleText},[(0,k.h)("h1",{class:"graph-add-node__popover-title"},["".concat(g.Ay.t("common.base.add")).concat(g.Ay.t("common.base.node"))]),(0,k.h)("ul",{class:"graph-add-node__popover"},[i.value.map((function(e){return(0,k.h)("li",{class:"graph-add-node__item",on:{click:function(){return o(e)}}},[(0,k.h)("div",{class:"graph-add-node__item-icon"},[(0,k.h)("i",{class:"iconfont ".concat(e.icon),style:{color:e.color}})]),(0,k.h)("div",{class:"graph-add-node__item-text"},[e.originName])])}))])])}}}),x=n(52257),T=n(87),E=n(70072),F=n(44377),D=n(19702),L=["sourceView","targetView","sourceMagnet","targetMagnet"],I=["data"],O=["node"],P=function(){function e(){(0,u.A)(this,e)}return(0,h.A)(e,null,[{key:"init",value:function(e){var t;this.vm=e;var n=null===(t=document.querySelector(".workflow-wrap-main"))||void 0===t?void 0:t.clientHeight,i=this;return this.graph=new f.ke({container:document.getElementById("container"),minimap:{enabled:!0,container:document.querySelector(".graph-minimap-box"),width:200,height:100,scalable:!1,padding:10,graphOptions:{async:!0}},background:{color:"#F0F2F4"},grid:{visible:!0,type:"fixedDot",size:10},scroller:{pageHeight:n?n-50:600,autoResize:!0,enabled:!0,pannable:!1,pageVisible:!1,autoResizeOptions:{border:100}},selecting:{enabled:!0,multiple:!0,rubberband:!0,movable:!0},history:{enabled:!0,beforeAddCommand:function(e,t){if(t.options.ignoreHistory||"tools"==t.key||"zIndex"==t.key)return!1}},connecting:{anchor:"center",connectionPoint:"anchor",allowBlank:!0,allowLoop:!1,highlight:!0,allowMulti:!1,snap:!0,createEdge:function(e){return new f.yp.oH({label:(0,c.A)((0,c.A)({},F.kW),{},{position:{distance:.5,options:{keepGradient:!0}}}),attrs:{line:{stroke:C.F3,strokeWidth:2,targetMarker:{name:"classic",size:8}}},router:{name:"manhattan",args:{padding:10}},tools:[{name:"vertices",args:{stopPropagation:!1}}],zIndex:-1})},validateConnection:function(e){var t=e.sourceView,n=e.targetView,i=e.sourceMagnet,o=e.targetMagnet;(0,d.A)(e,L);return(null===n||void 0===n?void 0:n.cell.shape)!=p.A.START_NODE&&((null===t||void 0===t?void 0:t.cell.shape)!=p.A.END_NODE&&!(!i||!o))},validateEdge:function(e){var t,n=e.edge;e.type,e.previous;if((null===(t=n.getSourceNode())||void 0===t?void 0:t.shape)===p.A.END_NODE)return!1;if(!n.getTargetNode()){i.vm.noTargetEdge=n;var o=document.querySelector(".x6-graph-scroller-content"),a=this.localToClient(n.getTargetPoint()),s=a.x,r=a.y,l=this.localToClient(n.getSourcePoint()),c=l.x,d=l.y,u=this.getScrollbarPosition(),h=u.left,f=u.top,v=h+s,m=f+r-96;if(c>s&&(v-=240),Math.abs(s-c)>50||Math.abs(r-d)>50){i.vm.popoverInstance&&(0,T.sT)(i.vm.popoverInstance);var g=(0,x.K)(),w=g.extendVue,b=w(S,{propsData:{styleText:"left:".concat(v,"px;top: ").concat(m,"px;display: block"),isNativePaaS:i.vm.isModuleForApproval}}),A=document.createElement("div");return o&&o.appendChild(A),b.$on("addNode",(function(e){i.vm.handleAddNode(e,(function(){(0,T.sT)(b),i.vm.popoverInstance=null}))})),i.vm.popoverInstance=b,b.$mount(A),!0}return!1}return!0}},interacting:function(){return!!e.flowIsDesigning||{nodeMovable:!1,edgeLabelMovable:!1,edgeMovable:!1}},translating:{restrict:-20},snapline:!0,clipboard:{enabled:!0},keyboard:{enabled:!0},onEdgeLabelRendered:function(e){var t=e.selectors,n=(e.label.background,t.foContent);if(n){var i=document.createElement("div");i.className="edge-label-box";var o=document.createElement("i");o.className="iconfont icon-filter",o.style.cssText="display:inline-block; color:#8C8C8C;",i.appendChild(o),i.style.width="100%",i.style.height="100%",i.style.textAlign="center",i.style.borderRadius="2px",i.style.background="#fff",i.style.display="none",n.appendChild(i)}},onPortRendered:function(e){var t=e.contentSelectors,n=t&&t.foContent;if(n){var i=e.port.position.name,o=document.createElement("div");o.className="flow-design-port no-base-tip",o.innerHTML="<span class='port-icon port-icon-".concat(i,'\'>\n                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_3830_18504"><rect x="3" y="3" width="10" height="10" rx="0"/></clipPath></defs><g><rect x="0" y="0" width="16" height="16" rx="8" fill="#B1B3B8" fill-opacity="1"/><g clip-path="url(#master_svg0_3830_18504)"><g><path d="M7.6875,7.6875C7.6875,7.6875,7.6875,4.25,7.6875,4.25C7.6875,4.15875,7.71677,4.083854,7.77531,4.0253125C7.83386,3.9667708,7.9087499999999995,3.9375,8,3.9375C8.091249999999999,3.9375,8.16614,3.9667708,8.224689999999999,4.0253125C8.28323,4.083854,8.3125,4.15875,8.3125,4.25C8.3125,4.25,8.3125,7.6875,8.3125,7.6875C8.3125,7.6875,11.75,7.6875,11.75,7.6875C11.841249999999999,7.6875,11.916129999999999,7.71677,11.97469,7.77531C12.03325,7.83386,12.0625,7.9087499999999995,12.0625,8C12.0625,8.091249999999999,12.03325,8.16614,11.97469,8.224689999999999C11.916129999999999,8.28323,11.841249999999999,8.3125,11.75,8.3125C11.75,8.3125,8.3125,8.3125,8.3125,8.3125C8.3125,8.3125,8.3125,11.75,8.3125,11.75C8.3125,11.841249999999999,8.28323,11.916129999999999,8.224689999999999,11.97469C8.16614,12.03325,8.091249999999999,12.0625,8,12.0625C7.9087499999999995,12.0625,7.83386,12.03325,7.77531,11.97469C7.71677,11.916129999999999,7.6875,11.841249999999999,7.6875,11.75C7.6875,11.75,7.6875,8.3125,7.6875,8.3125C7.6875,8.3125,4.25,8.3125,4.25,8.3125C4.15875,8.3125,4.083854,8.28323,4.0253125,8.224689999999999C3.9667708,8.16614,3.9375,8.091249999999999,3.9375,8C3.9375,7.9087499999999995,3.9667708,7.83386,4.0253125,7.77531C4.083854,7.71677,4.15875,7.6875,4.25,7.6875C4.25,7.6875,7.6875,7.6875,7.6875,7.6875C7.6875,7.6875,7.6875,7.6875,7.6875,7.6875Z" fill="#FFFFFF" fill-opacity="1"/></g></g></g></svg>\n                           </span>'),n.appendChild(o)}}}),this.initStencil(),this.initShape(),this.initEvent(),this.graph}},{key:"initStencil",value:function(){var e=this;this.stencil=new f.$J.zG({target:this.graph,stencilGraphWidth:800,collapsable:!1,groups:[{name:"basic",title:"",graphHeight:42,collapsable:!1,layoutOptions:{columns:5,columnWidth:110,rowHeight:30,dy:8}}],getDragNode:function(t){var n=new y.A({data:{attribute:{}}}),i=n.ports,o=t.clone({deep:!0}),a=e.vm.isNewNodeCardStyle,s=t.shape.replace("-group-item-shape","");return o.setProp("shape",s),o.setProp("type",s),o.setSize(a?C.Un:C.SF,a?C.Zb:C.Vm),o.setProp("ports",i),o}});var t=document.querySelector("#stencil");null===t||void 0===t||t.appendChild(this.stencil.container)}},{key:"initShape",value:function(){var e=new y.A({data:{attribute:{}}}),t=(e.ports,this.graph),n=t.createNode({shape:p.A.PROCEDD_NODE_GROUP_ITEM_SHAPE,attrs:{text:{textWrap:{text:g.Ay.t("view.designer.workFlow.nodeTypeEnum.flowNode")}}}}),i=t.createNode({shape:p.A.APPROVE_NODE_GROUP_ITEM_SHAPE,attrs:{text:{textWrap:{text:g.Ay.t("view.designer.workFlow.nodeTypeEnum.approveNode")}}}}),o=t.createNode({shape:p.A.CONVERGE_NODE_GROUP_ITEM_SHAPE,attrs:{text:{textWrap:{text:g.Ay.t("view.designer.workFlow.nodeTypeEnum.convergeNode")}}}}),a=t.createNode({shape:p.A.CARBON_COPY_NODE_GROUP_ITEM_SHAPE,attrs:{text:{textWrap:{text:g.Ay.t("view.designer.workFlow.nodeTypeEnum.carbonCopyNode")}}}}),s=[n,i,o];this.vm.mode===v.A.TASK?s.splice(0,1):s.push(a),this.stencil.load(s,"basic");var r=this;f.yp.rw.config({propHooks:{title:function(e){var t=e.data,n=(0,d.A)(e,I);if(t&&(f.D5.setByPath(n,"title",t.name),!Reflect.has((null===t||void 0===t?void 0:t.attribute)||{},"fieldAuthorityList"))){var i=(0,A.Lj)(r.vm.fields,n.shape);f.D5.setByPath(t,"attribute",i)}return(0,c.A)({data:t},n)}}})}},{key:"showPorts",value:function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"mouse",i=0,o=e.length;i<o;i+=1)"circle"===e[i].nodeName&&(e[i].style.visibility="select"===n?"hidden":t?"visible":"hidden"),"foreignObject"===e[i].nodeName&&"select"===n&&(e[i].querySelector(".flow-design-port").style.display=t?"block":"none")}},{key:"nodeSelectShowOrHidePort",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"select",i=document.querySelector("g[data-cell-id='".concat(e.id,"']")),o=i.querySelectorAll(".x6-port-body");this.showPorts(o,t,n)}},{key:"initEvent",value:function(){var e=this,t=this.graph,n=this.vm;document.getElementById("container");t.on("node:mouseenter",f.eY.debounce((function(t){var i=t.node;if(n.flowIsDesigning&&"#5F95FF"!==i.getAttrByPath("body/stroke")){var o=document.querySelector("g[data-cell-id='".concat(i.id,"']")),a=o.querySelectorAll(".x6-port-body");e.showPorts(a,!0)}})),500),t.on("node:mouseleave",(function(t){var i=t.node;t.view;if(n.flowIsDesigning&&"#5F95FF"!==i.getAttrByPath("body/stroke")){var o=document.querySelector("g[data-cell-id='".concat(i.id,"']")),a=o.querySelectorAll(".x6-port-body");e.showPorts(a,!1)}})),t.on("node:selected",(function(t){var i=t.node,o=((0,d.A)(t,O),e.graph.localToPage(i.position())),a=o.x,s=(o.y,document.body.clientWidth-500),r=n.isNewNodeCardStyle?220:100,l=a+r-s;l>0&&e.graph.scrollToPoint(i.position().x+80,i.position().y);var c=e.graph.getConnectedEdges(i,{outgoing:!0});c.forEach((function(e){e.setAttrs({line:{stroke:"#1890ff",strokeDasharray:5,targetMarker:"classic",style:{animation:"ant-line 30s infinite linear"}}})})),n.flowIsDesigning&&(i.shape,p.A.END_NODE,i.setAttrs({body:{stroke:(0,E.getThemeColor)(),strokeWidth:2}},{ignoreHistory:!0}),e.nodeSelectShowOrHidePort(i,!0))})),t.on("node:unselected",(function(t){var n=t.node,i=e.graph.getConnectedEdges(n,{outgoing:!0});i.forEach((function(e){var t,n={strokeWidth:2,strokeDasharray:0},i=null!==(t=e.data)&&void 0!==t&&t.isCondition?C.Ye:C.F3;e.setAttrs({line:(0,c.A)((0,c.A)({},n),{},{stroke:i})},{ignoreHistory:!0})})),n.shape===p.A.END_NODE?n.setAttrs({body:{stroke:"#D8D8D8",strokeWidth:1}},{ignoreHistory:!0}):n.setAttrs({body:{fill:"#FFFFFF",stroke:"#D8D8D8",strokeWidth:1}},{ignoreHistory:!0}),e.nodeSelectShowOrHidePort(n,!1)})),t.on("edge:mouseenter",(function(e){var t=e.e,i=e.cell,o=e.view;if(i.setAttrs({line:{stroke:C.Zo,strokeWidth:2}},{ignoreHistory:!0}),n.flowIsDesigning){var a=o.containers.labels.querySelector(".edge-label-box");a&&(a.style.display="block");var s=document.querySelector(".edge-tooltip-box"),r=s.style;r.left="".concat(t.clientX,"px"),r.top="".concat(t.clientY-48,"px"),(0,m.Tn)(n.$nextTick)&&(n.setTimeoutInc&&clearTimeout(n.setTimeoutInc),n.setTimeoutInc=setTimeout((function(){return n.$nextTick((function(){return n.isShowEdgeTooltip=!0}))}),200)),i.addTools([{name:"vertices",args:{stopPropagation:!1,removable:!1}},{name:"target-arrowhead",args:{attrs:{d:"M -6 -4 6 0 -6 4 Z",fill:C.Zo,"stroke-width":0,zIndex:1}}}])}})),t.on("edge:mouseleave",(function(t){t.e;var i,o=t.cell,a=t.view;if(o.removeTools(),(null===(i=e.selectEdge)||void 0===i?void 0:i.id)!==o.id){if(5!==o.attrs.line.strokeDasharray){var s,r=null!==(s=o.data)&&void 0!==s&&s.isCondition?C.Ye:C.F3;o.setAttrs({line:{stroke:r,strokeWidth:2}},{ignoreHistory:!0})}var l=a.containers.labels.querySelector(".edge-label-box");l&&(l.style.display="none")}var c=document.querySelector(".edge-tooltip-box"),d=null===c||void 0===c?void 0:c.style;d&&(d.left="-1000px",d.top="-1000px",(0,m.Tn)(n.$nextTick)&&(n.setTimeoutInc&&clearTimeout(n.setTimeoutInc),n.$nextTick((function(){return n.isShowEdgeTooltip=!1}))))})),t.on("edge:selected",(function(t){var n=t.edge;n.setAttrs({line:{stroke:C.Zo,strokeWidth:2}},{ignoreHistory:!0}),e.selectEdge=n,n.data||(n.data={})})),t.on("edge:unselected",(function(t){var n=t.edge,i=(t.options,(0,D.D)((null===n||void 0===n?void 0:n.data)||{})?C.Ye:C.F3);n.setAttrs({line:{stroke:i,strokeWidth:2}},{ignoreHistory:!0}),document.querySelectorAll(".x6-edge").forEach((function(t){var n;if(t.getAttribute("data-cell-id")===(null===(n=e.selectEdge)||void 0===n?void 0:n.id)){var i=t.querySelector(".edge-label-box");i&&(i.style.display="none")}})),e.selectEdge=null})),t.on("node:added",(function(e){var t=e.node,n=b.oO[t.shape],i=(0,w.jP)(n),o=t.getAttrByPath("text/textWrap/text");t.data={name:o,attribute:{nameLanguage:i}}})),t.on("edge:removed",(function(t){t.edge;e.vm.popoverInstance&&((0,T.sT)(e.vm.popoverInstance),e.vm.popoverInstance=null)}))}}])}();(0,l.A)(P,"graph",void 0),(0,l.A)(P,"stencil",void 0),(0,l.A)(P,"selectEdge",void 0),(0,l.A)(P,"scrollContentRect",void 0),(0,l.A)(P,"vm",void 0);var M=function(){var e=this,t=e._self._c;return t("div",{staticClass:"toolbar"},[t("div",{staticClass:"toolbar-group"},[t("div",{class:["toolbar-item",{disabled:!e.canUndo}],on:{click:e.undo}},[t("i",{staticClass:"iconfont icon-chexiao"})]),t("div",{class:["toolbar-item",{disabled:!e.canRedo}],on:{click:e.redo}},[t("i",{staticClass:"iconfont icon-chongzuo"})])]),t("div",{staticClass:"toolbar-group toolbar-group__delete"},[t("div",{class:["toolbar-item",e.isShowDelete?null:"toolbar-item-disabled"],on:{click:e.remove}},[t("i",{staticClass:"iconfont icon-delete"})])])])},V=[],$=(n(46622),n(50651)),R={name:"tool-bar",inject:["flowData"],props:["graph"],data:function(){return{canUndo:!1,canRedo:!1,canRemove:!1}},computed:(0,c.A)((0,c.A)({},(0,$.L8)(["flowIsDesigning"])),{},{isShowDelete:function(){if(!this.flowData.cell.shape)return!1;var e=[p.A.START_NODE,p.A.END_NODE];return!e.includes(this.flowData.cell.shape)}}),mounted:function(){var e=this,t=this.graph.history;t.on("change",(function(){e.canUndo=t.canUndo(),e.canRedo=t.canRedo()})),this.graph.bindKey(["meta+z","ctrl+z"],(function(){return t.canUndo()&&t.undo(),!1})),this.graph.bindKey(["meta+shift+z","ctrl+y"],(function(){return t.canRedo()&&t.redo(),!1})),this.graph.bindKey("backspace",(function(t){e.flowIsDesigning&&(t.preventDefault(),e.remove())}))},methods:{undo:function(){this.graph.history.undo(),this.$emit("setFinishNodeStatus")},redo:function(){this.graph.history.redo(),this.$emit("setFinishNodeStatus")},remove:function(){var e=this;return(0,r.A)((0,s.A)().mark((function t(){var n,i,o,a;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isShowDelete){t.next=2;break}return t.abrupt("return");case 2:if(n=e.graph.getSelectedCells(),n.length){t.next=5;break}return t.abrupt("return");case 5:return t.next=7,e.$platform.confirm(e.$t("view.designer.workFlow.tip1"));case 7:if(i=t.sent,i){t.next=10;break}return t.abrupt("return");case 10:if(o=[p.A.START_NODE,p.A.END_NODE],a=n.some((function(e){return o.indexOf(e.shape)>-1})),!a){t.next=14;break}return t.abrupt("return");case 14:n.length&&(e.graph.unselect(n),e.graph.removeCells(n)),e.$emit("setFinishNodeStatus");case 16:case"end":return t.stop()}}),t)})))()}}},B=R,z=n(49100),J=(0,z.A)(B,M,V,!1,null,"4f14d469",null),G=J.exports,H=function(){var e=this,t=e._self._c;return t("div",{class:["config-panel",e.isLanguageIsZh?null:"config-panel__multi"]},[e.isNode?t("config-node"):e._e(),e.isEdge?t("config-edge"):e._e()],1)},j=[],U=function(){var e=this,t=e._self._c;return t("div",{staticClass:"config-node-panel"},[t("div",{staticClass:"setting-toggle"},e._l(e.filterTabs,(function(n){return t("div",{key:n.value,class:["setting-toggle-item",{active:e.currTab==n.value}],on:{click:function(t){return e.selectTab(n)}}},[e._v(" "+e._s(n.name)+" ")])})),0),t(e.currTab,{tag:"component"})],1)},q=[],W=(n(13560),function(){var e=this,t=e._self._c;return t("div",{staticClass:"basic-setting-panel"},[t("div",{staticClass:"workflow-setting-item"},[t("h4",{staticClass:"workflow-item-title"},[e._v(e._s(e.$t("view.template.detail.nodeName")))]),t("div",{staticClass:"workflow-item-box"},[t("el-input",{directives:[{name:"input-filter",rawName:"v-input-filter:special-letter",value:e.setting.name,expression:"setting.name",arg:"special-letter"}],attrs:{maxlength:"20",placeholder:e.$t("common.placeholder.inputSomething",{data1:e.$t("view.template.detail.nodeName")})},on:{blur:e.updateName},model:{value:e.setting.name,callback:function(t){e.$set(e.setting,"name",t)},expression:"setting.name"}}),e.isOpenMultiLanguage?t("p",{on:{click:e.handleShowChooseLanguage}},[t("i",{staticClass:"iconfont icon-earth"})]):e._e()],1)]),e.allowEditCandidates?t("div",{staticClass:"workflow-setting-item"},[t("h4",{staticClass:"workflow-setting-item-title"},[e._v(e._s(e.nodeCandidateTitle))]),t("div",{staticClass:"workflow-setting-item-result",on:{click:function(t){e.visible=!0}}},[e.nodeCandidateText?t("span",{staticClass:"custom"},[e.isOpenData?[e._l(e.candidates,(function(n,i){return[n.staffId?[t("open-data",{key:i,attrs:{type:"userName",openid:n.staffId}})]:[e._v(" "+e._s(n.name)+" ")],i!==e.candidates.length-1?[e._v("+")]:e._e()]}))]:[e._v(" "+e._s(e.nodeCandidateText)+" ")]],2):t("span",{staticClass:"default"},[e._v(e._s(e.$t("view.designer.workFlow.clickSetSth",{data:e.nodeCandidateTitle})))]),t("span",{staticClass:"icon el-icon-plus"})])]):e._e(),e.isConvergeNode||e.isTaskMode?e._e():t("div",{staticClass:"workflow-setting-item"},[t("h4",{staticClass:"workflow-item-title"},[e._v(e._s(e.$t("common.base.fieldPermissions")))]),t("form-field-auth",{attrs:{"node-id":e.nodeId,fields:e.flowData.fields,value:e.fieldAuthorityList,disabled:e.fieldAuthDisabled},on:{update:e.updateFieldAuth}})],1),e.allowEditButton?t("div",{staticClass:"workflow-setting-item"},[t("h4",{staticClass:"workflow-item-title"},[e._v(e._s(e.$t("view.designer.workFlow.customFunctionButtonType")))]),t("custom-btn-setting",{attrs:{value:e.buttonList},on:{update:e.updateBtnAutn}})],1):e._e(),t("base-modal",{staticClass:"choose-user-modal",attrs:{title:e.$t("view.designer.workFlow.selectSth",{sth:e.nodeCandidateTitle}),show:e.visible,width:"640px"},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("config-contact",{ref:"configContact",attrs:{title:e.nodeCandidateTitle,fields:e.flowData.fields,value:e.candidates,"flow-api":e.flowApi,"show-dynamic":e.showConfigContactDynamic,showDynamicMenus:e.showDynamicMenus,showNewServiceProvider:""}}):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.chooseUser}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)],1)],1)}),Z=[],K=n(32914),Y=n(12986),Q=n(4946),X=n(66681),ee=n(51668),te=n(69396),ne=ee.A.NODE_OWNER,ie=ee.A.DEPT_MANAGER,oe=ee.A.USER,ae=(ee.A.CUSTOMER,ee.A.FORM_CUSTOMER),se=ee.A.FORM_LINK_MAN,re=ee.A.NODE_SUBMITTER,le={name:"basic-setting",components:(0,l.A)((0,l.A)((0,l.A)({},K.A.name,K.A),Y.A.name,Y.A),Q.A.name,Q.A),inject:["flowData","mode","moduleType","isModuleForApproval"],data:function(){return{visible:!1,setting:{},settingName:""}},computed:{cell:function(){return this.flowData.cell||{}},nodeId:function(){return this.cell.id},shape:function(){var e;return null===(e=this.cell)||void 0===e?void 0:e.shape},isStartNode:function(){return this.shape==p.A.START_NODE},isEndNode:function(){return this.shape==p.A.END_NODE},isConvergeNode:function(){return this.shape==p.A.CONVERGE_NODE},isCarbonNode:function(){return this.shape==p.A.CARBON_COPY_NODE},candidates:function(){var e;return(null===(e=this.setting)||void 0===e||null===(e=e.attribute)||void 0===e?void 0:e.candidate)||[]},nodeCandidateTitle:function(){return this.getNodeCandidateTitle(this.shape)},nodeCandidateText:function(){return this.candidates.map((function(e){return e.name})).join("+")},fieldAuthorityList:function(){var e;return(null===(e=this.setting)||void 0===e||null===(e=e.attribute)||void 0===e?void 0:e.fieldAuthorityList)||[]},fieldAuthDisabled:function(){return this.isEndNode||this.isCarbonNode||!this.flowData.fields.length},allowEditCandidates:function(){return!this.isEndNode&&!this.isConvergeNode},allowEditButton:function(){return!this.isCarbonNode&&!this.isConvergeNode&&!this.isEndNode&&!(this.isModuleForApproval&&this.isStartNode)},buttonList:function(){var e,t=(null===(e=this.setting)||void 0===e||null===(e=e.attribute)||void 0===e?void 0:e.buttonList)||[];return"approve-node"===this.shape&&this.isTaskMode&&(t=t.filter((function(e){return["agree","refuse"].includes(e.name)}))),t},showConfigContactDynamic:function(){return"start-node"!==this.cell.shape&&"project"!=this.moduleType},isOpenMultiLanguage:function(){return(0,w.g9)()||!1},flowApi:function(){return(0,c.A)({},te)},isOpenData:function(){return this.$platform.isOpenData},isTaskMode:function(){return this.mode===v.A.TASK},showDynamicMenus:function(){return[ne.name,ie.name,oe.name,ae.name,se.name,re.name]}},watch:{nodeId:{handler:function(e){this.setting=this.cell.data},immediate:!0}},methods:{handleShowChooseLanguage:function(){var e,t=this,n=this.cell.data.name,i=this.cell.data.attribute.nameLanguage,o=void 0===i?{}:i,a=this.$i18n.locale;0===Object.keys(o).length&&(o=(0,c.A)((0,c.A)({},(0,w.T2)()),{},(0,l.A)({},a,n))),this.$fast.languageSetting.show({title:this.$t("common.base.languageSetting"),type:"input",languageDefaultValueObj:(0,c.A)((0,c.A)({},o),{},(0,l.A)({},a,(null===(e=o)||void 0===e?void 0:e[a])||this.setting.name))}).then((function(e){t.cell.data.attribute["nameLanguage"]=e;var n=e[a];t.setting.name=n,t.cell.attr("text/textWrap/text",n,{ignoreHistory:!0})}))},updateName:function(e){var t=e.target.value;t||(this.setting.name=X.A.getName(this.shape));var n=this.$i18n.locale,i=this.cell.data.attribute.nameLanguage,o=void 0===i?{}:i;this.$set(this.setting.attribute,"nameLanguage",(0,c.A)((0,c.A)({},o),{},(0,l.A)({},n,this.setting.name))),this.cell.attr("text/textWrap/text",this.setting.name,{ignoreHistory:!0})},getNodeCandidateTitle:function(e){switch(e){case p.A.START_NODE:return this.$t("common.base.promoter");case p.A.PROCEDD_NODE:return this.$t("common.fields.executorUser.displayName");case p.A.APPROVE_NODE:return this.$t("common.base.approveUser");case p.A.CARBON_COPY_NODE:return this.$t("view.designer.workFlow.carbonCopy")}},chooseUser:function(){var e=this.$refs.configContact.checked;e=e.map((function(e){var t=e.id,n=e.name,i=e.type,o=e.extend,a=e.staffId,s=void 0===a?"":a,r=e.rootType,l=void 0===r?"":r,c={id:t,name:n,type:i,staffId:s,rootType:l};return o&&(c.extend=o),c})),this.visible=!1,this.$set(this.setting.attribute,"candidate",e)},updateFieldAuth:function(e){this.$set(this.setting.attribute,"fieldAuthorityList",e)},updateBtnAutn:function(e){this.$set(this.setting.attribute,"buttonList",e)}}},ce=le,de=(0,z.A)(ce,W,Z,!1,null,"b2d05f4c",null),ue=de.exports,he=n(28890),fe={name:"config-node",components:(0,l.A)((0,l.A)({},ue.name,ue),he.A.name,he.A),inject:["flowData","taskTypeId"],data:function(){return{tabs:[{name:g.Ay.t("common.base.baseSet"),value:"basic-setting"},{name:g.Ay.t("view.designer.advancedSetting"),value:"advanced-setting"}],currTab:"basic-setting"}},computed:{nodeId:function(){return this.flowData.cell.id},filterTabs:function(){if(this.taskTypeId&&!["approve-node"].includes(this.flowData.cell.shape))return this.tabs.slice(0,1);var e=["approve-node","process-node","end-node"];return e.includes(this.flowData.cell.shape)?this.tabs:this.tabs.slice(0,1)}},watch:{nodeId:function(){this.currTab="basic-setting"}},methods:{selectTab:function(e){this.currTab!=e.value&&(this.currTab=e.value)}}},pe=fe,ve=(0,z.A)(pe,U,q,!1,null,"f78c6de4",null),me=ve.exports,ge=function(){var e=this,t=e._self._c;return t("div",{staticClass:"node-edge-box"},[t("h1",[e._v(e._s(e.$t("view.designer.workFlow.link")))]),t("p",[e._v(" "+e._s(e.$t("view.designer.workFlow.label1"))+" ")]),"project"!==e.moduleType?t("div",{staticClass:"setting-item"},[t("h2",[e._v(e._s(e.$t("view.designer.workFlow.circulationConditionSetting")))]),t("el-select",{ref:"selectRef",on:{change:e.handeSelectChange},nativeOn:{"!blur":function(t){return e.selectBlur.apply(null,arguments)}},model:{value:e.conditionVal,callback:function(t){e.conditionVal=t},expression:"conditionVal"}},e._l(e.conditionOpts,(function(e){return t("el-option",{key:e.val,attrs:{label:e.name,value:e.val}})})),1),t("button",{directives:[{name:"show",rawName:"v-show",value:e.showConditionDom,expression:"showConditionDom"}],staticClass:"submit-btn",on:{click:e.handleShowModal}},[e._v(" "+e._s(e.$t("view.designer.workFlow.clickSetCondition"))+" ")])],1):e._e(),t("condition-list",{directives:[{name:"show",rawName:"v-show",value:e.showConditionDom,expression:"showConditionDom"}],ref:"conditionList",attrs:{list:e.conditionList}}),t("condition-modal",{ref:"conditionModal",on:{submit:e.handleConditionModalSubmit}})],1)},we=[],be=n(81798),Ae=n(5390),ye=n(19572),Ce={name:"config-edge",components:{ConditionModal:Ae.Ay,ConditionList:ye.Ay},inject:["flowData","moduleType"],data:function(){return{conditionVal:"none",conditionOpts:[{name:g.Ay.t("view.designer.workFlow.notSetCondition"),val:"none"},{name:g.Ay.t("view.designer.workFlow.useJudgeCondition"),val:"check"}],edgeName:"",conditionList:[]}},computed:{nodeId:function(){return this.flowData.cell.id},cell:function(){return this.flowData.cell},showConditionDom:function(){return"none"!==this.conditionVal}},watch:{nodeId:{handler:function(){var e=this.flowData.cell.data,t=e.isCondition,n=e.conditionObject,i=void 0===n?[]:n,o="undefined"!==typeof t&&t?"check":"none";"none"===o&&this.setCellLabels(!1),this.conditionVal=o,this.conditionList=i},immediate:!0}},methods:{selectBlur:function(e){var t=this;e.target.value&&(this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){var e,n=t.flowData.cell.data;n.isCondition=Reflect.has(n,"conditionObject")&&(null===n||void 0===n||null===(e=n.conditionObject)||void 0===e?void 0:e.length)>0&&n.isCondition}),200))},updateName:function(e){var t=e.target.value;this.cell.setLabelAt(0,t)},handeSelectChange:function(e){var t=this.flowData.cell.data,n=void 0===t?{}:t;"none"===e?(n.isCondition=!1,this.setCellLabels(!1)):(n.isCondition=!0,this.setCellLabels(!0))},setCellLabels:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e?this.flowData.cell.setLabels(this.genLabel()):this.flowData.cell.setLabels({})},genLabel:function(){return{markup:f.VK.getForeignObjectMarkup(),attrs:{fo:{width:24,height:6,x:-12,y:-3}},position:{distance:.5,options:{keepGradient:!0}},background:"#16C2C1"}},handleShowModal:function(){var e=this.flowData.cell.data;e="object"!==(0,be.A)(e)?e:{},this.$refs.conditionModal.show()},handleConditionModalSubmit:function(e){this.conditionList=e}}},ke=Ce,Ne=(0,z.A)(ke,ge,we,!1,null,null,null),_e=Ne.exports,Se={name:"config-panel",components:(0,l.A)((0,l.A)({},me.name,me),"ConfigEdge",_e),props:["cell"],computed:{isNode:function(){var e;return"function"===typeof this.cell.isNode&&(null===(e=this.cell)||void 0===e?void 0:e.isNode())},isEdge:function(){var e;return"function"===typeof this.cell.isEdge&&(null===(e=this.cell)||void 0===e?void 0:e.isEdge())},isLanguageIsZh:function(){return this.$i18n.locale===w.As}}},xe=Se,Te=(0,z.A)(xe,H,j,!1,null,"41ce310a",null),Ee=Te.exports,Fe=n(34117),De=n(15902),Le=n(17319),Ie=n.n(Le),Oe=n(13242),Pe=n(69749),Me=function(){var e={enable:{tip:"确定要启用流程吗？",content:"启用后，新提交的数据将按此版本的流程流转"},delete:{tip:"确定要删除流程版本吗？",content:"删除后，你将无法再编辑和启用此流程版本"},modify:{tip:"流程设定有修改，是否保存？",content:"你修改了流程设定但没有保存，是否需要保存流程设定并继续？"},create:{tip:"提示",content:"根据当前所在版本复制新版本内容, 是否新建流程版本？"}},t=function(e){e.cancelButtonClass="not-allowed",e.confirmButtonLoading=!0},n=function(e){e.cancelButtonClass="",e.confirmButtonLoading=!1},i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e[t]?Pe.MessageBox.confirm(e[t].content,e[t].tip,(0,c.A)({showClose:!1},n)):Promise.resolve(null)};return{showButtonLoading:t,hideButtonLoading:n,show:i}},Ve=n(70688),$e=n(14389),Re=function(){var e=Me(),t=e.show,n=e.showButtonLoading,i=e.hideButtonLoading,o=(0,Ve.M)(),a=o.loading,l=o.setLoading,c=(0,k.ref)({}),d=function(){var e=(0,r.A)((0,s.A)().mark((function e(o){var d;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(0,N.isEmpty)(o)&&!a.value){e.next=2;break}return e.abrupt("return");case 2:return d=function(){var e=(0,r.A)((0,s.A)().mark((function e(t,r,d){var u,h,f,p;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==t){e.next=26;break}return l(!0),n(r),e.prev=3,e.next=6,(0,te.createFlowVersion)({workflowTemplateId:null===o||void 0===o?void 0:o.bizId})["finally"]((function(){l(!1),i(r)}));case 6:if(u=e.sent,h=u.success,f=u.data,p=u.message,!h){e.next=16;break}return d(),Pe.Message.success("添加成功"),c.value=f,e.next=16,$e.A.dispatch("design/initFlowVersionList",{onlyRefreshList:!0});case 16:return h||Pe.Message.warning(p),e.abrupt("return",f);case 20:e.prev=20,e.t0=e["catch"](3),console.error("[fetch createFlowVersion error]",e.t0),Pe.Message.warning(JSON.stringify(e.t0));case 24:e.next=29;break;case 26:if(!a.value){e.next=28;break}return e.abrupt("return");case 28:d();case 29:case"end":return e.stop()}}),e,null,[[3,20]])})));return function(t,n,i){return e.apply(this,arguments)}}(),e.next=5,t("create",{beforeClose:d});case 5:return e.abrupt("return",c);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return{loading:a,createFlowVersionResult:c,fetchCreateFlowVersion:d}},Be=function(){var e=Me(),t=e.show,n=e.showButtonLoading,i=e.hideButtonLoading,o=(0,Ve.M)(),a=o.loading,l=o.setLoading,c=(0,k.ref)({}),d=function(){var e=(0,r.A)((0,s.A)().mark((function e(o){var d;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(0,N.isEmpty)(o)&&!a.value){e.next=2;break}return e.abrupt("return");case 2:return d=function(){var e=(0,r.A)((0,s.A)().mark((function e(t,r,d){var u,h,f,p;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==t){e.next=19;break}return l(!0),n(r),e.prev=3,e.next=6,(0,te.enableFlowVersion)({workflowTemplateId:null===o||void 0===o?void 0:o.bizId})["finally"]((function(){l(!1),i(r)}));case 6:u=e.sent,h=u.success,f=u.data,p=u.message,h?(c.value=f,Pe.Message.success("启用成功"),d()):Pe.Message.warning(p),e.next=17;break;case 13:e.prev=13,e.t0=e["catch"](3),console.error("[fetch enableFlowVersion error]",e.t0),Pe.Message.warning(JSON.stringify(e.t0));case 17:e.next=22;break;case 19:if(!a.value){e.next=21;break}return e.abrupt("return");case 21:d();case 22:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(t,n,i){return e.apply(this,arguments)}}(),e.next=5,t("enable",{beforeClose:d});case 5:return e.abrupt("return",c.value);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return{loading:a,result:c,fetchEnableFlowVersion:d}},ze=function(){var e=Me(),t=e.show,n=e.showButtonLoading,i=e.hideButtonLoading,o=(0,Ve.M)(),a=o.loading,l=o.setLoading,c=(0,k.ref)({}),d=function(){var e=(0,r.A)((0,s.A)().mark((function e(o){var d;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(0,N.isEmpty)(o)&&!a.value){e.next=2;break}return e.abrupt("return");case 2:return d=function(){var e=(0,r.A)((0,s.A)().mark((function e(t,r,d){var u,h,f,p;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==t){e.next=27;break}return l(!0),n(r),e.prev=3,e.next=6,(0,te.deleteFlowVersion)({workflowTemplateId:null===o||void 0===o?void 0:o.bizId})["finally"]((function(){l(!1),i(r)}));case 6:if(u=e.sent,h=u.success,f=u.data,p=u.message,h){e.next=14;break}Pe.Message.warning(p),e.next=19;break;case 14:return e.next=16,$e.A.dispatch("design/initFlowVersionList",{onlyRefreshList:!0});case 16:c.value=f,d(),Pe.Message.success("删除成功");case 19:e.next=25;break;case 21:e.prev=21,e.t0=e["catch"](3),console.error("[fetch deleteFlowVersion error]",e.t0),Pe.Message.warning(JSON.stringify(e.t0));case 25:e.next=30;break;case 27:if(!a.value){e.next=29;break}return e.abrupt("return");case 29:d();case 30:case"end":return e.stop()}}),e,null,[[3,21]])})));return function(t,n,i){return e.apply(this,arguments)}}(),e.next=5,t("delete",{beforeClose:d});case 5:return e.abrupt("return",c);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return{loading:a,result:c,fetchDeleteFlowVersion:d}},Je=function(){var e=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"text",i="text"!==n;switch(e){case p.A.DESIGNING:t=i?"design":"设计中";break;case p.A.ENABLED:t=i?"enabled":"启用中";break;case p.A.HISTORY:t=i?"history":"历史";break;default:}return t};return{formStatus:e}},Ge=(0,k.defineComponent)({props:{onHandleEditVersionItem:Function},emits:["handleEditVersionItem"],setup:function(e,t){var n=t.expose,i=t.emit,o=(0,Oe.s)(),a=o.visible,c=o.showDialog,d=o.hideDialog,u=Re(),h=u.createFlowVersionResult,f=u.fetchCreateFlowVersion,v=Be(),m=v.fetchEnableFlowVersion,w=ze(),b=w.fetchDeleteFlowVersion,A=Je(),y=A.formStatus,C=(0,k.computed)((function(){return $e.A.getters.flowDesignVersionList})),N=(0,k.computed)((function(){return $e.A.getters.flowCurrentVersion})),_=function(){var e=(0,r.A)((0,s.A)().mark((function e(t){var n,o;return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,b(t);case 2:n=e.sent,n&&N.value.bizId===t.bizId&&(o=C.value.find((function(e){return e.status==p.A.ENABLED})),o&&i("handleEditVersionItem",o));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),S=function(){var e=(0,r.A)((0,s.A)().mark((function e(t){return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,m(t);case 2:return e.next=4,$e.A.dispatch("design/initFlowVersionList");case 4:i("handleEditVersionItem",N.value),d();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),x=function(e){i("handleEditVersionItem",e),d()},T=function(){var e=(0,r.A)((0,s.A)().mark((function e(){return(0,s.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f(N.value);case 2:i("handleEditVersionItem",h.value),d();case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n({showDialog:c,hideDialog:d}),function(){var e;return(0,k.h)("base-modal",Ie()([{class:"graph-version-manage__modal",attrs:{title:"管理流程版本",show:a.value}},{on:(0,l.A)({},"update:show",(function(e){return e?c():d()}))},{attrs:{width:"500px","append-to-body":!0}}]),[(0,k.h)("div",{class:"graph-version-manage__modal-content"},[(0,k.h)("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:T}},["添加新版本"]),(0,k.h)("ul",{class:"graph-version-manage__modal-list"},[null===(e=C.value)||void 0===e?void 0:e.map((function(e){return(0,k.h)("li",{class:"graph-version-modal__list-item"},[(0,k.h)("div",{class:"graph-version-modal__list-item-l"},[(0,k.h)("span",{class:"graph-version-modal__list-item-txt"},["流程版本",e.newVersion]),(0,k.h)("span",{class:["graph-version-modal__list-item-tag",y(e.status,"class")]},[y(e.status)])]),(0,k.h)("div",{class:"graph-version-modal__list-item-r"},[e.status!==p.A.ENABLED?(0,k.h)("el-button",{attrs:{type:"text"},on:{click:function(){return S(e)}}},["启用流程"]):null,(0,k.h)("el-button",{attrs:{type:"text"},on:{click:function(){return x(e)}}},[g.Ay.t("common.base.edit")]),e.status!==p.A.ENABLED?(0,k.h)("el-button",{class:"delete-btn",attrs:{type:"text"},on:{click:function(){return _(e)}}},[g.Ay.t("common.base.delete")]):null])])}))])])])}}}),He=n(28792),je=(0,k.defineComponent)({props:{saveProcess:{type:Function,default:function(){},required:!0}},directives:{clickOutside:He["default"]},setup:function(e,t){var n=t.emit,i=(0,Oe.s)(),o=i.visible,a=i.showDialog,l=i.hideDialog,c=Be(),d=c.fetchEnableFlowVersion,u=Je(),h=u.formStatus,f=(0,k.ref)(),v=(0,k.computed)((function(){return $e.A.getters.flowDesignVersionList})),m=(0,k.computed)((function(){return $e.A.getters.flowCurrentVersion})),g=(0,k.computed)((function(){return $e.A.getters.flowDesignStatus})),w=function(){var t=(0,r.A)((0,s.A)().mark((function t(){return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.saveProcess();case 2:return t.next=4,d(m.value);case 4:return t.next=6,$e.A.dispatch("design/initFlowVersionList");case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),b=function(e,t){null!==e&&(e.preventDefault(),e.stopPropagation()),l(),n("handleVersionItemClick",t)},A=function(){f.value.showDialog(),l()};return function(){return(0,k.h)("div",{class:"graph-version-box"},[(0,k.h)("el-popover",{attrs:{placement:"bottom",width:"240",trigger:"manual",value:o.value,"popper-class":"graph-version-popover"}},[(0,k.h)("div",{slot:"reference",class:"graph-version-content",on:{click:a}},[m.value?(0,k.h)("div",{class:"graph-version-content__main"},[(0,k.h)("span",{class:"graph-version-content__text"},["流程版本",m.value.newVersion]),(0,k.h)("span",{class:["graph-version-content__tag",h(g.value,"class")]},[h(g.value)])]):null,(0,k.h)("div",{class:"graph-version-content__suffix"},[(0,k.h)("i",{class:"el-icon-arrow-down"})])]),(0,k.h)("div",Ie()([{class:"graph-version-popover"},{directives:[{name:"clickOutside",value:function(){return l()}}]}]),[(0,k.h)("ul",{class:"graph-version-list"},[v.value.map((function(e){return(0,k.h)("li",{class:"graph-version-list__item",on:{click:function(t){return b(t,e)}}},[(0,k.h)("span",{class:"graph-version-list__item-text"},["流程版本",e.newVersion]),(0,k.h)("span",{class:["graph-version-list__item-tag",h(e.status,"class")]},[h(e.status)])])}))]),(0,k.h)("div",{class:"graph-version-manage",on:{click:A}},[(0,k.h)("i",{class:"iconfont icon-setting"}),(0,k.h)("span",{class:"graph-version-manage__text"},["管理流程版本"])])])]),(0,k.h)("div",[(0,N.isNumber)(g.value)&&g.value!==p.A.ENABLED?(0,k.h)("el-button",{attrs:{type:"primary"},on:{click:w}},["启用流程"]):null]),(0,k.h)(Ge,{ref:f,on:{handleEditVersionItem:function(e){return b(null,e)}}})])}}}),Ue=(0,k.defineComponent)({name:"GraphNodeStyleActionBar",setup:function(e,t){var n=t.emit,i=(0,k.computed)((function(){return $e.A.getters.flowDesignNodeCardStyle})),o=function(e){n("changeFlowNodeCardStyle",e)};return function(){return(0,k.h)("div",{class:"graph-node-style__action-bar"},[(0,k.h)("el-tooltip",{attrs:{effect:"dark",content:"详细视图",placement:"top"}},[(0,k.h)("div",{class:["graph-node-style__action-bar-item",i.value===p.A.FULL_NODE_CARD?"active":null],on:{click:function(){return o(p.A.FULL_NODE_CARD)}}},[(0,k.h)("i",{class:"iconfont icon-xiangxi"})])]),(0,k.h)("el-tooltip",{attrs:{effect:"dark",content:"简易视图",placement:"bottom"}},[(0,k.h)("div",{class:["graph-node-style__action-bar-item",i.value===p.A.NORMAL_NODE_CARD?"active":null],on:{click:function(){return o(p.A.NORMAL_NODE_CARD)}}},[(0,k.h)("i",{class:"iconfont icon-jianyi"})])])])}}}),qe=n(56268),We=n(74526),Ze=n(32759),Ke=n(80906),Ye=n(49430),Qe=n(11592),Xe=n(87744),et=n(50152),tt=n(87512),nt=Re(),it=nt.fetchCreateFlowVersion,ot={name:"workflow-design",provide:function(){return{flowData:this.$data,mode:this.mode,taskTypeId:this.taskTypeId,moduleType:this.moduleType,newPanel:this.newPanel,isModuleForApproval:this.isModuleForApproval}},props:{mode:{type:String,default:v.A.PAAS}},data:function(){return{isEdit:!1,isReady:!1,showConfig:!1,submitting:!1,graph:null,initJson:{},antvJson:{},cell:{},fields:[],templateName:"",isShowEdgeTooltip:!1,finishNodes:[],endShowFlowStatus:[],sparePartCondition:[{displayName:g.Ay.t("common.part.target"),fieldName:"out_targetIds",formType:"select",tableName:"sparePart",setting:{selectType:1,out_fieldname:"out_targetIds"}},{displayName:g.Ay.t("common.part.sparePartTotal"),fieldName:"out_total",formType:"number",tableName:"sparePart"}],panelVersion:"v2",noTargetEdge:null,versionList:[]}},computed:(0,c.A)((0,c.A)({},(0,$.L8)(["flowIsDesigning","flowDesignNodeCardStyle","flowCurrentVersion"])),{},{appId:function(){return this.$route.query.appId},formTemplateId:function(){return this.$route.query.formId},isSparePart:function(){return this.moduleType===v.A.SPARE_PART},isEventModel:function(){return this.moduleType===v.A.EVENT_MODULE},isSmartSettlementModel:function(){return this.moduleType===v.A.SMART_SETTLEMENT},isTaskMode:function(){return this.mode===v.A.TASK},isModuleForApproval:function(){return this.isSparePart||this.isEventModel||this.isTaskMode||this.isSmartSettlementModel},taskTypeId:function(){return this.$route.query.taskTypeId},taskFlowType:function(){return this.$route.query.taskFlowType},currentNodeId:function(){return this.$route.query.currentNodeId},moduleType:function(){return this.$route.query.moduleType||""},isLanguageIsZh:function(){return this.$i18n.locale===w.As},defaultLocalKey:function(){return this.$i18n.locale},newPanel:function(){return"v1"!==this.panelVersion},isSpecialNode:function(){return this.cell.shape===p.A.EDGE},jsonCell:function(){var e,t=this.cell;(t instanceof f.oH||t instanceof f.bP)&&(t.type=t.shape!==p.A.EDGE?t.shape:p.A.CONDITION,t.title=t.shape!==p.A.EDGE?(null===t||void 0===t||null===(e=t.data)||void 0===e?void 0:e.name)||"":this.$t("view.designer.workFlow.condition"));return t},taskFlowExtendGray:function(){var e,t=(0,T.zO)(window);return Boolean(null===(e=t.grayAuth)||void 0===e?void 0:e.taskFlowExtend)},isNewNodeCardStyle:function(){return this.flowDesignNodeCardStyle===p.A.FULL_NODE_CARD}}),watch:{antvJson:function(e){"v1"===this.panelVersion&&this.isFinished(e)},finishNodes:{handler:function(e){this.toFinishName(e)},deep:!0}},methods:(0,c.A)((0,c.A)((0,c.A)({},(0,$.i0)({fetchVersionList:"design/initFlowVersionList",fetchEdgeConditionOption:"design/initFlowEdgeSupportConditionOptions"})),(0,$.PY)({updateNodeCardStyle:"design/".concat(et.Wd),setCurrentVersionItem:"design/".concat(et.ky)})),{},{updateFinishNodeStatus:function(){this.isFinished(this.packToJson())},isFinished:function(e){this.finishNodes=[];var t=JSON.parse(e||this.antvJson).cells;this.setEndShowFlowStatus(t)},setEndShowFlowStatus:function(e){var t,n=this,i=null===e||void 0===e?void 0:e.find((function(e){return"end-node"===e.shape})).id,o=e.filter((function(e){var t;return(null===e||void 0===e||null===(t=e.target)||void 0===t?void 0:t.cell)===i})),a=e.filter((function(e){var t;return null===(t=e.data)||void 0===t||null===(t=t.attribute)||void 0===t||null===(t=t.buttonList)||void 0===t?void 0:t.find((function(e){var t;return e.isOpen&&2===(null===e||void 0===e||null===(t=e.buttonConfig)||void 0===t||null===(t=t.backTypeConfig)||void 0===t?void 0:t.backType)}))})),s=null===(t=e.find((function(e){var t;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(t=t.attribute)||void 0===t||null===(t=t.endShowFlowStatus)||void 0===t?void 0:t.length})))||void 0===t||null===(t=t.data)||void 0===t||null===(t=t.attribute)||void 0===t?void 0:t.endShowFlowStatus;o.map((function(t){var i,o=e.find((function(e){var n;return(null===t||void 0===t||null===(n=t.source)||void 0===n?void 0:n.cell)===(null===e||void 0===e?void 0:e.id)})),a=null===s||void 0===s?void 0:s.find((function(e){return e.sourceNodeId===(null===o||void 0===o?void 0:o.id)})),r=(null===a||void 0===a?void 0:a.finishStatusLanguage)&&Object.values(a.finishStatusLanguage).some((function(e){return""!==e}));"carbon-copy-node"!==(null===o||void 0===o?void 0:o.shape)&&"start-node"!==(null===o||void 0===o?void 0:o.shape)&&n.finishNodes.push({id:null===o||void 0===o?void 0:o.id,finishName:r&&!a.functionBtn?a.finishStatusLanguage[n.defaultLocalKey]:n.$t("common.base.usualStatus.finish"),finishNameLanguage:r&&!a.functionBtn?a.finishStatusLanguage:(0,c.A)((0,c.A)({},(0,w.jP)("common.base.usualStatus.finish")),{},(0,l.A)({},n.defaultLocalKey,n.$t("common.base.usualStatus.finish")||"已完成")),name:null===o||void 0===o||null===(i=o.data)||void 0===i?void 0:i.name})})),a.map((function(e){var t,i,o,a,r=null===(t=e.data)||void 0===t||null===(t=t.attribute)||void 0===t||null===(t=t.buttonList)||void 0===t?void 0:t.find((function(e){var t;return e.isOpen&&2===(null===e||void 0===e||null===(t=e.buttonConfig)||void 0===t||null===(t=t.backTypeConfig)||void 0===t?void 0:t.backType)})),d=null===s||void 0===s?void 0:s.find((function(t){var n;return t.sourceNodeId===e.id&&t.functionBtn===(null===r||void 0===r||null===(n=r.buttonConfig)||void 0===n?void 0:n.buttonName)})),u=(null===d||void 0===d?void 0:d.finishStatusLanguage)&&Object.values(d.finishStatusLanguage).some((function(e){return""!==e}))||!1;r&&n.finishNodes.push({functionBtn:(null===r||void 0===r||null===(i=r.buttonConfig)||void 0===i||null===(i=i.buttonNameLanguage)||void 0===i?void 0:i[n.defaultLocalKey])||(null===r||void 0===r||null===(o=r.buttonConfig)||void 0===o?void 0:o.buttonName),id:null===e||void 0===e?void 0:e.id,finishName:u?d.finishStatusLanguage[n.defaultLocalKey]:(null===d||void 0===d?void 0:d.finishStatus)||n.$t("common.base.usualStatus.finish"),finishNameLanguage:u?d.finishStatusLanguage:(0,c.A)((0,c.A)({},(0,w.jP)("common.base.usualStatus.finish")),{},(0,l.A)({},n.defaultLocalKey,n.$t("common.base.usualStatus.finish")||"已完成")),name:null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.name})}))},toFinishName:function(e){this.endShowFlowStatus=e.map((function(e){return{sourceNodeId:e.id,finishStatus:e.finishName,functionBtn:e.functionBtn,finishStatusLanguage:e.finishNameLanguage}}))},initialize:function(){var e=arguments,t=this;return(0,r.A)((0,s.A)().mark((function n(){var i,o,a,r,l,c,d,u,h,f,v,m;return(0,s.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=e.length>0&&void 0!==e[0]?e[0]:t.flowCurrentVersion,t.submitting=!0,n.prev=2,a=t.appId,r=t.formTemplateId,n.next=6,te.getProcess({appId:a,formTemplateId:r,workflowTemplateId:null===i||void 0===i?void 0:i.bizId});case 6:if(l=n.sent,l.success){n.next=9;break}return n.abrupt("return",t.$platform.toast(l.message,"error"));case 9:t.isEdit=!(null===(o=l.data)||void 0===o||!o.bizId),c=l.data||{},d=c.attribute,u=c.name,h=c.nodeStyle,f=void 0===h?p.A.NORMAL_NODE_CARD:h,t.updateNodeCardStyle(f),v=JSON.parse(d||"{}"),t.$parent.formName=u||v.data.name,m=v.cells.map((function(e){return Xe.A.toCell(e,t.flowDesignNodeCardStyle===p.A.FULL_NODE_CARD)})),t.graph?(t.$refs.nodeConfigPanelMainRef.handleHide(),t.graph.fromJSON(m||[]),t.initJson=t.antvJson=t.packToJson(!0),t.handleGraphScrollToContent()):(t.graph=P.init(t),t.graph.fromJSON(m||[]),t.handleGraphScrollToContent(),t.initJson=t.antvJson=t.packToJson(!0),t.initEvent(),t.resizeFn(),t.isFinished()),t.isReady=!0,n.next=22;break;case 19:n.prev=19,n.t0=n["catch"](2),console.error("workflow initialize error: ",n.t0);case 22:t.submitting=!1;case 23:case"end":return n.stop()}}),n,null,[[2,19]])})))()},getContainerSize:function(){var e=this.isTaskMode?44:96;return{width:document.body.offsetWidth,height:document.body.offsetHeight-e}},resizeFn:function(){var e=this.getContainerSize(),t=e.width,n=e.height;this.graph.resize(t,n)},initEvent:function(){var e=this;this.graph.on("cell:selected",(function(t){var n,i=t.cell;null!==(n=i.data)&&void 0!==n&&n.attribute&&!Reflect.has(i.data.attribute,"showFlowStatus")&&(i.data.attribute=(0,A.RE)(Xe.A.toCell(i),e.fields)),e.cell=i,e.newPanel?e.$nextTick((function(){e.$refs.nodeConfigPanelMainRef.handleShow(),e.isShowEdgeTooltip=!1})):e.showConfig=!0})),this.graph.on("cell:unselected",(function(t){var n=t.cell;e.newPanel?(n.removeTools(),e.$nextTick((function(){e.$refs.nodeConfigPanelMainRef.handleHide()}))):e.showConfig=!1,e.cell={}}))},handlePanelUpdateVisible:function(e){e||this.graph.unselect(this.cell)},validate:function(){var e=this.graph.toJSON(),t=(0,Ye.A)((null===e||void 0===e?void 0:e.cells)||[],this.fields);return t},showNotification:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.$notify({type:"error",title:this.$t("view.designer.workFlow.tip35"),duration:0,customClass:"base-notification",message:function(t){var n=e.map((function(e){var n=e.message.map((function(e){return t("p",["- ",e])}));return n.unshift(t("h3",[e.title])),n}));return t("div",[n])}(this.$createElement)})},save:function(){var e,t=this.validate();if(t.length)return this.showNotification(t),Promise.reject("error");if(!this.submitting){this.submitting=!0,this.antvJson=this.packToJson();var n=this.appId,i=this.formTemplateId,o={appId:n,formTemplateId:i,antvJson:this.antvJson,workflowTemplateId:(null===(e=this.flowCurrentVersion)||void 0===e?void 0:e.bizId)||"",nodeStyle:this.flowDesignNodeCardStyle};return this.isEdit?this.updateProcessMethod(o):(this.isFinished(),this.createProcessMethod(o))}},createProcessMethod:function(e){var t=this;return te.deploymentProcess(e).then((function(e){var n=e.success;n&&(t.initJson=t.antvJson),t.$notify({type:n?"success":"error",title:n?t.$t("common.base.saveSuccess"):t.$t("common.base.saveFail"),message:!n&&e.message})}))["finally"]((function(){t.submitting=!1}))["catch"]((function(e){console.error("err",e)}))},updateProcessMethod:function(e){var t=this;return new Promise((function(n,i){te.updateProcess(e).then((function(e){var o=e.success;o&&(t.initJson=t.antvJson),t.$notify({type:o?"success":"error",title:o?t.$t("common.base.tip.edit2Success"):t.$t("common.base.tip.editFail"),message:!o&&e.message}),o?n(o):i(o)}))["finally"]((function(){t.submitting=!1}))["catch"]((function(e){i(e),console.error("err",e)}))}))},checkModified:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.antvJson=this.packToJson(e),this.antvJson!=this.initJson},packToJson:function(e){var t,n=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=null===(t=this.graph)||void 0===t?void 0:t.toJSON(),a=null===o||void 0===o?void 0:o.cells.map((function(t){return"end-node"!==t.shape||e||(t.data.attribute["endShowFlowStatus"]="v2"===n.panelVersion?(0,A.UE)(o.cells):n.endShowFlowStatus),Xe.A.packToCell(t)}));return i?JSON.stringify({cells:a,data:{name:this.$parent.formName}}):{cells:a,data:{name:this.$parent.formName}}},fetchFormFields:function(){var e=this;return this.isEventModel?this.fetchEventFields():this.isSparePart?this.fetchSparePartFields():this.isTaskMode&&!this.isSmartSettlementModel?this.fetchTaskFields():We.QA({templateBizId:this.formTemplateId}).then((function(t){var n=t.success,i=t.data;n&&(e.fields=e.newPanel?(0,Qe.i)(i.paasFormFieldVOList):e.filterFields(i.paasFormFieldVOList),e.templateName=i.templateName)}))["catch"]((function(e){return console.log(e)}))},fetchSparePartFields:function(){var e=this;return(0,r.A)((0,s.A)().mark((function t(){var n,i,o;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,n={typeId:"",tableName:"all"},t.next=4,Ze.QO(n);case 4:return i=t.sent,o=(i||[]).map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"task_".concat(e.fieldName)})})).filter((function(e){return e.formType!==tt.E.Cascader})),e.fields=[].concat((0,a.A)(e.filterFields(o)),(0,a.A)(e.sparePartCondition)),t.abrupt("return",i);case 10:t.prev=10,t.t0=t["catch"](0),console.log(t.t0);case 13:case"end":return t.stop()}}),t,null,[[0,10]])})))()},fetchTaskFields:function(){var e=this,t={typeId:this.taskTypeId,isFromSetting:!1,isShowRichText:!0},n={moduleId:this.taskTypeId},i=[Ze.jE((0,c.A)((0,c.A)({},t),{},{tableName:"task"})),Ze.jE((0,c.A)((0,c.A)({},t),{},{tableName:"task_receipt"})),Ze.eh(n)];return this.taskFlowExtendGray&&i.push(Ze.dM({taskTypeId:this.taskTypeId,currentNodeId:this.currentNodeId})),Promise.all(i).then((function(t){var n,i,o,s=t[0].map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"task_".concat(e.fieldName)})})),r=t[1].map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"task_receipt_".concat(e.fieldName)})})),l=((null===(n=t[2])||void 0===n?void 0:n.result)||[]).map((function(e){var t=e.business,n=void 0===t?"":t,i=e.id,o="task_receipt_amount_".concat(i).concat(n?"_".concat(n):"");return{formType:"number",operator:"",displayName:e.ruleName,fieldName:o,business:e.business,tableName:"task_receipt_amount"}})),d=null!==(i=null===t||void 0===t||null===(o=t[3])||void 0===o||null===(o=o.result)||void 0===o?void 0:o.map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"task_node_".concat(e.fieldName)})})))&&void 0!==i?i:[];e.fields=[].concat((0,a.A)(e.filterFields(s)),(0,a.A)(e.filterFields(r)),(0,a.A)(e.filterFields(l)),(0,a.A)(e.filterFields(d)))}))},fetchEventFields:function(){var e=this,t={isFromSetting:!1,templateId:this.taskTypeId};return Promise.all([te.getAllEventFields((0,c.A)((0,c.A)({},t),{},{tableName:"event"})),te.getAllEventFields((0,c.A)((0,c.A)({},t),{},{tableName:"eventReceipt"}))]).then((function(t){var n=t[0].map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"event_".concat(e.fieldName)})})),i=t[1].map((function(e){return(0,c.A)((0,c.A)({},e),{},{fieldName:"eventReceipt_".concat(e.fieldName)})}));e.fields=[].concat((0,a.A)(e.filterFields(n)),(0,a.A)(e.filterFields(i)))}))},filterFields:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t)return t.map((function(t){return t.subFormFieldList=e.filterFields(t.subFormFieldList,Ke.hL(t)),t})).filter((function(e){return"separator"!=e.formType&&!e.isHidden})).filter((function(e){var t=e.isSystem,i=e.setting,o=void 0===i?{}:i;return!n||!t||t&&(o.isShow||void 0==o.isShow)}))},initCellsAttributeData:function(e){var t=this;return new Promise((function(n,i){try{e.forEach((function(e){if("edge"!==e.shape){var n,i=(0,A.Lj)(t.fields,e.shape),o=i.fieldAuthorityList,s=void 0===o?[]:o,r=(null===(n=e.data)||void 0===n||null===(n=n.attribute)||void 0===n?void 0:n.fieldAuthorityList)||[];if(s.length>=r.length){var l=(0,N.differenceBy)(s,r,(function(e){return Reflect.has(e,"subFormFieldList")?e.subFormFieldList.map((function(e){return e.fieldName})).join(","):e.fieldName}));l.length>0&&(e.data.attribute.fieldAuthorityList=[].concat((0,a.A)(r),(0,a.A)(l)).reduce((function(e,t){var n=e.findIndex((function(e){return e.fieldName===t.fieldName}));return n>-1?e[n]=_().merge(t,e[n]):e.push(t),e}),[]))}if(r.length>s.length){var c=(0,N.differenceBy)(r,s,"fieldName").map((function(e){return e.fieldName}));e.data.attribute.fieldAuthorityList=r.filter((function(e){return!c.includes(e.fieldName)}))}[p.A.PROCEDD_NODE,p.A.APPROVE_NODE].includes(e.shape)&&(e.data.attribute=(0,A.RE)(e,t.fields))}}))}catch(o){console.error("[initCellsAttributeData error]",o),n(e)}n(e)}))},handleChangeGraphZoom:_().debounce((function(e,t){var n=this;if(this.graph){var i=this.graph.zoom(),o=(e-50)/100-(i-1);this.$nextTick((function(){n.graph.zoom(Math.ceil(100*o)/100),setTimeout((function(){}),0)}))}}),10),handleGraphScrollToContent:function(){var e=this;this.$nextTick((function(){return e.graph.scrollToContent()}))},handleAddNode:function(e,t){var n=this.graph.localToClient(this.noTargetEdge.getTargetPoint()),i=n.x,o=n.y,a=this.graph.localToClient(this.noTargetEdge.getSourcePoint()),s=a.x,r=a.y,l=this.noTargetEdge.getTargetPoint(),c=l.x,d=l.y,u="left";i>s?(u="left",d-=C.Zb/2):(u="right",c-=C.Un,d-=C.Zb/2),i-s<5&&(o<r?(u="bottom",c+=C.Un/2,d-=C.Zb/2):(c+=C.Un/2,d+=C.Zb/2,u="top"));var h=new y.A({x:c,y:d,shape:e.type,data:{name:e.originName,attribute:{}}},this.isNewNodeCardStyle),f=this.graph.addNode(h);this.noTargetEdge.setTarget(f,{port:u}),_().isFunction(t)&&t()},changeFlowNodeCardStyle:function(e){var t=this;this.updateNodeCardStyle(e);var n=this.packToJson(!0,!1),i=n.cells.map((function(e){return Xe.A.toCell(e,t.isNewNodeCardStyle)}));this.graph.fromJSON(i||[]),this.handleGraphScrollToContent()},handleVersionItemClick:function(e){var t=this;return(0,r.A)((0,s.A)().mark((function n(){var i,o;return(0,s.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=!1,!t.checkModified(!1)){n.next=12;break}return n.prev=2,n.next=5,t.$confirm(t.$t("view.designer.tip.tip4"),"",{confirmButtonText:t.$t("common.base.save"),cancelButtonText:t.$t("common.base.notSaveForNow"),type:"warning",showClose:!1,closeOnClickModal:!1});case 5:o=n.sent,i="confirm"===o,n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](2),console.log("[confirm catch]",n.t0);case 12:if(!i){n.next=15;break}return n.next=15,t.save();case 15:t.setCurrentVersionItem(e),t.initialize(e);case 17:case"end":return n.stop()}}),n,null,[[2,9]])})))()},handleCreateNewFlowVersion:function(){var e=this;return(0,r.A)((0,s.A)().mark((function t(){var n;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,it(e.flowCurrentVersion);case 2:n=t.sent,_().isEmpty(n.value)||e.handleVersionItemClick(n.value);case 4:case"end":return t.stop()}}),t)})))()}}),mounted:function(){var e=this;return(0,r.A)((0,s.A)().mark((function t(){return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return window.addEventListener("resize",e.resizeFn),e.submitting=!0,t.next=4,e.fetchFormFields();case 4:return t.next=6,e.fetchVersionList();case 6:e.fetchEdgeConditionOption(),e.initialize();case 8:case"end":return t.stop()}}),t)})))()},destroyed:function(){window.removeEventListener("resize",this.resizeFn)},components:(0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)({},G.name,G),Ee.name,Ee),"NodeConfigPanelMain",Fe.A),"GraphWrapToolActionBar",De.A),"GraphVersionActionBar",je),"GraphNodeActionBar",Ue),"Fragment",qe.F)},at=ot,st=at,rt=(0,z.A)(st,i,o,!1,null,"e66a4c8c",null),lt=rt.exports,ct=lt}}]);