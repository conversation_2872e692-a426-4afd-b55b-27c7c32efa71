"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[214],{56214:function(e,t,i){i.r(t),i.d(t,{default:function(){return F}});var o=function(){var e=this,t=e._self._c;return t("Fragment",[e.isPreview?[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"detail-workflow-wrap full"},[t("div",{attrs:{id:"container"}}),t("div",{staticClass:"detail-workflow-edge-tooltip-box"},[t("el-tooltip",{attrs:{value:e.isShowEdgeTooltip,effect:"light","visible-arrow":!1,"popper-class":"detail-workflow-edge-tooltip",placement:"right",enterable:!0}},[t("div"),t("template",{slot:"content"},[t("div",{staticClass:"condition-list-wrap",on:{mouseenter:e.handleEnterListWrap}},[t("div",{staticClass:"condition-list-header"},[e._v(e._s(e.$t("common.base.condition")))]),"code"!==e.convertType?t("div",e._l(e.conditionsList,(function(i,o){return t("div",{key:o,staticClass:"condition-list-item"},[t("div",{staticClass:"condition-list-item__title"},[e._v(e._s(e.$t("view.designer.workFlow.conditionGroup"))+" "+e._s(o+1))]),i.subCondition?t("div",{staticClass:"condition-list-item__sub_condition"},e._l(i.subCondition,(function(i,o){var n,a,s;return t("div",{key:o,staticClass:"condition-list-item__content"},[0!==o?t("span",{staticClass:"condition-list-item__and"},[e._v(e._s(e.$t("common.connector.text.and")))]):e._e(),t("span",{staticClass:"condition-list-item__displayName"},[e._v(e._s(e.fieldNameFindDisplayName(null===(n=i.content)||void 0===n?void 0:n.fieldName)))]),t("span",{staticClass:"condition-list-item__expression"},[e._v(e._s(e.symbolFindName(i.expression,null===(a=i.content)||void 0===a?void 0:a.fieldName)))]),"out_targetIds"===i.content.fieldName?[t("span",{staticClass:"content-value"},[e._v(" "+e._s(i.target.name)+" ")])]:[t("span",{staticClass:"content-value"},[e._v(e._s(e.formatTargetText(i.target,null===(s=i.content)||void 0===s?void 0:s.fieldName)))])]],2)})),0):e._e()])})),0):t("div",{staticClass:"mar-t-8 mar-b-8 pad-l-8"},[t("i",{staticClass:"el-icon-warning"}),e._v(" 代码格式暂不支持预览 ")])])])],2)],1)])]:t("base-modal",{staticClass:"detail-workflow-dialog",attrs:{title:e.$t("view.template.detail.flowDetail")+e.versionText,show:e.visible,width:"".concat(e.modalWidth,"px")},on:{"update:show":function(t){e.visible=t}}},[e.visible?t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"detail-workflow-wrap"},[e.newCanvas?[t("div",{staticClass:"detail-workflow-box_v2"},[t("flow-process-chart",{attrs:{data:e.newFlowProcessList,"node-through-list":e.logList,readonly:"",vertical:""}})],1)]:[t("div",{attrs:{id:"container"}}),t("div",{staticClass:"detail-workflow-edge-tooltip-box"},[t("el-tooltip",{attrs:{value:e.isShowEdgeTooltip,effect:"light","visible-arrow":!1,"popper-class":"detail-workflow-edge-tooltip",placement:"right",enterable:!0}},[t("div"),t("template",{slot:"content"},[t("div",{staticClass:"condition-list-wrap",on:{mouseenter:e.handleEnterListWrap}},[t("div",{staticClass:"condition-list-header"},[e._v(e._s(e.$t("common.base.condition")))]),"code"!==e.convertType?t("div",e._l(e.conditionsList,(function(i,o){return t("div",{key:o,staticClass:"condition-list-item"},[t("div",{staticClass:"condition-list-item__title"},[e._v(e._s(e.$t("view.designer.workFlow.conditionGroup"))+" "+e._s(o+1))]),i.subCondition?t("div",{staticClass:"condition-list-item__sub_condition"},e._l(i.subCondition,(function(i,o){var n,a,s;return t("div",{key:o,staticClass:"condition-list-item__content"},[0!==o?t("span",{staticClass:"condition-list-item__and"},[e._v(e._s(e.$t("common.connector.text.and")))]):e._e(),t("span",{staticClass:"condition-list-item__displayName"},[e._v(e._s(e.fieldNameFindDisplayName(null===(n=i.content)||void 0===n?void 0:n.fieldName)))]),t("span",{staticClass:"condition-list-item__expression"},[e._v(e._s(e.symbolFindName(i.expression,null===(a=i.content)||void 0===a?void 0:a.fieldName)))]),"out_targetIds"===i.content.fieldName?[t("span",{staticClass:"content-value"},[e._v(" "+e._s(i.target.name)+" ")])]:[t("span",{staticClass:"content-value"},[e._v(e._s(e.formatTargetText(i.target,null===(s=i.content)||void 0===s?void 0:s.fieldName)))])]],2)})),0):e._e()])})),0):t("div",{staticClass:"mar-t-8 mar-b-8 pad-l-8"},[t("i",{staticClass:"el-icon-warning"}),e._v(" 代码格式暂不支持预览 ")])])])],2)],1),t("GraphWrapToolActionBar",{attrs:{"default-data-zoom-value":30},on:{changeZoom:e.handleChangeGraphZoom,scrollToContent:e.handleGraphScrollToContent}})]],2):e._e()])],2)},n=[],a=i(18885),s=i(35730),r=i(42881),l=i(71357),d=(i(67880),i(2286),i(44807),i(3923),i(35256),i(16961),i(54615),i(7354),i(89370),i(32807),i(55650),i(75069),i(39789),i(13262),i(69396)),c=i(74526),u=i(92935),p=i.n(u),v=i(51280),m=(i(35564),i(15902)),f=i(56268),h=i(50651),g=i(84859),w=i(46687),C=i(19702),b=i(44377),_=i(81616),y=i(80602),T=i(19055),k=i(50152),x=i(19572),N=i(46274),A={line:{stroke:"#d9d9d9",strokeWidth:2,targetMarker:{name:"classic",size:8}}},L={name:"work-flow-dialog",components:{GraphWrapToolActionBar:m.A,Fragment:f.F},props:{logList:{type:Array,default:function(){return[]}}},data:function(){return{modalWidth:800,visible:!1,loading:!1,fields:[],conditions:[{name:(0,g.t)("common.base.equal"),val:"==",disable:!1},{name:(0,g.t)("common.base.notEqual"),val:"!=",disable:!1},{name:(0,g.t)("common.base.greaterThan"),val:">",disable:!1},{name:(0,g.t)("view.designer.workFlow.greaterThanOrEqual"),val:">=",disable:!1},{name:(0,g.t)("common.base.lessThan"),val:"<",disable:!1},{name:(0,g.t)("view.designer.workFlow.lessThanOrEqual"),val:"<=",disable:!1}],conditionsList:[],isShowEdgeTooltip:!1,newCanvas:!1,newFlowProcessList:[],graph:null,previewLogList:[],versionText:"",convertType:""}},computed:(0,l.A)((0,l.A)((0,l.A)({},(0,h.aH)(["common"])),(0,h.L8)(["flowDesignNodeCardStyle","flowEdgeSupportConditionFields"])),{},{processId:function(){return this.$route.query.processId||this.common.detailPageParams.processorInstanceId},templateId:function(){return this.$route.query.formId||this.common.detailPageParams.formTemplateId},formContentId:function(){return this.$route.query.formContentId},nodeStatus:function(){var e=p().reverse(p().cloneDeep(p().isEmpty(this.logList)?this.previewLogList:this.logList)),t={};return e.forEach((function(e){var i=e.nodeTemplateId;t[i]=e.isCurrent?y.A.Doing_Node:y.A.Done_Node})),t},nodeThroughIds:function(){return(p().isEmpty(this.logList)?this.previewLogList:this.logList).map((function(e){return e.nodeTemplateId})).reverse()},isNewNodeCardStyle:function(){return this.flowDesignNodeCardStyle===y.A.FULL_NODE_CARD},isPreview:function(){return"preview"===this.$route.query.mode}}),watch:{visible:function(){this.isShowEdgeTooltip=!1,this.setTimeoutInc&&clearTimeout(this.setTimeoutInc)}},methods:(0,l.A)((0,l.A)((0,l.A)({},(0,h.i0)({fetchEdgeConditionOption:"design/initFlowEdgeSupportConditionOptions"})),(0,h.PY)({updateNodeCardStyle:"design/".concat(k.Wd)})),{},{handleChangeGraphZoom:p().debounce((function(e,t){var i=this;if(this.graph){var o=this.graph.zoom(),n=(e-50)/100-(o-1);this.$nextTick((function(){i.graph.zoom(Math.ceil(100*n)/100)}))}}),10),handleGraphScrollToContent:function(){this.graph.scrollToContent()},open:function(){var e=this;this.visible=!0,this.$nextTick((function(){var t=document.body.clientHeight,i=t-150;e.isPreview?document.querySelector(".detail-workflow-wrap").style.height="100vh":document.querySelector(".detail-workflow-wrap").style.height=i+"px",e.fetchEdgeConditionOption(),e.initialize(i)}))},initialize:function(e){var t=this;return(0,r.A)((0,a.A)().mark((function i(){var o,n,r,u,p,m,f,h,g,T,k,x,N,L,I,S,E;return(0,a.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,t.loading=!0,i.next=4,c.QA({templateBizId:t.templateId});case 4:return o=i.sent,o.success&&(n=o.data||{},r=n.paasFormFieldVOList,u=void 0===r?[]:r,p=n.systemFormFieldVOList,m=void 0===p?[]:p,t.fields=[].concat((0,s.A)(u),(0,s.A)(m))),i.next=8,d.getProcessByProcessId({processorInstanceId:t.processId});case 8:if(f=i.sent,f.success){i.next=11;break}return i.abrupt("return",t.$platform.toast(f.message,"error"));case 11:h=f.data||{},g=h.attribute,T=h.newCanvas,k=void 0!==T&&T,x=h.nodeStyle,N=void 0===x?y.A.NORMAL_NODE_CARD:x,L=h.newVersion,t.versionText=L?"（流程版本".concat(L,"）"):"",t.updateNodeCardStyle(N),t.newCanvas=k,I=JSON.parse(g||"{}"),k?t.newFlowProcessList=(0,w.i5)(I.cells,t.fields):(S=I.cells.map((function(e){if("edge"==e.shape){var i,o=null===e||void 0===e||null===(i=e.target)||void 0===i?void 0:i.cell,n=!t.nodeStatus[o];n&&(e.attrs=A);var a=new b.Ay(e);return(0,C.D)((null===e||void 0===e?void 0:e.data)||{})?a.label=(0,l.A)((0,l.A)({},a.label),{},{markup:v.VK.getForeignObjectMarkup(),attrs:{fo:{width:24,height:6,x:-12,y:-3}}}):(Reflect.deleteProperty(a.label,"markup"),Reflect.deleteProperty(a.label,"attrs")),a}return e.data.originShape=e.shape,e.shape!==y.A.END_NODE&&(e.shape=t.nodeStatus[e.id]||y.A.UnDone_Node),new _.A(e,t.isNewNodeCardStyle)})),E=new v.ke({container:document.getElementById("container"),minimap:{enabled:!0,container:document.querySelector(".graph-minimap-box"),width:200,height:100,scalable:!1,padding:10,graphOptions:{async:!0}},width:t.isPreview?document.body.offsetWidth:800,height:t.isPreview?document.body.offsetHeight:e,scroller:{enabled:!0},mousewheel:{enabled:t.isPreview},grid:{visible:!0,type:"fixedDot",size:10},panning:!0,interacting:function(){return{nodeMovable:!1,edgeLabelMovable:!1,edgeMovable:!1}},connecting:{anchor:"center",connectionPoint:"anchor"},onEdgeLabelRendered:function(e){var t=e.selectors,i=e.label.background,o=void 0===i?"#8C8C8C":i,n=e.edge;n.setAttrs({line:{stroke:"#8C8C8C",strokeWidth:2}},{ignoreHistory:!0});var a=t.foContent;if(a){var s=document.createElement("div");s.className="edge-label-box",s.appendChild(document.createTextNode("")),s.style.width="100%",s.style.height="100%",s.style.borderRadius="3px",s.style.background=o,a.appendChild(s)}}}),E.fromJSON(S||[]),E.zoom(-.2),E.on("edge:mouseenter",(function(e){var i,o,n,a=e.e,s=e.cell,r=e.view;if(t.isShowEdgeTooltip=!1,t.conditionsList=null!==(i=s.data)&&void 0!==i&&i.isCondition&&(null===(o=s.data)||void 0===o?void 0:o.conditionObject)||[],t.convertType=null===(n=s.data)||void 0===n?void 0:n.convertType,(0,C.D)((null===s||void 0===s?void 0:s.data)||{})){var l=r.containers.labels.querySelector(".edge-label-box");l&&(l.style.background="#16C2C1"),s.setAttrs({line:{stroke:"#16C2C1"}},{ignoreHistory:!0});var d=document.querySelector(".detail-workflow-edge-tooltip-box"),c=d.style,u=document.body.clientWidth;c.left="".concat(a.clientX-(u-t.modalWidth)/2,"px"),c.top="".concat(a.clientY-50,"px"),t.setTimeoutInc&&clearTimeout(t.setTimeoutInc),t.setTimeoutInc=setTimeout((function(){return t.$nextTick((function(){return t.isShowEdgeTooltip=!0}))}),200)}})),E.on("edge:mouseleave",(function(e){e.e;var i,o=e.cell,n=e.view;if((null===(i=t.selectEdge)||void 0===i?void 0:i.id)!==o.id){o.setAttrs({line:{stroke:"#8C8C8C"}},{ignoreHistory:!0});var a=n.containers.labels.querySelector(".edge-label-box");a&&(a.style.background="#8C8C8C")}var s=document.querySelector(".detail-workflow-edge-tooltip-box"),r=null===s||void 0===s?void 0:s.style;r&&(r.left="-1000px",r.top="-1000px",t.setTimeoutInc&&clearTimeout(t.setTimeoutInc),t.setTimeoutInc=setTimeout((function(){return t.$nextTick((function(){return t.isShowEdgeTooltip=!1}))}),200))})),t.graph=E,t.$nextTick((function(){return E.scrollToContent()}))),t.loading=!1,i.next=24;break;case 20:i.prev=20,i.t0=i["catch"](0),t.loading=!1,console.error("workflow dialog initialize error: ",i.t0);case 24:case"end":return i.stop()}}),i,null,[[0,20]])})))()},symbolFindName:function(e,t){return e?(0,x.z8)(e,t).name:""},fieldNameFindDisplayName:function(e){if(!e)return"";var t=this.flowEdgeSupportConditionFields.find((function(t){return t.fieldName==e}))||{};return t.displayName||""},handleEnterListWrap:function(){this.setTimeoutInc&&clearTimeout(this.setTimeoutInc)},fetchProcessLog:function(){var e=this;if(this.processId)return c.O0({contentBizId:this.formContentId},!0).then((function(t){if(t.success){var i=t.data||{},o=i.createUser,n=i.createTime,a=i.nodeLogList,r=void 0===a?[]:a;r.forEach((function(e){if(e.collapse=!1,e.nodeType==y.A.START_NODE){var t=T.A.SUBMIT,i=t.name,a=t.code;e.operate=i,e.operateCode=a,e.userIds=[o],e.taskLogVOList=[{approveResult:i,completeTime:n,user:o}]}}));var l=r.filter((function(e){return e.isCurrent})),d=r.filter((function(e){return!e.isCurrent}));l.length>1&&(r=[{isCurrent:!0,isMulti:!0}].concat((0,s.A)(d))),e.previewLogList=r}}))["catch"]((function(e){return console.error(e)}))},formatTargetText:function(e,t){var i=this.flowEdgeSupportConditionFields.find((function(e){return e.fieldName==t}))||{};return(0,N.C)(e,i)}}),mounted:function(){var e=this;return(0,r.A)((0,a.A)().mark((function t(){return(0,a.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isPreview){t.next=4;break}return t.next=3,e.fetchProcessLog();case 3:e.open();case 4:case"end":return t.stop()}}),t)})))()}},I=L,S=i(49100),E=(0,S.A)(I,o,n,!1,null,null,null),F=E.exports}}]);