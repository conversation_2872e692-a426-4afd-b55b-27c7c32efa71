"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[727],{15727:function(t,e,s){s.r(e),s.d(e,{default:function(){return A}});var i=function(){var t,e,s,i,a=this,l=a._self._c;return l("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:a.loading,expression:"loading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"print-box"},[l("el-button",{directives:[{name:"print",rawName:"v-print",value:a.printOption,expression:"printOption"}],staticStyle:{"margin-bottom":"10px"},attrs:{id:"print-button",size:"small",icon:"iconfont icon-printer"}},[a._v(" "+a._s(a.$t("common.base.print"))+" ")]),l("div",{staticClass:"settlement-sheet",attrs:{id:"printMain"}},[l("h1",[a._v("Carrier Transicold Warranty Cost")]),l("div",{staticClass:"title"},[l("p",{staticClass:"title-msg"},[a._v(" BP："),l("span",[a._v(a._s(a.detailValueBp||""))])])]),l("table",{staticStyle:{"table-layout":"fixed"}},[a._m(0),l("tr",[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算单号")]),l("td",[l("span",{staticClass:"serial-number"},[a._v(a._s(a.detailValue.serialNumber))])]),l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算对象")]),l("td",[a._v(a._s(a.detailValue.settleObject))]),"服务商"==a.detailValue.settleObject?[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算对象名称(服务商)")]),l("td",{staticStyle:{width:"10%"}},[a._v(a._s((null===(t=a.detailValue.serviceProvider)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.name)||""))])]:"内部团队"==a.detailValue.settleObject?[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算对象名称(部门)")]),l("td",{staticStyle:{width:"10%"}},[a._v(a._s((null===(e=a.detailValue.settleDepartment)||void 0===e||null===(e=e[0])||void 0===e?void 0:e.name)||""))])]:"人员"==a.detailValue.settleObject?[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算对象名称(人员)")]),l("td",{staticStyle:{width:"10%"}},[a._v(a._s((null===(s=a.detailValue.settleUser)||void 0===s||null===(s=s[0])||void 0===s?void 0:s.displayName)||""))])]:[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算对象名称")]),l("td",{staticStyle:{width:"10%"}})]],2),l("tr",[l("td",{staticClass:"bg-gray",staticStyle:{width:"10%"}},[a._v("结算单据数量")]),l("td",[a._v(a._s(a.detailValue.settleReceiptsCount))]),l("td",{staticClass:"bg-gray",staticStyle:{width:"15%"}},[a._v("结算项目数量")]),l("td",[a._v(a._s(a.detailValue.settleItemCount))]),l("td",{staticClass:"bg-gray",staticStyle:{width:"20%"}},[a._v("付款方式")]),l("td",[a._v(a._s(a.detailValue.payType))])]),l("tr",[l("td",{staticClass:"bg-gray"},[a._v("申请备注")]),l("td",{attrs:{colspan:"5"}},[a._v(a._s(a.detailValue.applyRemark))])]),l("tr",[l("td",{staticClass:"bg-gray"},[a._v("创建人")]),l("td",[a._v(a._s((null===(i=a.detailValue)||void 0===i||null===(i=i.CREATE_USER)||void 0===i?void 0:i.displayName)||""))]),l("td",{staticClass:"bg-gray"},[a._v("创建时间")]),l("td",{attrs:{colspan:"3"}},[a._v(a._s(a.detailValue.CREATE_TIME))])])]),a._m(1),a._l(a.taskInfo,(function(t,e){return l("div",{key:e,staticClass:"table-wrap"},[l("div",{staticClass:"detail-wrap"},[l("table",[a._m(2,!0),l("tbody",[l("tr",[l("td",[a._v(a._s(t.taskNo))]),l("td",[a._v(a._s(t.productNo))]),l("td",[a._v(a._s(t.productDesc))]),l("td",[a._v(a._s(t.materialExpenseCost||""))]),l("td",[a._v(a._s(t.processExpenseCost||""))]),l("td",[a._v(a._s(t.travelExpenseCost))])])])])]),l("div",{staticClass:"detail-wrap manual-wrap"},[a._m(3,!0),l("table",[a._m(4,!0),l("tbody",[a._l(t.manualItems,(function(t){return a._l(t.details,(function(e,s){return l("tr",{key:t.name+"-"+s},[0===s?l("td",{attrs:{rowspan:t.details.length}},[a._v(a._s(t.name))]):a._e(),l("td",[a._v(a._s(e.serialNumber))]),l("td",[a._v(a._s(e.describe))]),l("td",[a._v(a._s(e.number))]),l("td",[a._v(a._s(e.salePrice))]),l("td",[a._v(a._s(e.subtotal))])])}))}))],2)])])])})),l("div",{staticClass:"settle-total"},[l("div",{staticClass:"settle-detail"},[a._v(" 结算项合计： "),a._l(a.detailValue.settleItemDetails,(function(t,e){return l("span",{key:e},[t.settleItemAmount>0?[a._v(" "+a._s(t.settleItem)+"："+a._s(t.settleItemAmount)+"元； ")]:a._e()],2)}))],2)]),l("div",{staticClass:"settle-total"},[l("p",[a._v("总合计："+a._s(a.detailValue.settleAmountTotal))]),l("p",[a._v("总税额："+a._s(a.settleInCount("taxAmount")))]),l("p",[a._v("总价税合计："+a._s(a.settleInCount("settleAmount")))])]),a._m(5)],2)],1)},a=[function(){var t=this,e=t._self._c;return e("colgroup",[e("col",{staticStyle:{width:"90px"}}),e("col",{staticStyle:{width:"160px"}}),e("col",{staticStyle:{width:"100px"}}),e("col",{staticStyle:{width:"100px"}}),e("col",{staticStyle:{width:"150px"}}),e("col",{staticStyle:{width:"150px"}})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"detail",staticStyle:{display:"grid","grid-template-columns":"1fr 2fr"}},[e("h2",{staticClass:"detail-h2"},[t._v("结算明细")])])},function(){var t=this,e=t._self._c;return e("thead",[e("tr",[e("th",[t._v("工单编号")]),e("th",[t._v("机组序列号")]),e("th",[t._v("机组型号")]),e("th",[t._v("材料费用")]),e("th",[t._v("工序费用")]),e("th",[t._v("差旅费用")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"detail detail-title"},[e("h2",{staticClass:"detail-h2"},[t._v("结算明细")])])},function(){var t=this,e=t._self._c;return e("thead",[e("tr",[e("th",[t._v("结算项")]),e("th",[t._v("代码")]),e("th",[t._v("描述")]),e("th",[t._v("数量")]),e("th",[t._v("单价")]),e("th",[t._v("小计")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer-page"},[e("div",{staticClass:"flex"},[e("p",{staticClass:"info"},[e("span",{staticClass:"msg"},[t._v("Prepared by： ")]),e("span",{staticClass:"line"})]),e("p",{staticClass:"info"},[e("span",{staticClass:"msg"},[t._v("Approved By：")]),t._v(" "),e("span",{staticClass:"line"})]),e("p",{staticClass:"info"},[e("span",{staticClass:"msg label"},[t._v("财务部：")]),t._v(" "),e("span",{staticClass:"line"})])]),e("div",{staticClass:"flex"},[e("p",{staticClass:"info"},[e("span",{staticClass:"msg"},[t._v("日期：")]),e("span",{staticClass:"line"})]),e("p",{staticClass:"info"},[e("span",{staticClass:"msg"},[t._v("日期：")]),e("span",{staticClass:"line"})]),e("p",{staticClass:"info"},[e("span",{staticClass:"msg label"},[t._v("日期：")]),e("span",{staticClass:"line"})])])])}],l=s(71357),n=s(18885),r=s(42881),d=(s(67880),s(44807),s(3923),s(35256),s(21484),s(16961),s(7354),s(89370),s(32807),s(75069),s(13262),s(74526)),o=s(22229);function u(t){return o.A.post("/api/middleware/outside/kailiCold/settle/getSettleItemDetail",t)}var c=s(80906),v=s(50378),m={devFields:{settleAmount:"fieldyTKBjc0XX2zAclq41lZw35sFR3hGbNYj",taxAmount:"fieldhGgsyVodkiMPOAiMYWlGHB3NUq4ufQcK"},produceFields:{settleAmount:"fieldssVQnfcKTYBgoqRH5lr2R5BPtD7ci0Bl",taxAmount:"fieldl2qITZct2UA2Rq335dwL6fpGGeh9utv8"}},p={devFields:{serialNumber:"fieldk8ssVnZwrjZB3m1E7RSd69MbQ1g68zBB",describe:"fieldT6jkKLZcS4U3lJQZKcX5ICFd5AwtYFXX",number:"field_DNogqppoibXn59ej",salePrice:"fieldWdHGHbcRcUYQl9YcBmuJulxtjmKYdygx",subtotal:"field_cQ0dPBPUNDANETUt"},produceFields:{serialNumber:"field5naOlwauSBDMkBuSEieJYitfgQv7WF43",describe:"fieldrMRfgzJZptHPFVqGmg5WM5lvKlwSdmS5",number:"field_mawLZs6xmITUiOWM",salePrice:"fieldylGJkWCGRvRvbZ4ttU7eqxsZXrJgNO7O",subtotal:"field_AbXKvrKBPwZcbQ4Y"}},_={devFields:{salePrice:"fieldnIdZQvCVsop3kbvKURCxMBFkkVTGGp4r",subtotal:"field6YmYPZKLi8KowEoSqy35TEwLCEDVh3Vi"},produceFields:{salePrice:"fieldvWPjqQ2cs9FdO8MDjNaiO4JhGvPzKxGY",subtotal:"fieldqiRDOWTH9NrA2kmpMiuiZjDrgttOXdaa"}},f={devFields:{serialNumber:"",describe:"field_a34WizW3u8g5LJFA",number:"",salePrice:"",subtotal:"field_zWyndUSjGX5eYOqz"},produceFields:{serialNumber:"",describe:"field_a34WizW3u8g5LJFA",number:"",salePrice:"",subtotal:"field_RnWxMqh5eJZaRc4Q"}};function h(){var t=(0,v.T)();return t.env||"multi_prod"}function b(){var t=h();return"multi_prod"==t?{productionProcessesFields:p.produceFields,materialFields:_.produceFields,travelFields:f.produceFields}:{productionProcessesFields:p.devFields,materialFields:_.devFields,travelFields:f.devFields}}function C(){var t=h();return"multi_prod"==t?{settleAmount:m.produceFields.settleAmount,taxAmount:m.produceFields.taxAmount}:{settleAmount:m.devFields.settleAmount,taxAmount:m.devFields.taxAmount}}var g=s(92648),y={name:"SettlementSheet",data:function(){return{loading:!0,printOption:{id:"printMain",popTitle:"&nbsp;"},systemFields:[],customFields:[],detailValue:{},taskInfo:[],templateName:""}},computed:{templateId:function(){return this.$route.query.formId},nodeInstanceId:function(){return this.$route.query.nodeInstanceId},formContentId:function(){return this.$route.query.formContentId},processId:function(){return this.$route.query.processId},detailValueBp:function(){var t,e=null===(t=this.taskInfo)||void 0===t?void 0:t.find((function(t){return t.bp}));return e?e.bp:""}},mounted:function(){this.initialize()},methods:{initPdfName:function(){var t,e=this,s=this.detailValue.CREATE_TIME.split(" ")[0],i={"服务商":function(){var t;return(null===(t=e.detailValue)||void 0===t||null===(t=t.serviceProvider)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.name)||""},"内部团队":function(){var t;return(null===(t=e.detailValue)||void 0===t||null===(t=t.settleDepartment)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.name)||""},"人员":function(){var t;return(null===(t=e.detailValue)||void 0===t||null===(t=t.settleUser)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.displayName)||""}},a=(null===(t=i[this.detailValue.settleObject])||void 0===t?void 0:t.call(i))||"";document.title=a?"".concat(a," ").concat(s):this.templateName},settleInCount:function(t){var e,s,i=(null===(e=C())||void 0===e?void 0:e[t])||"";return(null===(s=this.detailValue)||void 0===s?void 0:s[i])||""},initialize:function(){var t=this;return(0,r.A)((0,n.A)().mark((function e(){return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.loading=!0,e.next=4,t.fetchFields();case 4:return e.next=6,t.fetchFormData();case 6:return e.next=8,t.getSettleItemDetailReq();case 8:t.loading=!1,t.initPdfName(),e.next=16;break;case 12:e.prev=12,e.t0=e["catch"](0),console.log(e.t0),t.loading=!1;case 16:case"end":return e.stop()}}),e,null,[[0,12]])})))()},fetchFields:function(t){var e=this;return(0,r.A)((0,n.A)().mark((function s(){var i,a,l,r,o,u,c,v,m,p;return(0,n.A)().wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.prev=0,i={contentBizId:e.formContentId,nodeInstanceId:t,excludeConnectOption:!0},s.next=4,d.Tw(i,!0);case 4:a=s.sent,l=a.data,r=a.success,o=a.message,r?(u=l||{},c=u.paasFormFieldVOList,v=void 0===c?[]:c,m=u.systemFormFieldVOList,p=void 0===m?[]:m,e.systemFields=p||[],e.customFields=v||[]):e.$message.warning(o),s.next=14;break;case 11:s.prev=11,s.t0=s["catch"](0),console.log(s.t0);case 14:case"end":return s.stop()}}),s,null,[[0,11]])})))()},fetchFormData:function(){var t=this;return(0,r.A)((0,n.A)().mark((function e(){var s,i,a,r,o;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,d.Ev({contentBizId:t.formContentId});case 3:s=e.sent,i=s.data,a=void 0===i?{}:i,r=c.n_(t.customFields,(0,g.Sq)((null===a||void 0===a?void 0:a.paasFormValueList)||[])),o=(0,g.bI)(t.systemFields,a||{}),t.detailValue=(0,l.A)((0,l.A)({},r),o),t.templateName="".concat(a.templateName," ").concat(t.detailValue.serialNumber),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](0),console.log(e.t0);case 15:case"end":return e.stop()}}),e,null,[[0,12]])})))()},getSettleItemDetailReq:function(){var t=this;return(0,r.A)((0,n.A)().mark((function e(){var s,i,a,l;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,s={settleFormNo:t.detailValue.serialNumber},e.next=4,u(s);case 4:i=e.sent,a=i.success,l=i.data,a&&(t.taskInfo=l||[],t.handelDetail()),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),console.log(e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})))()},handelDetail:function(){var t=this;console.log(b()),this.taskInfo.forEach((function(e){var s,i,a,l,n=[],r=null!==(s=null===e||void 0===e?void 0:e.materialExpenseDetailList)&&void 0!==s?s:[];if((null===r||void 0===r?void 0:r.length)>0){var d=r.map((function(t){var e=t.serialNumber,s=t.name,i=t.number,a=(t.salePrice,t.attribute);Object.assign(t,a||{});var l=b().materialFields;return{serialNumber:e,describe:s,number:i,salePrice:(null===t||void 0===t?void 0:t[l.salePrice])||"",subtotal:(null===t||void 0===t?void 0:t[l.subtotal])||""}}));n.push({name:"材料",details:d})}var o=null!==(i=null===e||void 0===e||null===(a=e.travelExpenseDetailList)||void 0===a||null===(a=a[0])||void 0===a?void 0:a.attribute)&&void 0!==i?i:[];if((null===o||void 0===o?void 0:o.length)>0){console.log(b());var u=o.map((function(e){var s=b().travelFields;return t.handleData(e,s)}));n.push({name:"差旅",details:u})}var c=null!==(l=null===e||void 0===e?void 0:e.processExpenseCostDetailList)&&void 0!==l?l:[];if((null===c||void 0===c?void 0:c.length)>0){var v=c.map((function(e){e.serialNumber,e.salePrice;var s=b().productionProcessesFields;return t.handleData(e,s)}));n.push({name:"工序",details:v})}e.manualItems=n}))},handleData:function(t,e){return{serialNumber:(null===t||void 0===t?void 0:t[e.serialNumber])||"",describe:(null===t||void 0===t?void 0:t[e.describe])||"",number:(null===t||void 0===t?void 0:t[e.number])||"",salePrice:(null===t||void 0===t?void 0:t[e.salePrice])||"",subtotal:(null===t||void 0===t?void 0:t[e.subtotal])||""}},toPrint:function(){setTimeout((function(){document.getElementById("print-button").click()}),100)}}},w=y,F=s(49100),x=(0,F.A)(w,i,a,!1,null,"42994bfe",null),A=x.exports}}]);