"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[914],{10888:function(e,t,n){n.d(t,{A:function(){return c}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"tag"},[e.prefix?t("div",{staticClass:"prefix-icon"},[e._t("prefix-icon",(function(){return[t("i",{staticClass:"iconfont icon-fdn-user prefix-icon"})]}))],2):e._e(),t("span",{staticClass:"txt",style:{marginRight:e.closable?"16px":"0",marginLeft:e.prefix?"10px":"0"}},[e._t("default")],2),e.closable?t("div",{staticClass:"end-icon-box",on:{click:e.handleDel}},[e.isNeedTagSlot?[e._t("end-icon",(function(){return[t("i",{staticClass:"iconfont icon-circle-delete end-icon"})]}))]:[t("i",{staticClass:"iconfont icon-circle-delete end-icon"})]],2):e._e()])},i=[],s={name:"Tag",props:{closable:{type:Boolean,default:function(){return!1}},prefix:{type:Boolean,default:function(){return!1}},isNeedTagSlot:{type:Boolean,default:function(){return!0}}},methods:{handleDel:function(){this.$emit("close")}}},r=s,o=n(49100),l=(0,o.A)(r,a,i,!1,null,"6a3821fc",null),c=l.exports},32914:function(e,t,n){n.d(t,{A:function(){return Ye}});var a,i,s=n(71357),r=(n(42925),n(20592),function(){var e=this,t=e._self._c;return t("div",{staticClass:"config-contact-panel"},[t("div",{staticClass:"result-set"},[e.checked.length?[e.isNeedTagSlot?[e._t("tag",(function(){return e._l(e.checked,(function(n,a){return t("custom-tag",{key:a,staticClass:"result-tag",attrs:{closable:e.isShowClosable(n),prefix:""},on:{close:function(t){return e.handleDelete(n,a)}}},["1"===n.type?t("i",{staticClass:"iconfont icon-fdn-user",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"2"===n.type?t("i",{staticClass:"iconfont icon-apartment",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),"3"===n.type?t("i",{staticClass:"iconfont icon-idcard",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),e.isServerProvideDept(n)?t("i",{staticClass:"iconfont icon-fdn-serviceProvider",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):e._e(),e.isOpenData&&n.staffId?[t("open-data",{attrs:{type:"userName",openid:n.staffId}})]:[e._v(" "+e._s(n.name)+" ")]],2)}))}))]:e._l(e.checked,(function(n,a){return t("custom-tag",{key:a,staticClass:"result-tag",attrs:{"is-need-tag-slot":e.isNeedTagSlot,closable:e.isShowClosable(n),prefix:""},on:{close:function(t){return e.handleDelete(n,a)}}},["1"===n.type?t("i",{staticClass:"iconfont icon-fdn-user",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):"2"===n.type?t("i",{staticClass:"iconfont icon-apartment",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):"3"===n.type?t("i",{staticClass:"iconfont icon-idcard",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}):t("i",{staticClass:"iconfont icon-fdn-user",attrs:{slot:"prefix-icon"},slot:"prefix-icon"}),e.isOpenData&&n.staffId?[t("open-data",{attrs:{type:"userName",openid:n.staffId}})]:[e._v(" "+e._s(n.name)+" ")]],2)}))]:t("span",{staticClass:"default"},[e._v(e._s(e.$t("view.designer.workFlow.selectContentBelow")))])],2),t("div",{staticClass:"contact-tabs-panel"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.showInputEnable,expression:"showInputEnable"}],staticClass:"search-panel"},[t("i",{staticClass:"el-icon-search"}),t("input",{directives:[{name:"model",rawName:"v-model",value:e.keyword,expression:"keyword"}],attrs:{type:"text",placeholder:e.$t("common.base.search")},domProps:{value:e.keyword},on:{input:[function(t){t.target.composing||(e.keyword=t.target.value)},e.search],compositionstart:e.compositionstart,compositionend:e.compositionend}}),e.keyword?t("i",{staticClass:"clear el-icon-error",on:{click:e.handleClear}}):e._e()]),t("el-tabs",{staticClass:"tabs",model:{value:e.currTab,callback:function(t){e.currTab=t},expression:"currTab"}},e._l(e.tabs,(function(n){return t("el-tab-pane",{key:n.value,attrs:{label:n.displayName,name:n.value}},[t(n.component,{ref:"tabPane".concat(n.value),refInFor:!0,tag:"component",attrs:{"flow-api":e.flowApi,"share-data":(0,s.A)((0,s.A)({},e.propsForSubComponents),{},{type:n.value,isMulti:n.isMulti})},on:{update:e.update}})],1)})),1)],1)])}),o=[],l=n(62361),c=(n(2286),n(62838),n(21484),n(27408),n(16961),n(54615),n(75069),function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"department-user-panel"},[t("div",{staticClass:"department"},[t("base-tree",{attrs:{expand:"",data:e.depts,"node-render":e.nodeRender,selected:e.selectedDept},on:{"node-selected":e.chooseDept}})],1),t("div",{directives:[{name:"loadmore",rawName:"v-loadmore",value:e.loadmoreOptions,expression:"loadmoreOptions"}],class:["user",{"search-container":e.isSearch}]},[e.userPage.list.length?t("select-all-btn",{attrs:{isSelectAll:e.isSelectAll},on:{selectAll:e.selectAllHandler}}):e._e(),e.userPage.list.length?t("el-tree",{ref:"contactTree",attrs:{props:{label:"displayName",disabled:e.setDisable},data:e.userPage.list,"node-key":"id","show-checkbox":"","check-on-click-node":"","default-checked-keys":e.value},on:{"check-change":e.chooseUser},scopedSlots:e._u([{key:"default",fn:function(n){var a=n.node,i=n.data;return t("span",{},[e.isOpenData&&i.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:[e._v(" "+e._s(a.label)+" ")]],2)}}],null,!1,**********)}):e._e()],1)])}),d=[],u=(n(87313),n(3923),n(80793),n(35256),n(14126),n(89370),n(32807),n(21633),n(13262),n(57698)),h=function(){var e=this,t=e._self._c;return t("div",{staticClass:"select-all-btn"},[t("el-checkbox",{attrs:{value:e.isSelectAll},on:{change:e.selectAllHandler}},[e._v(e._s(e.$t("common.base.selectAll")))])],1)},p=[],f={name:"select-all-btn",props:{isSelectAll:{type:Boolean,default:!1}},data:function(){return{}},methods:{selectAllHandler:function(e){this.$emit("selectAll",e)}}},v=f,m=n(49100),y=(0,m.A)(v,h,p,!1,null,"37b50f13",null),g=y.exports,b={name:"config-contact-dept-user",components:{SelectAllBtn:g},props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,isSearch:!1,depts:[],userPage:new u.A,selectedDept:{},params:{keyword:"",tagId:"",pageNum:1,pageSize:20},loadmoreOptions:{disabled:!1,callback:this.loadmore},disableValues:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isOpenData:function(){return this.shareData.isOpenData},isSelectAll:function(){var e,t=this;return!(null===(e=this.userPage.list)||void 0===e||!e.length)&&this.userPage.list.every((function(e){return t.value.includes(e.id)}))}},watch:{value:{handler:function(e){var t;null===(t=this.$refs.contactTree)||void 0===t||t.setCheckedKeys(e),this.shareData.isMulti||0!==e.length||(this.disableValues=[])},deep:!0}},mounted:function(){this.fetchDept()},methods:{fetchDept:function(){var e=this;return this.flowApi.tagV2List({seeAllOrg:!1}).then((function(t){e.depts=t&&t.list||[],e.chooseDept(t.list[0])}))["catch"]((function(e){return console.error("err",e)}))},search:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.isSearch=e,this.chooseDept(this.selectedDept)},chooseDept:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selectedDept=e,this.params.pageNum=1,this.params.keyword=this.shareData.keyword,this.params.tagId=e.parentId?e.id:"",this.params.type=e.parentId?null:e.type,this.userPage.list=[],this.fetchUser(this.params)},fetchUser:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.loading=!0,this.loadmoreOptions.disabled=!0,this.flowApi.userList(t).then((function(t){(t.list||[]).forEach((function(e){e.label=e.displayName,e.id=e.userId})),e.userPage.merge(u.A.as(t)),(e.disableValues.length>0||!e.shareData.isMulti&&e.value.length>0)&&e.isSingleSelectFilterIds(e.value[0])}))["finally"]((function(){e.loadmoreOptions.disabled=!e.userPage.hasNextPage,e.loading=!1}))["catch"]((function(e){return console.log(e)}))},loadmore:function(){this.params.pageNum+=1,this.fetchUser(this.params)},chooseUser:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{id:e.userId,name:e.displayName});this.shareData.isMulti||(t?this.isSingleSelectFilterIds(n.id):this.disableValues=[]),this.$emit("update",{data:n,checked:t,type:this.shareData.type})},isSingleSelectFilterIds:function(e){this.disableValues=this.userPage.list.filter((function(t){return t.userId!==e})).map((function(e){return e.id}))},setDisable:function(e,t){if(this.shareData.disableValues.includes(e.id)||this.disableValues.includes(e.id))return!0},nodeRender:function(e,t){return e("span",[t.tagName])},selectAllHandler:function(e){var t,n=this,a=this.shareData.disableValues,i=null===(t=this.userPage)||void 0===t?void 0:t.list.filter((function(e){return!a.includes(e.id)}));i.map((function(t){n.chooseUser(t,e)}))}}},A=b,k=(0,m.A)(A,c,d,!1,null,"341a2241",null),D=k.exports,w=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"department-user-panel"},[t("div",{staticClass:"department"},[e.depts.length?t("select-all-btn",{attrs:{isSelectAll:e.isSelectAll},on:{selectAll:e.selectAllHandler}}):e._e(),t("el-tree",{ref:"ProviderRole",attrs:{props:{label:"name",disabled:e.setDisable},"highlight-current":"",data:e.depts,"node-key":"id","show-checkbox":!0,"default-checked-keys":e.value},on:{"check-change":e.chooseDept},scopedSlots:e._u([{key:"default",fn:function(n){n.node;var a=n.data;return t("span",{},[e._v(" "+e._s(a.name)+" ")])}}])})],1)])},S=[],T={name:"config-contact-service-provider-role",components:{SelectAllBtn:g},props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,isSearch:!1,depts:[],userPage:new u.A,selectedDept:{},disableValues:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isOpenData:function(){return this.shareData.isOpenData},isSelectAll:function(){var e=this;return this.depts.every((function(t){return e.value.includes(t.id)}))}},watch:{value:{handler:function(e){var t;null===(t=this.$refs.ProviderRole)||void 0===t||t.setCheckedKeys(e),this.shareData.isMulti||0!==e.length||(this.disableValues=[])},deep:!0}},mounted:function(){this.fetchDept()},methods:{fetchDept:function(){var e=this;return this.flowApi.getServiceRoleTabList({seeAllOrg:!1}).then((function(t){e.depts=t&&t.data.children[0].children||[]}))["catch"]((function(e){return console.error("err",e)}))},chooseDept:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{name:e.name}),a={subType:3};n.extend=a,this.$emit("update",{data:n,checked:t,type:this.shareData.type})},search:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.isSearch=e,this.chooseDept(this.selectedDept)},isSingleSelectFilterIds:function(e){this.disableValues=this.depts.filter((function(t){return t.userId!==e})).map((function(e){return e.id}))},setDisable:function(e,t){if(this.shareData.disableValues.includes(e.id)||this.disableValues.includes(e.id))return!0},nodeRender:function(e,t){return e("span",[t.name])},selectAllHandler:function(e){var t=this,n=this.shareData.disableValues,a=this.depts.filter((function(e){return!n.includes(e.id)}));a.map((function(n){t.chooseDept(n,e)}))}}},N=T,C=(0,m.A)(N,w,S,!1,null,"6aaa07b2",null),_=C.exports,x=function(){var e=this,t=e._self._c;return t("div",{staticClass:"department-panel"},[e.flatDepts.length?t("select-all-btn",{attrs:{isSelectAll:e.isSelectAll},on:{selectAll:e.selectAllHandler}}):e._e(),t("el-tree",{ref:"contactTree",attrs:{props:{label:"tagName",disabled:e.setDisable},data:e.depts,"node-key":"id","show-checkbox":"","check-strictly":"","highlight-current":"","default-expand-all":"","check-on-click-node":"","expand-on-click-node":!1,"default-checked-keys":e.value},on:{"check-change":e.chooseDept}})],1)},P=[],I=n(18885),M=n(42881),E=n(35730),R=(n(67880),n(92935)),O=n.n(R),L={name:"config-contact-department",components:{SelectAllBtn:g},props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{depts:[],disableArr:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isSelectAll:function(){var e=this;return this.flatDepts.every((function(t){return e.value.includes(t.id)}))},flatDepts:function(){var e=function(t){var n;return[t].concat((0,E.A)(null!==(n=t.children)&&void 0!==n&&n.length?(0,R.flatMapDeep)(t.children,e):[]))};return(0,R.flatMapDeep)(this.depts,e)}},watch:{value:{handler:function(e){var t;null===(t=this.$refs.contactTree)||void 0===t||t.setCheckedKeys(e)},deep:!0}},mounted:function(){var e=this;return(0,M.A)((0,I.A)().mark((function t(){return(0,I.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.search();case 2:case"end":return t.stop()}}),t)})))()},methods:{search:function(){var e=this,t={seeAllOrg:!1,keyword:this.shareData.keyword};return this.flowApi.tagV2List(t).then((function(t){e.depts=t&&t.list||[]}))["catch"]((function(e){return console.error("err",e)}))},chooseDept:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{name:e.tagName});this.$emit("update",{data:n,checked:t,type:this.shareData.type})},getDisableDataList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:(0,E.A)(this.depts),n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(0,E.A)(this.shareData.disableValues),a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t.forEach((function(t){n.includes(t.id)?(a.push(t.id),t.children&&e.deepChildren(t.children,a)):e.getDisableDataList(t.children,n,a)})),a},deepChildren:function(e,t){var n=this;e&&e.forEach((function(e){t.push(e.id),e.children&&n.deepChildren(e.children,t)}))},setDisable:function(e,t){var n=this.shareData.disableValues;if(n.includes(e.id))return!0},selectAllHandler:function(e){var t=this,n=this.shareData.disableValues,a=this.flatDepts.filter((function(e){return!n.includes(e.id)}));a.map((function(n){t.chooseDept(n,e)}))}}},V=L,U=(0,m.A)(V,x,P,!1,null,"d0cd47c6",null),$=U.exports,F=(n(44807),n(13560),n(7509),n(7354),n(19944),n(51668)),B=n(95939),j=n(80602),H=n(87744),z=j.A.PROCEDD_NODE,q=j.A.APPROVE_NODE,G={name:"config-contact-dynamic",components:{"dynamic-menu":{props:{update:Function,menus:{type:Array,default:function(){return[]}},value:{type:String,default:""}},render:function(e){var t=this;return e("div",{class:"dynamic-menu"},[e("el-radio-group",{attrs:{size:"medium",value:this.value},on:{input:function(e){return t.update(e)}}},[this.menus.map((function(t){return e("el-radio-button",{attrs:{label:t.value}},[t.label])}))])])}}},inject:["flowData"],props:{shareData:{type:Object,default:function(){return{}}}},data:function(){return{menus:[],currentCell:{},currMenu:"".concat(F.A.NODE_OWNER.type,",").concat(F.A.CELL.type),subMenu:F.A.CREATE_USER.value,beforeNodeCells:[]}},computed:{menuItem:function(){var e=this;return this.menus.find((function(t){return t.type==e.currMenu}))||{}},isManager:function(){return this.currMenu==F.A.DEPT_MANAGER.type},cell:function(){var e;return(null===(e=this.flowData)||void 0===e?void 0:e.cell)||{}},fields:function(){var e;return(null===(e=this.shareData)||void 0===e?void 0:e.fields)||[]},sortNode:function(){var e,t,n=(null===(e=this.flowData)||void 0===e||null===(e=e.graph)||void 0===e?void 0:e.toJSON())||(null===(t=this.flowData)||void 0===t?void 0:t.defaultAntvJson);if(!n)return[];var a=n.cells.map((function(e){return H.A.packToCell(e)}));return(0,B.yQ)(null!==a&&void 0!==a?a:[])},supportCells:function(){var e;if(null===(e=this.shareData)||void 0===e||!e.isDynamicFlowDesignChoose)return this.filterSupportNode(this.sortNode);var t=[];return this.filterSupportNode(t)},showNewNodeFlowLeader:function(){var e;return(null===(e=this.shareData)||void 0===e?void 0:e.showNewNodeFlowLeader)||[]}},mounted:function(){var e;null!==(e=this.shareData)&&void 0!==e&&null!==(e=e.showDynamicMenus)&&void 0!==e&&e.includes(F.A.NODE_SUBMITTER.name)&&this.fetchBackNodeList(),this.showNewNodeFlowLeader.length>0?this.menus=(0,B.vH)(this.shareData.showDynamicMenus,this.showNewNodeFlowLeader):this.menus=(0,B.WA)(this.fields,this.supportCells,this.shareData.showDynamicMenus,this.beforeNodeCells)},methods:{fetchBackNodeList:function(){var e=this;return(0,M.A)((0,I.A)().mark((function t(){var n,a,i,s;return(0,I.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{i=null===(n=e.sortNode)||void 0===n?void 0:n.findIndex((function(t){var n;return t.id===(null===(n=e.cell)||void 0===n?void 0:n.id)})),s=i>1?null===(a=e.sortNode)||void 0===a?void 0:a.slice(1,i):[],e.beforeNodeCells=null===s||void 0===s?void 0:s.map((function(e){var t,n,a;return{name:null!==(t=null===e||void 0===e||null===(n=e.data)||void 0===n?void 0:n.name)&&void 0!==t?t:"",bizId:null!==(a=null===e||void 0===e?void 0:e.id)&&void 0!==a?a:""}}))}catch(r){console.log(r)}case 1:case"end":return t.stop()}}),t)})))()},filterSupportNode:function(e){return e.filter((function(e){return[z,q].includes(e.shape)}))},getValue:function(){var e=this,t=this.shareData.value.filter((function(t){return"{}"===JSON.stringify(e.menuItem)||e.menuItem.type.includes(t.type)}));return this.isManager?(t=this.subMenu==F.A.CREATE_USER.value?t.filter((function(e){var t;return(null===(t=e.extend)||void 0===t?void 0:t.subType)==F.A.CREATE_USER.type})):t.filter((function(t){var n;return(null===(n=t.extend)||void 0===n?void 0:n.fieldName)==e.subMenu})),t.map((function(e){return(0,B.t5)(e)}))):t.map((function(e){return e.id}))},getDataSource:function(){var e,t=this,n=this.menuItem.children,a=void 0===n?[]:n;return this.isManager?(null===(e=a.find((function(e){return e.value===t.subMenu}))||{})||void 0===e?void 0:e.children)||[]:(a.map((function(e){"EXECUTOR_USER"===e.id&&t.showNewNodeFlowLeader.length})),a)},handleCheck:function(e,t){var n=e.id,a=e.label,i=e.extend,s=e.oldId,r=e.type,o={id:n,name:a};this.isManager&&(o.id=s,o.name="".concat(i.subName," | ").concat(a),o.extend=i),r||(r=this.menuItem.type),this.update(o,t,r)},handleDelete:function(e){e.type==F.A.DEPT_MANAGER.type&&(e.id=(0,B.t5)(e)),this.$refs.contactTree.setChecked(e,!1)},update:function(e,t,n){this.$emit("update",{data:e,checked:t,type:n})},renderMenuTree:function(e){return e("el-tree",{ref:"contactTree",attrs:{data:this.getDataSource(),"node-key":"id","show-checkbox":!0,"check-on-click-node":!0,"highlight-current":!0,"default-checked-keys":this.getValue()},on:{"check-change":this.handleCheck}})},renderDynamicContentLeftDom:function(e){var t=this;return this.isManager?e("dynamic-menu",{attrs:{menus:this.menuItem.children,value:this.subMenu,update:function(e){t.subMenu=e}}}):this.renderMenuTree(e)},renderDynamicContentRight:function(e){return e("div",{class:"dynamic-content-right"},[this.renderMenuTree(e)])}},render:function(e){var t=this;return e("div",{class:"dynamic-panel"},[e("dynamic-menu",{attrs:{menus:this.menus,value:this.currMenu,update:function(e){t.currMenu=e}}}),e("div",{class:"dynamic-content"},[e("div",{class:"dynamic-content-left"},[this.renderDynamicContentLeftDom(e)]),this.isManager&&this.renderDynamicContentRight(e)])])}},K=G,Y=(0,m.A)(K,a,i,!1,null,null,null),J=Y.exports,W=function(){var e=this,t=e._self._c;return t("div",{staticClass:"dynamic-panel"},[t("div",{staticClass:"dynamic-menu"},[t("el-radio-group",{attrs:{size:"medium"},on:{input:e.handleLeftMenuCheck},model:{value:e.currentMenuId,callback:function(t){e.currentMenuId=t},expression:"currentMenuId"}},e._l(e.menuList,(function(n){return t("el-radio-button",{key:n.id,attrs:{label:n.id}},[e._v(" "+e._s(n.label)+" ")])})),1)],1),t("div",{staticClass:"dynamic-content"},[t("div",{staticClass:"dynamic-content-left"},[e.isEmpty(e.currentMenu.children)?t("div",{staticClass:"empty-data"},[e._v(e._s(e.$t("common.base.tip.noData")))]):[t("el-checkbox-group",{attrs:{size:"medium"},model:{value:e.checkListLeft,callback:function(t){e.checkListLeft=t},expression:"checkListLeft"}},[e._l(e.currentMenu.children,(function(n){return[e.isEmpty(n.children)?t("el-checkbox",{key:n.id,attrs:{label:n.id},on:{change:function(t){e.handleLeftTreeCheck(t,n)}}},[e._v(" "+e._s("sparePart"===e.moduleType?n.label:n.name||n.label)+" ")]):e._e()]}))],2),t("el-radio-group",{attrs:{size:"medium"},model:{value:e.radioCheckedLeft,callback:function(t){e.radioCheckedLeft=t},expression:"radioCheckedLeft"}},[e._l(e.currentMenu.children,(function(n){return[e.isEmpty(n.children)?e._e():t("el-radio-button",{key:n.id,attrs:{label:n.id},nativeOn:{click:function(t){return e.handleLeftTreeRadio(n)}}},[t("span",{staticClass:"no-use-box"}),e._v(" "+e._s(n.label)+" ")])]}))],2)]],2),t("div",{staticClass:"dynamic-content-right"},[e.isEmpty(e.checkedLeft.children)?t("div",{staticClass:"empty-data"},[e._v(e._s(e.$t("common.base.tip.noData")))]):t("el-checkbox-group",{attrs:{size:"medium"},model:{value:e.checkListRight,callback:function(t){e.checkListRight=t},expression:"checkListRight"}},e._l(e.checkedLeft.children,(function(n){return t("el-checkbox",{key:n.id,attrs:{label:n.id},on:{change:function(t){e.handleRightTreeCheck(t,n)}}},[e._v(" "+e._s("sparePart"===e.moduleType?n.label:n.name||n.label)+" ")])})),1)],1)])])},Z=[],Q=(n(46622),n(42519),n(69594),n(68735),F.A.NODE_OWNER),X=F.A.DEPT_MANAGER,ee=F.A.USER,te=F.A.CELL,ne=F.A.CREATE_USER,ae={name:"config-contact-dynamic-task",inject:["flowData"],props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{menuList:[],currentMenu:{},currentMenuId:"",radioCheckedLeft:"",checkListLeft:[],checkListRight:[],checkedLeft:{},checkedRight:{},approveUsers:new Map,USERTYPE:{sparePart:14,eventModule:17}}},computed:{approveNode:function(){var e=this.flowData.graph.toJSON();return e.cells.filter((function(e){return"approve-node"===e.shape}))||[]},cell:function(){return this.flowData.cell||{}},nodeId:function(){return this.cell.id},taskTypeId:function(){return this.$route.query.taskTypeId},isCustomNode:function(){return"isCustomNode"===this.$route.query.isCustomNode},taskFlowType:function(){return this.$route.query.taskFlowType},moduleType:function(){return this.$route.query.moduleType||""},currentNodeId:function(){return this.$route.query.currentNodeId}},created:function(){this.getApproveUsers(),this.fetchData(),this.initCheckedValue()},methods:{fetchData:function(){var e=this;return(0,M.A)((0,I.A)().mark((function t(){var n,a,i,s,r,o,l,c;return(0,I.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n={taskTypeId:e.taskTypeId,node:e.taskFlowType,nodeId:""},a=[],t.prev=2,"sparePart"!==e.moduleType){t.next=11;break}return t.next=6,e.flowApi.getPartLeaderForSetting();case 6:i=t.sent,s=i.result,a=void 0===s?[]:s,t.next=25;break;case 11:if("eventModule"!==e.moduleType){t.next=19;break}return t.next=14,e.flowApi.getPartEventLeaderForSetting({eventTypeId:e.taskTypeId,node:e.taskFlowType});case 14:r=t.sent,o=r.result,a=void 0===o?[]:o,t.next=25;break;case 19:return e.isCustomNode&&(n.node="nodeTask",n.nodeId=e.currentNodeId),t.next=22,e.flowApi.getLeaderForSetting(n);case 22:l=t.sent,c=l.result,a=void 0===c?[]:c;case 25:e.menuList=e.buildMenus(a),t.next=31;break;case 28:t.prev=28,t.t0=t["catch"](2),console.error("Error fetching data:",t.t0);case 31:case"end":return t.stop()}}),t,null,[[2,28]])})))()},initCheckedValue:function(){var e=this;this.shareData.value.forEach((function(t){var n=t.extend,a=t.id,i=t.rootType;i||a!==ne.value||(t.rootType=Q.value),t.rootType?n?e.checkListRight.push("".concat(t.rootType,"-").concat(n.subId,"-").concat(a)):e.checkListLeft.push("".concat(t.rootType,"-").concat(a)):e.checkListLeft.push(a)}))},handleLeftMenuCheck:function(e){this.checkedLeft={};var t=this.menuList.filter((function(t){return t.id===e}));this.currentMenu=t[0]||{}},handleLeftTreeCheck:function(e,t){var n=t.id,a=t.label,i=t.oldId,s=void 0===i?"":i,r=t.type,o=void 0===r?"":r,l=t.rootType,c=void 0===l?"":l;this.checkedLeft=t,this.update({id:s||n,name:a,rootType:c},e,o)},handleLeftTreeRadio:function(e){this.checkedLeft=e},handleRightTreeCheck:function(e,t){var n=t.id,a=t.label,i=t.oldId,s=t.type,r=t.extend,o=t.rootType;this.checkedRight=t,this.update({id:i||n,name:"".concat(r.subName," | ").concat(a),extend:r,rootType:o},e,s)},update:function(e,t,n){this.$emit("update",{data:e,checked:t,type:n})},handleDelete:function(e){var t="".concat(e.rootType,"-").concat(e.id);e.extend&&(t="".concat(e.rootType,"-").concat(e.extend.subId,"-").concat(e.id)),this.checkListLeft.includes(t)&&(this.checkListLeft=this.checkListLeft.filter((function(e){return e!==t}))),this.checkListRight.includes(t)&&(this.checkListRight=this.checkListRight.filter((function(e){return e!==t})))},buildMenus:function(e){var t,n,a=this;if(!e)return[];var i=e.nodeLeader,s=void 0===i?[]:i,r=e.superLeader,o=void 0===r?[]:r,l=e.formLeader,c=void 0===l?[]:l;return[{id:Q.value,label:Q.name,value:"".concat(Q.type,",").concat(te.type),type:"".concat(Q.type,",").concat(te.type),children:this.formatNormalChildren(s,Q.value)},{id:X.value,label:X.name,value:X.type,type:X.type,children:this.formatNormalChildren(o,X.value)},{id:ee.value,label:ee.name,value:ee.type,type:null!==(t=null===(n=this.USERTYPE)||void 0===n?void 0:n[this.moduleType])&&void 0!==t?t:10,children:(c||[]).map((function(e){var t,n;return{id:e.id,name:a.approveUsers.get(e.id)||"",label:e.name,type:null!==(t=null===(n=a.USERTYPE)||void 0===n?void 0:n[a.moduleType])&&void 0!==t?t:10}}))}]},formatNormalChildren:function(e,t){var n,a,i=this,s=null!==(n=null===(a=this.USERTYPE)||void 0===a?void 0:a[this.moduleType])&&void 0!==n?n:10;return e.map((function(e){var n="".concat(t,"-").concat(e.id),a=e.id===ne.value?ne.type:s,r={subType:a,subName:e.name,subId:e.id},o=O().isEmpty(e.children)&&i.approveUsers.get(n)||"";return{id:n,oldId:e.id,name:o,label:e.name,type:a,rootType:t,children:i.convertDeptManagerList(e.children,r,t)}}))},convertDeptManagerList:function(e,t,n){var a,i,s=this,r=null!==(a=null===(i=this.USERTYPE)||void 0===i?void 0:i[this.moduleType])&&void 0!==a?a:10,o=t.subType===ne.type?X.type:r;return e.map((function(e){var a="".concat(n,"-").concat(t.subId,"-").concat(e.id);return{id:a,name:s.approveUsers.get(a)||"",label:e.name,oldId:e.id,type:o,rootType:n,extend:t}}))},getApproveUsers:function(){for(var e=this,t=function(t){var n;if(e.nodeId===e.approveNode[t].id)return 1;var a=(null===(n=e.approveNode[t])||void 0===n||null===(n=n.data)||void 0===n||null===(n=n.attribute)||void 0===n?void 0:n.candidate)||[];a.forEach((function(n){var a;n.rootType||n.id!==ne.value||(n.rootType=Q.value);var i=e.approveUsers.get(e.getUniqId(n));if(!i){var s=null===(a=e.approveNode[t])||void 0===a||null===(a=a.data)||void 0===a?void 0:a.name,r=n.name;r.indexOf("|")>-1&&(r=r.slice(r.indexOf("|")+2,r.length)),e.approveUsers.set(e.getUniqId(n),"[".concat(s,"]").concat(e.$t("common.fields.executorUser.displayName"),": ").concat(r))}}))},n=0;n<this.approveNode.length;n++)if(t(n))break},getUniqId:function(e){var t=e||{},n=t.rootType,a=t.extend,i=t.id;return n?a?"".concat(n,"-").concat(a.subId,"-").concat(i):"".concat(n,"-").concat(i):i},isEmpty:function(e){return O().isEmpty(e)}}},ie=ae,se=(0,m.A)(ie,W,Z,!1,null,"18565edf",null),re=se.exports,oe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"role-panel",class:e.classNames},[e.roles.length?t("select-all-btn",{attrs:{isSelectAll:e.isSelectAll},on:{selectAll:e.selectAllHandler}}):e._e(),t("el-tree",{ref:"contactTree",attrs:{props:{label:"name",disabled:e.setDisable},data:e.roles,"node-key":"id","show-checkbox":"","check-strictly":"","highlight-current":"","check-on-click-node":"","expand-on-click-node":!1,"default-checked-keys":e.value,"default-expand-all":""},on:{"check-change":e.chooseRole}})],1)},le=[],ce=n(61725),de=n(32988),ue={name:"config-contact-role",components:{SelectAllBtn:g},props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{roles:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isSelectAll:function(){var e=this;return this.roles.every((function(t){return e.value.includes(t.id)}))},isTreeMode:function(){var e=this.roles[0]||{},t=e.children||[];return(0,ce.hj)(t)},classNames:function(){return{"role-panel-tree":this.isTreeMode}}},watch:{value:{handler:function(e){var t;null===(t=this.$refs.contactTree)||void 0===t||t.setCheckedKeys(e)},deep:!0}},mounted:function(){this.search()},methods:{search:function(){var e=this,t={pageSize:0,keyword:this.shareData.keyword};return this.flowApi.getRoleList(t).then((function(t){e.roles=t&&t.list||[],e.roles=(0,de.JB)(e.roles)}))["catch"]((function(e){return console.error("err",e)}))},chooseRole:function(e,t){console.log("chooseRole data",e);var n=(null===e||void 0===e?void 0:e.id)||"",a=[de.EM,de.SS].includes(n);if(!this.isTreeMode||!a){var i=(0,s.A)({},e);if(this.isTreeMode){var r=!0;i.name=(0,de.DO)(i,r)}this.$emit("update",{data:i,checked:t,type:this.shareData.type})}},setDisable:function(e,t){if(this.shareData.disableValues.includes(e.id))return!0},selectAllHandler:function(e){var t=this,n=this.shareData.disableValues,a=this.roles.filter((function(e){return!n.includes(e.id)}));a.map((function(n){t.chooseRole(n,e)}))}}},he=ue,pe=(0,m.A)(he,oe,le,!1,null,"3f6171ed",null),fe=pe.exports,ve=n(10888),me=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"service-provider-user-panel"},[t("div",{staticClass:"service-provider"},[t("base-tree",{attrs:{expand:"",data:e.spList,"node-render":e.nodeRender,selected:e.selectedProvider,"children-key":"tagChildren"},on:{"node-selected":e.chooseProvider}})],1),t("div",{directives:[{name:"loadmore",rawName:"v-loadmore",value:e.loadmoreOptions,expression:"loadmoreOptions"}],class:["user",{"search-container":e.isSearch}]},[e.userPage.list.length?t("select-all-btn",{attrs:{isSelectAll:e.isSelectAll},on:{selectAll:e.selectAllHandler}}):e._e(),e.userPage.list.length?t("el-tree",{ref:"contactTree",attrs:{props:{label:"displayName",disabled:e.setDisable},data:e.userPage.list,"node-key":"id","show-checkbox":"","check-on-click-node":"","default-checked-keys":e.value},on:{"check-change":e.chooseUser},scopedSlots:e._u([{key:"default",fn:function(n){var a=n.node,i=n.data;return t("span",{staticClass:"provider-user-name"},[e.isOpenData&&i.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:[e._v(" "+e._s(a.label)+" ")]],2)}}],null,!1,**********)}):e._e()],1)])},ye=[],ge={name:"config-contact-service-provider-user",components:{SelectAllBtn:g},props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,isSearch:!1,spList:[],userPage:new u.A,selectedProvider:{},params:{keyword:"",tagId:"",pageNum:1,pageSize:30},loadmoreOptions:{disabled:!1,callback:this.loadmore},disableValues:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isOpenData:function(){return this.shareData.isOpenData},isSelectAll:function(){var e=this;return this.userPage.list.every((function(t){return e.value.includes(t.id)}))}},watch:{value:{handler:function(e){var t;null===(t=this.$refs.contactTree)||void 0===t||t.setCheckedKeys(e),this.shareData.isMulti||0!==e.length||(this.disableValues=[])},deep:!0}},mounted:function(){this.fetchProvider()},methods:{fetchProvider:function(){var e=this,t={showCancel:!1,showSpAll:!0};return this.flowApi.getSelectServiceProviderList(t).then((function(t){e.spList=t&&t.data||[],e.chooseProvider(t.data[0])}))["catch"]((function(e){return console.error("err",e)}))},search:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.isSearch=e,this.chooseProvider(this.selectedProvider)},chooseProvider:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selectedProvider=e,this.params.pageNum=1,this.params.keyword=this.shareData.keyword,this.params.id=e.id||"",this.userPage.list=[],this.fetchUser(this.params)},fetchUser:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.loading=!0,this.loadmoreOptions.disabled=!0,this.flowApi.getSelectServiceProviderUserList(t).then((function(t){var n,a=(null===(n=t.data)||void 0===n?void 0:n.userList)||[];a.forEach((function(e){e.label=e.displayName,e.id=e.userId})),e.userPage.merge(u.A.as({list:a})),(e.disableValues.length>0||!e.shareData.isMulti&&e.value.length>0)&&e.isSingleSelectFilterIds(e.value[0])}))["finally"]((function(){e.loadmoreOptions.disabled=!e.userPage.hasNextPage,e.loading=!1}))["catch"]((function(e){return console.log(e)}))},loadmore:function(){this.params.pageNum+=1,this.fetchUser(this.params)},chooseUser:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{id:e.userId,name:e.displayName});this.shareData.isMulti||(t?this.isSingleSelectFilterIds(n.id):this.disableValues=[]),this.$emit("update",{data:n,checked:t,type:this.shareData.type})},isSingleSelectFilterIds:function(e){this.disableValues=this.userPage.list.filter((function(t){return t.userId!==e})).map((function(e){return e.id}))},setDisable:function(e,t){if(this.shareData.disableValues.includes(e.id)||this.disableValues.includes(e.id))return!0},nodeRender:function(e,t){return e("span",[t.name])},selectAllHandler:function(e){var t=this,n=this.shareData.disableValues,a=this.userPage.list.filter((function(e){return!n.includes(e.id)}));a.map((function(n){t.chooseUser(n,e)}))}}},be=ge,Ae=(0,m.A)(be,me,ye,!1,null,"1e177f1e",null),ke=Ae.exports,De=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"service-provider-user-panel"},[t("div",{staticClass:"service-provider"},[t("el-tree",{ref:"ProviderTree",attrs:{"highlight-current":"",data:e.spList,"node-key":"id","show-checkbox":!0,"default-checked-keys":e.value},on:{"check-change":e.chooseDept,"node-click":e.chooseProvider},scopedSlots:e._u([{key:"default",fn:function(n){n.node;var a=n.data;return t("span",{},[e._v(" "+e._s(a.name)+"（"+e._s(a.userCount)+"） ")])}}])})],1),t("div",{directives:[{name:"loadmore",rawName:"v-loadmore",value:e.loadmoreOptions,expression:"loadmoreOptions"}],class:["user",{"search-container":e.isSearch}]},[e.userPage.list.length?t("el-tree",{ref:"contactTree",attrs:{props:{label:"displayName",disabled:e.setDisable},data:e.userPage.list,"node-key":"id","show-checkbox":"","check-on-click-node":"","default-checked-keys":e.value},on:{"check-change":e.chooseUser},scopedSlots:e._u([{key:"default",fn:function(n){var a=n.node,i=n.data;return t("span",{},[e.isOpenData&&i.staffId?[t("open-data",{attrs:{type:"userName",openid:i.staffId}})]:[e._v(" "+e._s(a.label)+" ")]],2)}}],null,!1,**********)}):e._e()],1)])},we=[],Se={name:"config-contact-service-provider-user-new",props:{shareData:{type:Object,default:function(){return{}}},flowApi:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,isSearch:!1,spList:[],userPage:new u.A,selectedProvider:{},params:{keyword:"",tagId:"",pageNum:1,pageSize:30},loadmoreOptions:{disabled:!1,callback:this.loadmore},disableValues:[]}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},isOpenData:function(){return this.shareData.isOpenData}},watch:{value:{handler:function(e){var t,n;null===(t=this.$refs.ProviderTree)||void 0===t||t.setCheckedKeys(e),null===(n=this.$refs.contactTree)||void 0===n||n.setCheckedKeys(e),this.shareData.isMulti||0!==e.length||(this.disableValues=[])},deep:!0}},mounted:function(){this.fetchProvider()},methods:{fetchProvider:function(){var e=this,t={showCancel:!1,showSpAll:!1,hideNullProvider:!1};return this.flowApi.getSelectServiceProviderList(t).then((function(t){e.spList=t&&t.data||[],e.chooseProvider(t.data[0])}))["catch"]((function(e){return console.error("err",e)}))},search:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.isSearch=e,this.chooseProvider(this.selectedProvider)},chooseProvider:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selectedProvider=e,this.params.pageNum=1,this.params.keyword=this.shareData.keyword,this.params.id=e.id||"",this.userPage.list=[],this.fetchUser(this.params)},fetchUser:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.loading=!0,this.loadmoreOptions.disabled=!0,this.flowApi.getSelectServiceProviderUserList(t).then((function(t){var n,a=(null===(n=t.data)||void 0===n?void 0:n.userList)||[];a.forEach((function(e){e.label=e.displayName,e.id=e.userId})),e.userPage.merge(u.A.as({list:a})),(e.disableValues.length>0||!e.shareData.isMulti&&e.value.length>0)&&e.isSingleSelectFilterIds(e.value[0])}))["finally"]((function(){e.loadmoreOptions.disabled=!e.userPage.hasNextPage,e.loading=!1}))["catch"]((function(e){return console.log(e)}))},loadmore:function(){this.params.pageNum+=1,this.fetchUser(this.params)},chooseUser:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{id:e.userId,name:e.displayName}),a={subType:1};n.extend=a,this.shareData.isMulti||(t?this.isSingleSelectFilterIds(n.id):this.disableValues=[]),this.$emit("update",{data:n,checked:t,type:this.shareData.type})},isSingleSelectFilterIds:function(e){this.disableValues=this.userPage.list.filter((function(t){return t.userId!==e})).map((function(e){return e.id}))},setDisable:function(e,t){if(this.shareData.disableValues.includes(e.id)||this.disableValues.includes(e.id))return!0},nodeRender:function(e,t){return e("span",[t.name])},chooseDept:function(e,t){var n=(0,s.A)((0,s.A)({},e),{},{name:e.name,extend:{subType:2}});this.$emit("update",{data:n,checked:t,type:this.shareData.type})}}},Te=Se,Ne=(0,m.A)(Te,De,we,!1,null,"2721966e",null),Ce=Ne.exports,_e=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"dynamic-agent"},[t("div",{directives:[{name:"loadmore",rawName:"v-loadmore",value:e.loadmoreOptions,expression:"loadmoreOptions"}],staticClass:"dynamic-agent-container"},[t("el-tree",{ref:"contactTree",attrs:{data:e.passDispatchAgentsPage.list,"node-key":"id","show-checkbox":"","check-on-click-node":"","highlight-current":"","default-checked-keys":e.value},on:{"check-change":e.handleCheck},scopedSlots:e._u([{key:"default",fn:function(n){var a=n.node,i=n.data;return t("div",{staticClass:"dynamic-agent-container-option"},[t("span",{staticClass:"label"},[e._v(e._s(a.label))]),i.agentRemark?t("el-tooltip",{attrs:{content:i.agentRemark}},[t("span",{staticClass:"iconfont icon-fdn-info"})]):e._e()],1)}}])})],1)])},xe=[],Pe=n(69396),Ie={name:"SmartDispatchAgents",props:{shareData:{type:Object,default:function(){return{}}}},data:function(){return{roles:[],loadmoreOptions:{disabled:!1,callback:this.loadmoreAgent},agentParams:{bizType:"PAAS",bizTypeId:"",pageNum:1,pageSize:20,keyword:"",enabled:!0},passDispatchAgentsPage:new u.A,loading:!1}},computed:{value:function(){var e=this.shareData,t=e.type,n=e.value;return n.filter((function(e){return e.type==t})).map((function(e){return e.id}))},keyword:function(){var e,t;return null!==(e=null===(t=this.shareData)||void 0===t?void 0:t.keyword)&&void 0!==e?e:""}},mounted:function(){var e=this.$route.query.formId,t=void 0===e?"":e;this.agentParams.bizTypeId=t,this.init()},methods:{search:function(){this.init()},init:function(){this.passDispatchAgentsPage=new u.A,this.agentParams.pageNum=1,this.fetchDispatchAgent(this.agentParams)},loadmoreAgent:function(){this.agentParams.pageNum+=1,this.fetchDispatchAgent(this.agentParams)},fetchDispatchAgent:function(e){var t=this;return(0,M.A)((0,I.A)().mark((function n(){var a,i,s,r;return(0,I.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,e.keyword=t.keyword,t.loading=!0,t.loadmoreOptions.disabled=!0,n.next=6,(0,Pe.getSmartDispatchAgentPageList)(e);case 6:if(i=n.sent,s=i.data,r=i.success,r){n.next=11;break}return n.abrupt("return");case 11:null===s||void 0===s||null===(a=s.list)||void 0===a||a.forEach((function(e){e.label=e.agentName,e.id=String(e.id)})),t.passDispatchAgentsPage.merge(u.A.as(s)),n.next=18;break;case 15:n.prev=15,n.t0=n["catch"](0),console.error(n.t0);case 18:return n.prev=18,t.loadmoreOptions.disabled=!t.passDispatchAgentsPage.hasNextPage,t.loading=!1,n.finish(18);case 22:case"end":return n.stop()}}),n,null,[[0,15,18,22]])})))()},handleCheck:function(e,t){var n=e.id,a=e.label,i=(e.extend,e.oldId,e.type,{id:n,name:a});if(t){var s,r,o=3,l=null===(s=this.shareData)||void 0===s||null===(s=s.value)||void 0===s?void 0:s.filter((function(e){return(null===e||void 0===e?void 0:e.type)===F.A.SMART_DISPATCH_AGENT.type}));if((null===l||void 0===l?void 0:l.length)>=o)return null===(r=this.$refs.contactTree)||void 0===r||r.setChecked(i,!1),this.$message.warning(this.$t("forOther.setting.maxAgentsTip",{num:o}));console.log("======",l)}this.$emit("update",{data:i,checked:t,type:this.shareData.type})}}},Me=Ie,Ee=(0,m.A)(Me,_e,xe,!1,null,"43f688d9",null),Re=Ee.exports,Oe=F.A.NODE_OWNER,Le=F.A.DEPT_MANAGER,Ve=F.A.USER,Ue=(F.A.CUSTOMER,F.A.FORM_CUSTOMER),$e=F.A.FORM_LINK_MAN,Fe=F.A.FORM_TAG,Be=F.A.FORM_TAG_SUPERVISOR,je=F.A.SMART_DISPATCH_AGENT,He=F.A.FORM_SERVICE_PROVIDER,ze={show:!0,isMulti:!0},qe={name:"config-contact",components:(0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)((0,l.A)({},D.name,D),$.name,$),J.name,J),re.name,re),fe.name,fe),"CustomTag",ve.A),ke.name,ke),Ce.name,Ce),_.name,_),"SmartDispatchAgents",Re),inject:["mode"],props:{value:{type:Array,default:function(){return[]}},title:{type:String,default:""},showDynamic:{type:Boolean,default:!0},fields:{type:Array,default:function(){return[]}},showTagClosable:{type:Boolean,default:function(){return!0}},disableValues:{type:Array,default:function(){return[]}},showDynamicMenus:{type:Array,default:function(){return[Oe.name,Le.name,Ve.name,Ue.name,$e.name,Fe.name,Be.name,He.name]}},isDynamicFlowDesignChoose:{type:Boolean,default:function(){return!0}},flowApi:{type:Object,required:!0,default:function(){return{}}},isNeedTagSlot:{type:Boolean,default:function(){return!0}},showTabSetting:{type:Object,default:function(){return{department:(0,s.A)({},ze),member:(0,s.A)({},ze),role:(0,s.A)({},ze),dynamic:(0,s.A)((0,s.A)({},ze),{},{show:!0}),serviceProvider:(0,s.A)({},ze),serviceProviderRole:(0,s.A)({},ze)}}},isOpenDataResource:{type:Boolean,default:function(){return!1}},showServiceProvider:{type:Boolean,default:!1},showNewServiceProvider:{type:Boolean,default:!1},showNewNodeFlowLeader:{type:Array,default:function(){return[]}},showSmartDispatch:{type:Boolean,default:!1}},data:function(){return{$isInputZh:!1,tabs:this.buildTabs(),currTab:"1",checked:[],keyword:""}},computed:{showInputEnable:function(){return this.currTab<3||this.currTab==je.type},propsForSubComponents:function(){return{keyword:this.keyword,value:this.checked,fields:this.fields,showDynamicMenus:this.showDynamicMenus,disableValues:this.disableValues,isDynamicFlowDesignChoose:this.isDynamicFlowDesignChoose,showNewNodeFlowLeader:this.showNewNodeFlowLeader,isOpenData:this.isOpenData}},isOpenData:function(){return this.$platform.isOpenData||this.isOpenDataResource}},watch:{currTab:function(e,t){var n=this;this.keyword&&(this.keyword="",this.$nextTick((function(){n.$refs["tabPane".concat(t)][0].search()})))},value:{handler:function(e){this.checked=O().cloneDeep(e)},immediate:!0}},mounted:function(){var e,t=this.tabs.filter((function(e){return e.show}));this.currTab=t.length>0&&((null===(e=t[0])||void 0===e?void 0:e.value)||"1")},methods:{isShowClosable:function(e){return!0},buildTabs:function(){var e,t,n=F.A.MEMBER,a=F.A.DEPARTMENT,i=F.A.ROLE,s=F.A.DYNAMIC,r=F.A.SERVICE_PROVIDER,o=(F.A.FORM_CUSTOMER,F.A.SERVICE_PROVIDER_New),l=F.A.SMART_DISPATCH_AGENT,c=F.A.SERVICE_PROVIDER_Role;return[{displayName:n.name,component:D.name,value:n.type,show:this.showTabSetting.member.show,isMulti:this.showTabSetting.member.isMulti},{displayName:a.name,component:$.name,value:a.type,show:this.showTabSetting.department.show,isMulti:this.showTabSetting.department.isMulti},{displayName:i.name,component:fe.name,value:i.type,show:this.showTabSetting.role.show,isMulti:this.showTabSetting.role.isMulti},{displayName:s.name,component:"task"===this.mode?re.name:J.name,value:s.type,show:this.showDynamic&&this.showTabSetting.dynamic.show,isMulti:this.showTabSetting.dynamic.isMulti},{displayName:r.name,component:ke.name,value:r.type,show:this.showServiceProvider&&(null===(e=this.showTabSetting)||void 0===e||null===(e=e.serviceProvider)||void 0===e?void 0:e.show),isMulti:null===(t=this.showTabSetting)||void 0===t||null===(t=t.serviceProvider)||void 0===t?void 0:t.isMulti},{displayName:o.name,component:Ce.name,value:o.type,show:this.showNewServiceProvider,isMulti:!0},{displayName:l.name,component:Re.name,value:l.type,show:this.showSmartDispatch,isMulti:!0},{displayName:c.name,component:_.name,value:c.type,show:this.showTabSetting.serviceProviderRole.show,isMulti:!0}].filter((function(e){return e.show}))},handleClear:function(){this.keyword="",this.search()},compositionstart:function(e){this.isInputZh=!0},compositionend:function(e){this.isInputZh=!1,this.search()},search:O().debounce((function(){if(!this.isInputZh){var e=this.keyword;this.$refs["tabPane".concat(this.currTab)][0].search(e)}}),500),update:function(e){var t=e.data,n=e.checked,a=e.type,i=this.checked.findIndex((function(e){var n,i,s,r,o,l=e.id==t.id&&e.type==a;if(t.rootType)return l&&t.name===e.name;if(a!=F.A.DEPT_MANAGER.type)return l;var c=l&&(null===(n=t.extend)||void 0===n?void 0:n.subType)==(null===(i=e.extend)||void 0===i?void 0:i.subType);return(null===(s=t.extend)||void 0===s?void 0:s.subType)==F.A.CREATE_USER.type?c:c&&(null===(r=t.extend)||void 0===r?void 0:r.fieldName)==(null===(o=e.extend)||void 0===o?void 0:o.fieldName)}));n?-1==i&&this.checked.push((0,s.A)((0,s.A)({},t),{},{type:a})):i>-1&&this.checked.splice(i,1),this.$emit("checkedChange",this.checked)},handleDelete:function(e,t){var n,a=e.type;if(this.checked.splice(t,1),a>4)return null===(n=this.$refs.tabPane4[0])||void 0===n?void 0:n.handleDelete(e);var i=this.$refs["tabPane".concat(a)][0];"undefined"!==typeof i.$refs.contactTree&&i.$refs.contactTree.setChecked(e,!1)},isServerProvideDept:function(e){var t;return"16"==e.type&&2===(null===(t=e.extend)||void 0===t?void 0:t.subType)||"23"==e.type}}},Ge=qe,Ke=(0,m.A)(Ge,r,o,!1,null,"422300f0",null),Ye=Ke.exports}}]);