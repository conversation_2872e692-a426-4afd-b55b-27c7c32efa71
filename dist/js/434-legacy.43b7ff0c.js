"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[434],{3402:function(e,t,n){n.d(t,{C2:function(){return i},TO:function(){return u},WE:function(){return s},Y2:function(){return p},Y8:function(){return l},a_:function(){return c},tl:function(){return d}});var o=n(22229),r=n(9792),a=r.z.paas;function i(e){return o.A.post("".concat(a,"/outside/pc/tag/saveBatch"),e)}function u(e){return o.A.get("".concat(a,"/outside/pc/tag/selectList"),e)}function s(e){return o.A.post("".concat(a,"/outside/pc/tag/selectTagSwitchState"),e)}function l(e){return o.A.post("".concat(a,"/outside/pc/tag/setTagSwitchState"),e)}function c(e){return o.A.post("".concat(a,"/outside/pc/paasTagFormContent/saveOrUpdateBatch"),e)}function p(e){return o.A.get("".concat(a,"/outside/pc/tag/selectTagNavigationBar"),e)}function d(e){return o.A.post("/api/user/outside/select/getUserLabelList",e)}},11592:function(e,t,n){n.d(t,{i:function(){return r}});n(2286),n(35256),n(16961),n(54615),n(32807),n(75069);var o=n(80906),r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return e.map((function(e){return e.subFormFieldList=r(e.subFormFieldList,o.hL(e)),e})).filter((function(e){return!e.isHidden})).filter((function(e){var n=e.isSystem,o=e.setting,r=void 0===o?{}:o;return!t||!n||n&&(r.isShow||void 0==r.isShow)}))}},18495:function(e,t,n){n.d(t,{A0:function(){return a},m$:function(){return i},rD:function(){return r}});var o=n(22229);function r(e){return o.A.post("/api/application/outside/trigger/getTriggerByBizTypeInfo",e)}function a(e){return o.A.post("/api/voice/outside/container/button/updateButton",e)}function i(e){return o.A.post("/api/voice/outside/container/button/getButtonByModule",e)}},23403:function(e,t,n){var o,r,a;n.d(t,{CQ:function(){return r},FW:function(){return o},I3:function(){return a}}),function(e){e["Visible"]="visible",e["Revisable"]="revisable",e["Required"]="required"}(o||(o={})),function(e){e["VisibleCheckedAll"]="visibleCheckedAll",e["RevisableCheckedAll"]="revisableCheckedAll",e["RequiredCheckedAll"]="requiredCheckedAll"}(r||(r={})),function(e){e["Trigger"]="trigger",e["Linker"]="linker",e["CodeContent"]="codeContent"}(a||(a={}))},63590:function(e,t,n){n.d(t,{GG:function(){return C},Yi:function(){return A},b2:function(){return y},eW:function(){return w},fT:function(){return b},iH:function(){return x},n3:function(){return S}});var o=n(71357),r=n(18885),a=n(42881),i=n(62361),u=(n(2286),n(44807),n(80793),n(48152),n(35256),n(21484),n(16961),n(54615),n(7354),n(32807),n(55650),n(75069),n(42925),n(944),n(21633),n(33656),n(84859)),s=n(65582),l=n(70362),c=n(70072),p=n(92935),d=n(18495),m=n(56582),v=n(34987),f=n(88376),g=n(78670),h=n(23403),b=5,w="primaryKey",S=function(){var e=function(e,t){return{label:e,value:t}},t=(0,i.A)((0,i.A)({},l.LY.Primary,"type-primary"),l.LY.Gray,"type-gray"),n=[e((0,u.t)("common.page.buttonSet.text6"),l.LY.Primary),e((0,u.t)("common.page.buttonSet.text7"),l.LY.Gray)],o=function(e,t,n){return{label:e,value:t,des:n,disabled:!1}},s=[o((0,u.t)("common.page.buttonSet.text8"),l.cS.PcList,"")],p=function(){var e=(0,a.A)((0,r.A)().mark((function e(t){var n,o,a,i,u,s,p,d,m;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n={pass:!0},e.prev=1,o=0;case 3:if(!(o<t.length)){e.next=19;break}if(p=t[o],!(0,c.isEmpty)(p.name)&&0!==p.name.trim().length&&null!==(a=p.position)&&void 0!==a&&a.length){e.next=8;break}return n={pass:!1,pathId:p.pathId,errorModule:l.tP.Base},e.abrupt("break",19);case 8:if(d=p.event.find((function(e){return e.type===l.Sd.Trigger})),0!==(null===d||void 0===d||null===(i=d.execute)||void 0===i?void 0:i.length)){e.next=12;break}return n={pass:!1,pathId:p.pathId,errorModule:l.tP.Event},e.abrupt("break",19);case 12:if(m=p.event.find((function(e){return e.type===l.Sd.Linker})),(!m||0!==(null===m||void 0===m||null===(u=m.execute)||void 0===u?void 0:u.length)&&g.WC.test(null===m||void 0===m?void 0:m.execute[0]))&&null!==(s=p.event[0])&&void 0!==s&&s.type){e.next=16;break}return n={pass:!1,pathId:p.pathId,errorModule:l.tP.Event},e.abrupt("break",19);case 16:o++,e.next=3;break;case 19:e.next=25;break;case 21:return e.prev=21,e.t0=e["catch"](1),console.error(e.t0,"<-----validateForButtonSet is Error"),e.abrupt("return",Promise.resolve({pass:!1}));case 25:return e.abrupt("return",Promise.resolve(n));case 26:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}();return{typeChooseArr:n,showPositionChooseArr:s,typeColorEnum:t,validateForButtonSet:p}},C=function(e){var t=(0,p.cloneDeep)(e);return t.map((function(e,t){var n,o=e.buttonId,r=e.show;o?(e["pathId"]=[o],e[w]=o):console.error("".concat(t," buttonId is Miss"));var a=[];return r.detail&&a.push(l.cS.PcDetail),r.list&&a.push(l.cS.PcList),r.mobile&&a.push(l.cS.MobileDetail),e["position"]=a,null===(n=e.event)||void 0===n||n.map((function(e){return e.execute||(e.execute=[]),e})),e}))},y=function(e){var t=(0,p.cloneDeep)(e);return t.map((function(e){var t=e.position;return delete e[w],delete e["pathId"],e["show"]={mobile:!(null===t||void 0===t||!t.includes(l.cS.MobileDetail)),detail:!(null===t||void 0===t||!t.includes(l.cS.PcDetail)),list:!(null===t||void 0===t||!t.includes(l.cS.PcList))},delete e["position"],e}))},x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=(0,i.A)((0,i.A)((0,i.A)({},l.cS.MobileDetail,"mobile"),l.cS.PcDetail,"detail"),l.cS.PcList,"list"),r={module:"PAAS",moduleId:t,isEdit:!1};return(0,d.m$)(r).then((function(t){if(0===t.status){var r,a=(null===(r=t.data)||void 0===r?void 0:r.filter((function(t){var n;return o[e]&&!0===(null===(n=t.show)||void 0===n?void 0:n[o[e]])})))||[];n&&n(a)}else m.Ay.notification({type:"error",title:(0,u.t)("common.base.fail"),message:t.message})}))},A=function(){var e=(0,a.A)((0,r.A)().mark((function e(t){var n,a,i,s,p,d,g,b,w,S,C,y,x,A,T,B,k,L,E=arguments;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=E.length>1&&void 0!==E[1]?E[1]:null,a=E.length>2&&void 0!==E[2]?E[2]:{},i=E.length>3&&void 0!==E[3]?E[3]:null,s=E.length>4&&void 0!==E[4]?E[4]:null,p=E.length>5&&void 0!==E[5]?E[5]:null,d=t.event,g=void 0===d?[]:d,b=g.find((function(e){return e.type===l.Sd.Linker})),!b){e.next=14;break}if(C=null===(w=window.frameElement)||void 0===w?void 0:w.getAttribute("id"),y="".concat(null===(S=g[0])||void 0===S||null===(S=S.execute)||void 0===S?void 0:S[0]),y){e.next=12;break}return e.abrupt("return");case 12:return m.Ay.openTab({id:"".concat(t.buttonId),title:"".concat(t.name||t.cnName),close:!0,url:y,fromId:C}),e.abrupt("return");case 14:if(0!==(null===n||void 0===n?void 0:n.length)){e.next=16;break}return e.abrupt("return",m.Ay.alert((0,u.t)("common.modal.PLEASE_SELECT_DATA_MESSAGE")));case 16:if(x=g.find((function(e){return[l.Sd.Code,h.I3.CodeContent].includes(e.type)})),!x){e.next=29;break}return i&&i(),e.next=21,I(g[0].codeContent,n,(0,o.A)({},a));case 21:if(A=e.sent,p&&p(),T="errorMessage",!A){e.next=28;break}if(B=JSON.parse(A),!B.hasOwnProperty(T)){e.next=28;break}return e.abrupt("return",v.A.warning(B[T]));case 28:return e.abrupt("return");case 29:k=g.find((function(e){return e.type===l.Sd.Trigger})),L=[],n.length>0&&(L=n.map((function(e){return(0,c.isObject)(e)?e.bizId:e}))),i&&i(),(0,f.D$)({triggerId:k.execute.join("，"),bizIdList:L}).then((function(e){var t=0===e.status;m.Ay.notification({type:t?"success":"error",title:t?(0,u.t)("common.base.tip.operationSuccess"):(0,u.t)("common.base.tip.operationFail"),message:!t&&e.message}),s&&s()}))["finally"]((function(){p&&p()}));case 34:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();function I(e,t){return T.apply(this,arguments)}function T(){return T=(0,a.A)((0,r.A)().mark((function e(t,n){var a,i,u=arguments;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=u.length>2&&void 0!==u[2]?u[2]:{},i=s.A.getInstance((0,o.A)({},a)),e.next=4,i.executeCodeForListAndDetail(t,n,a);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)}))),T.apply(this,arguments)}},70362:function(e,t,n){n.d(t,{LY:function(){return r},Sd:function(){return a},cS:function(){return o},qy:function(){return u},tP:function(){return i},w1:function(){return s}});var o=(e=>(e.PcList="pcList",e.MobileDetail="mobileDetail",e.PcDetail="pcDetail",e))(o||{}),r=(e=>(e.Gray="plain-third",e.Primary="primary",e))(r||{}),a=(e=>(e.Trigger="trigger",e.Linker="linker",e.Code="code",e))(a||{}),i=(e=>(e.Base="base",e.Event="event",e))(i||{}),u=(e=>(e.Customer="CUSTOMER",e.Product="PRODUCT",e.TASK="TASK",e.B2BSHOP="B2B_SHOP",e.B2BSHOPSYSTEM="B2B_SHOP_SYSTEM",e.TOCSHOP="TOC_SHOP",e.TOCSHOPSYSTE="TOC_SHOP_SYSTEM",e.CLOUDWAREHOUSE="CLOUD_WAREHOUSE",e.CLOUDWAREHOUSESYSTEM="CLOUD_WAREHOUSE_SYSTEM",e.PAASSYSTEM="PAAS_SYSTEM",e.EVENT="EVENT",e.PROJECT="PROJECT",e.CONTRACT="CONTRACT",e))(u||{}),s=(e=>(e.Button="button",e.ButtonGroup="buttonGroup",e))(s||{})},75434:function(e,t,n){n.r(t),n.d(t,{default:function(){return Ie}});var o,r,a,i,u,s=function(){var e=this,t=e._self._c;return t("ButtonSet",{ref:"buttonSetDom",attrs:{templateId:e.templateId}})},l=[],c=n(18885),p=n(42881),d=(n(75069),n(18495)),m=n(63590),v=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"button-set-box"},[t("div",{staticClass:"left-operation",class:[e.isExpand?"is-expand":""]},[t("ButtonSetEdit",{ref:"buttonSetEditDom",staticClass:"mar-b-16",attrs:{value:e.btnList,"now-focus-item":e.nowFocusItemIndex},on:{update:e.updateArr,changeFocus:e.changeFocus}}),t("ButtonSetPreview",{attrs:{value:e.btnList}}),t("div",{staticClass:"operation-expand"},[t("div",{class:[e.isExpand?"rotate-180":""],on:{click:function(t){e.isExpand=!e.isExpand}}},[t("i",{staticClass:"iconfont icon-right font-12"})])])],1),t("div",{staticClass:"right-operation"},[t("ButtonSetDetailSet",{directives:[{name:"show",rawName:"v-show",value:!e.isExpand,expression:"!isExpand"}],ref:"buttonSetDetailSetDom",attrs:{"now-item":e.nowFocusItem},on:{changeTrigger:e.changeTrigger,changeLinker:e.changeLinker,changeCode:e.changeCode,changeBtnListEventType:e.changeBtnListEventType}})],1)])},f=[],g=(n(44807),n(16961),n(7354),n(48649)),h=(n(67880),function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"button-set-edit-box"},[t("div",{staticClass:"header-box"},[t("div",{staticClass:"header-title flex-1"},[e._v(e._s(e.$t("common.page.buttonSet.text2")))]),t("div",{staticClass:"header-operation"},[t("div",{staticClass:"add-btn",class:[e.banAddButton?"is-ban":""],on:{click:e.addNewButton}},[e._v(e._s(e.$t("common.page.buttonSet.btn1"))+e._s("（".concat(e.btnList.length,"/").concat(e.normalBtnMixNumber,"）")))])])]),t("div",{staticClass:"btn-list-box",class:[e.nowChooseEndItem?"is-focus":""]},[e.btnList.length?e._e():t("div",{staticClass:"flex-x mar-b-8 tips-box"},[t("i",{staticClass:"iconfont icon-info-circle mar-r-8"}),e._v(" "+e._s(e.$t("common.page.buttonSet.text15"))+" ")]),t("draggable",{staticClass:"edit-btn-box",attrs:{tag:"div",list:e.btnList,group:{name:"g1"},handle:".handle"}},e._l(e.btnList,(function(n,o){return t("button",{key:o,staticClass:"btn-item-box just-cur-point can-move handle",class:[e.nowChooseEndItem===n.primaryKey?"is-focus":""],attrs:{draggable:"true"},on:{click:function(t){return e.focusNow(n)}}},[e.nowChooseEndItem===n.primaryKey?t("div",{staticClass:"btn-item-operation"},[t("div",{staticClass:"btn-item-operation-content overHideCon-1 flex-1"},[t("span",[e._v(e._s(n.name))])]),t("div",{staticClass:"mar-l-5 mar-r-4 split-line"}),t("i",{staticClass:"iconfont icon-delete font-12 just-cur-point",on:{click:function(t){return e.deleteBtn(o)}}})]):e._e(),t("el-button",{staticClass:"btn-item-content overHideCon-1",attrs:{type:n.viewType}},[e._v(e._s(n.name))])],1)})),0)],1)])}),b=[],w=(n(21484),n(27408),n(20462)),S=n(84859),C=n(70362),y=n(64055),x=n(70072),A=n(38602),I=n.n(A),T=(0,g.defineComponent)({name:"ButtonSetEdit",props:{value:{type:Array,default:function(){return[]}},nowFocusItem:{type:[Array,null],default:null}},components:{draggable:I()},setup:function(e,t){var n=t.emit,o=(0,g.ref)([]),r=function(){var e=(0,x.randomString)();return{primaryKey:e,name:(0,S.t)("common.paas.view.designer.workFlow.functionButton"),nameLanguage:(0,y.jP)("common.paas.view.designer.workFlow.functionButton"),viewType:C.LY.Primary,role:[],position:[C.cS.PcList],event:[{type:"",execute:[]}],pathId:[e],type:C.w1.Button,system:0}},a=(0,g.computed)((function(){if(!e.nowFocusItem)return null;var t=e.nowFocusItem.length-1;return e.nowFocusItem[t]}));function i(){o.value.length>=m.fT||o.value.push(r())}function u(e){n("changeFocus",[e.primaryKey])}function s(){n("changeFocus",null)}function l(e){return d.apply(this,arguments)}function d(){return d=(0,p.A)((0,c.A)().mark((function e(t){var n;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,w.Ay.confirm((0,S.t)("common.paas.view.designer.workFlow.tip1"));case 2:if(n=e.sent,n){e.next=5;break}return e.abrupt("return");case 5:o.value.splice(t,1),s();case 7:case"end":return e.stop()}}),e)}))),d.apply(this,arguments)}function v(e){o.value=e}var f=(0,g.computed)((function(){return o.value.length>=m.fT}));return(0,g.watch)((function(){return o.value}),(function(){n("update",o.value)}),{deep:!0}),{btnList:o,addNewButton:i,focusNow:u,deleteBtn:l,initBtnList:v,normalBtnMixNumber:m.fT,blurNow:s,nowChooseEndItem:a,banAddButton:f}}}),B=T,k=n(49100),L=(0,k.A)(B,h,b,!1,null,"942572d8",null),E=L.exports,_=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"button-set-preview-box"},[t("div",{staticClass:"button-set-preview-title"},[e._v(e._s(e.$t("common.page.buttonSet.text3"))+"（"+e._s(e.$t("common.page.buttonSet.text14"))+"）")]),t("div",{staticClass:"button-set-table-preview"},[t("div",{staticClass:"button-set-table-preview-btn-box"},[e._l(e.previewValue,(function(n,o){return[t("el-button",{key:o,staticClass:"button-set-table-preview-btn-item",attrs:{type:n.viewType}},[e._v(e._s(n.name))])]}))],2),t("el-table",{class:["common-list-table","bg-w","bbx-normal-list-box"],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",border:"","header-row-class-name":"common-table-header"}},[t("el-table-column",{attrs:{prop:"date"}},[t("div",{staticClass:"table-cell-bg"})]),t("el-table-column",{attrs:{prop:"name"}},[t("div",{staticClass:"table-cell-bg"})]),t("el-table-column",{attrs:{prop:"name"}},[t("div",{staticClass:"table-cell-bg"})]),t("el-table-column",{attrs:{prop:"name"}},[t("div",{staticClass:"table-cell-bg"})])],1)],1)])},D=[],P=n(35730);(function(e){e["PcList"]="pcList",e["MobileDetail"]="mobileDetail",e["PcDetail"]="pcDetail"})(o||(o={})),function(e){e["Gray"]="plain-third",e["Primary"]="primary"}(r||(r={})),function(e){e["Trigger"]="trigger"}(a||(a={})),function(e){e["Base"]="base",e["Event"]="event"}(i||(i={})),function(e){e["Button"]="button",e["ButtonGroup"]="buttonGroup"}(u||(u={}));var $=(0,g.defineComponent)({name:"ButtonSetPreview",props:{value:{type:Array,default:function(){return[]}}},setup:function(e){var t=[{date:"",name:"",address:""},{date:"",name:"",address:""},{date:"",name:"",address:""},{date:"",name:"",address:""}],n=(0,g.computed)((function(){return[{name:(0,S.t)("common.base.create"),viewType:r.Primary},{name:(0,S.t)("common.base.delete"),viewType:r.Gray},{name:(0,S.t)("view.designer.rule.tagSetting.handleTag"),viewType:r.Gray},{name:(0,S.t)("view.template.list.generateLink"),viewType:r.Gray}].concat((0,P.A)(e.value))}));return{tableData:t,previewValue:n}}}),F=$,O=(0,k.A)(F,_,D,!1,null,"66d932e4",null),N=O.exports,U=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"button-set-detail-box"},[e.nowItem?[t("div",{staticClass:"button-set-detail-type-change-box"},[t("div",{staticClass:"button-set-detail-type-change-item overHideCon-1",class:[e.nowIsBase?"is-checked":""],on:{click:function(t){e.nowType=0}}},[e._v(e._s(e.$t("common.base.baseSet")))]),t("div",{staticClass:"button-set-detail-type-change-item overHideCon-1",class:[e.nowIsConnect?"is-checked":""],on:{click:function(t){e.nowType=1}}},[e._v(e._s(e.$t("common.page.buttonSet.text13")))])]),t("div",{staticClass:"flex-1"},[t("ButtonSetDetailSetBaseSet",{directives:[{name:"show",rawName:"v-show",value:e.nowIsBase,expression:"nowIsBase"}],ref:"buttonSetDetailSetBaseSetDom",attrs:{"now-item":e.nowItem}}),t("ButtonSetDetailSetEventSet",{directives:[{name:"show",rawName:"v-show",value:e.nowIsConnect,expression:"nowIsConnect"}],ref:"ButtonSetDetailSetEventSet",attrs:{"now-item":e.nowItem},on:{changeTrigger:e.changeTrigger,changeLinker:e.changeLinker,changeCode:e.changeCode,changeBtnListEventType:e.changeBtnListEventType}})],1)]:e._e()],2)},M=[],R=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"detail-set-box"},[t("el-form",{ref:"formDom",attrs:{model:e.nowItem,rules:e.formRulers,"label-position":"top"}},[t("el-form-item",{attrs:{label:e.$t("common.paas.view.designer.workFlow.buttonName"),prop:"name"}},[t("div",{staticStyle:{display:"flex"}},[t("el-input",{attrs:{placeholder:e.$t("common.placeholder.input2"),maxlength:e.nameMaxLength},on:{input:e.changeNameLanguage},model:{value:e.nowItem.name,callback:function(t){e.$set(e.nowItem,"name",t)},expression:"nowItem.name"}}),t("p",{staticClass:"form-language-btn",on:{click:e.handleShowChooseLanguage}},[t("i",{staticClass:"iconfont icon-earth"})])],1)]),t("el-form-item",{attrs:{label:e.$t("common.page.buttonSet.text4"),prop:"viewType"}},[t("el-select",{staticClass:"w-100-p",attrs:{placeholder:e.$t("common.base.pleaseSelect")},scopedSlots:e._u([{key:"prefix",fn:function(){return[t("div",{staticClass:"type-symbol",class:e.getNowTypeColor(e.nowItem.viewType)})]},proxy:!0}]),model:{value:e.nowItem.viewType,callback:function(t){e.$set(e.nowItem,"viewType",t)},expression:"nowItem.viewType"}},e._l(e.typeChooseArr,(function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}},[t("div",{staticClass:"flex-x al-center"},[t("div",{staticClass:"type-symbol-inner",class:e.getNowTypeColor(n.value)}),e._v(e._s(n.label))])])})),1)],1),t("el-form-item",{attrs:{label:e.$t("common.base.visibility"),prop:"role"}},[t("roleSelect",{attrs:{nowItem:e.nowItem}})],1),t("el-form-item",{attrs:{label:e.$t("common.page.buttonSet.text5"),prop:"position"}},[t("el-checkbox-group",{model:{value:e.nowItem.position,callback:function(t){e.$set(e.nowItem,"position",t)},expression:"nowItem.position"}},e._l(e.showPositionChooseArr,(function(n,o){return t("div",[t("el-checkbox",{key:o,attrs:{disabled:n.disabled,label:n.value}},[e._v(e._s(n.label))])],1)})),0)],1)],1)],1)},G=[],H=n(71357),j=(n(7130),n(98316),n(35256),n(32807),n(33656),n(51618)),Y=(0,g.reactive)({roleList:[],roleListIsIniting:!1,initRoleList:function(){var e=this;!0!==this.roleListIsIniting&&(this.roleListIsIniting=!0,(0,j.N4)({pageSize:0}).then((function(t){e.roleList=t.list||[]}))["finally"]((function(){e.roleListIsIniting=!1})))},getRoleList:function(){return this.roleList},ButtonSetChooseUpdateTriggerList:[],ButtonSetChooseUpdateTriggerListIsIniting:!1,getButtonSetChooseUpdateTriggerList:function(){return this.ButtonSetChooseUpdateTriggerList},initButtonSetChooseUpdateTriggerList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!0!==this.ButtonSetChooseUpdateTriggerListIsIniting&&(this.ButtonSetChooseUpdateTriggerListIsIniting=!0,(0,d.rD)(t).then((function(t){0===t.status&&(e.ButtonSetChooseUpdateTriggerList=t.data)}))["finally"]((function(){e.ButtonSetChooseUpdateTriggerListIsIniting=!1})))}}),V=function(){var e=this,t=e._self._c;return t("div",{staticClass:"role-select"},[t("el-select",{ref:"roleSelect",staticStyle:{width:"100%"},attrs:{value:e.selectUserOptions.map((function(e){return e.value})),multiple:"",placeholder:e.$t("common.base.pleaseSelect"),clearable:"","collapse-tags":""},on:{"remove-tag":e.handleRemoveTag,clear:function(t){e.nowItem.role=[]}},nativeOn:{click:function(t){return e.openSelectUser.apply(null,arguments)}}},e._l(e.selectUserOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)},q=[],W=(n(2286),n(54615),n(3402)),z={name:"RoleSelect",props:{nowItem:{type:[Object,null],default:null}},data:function(){return{}},computed:{selectUserOptions:function(){return this.nowItem.role.map((function(e){return{label:e.displayName||e.name,value:e.id||e.userId}}))}},methods:{handleRemoveTag:function(e){this.nowItem.role=this.nowItem.role.filter((function(t){return t.id!=e&&t.userId!=e}))},openSelectUser:function(){var e=this;return(0,p.A)((0,c.A)().mark((function t(){var n,o,r;return(0,c.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$refs.roleSelect.blur(),n=(0,x.getRootWindow)(window),t.prev=2,o={title:"选择人员或部门",showServiceProvider:!0,isTag:!0,isShowUser:!1,isShowDepartmentUser:!0,isCustomDataAuth:!0,showDelete:!1,isCanChooseIntelligentTags:!0,selectedAll:e.nowItem.role||[],fetchLabelList:W.tl},t.next=6,n.$fast.select.multi.all(o);case 6:r=t.sent,e.nowItem.role=r.data.all.map((function(e){return delete e.tagChildren,delete e.userList,e}))||[],t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),console.error("[handleAddAuthObject error]",t.t0);case 13:case"end":return t.stop()}}),t,null,[[2,10]])})))()}}},K=z,J=(0,k.A)(K,V,q,!1,null,null,null),Q=J.exports,X=10,Z=(0,g.defineComponent)({name:"ButtonSetDetailBaseSet",props:{nowItem:{type:[Object,null],default:null}},components:{roleSelect:Q},setup:function(e,t){t.emit;var n=(0,g.getCurrentInstance)()||{},o=n.proxy,r=(0,g.ref)({name:[{required:!0,message:(0,S.t)("common.placeholder.input2"),validator:function(e,t,n){""===t.trim()?n(new Error((0,S.t)("common.placeholder.input2"))):n()}},{max:X,message:(0,S.t)("common.base.tip.maxCharacterTip",{num:X})}],position:[{required:!0,message:(0,S.t)("common.placeholder.select"),validator:function(e,t,n){0===t.length?n(new Error((0,S.t)("common.placeholder.select"))):n()},trigger:"change"}]}),a=(0,g.ref)(null),i=(0,g.computed)((function(){var e=Y.getRoleList();return e=e.map((function(e){return{label:e.name,value:isNaN(1*e.id)?e.id:1*e.id}})),e})),u=(0,m.n3)(),s=u.typeChooseArr,l=u.showPositionChooseArr,d=u.typeColorEnum;function v(e){return d[e]}function f(){return h.apply(this,arguments)}function h(){return h=(0,p.A)((0,c.A)().mark((function e(){var t;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a.value.validate();case 3:return t=e.sent,e.abrupt("return",Promise.resolve(t));case 7:e.prev=7,e.t0=e["catch"](0),Promise.resolve(!1);case 10:case"end":return e.stop()}}),e,null,[[0,7]])}))),h.apply(this,arguments)}function b(t){var n=e.nowItem.nameLanguage;n[S.Ay.locale]=t}function w(){var t=(0,H.A)({},e.nowItem.nameLanguage);o.$fast.languageSetting.show({title:(0,S.t)("common.base.languageSetting"),type:"input",languageDefaultValueObj:t}).then((function(t){e.nowItem.name=t[S.Ay.locale],e.nowItem.nameLanguage=t}))}return{formRulers:r,typeChooseArr:s,showPositionChooseArr:l,roleList:i,getNowTypeColor:v,nameMaxLength:X,formDom:a,validate:f,changeNameLanguage:b,handleShowChooseLanguage:w}}}),ee=Z,te=(0,k.A)(ee,R,G,!1,null,"f8479378",null),ne=te.exports,oe=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"connect-set-box"},[t("div",{staticClass:"pad-16"},[t("el-form",{ref:"formDom",attrs:{model:e.nowItem,"label-position":"top"}},[t("el-form-item",{attrs:{label:e.$t("common.projectManage.taskTypeText"),prop:"event[0].type",rules:{required:!0,message:e.$t("common.base.pleaseSelect"),trigger:["change"]}}},[t("el-select",{staticClass:"w-100-p",attrs:{value:e.nowItem.event[0].type,placeholder:e.$t("common.base.pleaseSelect"),clearable:""},on:{change:e.changeTaskType}},e._l(e.taskTypeArr,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.nowItem.event[0].type===e.ButtonSetDetailForButtonConcatEventEnum.Trigger,expression:"nowItem.event[0].type ===  ButtonSetDetailForButtonConcatEventEnum.Trigger"}],attrs:{label:e.$t("common.page.buttonSet.text12"),prop:"event[0].execute",rules:{required:!0,message:e.$t("common.base.pleaseSelect"),trigger:["change"]}}},[t("el-select",{staticClass:"w-100-p",attrs:{value:e.nowItem.event[0].execute[0],placeholder:e.$t("common.placeholder.input2"),clearable:""},on:{change:e.changeTrigger}},e._l(e.triggerArr,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.nowItem.event[0].type===e.ButtonSetDetailForButtonConcatEventEnum.Linker,expression:"nowItem.event[0].type === ButtonSetDetailForButtonConcatEventEnum.Linker"}],attrs:{label:e.$t("common.page.buttonSet.text16"),prop:"event[0].execute",rules:[{required:!0,message:e.$t("common.page.buttonSet.text17"),trigger:["change"]},{validator:e.customUrlValidator,trigger:["blur"]}]}},[t("el-input",{attrs:{placeholder:e.$t("common.page.buttonSet.text17"),clearable:""},on:{input:e.changeLinker},model:{value:e.nowItem.event[0].execute[0],callback:function(t){e.$set(e.nowItem.event[0].execute,0,t)},expression:"nowItem.event[0].execute[0]"}})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.nowItem.event[0].type===e.ButtonSetDetailForButtonConcatEventEnum.Code,expression:"nowItem.event[0].type === ButtonSetDetailForButtonConcatEventEnum.Code"}],staticClass:"form-item-box form-item-compilation"},[t("el-button",{on:{click:e.openTestDialog}},[e._v(e._s(e.$t("formSetting.jsCodeBlock.jsCodeTest")))]),t("p",[e._v(e._s(e.$t("formSetting.jsCodeBlock.jsCodeTestBtnText")))])],1)],1)],1),t("run-test-dialog",{ref:"compilationTestDialog",attrs:{field:e.codeField,fields:e.fields},on:{input:e.changeCode}})],1)},re=[],ae=(n(42925),n(944),n(74526)),ie=n(78670),ue=n(11592),se=n(31235),le=(0,g.defineComponent)({name:"ButtonSetDetailConnectSet",props:{nowItem:{type:[Object,null],default:null}},components:{runTestDialog:se.A},data:function(){return{fields:[]}},computed:{formTemplateId:function(){return this.$route.query.formId}},methods:{fetchFormFields:function(){var e=this;ae.QA({templateBizId:this.formTemplateId}).then((function(t){var n=t.success,o=t.data;n&&(e.fields=(0,ue.i)(o.paasFormFieldVOList))}))}},mounted:function(){this.fetchFormFields()},setup:function(e,t){var n=t.emit,o=(0,g.ref)(null),r=(0,g.ref)(null),a=(0,g.ref)({setting:{codeBlockConfig:{automaticOperation:0,codeContent:e.nowItem.event[0].codeContent||"",resultAliasPath:[]}}});function i(e,t,n){if(t&&!ie.WC.test(t))return n(new Error((0,S.t)("common.page.buttonSet.text18")));n()}var u=(0,g.computed)((function(){var e=Y.getButtonSetChooseUpdateTriggerList();return e.map((function(e){return{label:e.triggerName,value:e.id}}))})),s=(0,g.computed)((function(){var e=[{label:(0,S.t)("common.connector.title.trigger"),value:C.Sd.Trigger},{label:(0,S.t)("common.page.buttonSet.text19"),value:C.Sd.Linker},{label:(0,S.t)("common.form.type.jsCodeBlock"),value:C.Sd.Code}];return e}));function l(e){var t=e?[e]:[];n("changeTrigger",t)}function d(t){e.nowItem.event[0].execute=[],n("changeBtnListEventType",t)}function m(e){var t=e?[e]:[];n("changeLinker",t)}function v(t){e.nowItem.event[0].codeContent=(null===t||void 0===t?void 0:t.codeContent)||"",n("changeCode",(null===t||void 0===t?void 0:t.codeContent)||"")}function f(){a.value.setting.codeBlockConfig.codeContent=e.nowItem.event[0].codeContent||"",r.value.visible=!0}function h(){return b.apply(this,arguments)}function b(){return b=(0,p.A)((0,c.A)().mark((function e(){var t;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,o.value.validate();case 3:return t=e.sent,e.abrupt("return",Promise.resolve(t));case 7:e.prev=7,e.t0=e["catch"](0),Promise.resolve(!1);case 10:case"end":return e.stop()}}),e,null,[[0,7]])}))),b.apply(this,arguments)}return{validate:h,customUrlValidator:i,formDom:o,triggerArr:u,taskTypeArr:s,changeTrigger:l,changeLinker:m,changeTaskType:d,changeCode:v,openTestDialog:f,codeField:a,compilationTestDialog:r,ButtonSetDetailForButtonConcatEventEnum:C.Sd}}}),ce=le,pe=(0,k.A)(ce,oe,re,!1,null,"2fde55af",null),de=pe.exports,me=(0,g.defineComponent)({name:"ButtonSetDetailSet",components:{ButtonSetDetailSetBaseSet:ne,ButtonSetDetailSetEventSet:de},props:{nowItem:{type:[Object,null],default:null}},setup:function(e,t){var n=t.emit,o=(0,g.ref)(null),r=(0,g.ref)(null),a=(0,g.ref)(0),i=(0,g.computed)((function(){return 0===a.value})),u=(0,g.computed)((function(){return 1===a.value}));function s(){a.value=0}function l(){return d.apply(this,arguments)}function d(){return d=(0,p.A)((0,c.A)().mark((function e(){var t,n;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o.value.validate();case 2:if(t=e.sent,t){e.next=6;break}return a.value=0,e.abrupt("return",Promise.resolve(!1));case 6:return e.next=8,r.value.validate();case 8:if(n=e.sent,n){e.next=12;break}return a.value=1,e.abrupt("return",Promise.resolve(!1));case 12:return e.abrupt("return",Promise.resolve(!0));case 13:case"end":return e.stop()}}),e)}))),d.apply(this,arguments)}function m(e){n("changeTrigger",e)}function v(e){n("changeLinker",e)}function f(e){n("changeCode",e)}function h(e){n("changeBtnListEventType",e)}function b(e){console.log(e,"<----moduleName"),e===C.tP.Event?(0,g.nextTick)((function(){a.value=1,r.value.validate()})):e===C.tP.Base&&(0,g.nextTick)((function(){a.value=0,o.value.validate()}))}return s(),{nowType:a,nowIsBase:i,nowIsConnect:u,buttonSetDetailSetBaseSetDom:o,ButtonSetDetailSetEventSet:r,validate:l,changeTrigger:m,changeLinker:v,changeCode:f,changeBtnListEventType:h,showAndValidate:b,initComData:s}}}),ve=me,fe=(0,k.A)(ve,U,M,!1,null,"3e63b40e",null),ge=fe.exports,he=n(34987),be=(0,g.defineComponent)({name:"ButtonSet",props:{templateId:{type:String,default:""}},setup:function(e){var t=(0,g.ref)([]),n=(0,g.ref)(null),o=(0,g.ref)(!1),r=(0,g.ref)(null),a=(0,g.ref)(null),i=(0,g.ref)(null),u=(0,m.n3)(),s=u.validateForButtonSet;function l(e){t.value=e}function d(e){t.value=e,n.value.initBtnList(e),I()}function v(e){return f.apply(this,arguments)}function f(){return f=(0,p.A)((0,c.A)().mark((function e(n){var o,u;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n){e.next=4;break}return r.value=null,a.value=[],e.abrupt("return");case 4:o=n[n.length-1],u=t.value.find((function(e){return e[m.eW]===o})),r.value=u,a.value=n,i.value.initComData();case 9:case"end":return e.stop()}}),e)}))),f.apply(this,arguments)}function h(e){var t,n=null===(t=r.value.event)||void 0===t?void 0:t.find((function(e){return e.type===C.Sd.Trigger}));n.execute=e}function b(e){var t,n=null===(t=r.value.event)||void 0===t?void 0:t.find((function(e){return e.type===C.Sd.Linker}));n.execute=e}function w(e){var t,n=null===(t=r.value.event)||void 0===t?void 0:t.find((function(e){return e.type===C.Sd.Code}));n.codeContent=e}function y(e){r.value.event[0]&&(r.value.event[0].type=e)}function x(){return A.apply(this,arguments)}function A(){return A=(0,p.A)((0,c.A)().mark((function e(){var n,o,r;return(0,c.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==t.value.length){e.next=2;break}return e.abrupt("return",Promise.resolve([]));case 2:return e.next=4,s(t.value);case 4:if(n=e.sent,!1!==n.pass){e.next=14;break}if(o=n.pathId,r=n.errorModule,!o){e.next=12;break}return e.next=10,v(o);case 10:return(0,g.nextTick)((function(){i.value.showAndValidate(r)})),e.abrupt("return",Promise.resolve(!1));case 12:return he.A.error((0,S.t)("common.page.buttonSet.tip2")),e.abrupt("return",Promise.resolve(!1));case 14:return console.log(n,"<---allValidateRes"),e.abrupt("return",Promise.resolve(t.value));case 16:case"end":return e.stop()}}),e)}))),A.apply(this,arguments)}function I(){v(null)}Y.initRoleList();var T=[{bizType:"PAAS",bizTypeId:e.templateId}];return Y.initButtonSetChooseUpdateTriggerList(T),{btnList:t,updateArr:l,buttonSetEditDom:n,isExpand:o,nowFocusItem:r,changeFocus:v,buttonSetDetailSetDom:i,nowFocusItemIndex:a,changeTrigger:h,changeLinker:b,changeCode:w,changeBtnListEventType:y,getValue:x,initArr:d}},components:{ButtonSetEdit:E,ButtonSetPreview:N,ButtonSetDetailSet:ge}}),we=be,Se=(0,k.A)(we,v,f,!1,null,"55335ab4",null),Ce=Se.exports,ye={data:function(){return{pending:!1}},computed:{templateId:function(){return this.$route.query.formId}},mounted:function(){this.getButtonSetData()},methods:{getButtonSetData:function(){var e=this;(0,d.m$)({module:"PAAS",moduleId:this.templateId,showArea:"list",isEdit:!0}).then((function(t){if(0===t.status){var n=(0,m.GG)(t.data);e.$refs.buttonSetDom.initArr(n)}}))},save:function(){var e=this;return(0,p.A)((0,c.A)().mark((function t(){var n,o;return(0,c.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(1!=e.pending){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$refs.buttonSetDom.getValue();case 4:if(n=t.sent,n){t.next=7;break}return t.abrupt("return");case 7:if(o=(0,m.b2)(n),o.length){t.next=10;break}return t.abrupt("return",e.$message.warning("请至少添加一个按钮再保存"));case 10:e.pending=!0,(0,d.A0)({module:"PAAS",buttonList:o,moduleId:e.templateId}).then((function(t){if(0===t.status)return e.$message.success(e.$t("common.base.saveSuccess")),e.getButtonSetData();e.$message.error(t.message)}))["finally"]((function(){e.pending=!1}));case 13:case"end":return t.stop()}}),t)})))()}},components:{ButtonSet:Ce}},xe=ye,Ae=(0,k.A)(xe,s,l,!1,null,null,null),Ie=Ae.exports},88376:function(e,t,n){n.d(t,{BI:function(){return a},D$:function(){return l},GK:function(){return c},GL:function(){return i},fG:function(){return s},oG:function(){return u}});n(67880);var o=n(22229),r="/api/application";function a(e){return o.A.post("".concat(r,"/outside/trigger/getTriggerInfoList"),e)}function i(e){return o.A.post("".concat(r,"/outside/trigger/updateTrigger"),e)}function u(e){return o.A.post("".concat(r,"/outside/trigger/deleteTrigger"),{triggerId:e})}function s(e){return o.A.post("".concat(r,"/outside/trigger/getTriggerFormInfo"),e)}function l(e){return o.A.post("".concat(r,"/outside/trigger/manualTrigger"),e)}function c(e){return o.A.get("".concat(r,"/outside/trigger/v3/getAllExecuteWayList"),e)}}}]);