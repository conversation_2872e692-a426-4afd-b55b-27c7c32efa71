"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[926],{48926:function(e,t,a){a.r(t),a.d(t,{default:function(){return Ha}});a(80793),a(46622),a(36700),a(7509),a(19944);var n=function(){var e,t,a=this,n=a._self._c;a._self._setupProxy;return n("div",{staticClass:"common-list-container__v2"},[n("div",{staticClass:"commonn-list-header__v2 int-tags-btn"},[n("BizIntelligentTagsFilterPanelOperatorButton",{attrs:{"show-dot":a.showTagOperatorButtonDot,active:a.filterTagPanelShow},on:{click:a.changeIntelligentTagsFilterPanelShow}}),n("div",[a.appId?n("ViewportDropdown",{ref:"viewportListRef",attrs:{module:"paas","app-id":a.appId,"template-id":a.templateId,"current-view":a.currentView},on:{choose:a.chooseView,edit:a.editViewByViewport,create:a.editViewByViewport}}):a._e(),n("AdvancedSearchModal",{ref:"advancedSearchModalRef",attrs:{module:"paas",config:a.viewConfig,"allow-empty":!0,fields:a.advanceSearchColumn,"before-save":a.beforeSaveView},on:{save:a.handleViewportSave}},[n("paas-common-advanced-search-form",{attrs:{slot:"prefix","status-label":a.statusLabel,"angle-label":a.angleLabel},slot:"prefix"})],1)],1),n("div",{staticClass:"flex-1"}),n("div",{staticClass:"commonn-advanced-search"},[n("el-input",{directives:[{name:"trim",rawName:"v-trim:blur",arg:"blur"}],staticClass:"search-input",attrs:{placeholder:a.$t("view.template.list.placeholder1")},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&a._k(e.keyCode,"enter",13,e.key,"Enter")?null:a.handleBaseSearch.apply(null,arguments)}},model:{value:a.searchParams.keyword,callback:function(e){a.$set(a.searchParams,"keyword",e)},expression:"searchParams.keyword"}},[n("el-button",{staticClass:"search-input__append",attrs:{slot:"append"},on:{click:a.handleBaseSearch},slot:"append"},[a._v(a._s(a.$t("common.base.search")))])],1),n("el-button",{staticClass:"ml_12",attrs:{type:"plain-third"},on:{click:a.reset}},[a._v(a._s(a.$t("common.base.reset")))]),n("i",{staticClass:"iconfont icon-setting pointer",on:{click:a.openSearchConditionSetting}})],1),n("div",{staticClass:"advanced-search-btn pointer"},[n("AdvancedSearch",{ref:"popperAdvancedSearchRef",attrs:{fields:a.advanceSearchColumn,"search-model":a.viewportSearchModel,"has-save":!!(a.currentView&&a.currentView.viewId&&a.currentView.authEdit),"in-common-use":a.inCommonUse,mode:"pass"},on:{search:a.handleAdvancedSearch,create:a.createViewBySearchModel,save:a.updateViewBySearchModel,changeCommonUse:a.changeCommonUse}})],1)],1),n("el-collapse-transition",[n("div",{directives:[{name:"show",rawName:"v-show",value:a.packUp,expression:"packUp"}],staticClass:"common-list-header-nav"},[a.listPageIsCardStyle?a._e():[n("div",{staticClass:"list-header-nav-item"},[n("label",{class:{"multi-language__txt":!a.isLanguageIsZh,"column-color":!0}},[a._v(a._s(a.$t("common.base.dataState"))+":")]),a._l(a.formStatus,(function(e){return n("div",{key:e.code,staticClass:"item",class:e.code===a.filterParams.status&&"selected",on:{click:function(t){return a.changeFilterParams("status",e.code,!0)}}},[n("span",[a._v(" "+a._s(e.name)+" ("+a._s(e.num)+") "),e.childList&&e.childList.length?n("i",{class:["iconfont",a.collaspe||e.code!==a.filterParams.status?"icon-more":"icon-Icon_up"],on:{click:function(t){return t.stopPropagation(),a.changeFilterParams("status",e.code,!0)}}}):a._e()])])}))],2),a.customStatusList.length&&!a.collaspe?n("div",{staticClass:"list-header-nav-item"},[n("el-checkbox-group",{on:{change:function(e){return a.changeFilterParams("status",a.filterParams.status,!1)}},model:{value:a.filterParams.customStatusList,callback:function(e){a.$set(a.filterParams,"customStatusList",e)},expression:"filterParams.customStatusList"}},a._l(a.customStatusList,(function(e){return n("el-checkbox",{key:e.code,attrs:{label:e.name}},[n("span",[a._v(a._s(a.getFlowStateLabel(e.name)))]),a._v(" ("+a._s(e.num)+") ")])})),1)],1):a._e()],n("div",{staticClass:"list-header-nav-item"},[n("label",{class:{"multi-language__txt":!a.isLanguageIsZh,"column-color":!0}},[a._v(a._s(a.$t("common.base.createAngle"))+":")]),a._l(a.createView,(function(e){return n("div",{key:e.value,staticClass:"item",class:e.value===a.filterParams.createView&&"selected",on:{click:function(t){return a.changeFilterParams("createView",e.value,0)}}},[n("span",[a._v(a._s(e.label))])])}))],2)],2)]),n("div",{staticClass:"pack-up"},[n("div",{staticClass:"pack-up-btn",on:{click:function(e){a.packUp=!a.packUp}}},[n("i",{class:["iconfont",a.packUp?"icon-Icon_up":"icon-more"]})])]),n("div",{staticClass:"common-list-table__flex-row"},[n("BizIntelligentTagsFilterPanel",a._g(a._b({},"BizIntelligentTagsFilterPanel",a.filterTagsPanelBindAttr,!1),a.filterTagsPanelBindOn)),a.fullLoading?a._e():n("div",{class:["common-list-view__v2",a.listPageIsCardStyle?"common-list-view__v2-card":null]},[n("div",{staticClass:"common-list-view-header__v2"},["{}"===JSON.stringify(a.authData)||a.houseCarTenantGray||a.isSettlementForm||a.isSettlementChangeForm?a._e():n("div",{staticClass:"common-list-view-header__v2-left"},[n("el-button",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.approveAuth,expression:"authData.approveAuth"}],attrs:{type:"primary"},on:{click:function(e){return a.goToCreate()}}},[a._v(" "+a._s(a.$t("common.base.create"))+" ")]),a.listPageIsTableStyle?n("el-button",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.deleteAuth,expression:"authData.deleteAuth"}],attrs:{type:"plain-third"},on:{click:a.deleteHandler}},[a._v(" "+a._s(a.$t("common.base.delete"))+" ")]):a._e()],1),a.shareLinkOpen?n("div",{staticClass:"common-list-view-header__v2-left mar-l-12"},[n("el-button",{attrs:{type:"plain-third"},on:{click:a.openShareFormDialog}},[a._v(" "+a._s(a.$t("view.template.list.generateLink"))+" ")])],1):a._e(),a.isContainerGray&&a.pageButtonList.length?n("div",{staticClass:"common-list-view-header__v2-left mar-l-12"},a._l(a.pageButtonList,(function(e,t){return n("el-button",{key:t,attrs:{type:e.viewType,loading:[a.ButtonSetDetailForButtonConcatEventEnum.Trigger,a.ButtonSetDetailForButtonConcatEventEnum.Code].includes(e.event[0].type)&&a.pageButtonLoading},on:{click:function(t){return a.handelPageButtonClick(e,a.multipleSelection)}}},[a._v(" "+a._s(e.name)+" ")])})),1):a._e(),n("div",{staticClass:"common-list-view-header__v2-right"},[a.listPageIsTableStyle?n("BizIntelligentTaggingButtonMulti",a._g(a._b({},"BizIntelligentTaggingButtonMulti",a.taggingBindAttr,!1),a.taggingBindOn)):a._e(),a.houseCarTenantGray||null!==(e=a.authData)&&void 0!==e&&e.exportAuth||null!==(t=a.authData)&&void 0!==t&&t.approveAuth?n("el-dropdown",{attrs:{trigger:"click"}},[n("div",{staticClass:"el-dropdown-btn pointer"},[a._v(" "+a._s(a.$t("common.base.moreOperator"))),n("i",{staticClass:"iconfont icon-fdn-select"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a.houseCarTenantGray&&a.listPageIsTableStyle?[n("el-dropdown-item",[n("div",{on:{click:function(e){return a.exportData(!1)}}},[a._v(a._s(a.$t("common.base.export")))])]),n("el-dropdown-item",[n("div",{on:{click:function(e){return a.exportData(!0)}}},[a._v(a._s(a.$t("common.base.exportAll")))])])]:["{}"!==JSON.stringify(a.authData)&&a.listPageIsTableStyle?n("el-dropdown-item",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.exportAuth,expression:"authData.exportAuth"}]},[n("div",{on:{click:function(e){return a.exportData(!1)}}},[a._v(a._s(a.$t("common.base.export")))])]):a._e(),"{}"!==JSON.stringify(a.authData)&&a.listPageIsTableStyle?n("el-dropdown-item",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.exportAuth,expression:"authData.exportAuth"}]},[n("div",{on:{click:function(e){return a.exportData(!0)}}},[a._v(a._s(a.$t("common.base.exportAll")))])]):a._e()],"{}"!==JSON.stringify(a.authData)?n("el-dropdown-item",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.approveAuth,expression:"authData.approveAuth"}]},[n("div",{on:{click:a.importData}},[a._v(a._s(a.$t("common.base.import")))])]):a._e()],2)],1):a._e(),a.listPageIsCardStyle?a._e():n("div",{staticClass:"el-dropdown-btn pointer",on:{click:a.showAdvancedSetting}},[a._v(" "+a._s(a.$t("common.base.choiceCol"))),n("i",{staticClass:"iconfont icon-fdn-select"})]),a.viewModelGray?n("ViewChangeDropdown",{attrs:{"rest-adv-params":a.reset},on:{refresh:a.successRefreshCallBack}}):a._e()],1)]),a.listPageIsTableStyle?[n("el-table",{directives:[{name:"table-style",rawName:"v-table-style"},{name:"loading",rawName:"v-loading",value:a.loading,expression:"loading"}],ref:"table",staticClass:"common-list-table__v2 mt_8",attrs:{"header-row-class-name":"common-list-table-header__v2",data:a.data.list,height:a.tableCaleHeight,border:"",stripe:""},on:{select:a.handleSelection,"select-all":a.handleSelection,"sort-change":a.sortChangeHandler,"header-dragend":a.handleHeaderDragend}},[n("el-table-column",{attrs:{type:"selection",width:"48",align:"center"}}),a._l(a.tableColumns,(function(e){return n("el-table-column",{key:e.fieldName,attrs:{align:e.align,label:e.displayName,prop:e.fieldName,sortable:e.sortable,width:e.width,"min-width":e.minWidth,fixed:!!e.fixed&&"left","show-overflow-tooltip":!(a.isOpenData&&"user"===e.formType),resizable:""},scopedSlots:a._u([{key:"default",fn:function(t){var i,r;return["WFLOW_STATUS"==e.fieldName?[a._l(t.row.customStatus,(function(e,i){return[e?n("span",{key:i,staticClass:"flow-state-block",style:{"background-color":a.flowStateEnum.getBgColor(t.row.status,1),color:a.flowStateEnum.getColor(t.row.status)}},[[a._v(a._s(a.getFlowStateLabel(e)))]],2):a._e()]})),null!==(i=t.row)&&void 0!==i&&i.pauseFlag?[n("span",{staticClass:"flow-state-block",style:{"background-color":" #F56C6C",color:"#fff"}},[a._v(" "+a._s(a.$t("buttons.pause"))+a._s(a.$t("formType.middle"))+" ")])]:a._e()]:"serialNumber"==e.fieldName?[n("BizIntelligentTagsViewToConfig",{attrs:{type:"table",value:t.row[e.fieldName],"tags-list":t.row.labelList||[]},on:{viewClick:function(n){return a.goToDetail(t.row,t.row[e.field])}}})]:"sourceTemplateName"===e.fieldName&&a.isOpenConnector?[a._v(" "+a._s(t.row.connectorInfo&&t.row.connectorInfo["sourceTemplateName"])+" ")]:"sourceBizNo"===e.fieldName&&a.isOpenConnector?[t.row.connectorInfo?n("a",{staticClass:"view-detail-btn",attrs:{href:"javascript:;"},on:{click:function(e){return a.openSourceBizNoTab(t.row.connectorInfo)}}},[a._v(" "+a._s(t.row.connectorInfo["sourceBizNo"])+" ")]):a._e()]:"related_task"==e.formType?[Array.isArray(t.row[e.fieldName])?[n("div",{staticClass:"common-table-column__view-list table-blacklist"},a._l(t.row[e.fieldName],(function(i,r){return n("div",{key:i.taskId,staticClass:"common-table-column__view-list-item"},[n("a",{staticClass:"view-detail-btn",staticStyle:{display:"flex","align-items":"center"},attrs:{href:"javascript:;"},on:{click:function(e){return a.goToTaskDetail(i)}}},[a._v(" "+a._s(i.taskNo)+" ")]),a.showLinkIntelligentTags?n("BizIntelligentTagsView",{attrs:{type:"detail","tags-list":a.getLinkInitIntelligentTagsList(i),config:{normalShowType:"icon",normalMaxLength:1},"show-more-icon":!1}}):a._e(),a._v(" "+a._s("".concat(t.row[e.fieldName].length-1!==r?",":""))+" ")],1)})),0)]:a._e()]:"product"===e.formType?[Array.isArray(t.row[e.fieldName])?[n("div",{staticClass:"common-table-column__view-list table-blacklist"},a._l(t.row[e.fieldName],(function(i,r){return n("div",{key:i["".concat(e.fieldName,"_productId")],staticClass:"common-table-column__view-list-item"},[n("a",{staticClass:"view-detail-btn",staticStyle:{display:"flex","align-items":"center"},attrs:{href:"javascript:;"},on:{click:function(t){return a.handleToProductView(i["".concat(e.fieldName,"_productId")])}}},[a._v(" "+a._s(i["".concat(e.fieldName,"_name")])+" ")]),a.showLinkIntelligentTags?n("BizIntelligentTagsView",{attrs:{type:"detail","tags-list":a.getLinkInitIntelligentTagsList(t.row[e.fieldName],"".concat(e.fieldName,"_labelList")),config:{normalShowType:"icon",normalMaxLength:1},"show-more-icon":!1}}):a._e(),a._v(" "+a._s("".concat(t.row[e.fieldName].length-1!==r?",":""))+" ")],1)})),0)]:a._e()]:"assignUser"==e.fieldName?[a.isOpenData&&t.row.approveUserStaffIds&&Array.isArray(t.row.approveUserStaffIds)&&t.row.approveUserStaffIds.length>0?a._l(t.row.approveUserStaffIds,(function(e,i){return n("span",{key:e},[n("open-data",{attrs:{type:"userName",openid:e||""}}),i+1!==t.row.approveUserStaffIds.length?[a._v("，")]:a._e()],2)})):[a._v(" "+a._s(t.row.approveUserName||"")+" ")]]:"CREATE_USER"==e.fieldName&&t.row.createUser?[a.isOpenData&&t.row.createUser.staffId?[n("open-data",{attrs:{type:"userName",openid:t.row.createUser.staffId}})]:[a._v(" "+a._s((null===(r=t.row.createUser)||void 0===r?void 0:r.displayName)||"")+" ")]]:"user"===e.formType?[a.isOpenData&&t.row[e.fieldName]&&Array.isArray(t.row[e.fieldName])?n("div",a._l(t.row[e.fieldName],(function(i,r){return n("span",{key:i.userId},[n("open-data",{attrs:{type:"userName",openid:i.staffId}}),r+1!==t.row[e.fieldName].length?[a._v("，")]:a._e()],2)})),0):[a._v(" "+a._s(a._f("fmt_form_field")(t.row[e.fieldName],e))+" ")]]:"logistics"===e.formType?[n("div",{class:{link:a.hasQuota&&a.isLogisticsNo(e)},on:{click:function(n){return a.handleViewLogistics(t.row,e)}}},[a._v(" "+a._s(a._f("fmt_form_field")(t.row[e.fieldName.split("_")[0]],e))+" ")])]:"materialOrder"===e.formType?[n("div",{staticClass:"operation—btn"},[n("span",{staticClass:"operation—primary",on:{click:function(n){return a.handleShowMaterial(e,t.row)}}},[a._v(a._s(a.$t("common.base.view")))])])]:"providerSettlement"===e.formType?[n("div",{staticClass:"operation—btn"},[n("span",{staticClass:"operation—primary",on:{click:function(n){return a.handleShowproviderSettlementDialog(e,t.row)}}},[a._v(a._s(a.$t("common.base.view")))])])]:"customer"===e.formType?[a._l(t.row[e.fieldName],(function(e,t){return[a.showLinkIntelligentTags?n("BizIntelligentTagsViewToConfig",{key:t,staticClass:"table-blacklist",attrs:{type:"table",value:e.name,"tags-list":e.labelList||[]},on:{viewClick:function(t){return a.toCustomerPage(e.id)}}}):n("a",{key:t,class:[e.id?"view-detail-btn":"view-detail-btn__normal"],attrs:{href:"javascript:;"},on:{click:function(t){return a.toCustomerPage(e.id)}}},[a._v(" "+a._s(e.name)+" ")])]}))]:"text"===e.formType&&a.isColumnLink(e)?[n("a",{staticClass:"view-detail-btn",staticStyle:{display:"flex","align-items":"center"},attrs:{href:"javascript:;"},on:{click:function(n){return a.handleTextLinkClick(t.row,e)}}},[a._v(" "+a._s(a._f("fmt_form_field")(t.row[e.fieldName],e))+" ")])]:[a._v(" "+a._s(a._f("fmt_form_field")(t.row[e.fieldName],e))+" ")]]}}],null,!0)})})),n("el-table-column",{attrs:{fixed:"right",label:a.$t("common.base.operation"),width:"120"},scopedSlots:a._u([{key:"default",fn:function(e){var t;return["{}"!==JSON.stringify(a.authData)&&a.isShowEditBtn(e.row)||a.moreBtnList.length>1&&(null===(t=a.rowBtn.wfButtonVO)||void 0===t||!t.isBackStart)?n("el-dropdown",{staticClass:"dropdown",on:{"visible-change":function(t){return a.handleMoreButtonList(t,e.row)}}},[n("span",{staticClass:"cur-point pointer"},[a._v(" "+a._s(a.$t("common.base.more"))),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{directives:[{name:"loading",rawName:"v-loading",value:a.moreBtnLoading,expression:"moreBtnLoading"}],staticClass:"lang-select-dropdown table-min-width",attrs:{slot:"dropdown"},slot:"dropdown"},[a.isShowMoreOperatorBtns(e.row)?n("div",a._l(a.moreBtnList,(function(t,i){return n("el-dropdown-item",{key:i,attrs:{disabled:a.checkedOperatorButtonIsDisabled(e.row,t)}},[t.isOpen?n("div",{on:{click:function(n){return a.handleBtn(e.row,t)}}},[a._v(" "+a._s(a.btnName(t))+" ")]):a._e()])})),1):a._e(),a.moreBtnLoading?a._e():n("div",[a.isShowMoreOperatorBtns(e.row)?a._e():n("el-dropdown-item",[n("span",{on:{click:function(t){return a.goToDetail(e.row)}}},[a._v(a._s(a.$t("common.base.detail")))])]),"{}"!==JSON.stringify(a.authData)&&a.isShowEditBtn(e.row)?n("el-dropdown-item",{attrs:{disabled:!a.currentOperatorRecordIsEmpty&&a.currentOperatorRow.bizId===e.row.bizId}},[n("div",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.editAuth,expression:"authData.editAuth"}],on:{click:function(t){return a.goToEdit(e.row)}}},[a._v(" "+a._s(a.$t("common.base.edit")))])]):a._e()],1)])],1):n("div",{staticClass:"is-flex align-items-center operator-column"},[a.isShowMoreOperatorBtns(e.row)?n("operator-btn",{attrs:{record:e.row,pending:!a.currentOperatorRecordIsEmpty&&a.currentOperatorRow.bizId===e.row.bizId},on:{handleBtnClick:a.handleOperatorBtnClick}}):n("div",{staticClass:"operation—btn"},[n("span",{staticClass:"operation—primary",on:{click:function(t){return a.goToDetail(e.row)}}},[a._v(a._s(a.$t("common.base.detail")))])]),n("div",{staticClass:"operation—btn"},["{}"!==JSON.stringify(a.authData)&&a.isShowEditBtn(e.row)?n("el-button",{directives:[{name:"auth",rawName:"v-auth",value:a.authData.editAuth,expression:"authData.editAuth"}],staticClass:"button",attrs:{type:"text",disabled:!a.currentOperatorRecordIsEmpty&&a.currentOperatorRow.bizId===e.row.bizId},on:{click:function(t){return a.goToEdit(e.row)}}},[a._v(" "+a._s(a.$t("common.base.edit"))+" ")]):a._e()],1)],1)]}}],null,!1,3039622579)}),n("template",{slot:"empty"},[n("no-data-view",{directives:[{name:"show",rawName:"v-show",value:!a.loading,expression:"!loading"}]})],1)],2),n("div",{staticClass:"common-list-table-footer__v2 mt_12"},[n("el-pagination",{attrs:{background:"","page-sizes":a.defaultTableData.defaultPageSizes,"page-size":a.searchParams.pageSize,"current-page":a.searchParams.pageNum,layout:"slot, prev, pager,next, sizes,jumper",total:a.data.total},on:{"current-change":a.handlePageNumChange,"size-change":a.handleSizeChange}},[n("div",{staticClass:"custom-pagination__total"},[n("i18n",{attrs:{path:"common.base.table.totalCount"}},[n("em",{staticClass:"pagination-total__num",attrs:{place:"count"}},[a._v(a._s(a.data.total))])]),a.multipleSelection.length>0?[n("i18n",{attrs:{path:"common.base.table.selectedNth"}},[n("em",{staticClass:"pagination-total__warn",attrs:{place:"count"}},[a._v(" "+a._s(a.multipleSelection.length))])]),n("em",{staticClass:"pagination-total__warn pointer",on:{click:a.toggleSelection}},[a._v(a._s(a.$t("common.base.clear")))])]:a._e()],2)])],1)]:a._e(),a.listPageIsCardStyle?n("CardView",{ref:"cardViewRef",attrs:{"build-search-params":a.buildParams},on:{btnClick:a.handleBtn,cardClick:a.goToDetail,edit:a.goToEdit}}):a._e()],2)],1),n("biz-select-column",{ref:"advanced",attrs:{"submit-auto-close":!1},on:{save:a.saveColumnStatus}}),n("base-export-group",{ref:"exportPanel",attrs:{alert:a.exportAlert,columns:a.exportColumns,"build-params":a.buildExportParams,validate:a.checkExportCount,method:"post",action:"/api/paas/outside/pc/form/excel/content/export","storage-key":"template-list-export_".concat(a.templateId),group:""}}),n("base-import-upload",{ref:"templateUpload",attrs:{title:a.$t("view.template.list.importData"),"is-import-special":"","template-url":"/api/paas/outside/pc/form/excel/import/template?formTemplateId=".concat(a.templateId),action:"/excels/paas/form/import?formTemplateId=".concat(a.templateId)},on:{success:a.successRefreshCallBack}}),n("logistics-dialog",{attrs:{visible:a.logisticsDialogShow,info:a.logisticsInfo},on:{"update:visible":function(e){a.logisticsDialogShow=e}}}),n("search-condition-setting",{ref:"SearchConditionSetting",attrs:{fields:a.fields,"template-id":a.templateId,value:a.searchCondition},on:{"update:value":function(e){a.searchCondition=e}}}),n("material-table-dialog",{ref:"materialDialog"}),n("providerSettlement-table-dialog",{ref:"providerSettlementDialog"}),n("on-tag-dialog",{ref:"onTagDialog",attrs:{"biz-ids":a.selectedIds,"tag-id-list":a.tagIdList,"template-biz-id":a.templateId,"app-id":a.appId},on:{saveOnTag:a.saveOnTag}}),n("operator-dialog",{ref:"operatorDialog",on:{handleJumpDetail:a.goToDetail,refreshData:a.successRefreshCallBack,loading:function(e){return a.handleRowOperatorLoading(e,!0)},closeLoading:function(e){return a.handleRowOperatorLoading(e,!1)}}}),n("share-form-dialog",{ref:"shareFormDialog",attrs:{"origin-fields":a.fields,"template-name":a.templateName,"template-id":a.templateId,"app-id":a.appId,"form-write-config":a.formWriteConfig}}),n("pass-save",{ref:"passSaveRef",attrs:{"has-save":!!(a.currentView&&a.currentView.viewId&&a.currentView.authEdit),"current-view":a.currentView,"before-save":a.beforeSaveView,config:a.viewConfig,"filter-params":a.filterParams,module:"paas"},on:{save:a.handleViewportSave}}),n("back-node-dialog",{ref:"backNodeDialogRef",attrs:{"back-button":a.backButton},on:{update:a.updateBtn}}),a.viewModelGray?n("CardViewManageDrawer",{on:{refresh:a.handlePageModeRefresh}}):a._e()],1)},i=[],r=a(18885),o=a(42881),s=a(35730),l=a(71357),c=a(62361),u=(a(98316),a(67880),a(87313),a(2286),a(44807),a(3923),a(71620),a(35256),a(21484),a(8326),a(13560),a(89716),a(27408),a(76119),a(16961),a(14126),a(54615),a(7354),a(89370),a(32807),a(88747),a(24929),a(33438),a(55650),a(75069),a(39789),a(42925),a(93431),a(53417),a(8200),a(36886),a(56831),a(4118),a(5981),a(63074),a(39724),a(21633),a(69594),a(79526),a(20592),a(13262),a(68735),a(74526)),d=a(69396),m=a(48215),p=a(3402),f=a(22229),v="/api/workbench/outside";function h(e){return w.apply(this,arguments)}function w(){return w=(0,o.A)((0,r.A)().mark((function e(t){var a;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.get("".concat(v,"/common/view/list"),{module:t});case 3:if(a=e.sent,!a.succ&&0!==a.status){e.next=6;break}return e.abrupt("return",Promise.resolve(a.data||[]));case 6:return e.abrupt("return",Promise.reject(a));case 9:return e.prev=9,e.t0=e["catch"](0),e.abrupt("return",Promise.reject(e.t0));case 12:case"end":return e.stop()}}),e,null,[[0,9]])}))),w.apply(this,arguments)}function g(e){return b.apply(this,arguments)}function b(){return b=(0,o.A)((0,r.A)().mark((function e(t){var a;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.get("".concat(v,"/common/view/getViewUrl"),t);case 3:if(a=e.sent,!a.succ&&0!==a.status){e.next=6;break}return e.abrupt("return",Promise.resolve(a.data));case 6:return e.abrupt("return",Promise.reject(a));case 9:return e.prev=9,e.t0=e["catch"](0),e.abrupt("return",Promise.reject(e.t0));case 12:case"end":return e.stop()}}),e,null,[[0,9]])}))),b.apply(this,arguments)}function C(e){return _.apply(this,arguments)}function _(){return _=(0,o.A)((0,r.A)().mark((function e(t){var a,n;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a=t.viewId?"update":"create",e.next=4,f.A.post("".concat(v,"/common/view/").concat(a),t);case 4:if(n=e.sent,!n.succ&&0!==n.status){e.next=7;break}return e.abrupt("return",Promise.resolve(n.data));case 7:return e.abrupt("return",Promise.reject(n));case 10:return e.prev=10,e.t0=e["catch"](0),e.abrupt("return",Promise.reject(e.t0));case 13:case"end":return e.stop()}}),e,null,[[0,10]])}))),_.apply(this,arguments)}function y(e){return S.apply(this,arguments)}function S(){return S=(0,o.A)((0,r.A)().mark((function e(t){var a;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.post("".concat(v,"/common/view/delete"),t);case 3:if(a=e.sent,!a.succ&&0!==a.status){e.next=6;break}return e.abrupt("return",Promise.resolve(a.data));case 6:return e.abrupt("return",Promise.reject(a));case 9:return e.prev=9,e.t0=e["catch"](0),e.abrupt("return",Promise.reject(e.t0));case 12:case"end":return e.stop()}}),e,null,[[0,9]])}))),S.apply(this,arguments)}var A=a(97022),k=a(92060),I=(a(51280),"template_list_search_condition"),N="template_list_columns",x="template_list_page_size",L={searchCondition:I,columns:N,pageSize:x},T={watch:{searchCondition:function(e){this.setListStorage(this.templateId,e,"searchCondition")},columns:function(e){var t=[];Array.isArray(e)&&e.length>0&&(t=e.filter((function(e){return e.show})).map((function(e){return{fieldName:"".concat(null!==e&&void 0!==e&&e.fixed?"".concat(e.fieldName,"_fixed"):e.fieldName),width:e.width}}))),this.setListStorage(this.templateId,t,"columns")}},methods:{getListStorage:function(e,t){return(0,o.A)((0,r.A)().mark((function a(){return(0,r.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,(0,k["if"])(e+"_"+L[t]);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),a)})))()},setListStorage:function(e,t,a){return(0,o.A)((0,r.A)().mark((function n(){return(0,r.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,k.EI)(e+"_"+L[a],t);case 2:return n.abrupt("return",n.sent);case 3:case"end":return n.stop()}}),n)})))()}}},P={methods:{handleShowSave:function(){var e,t,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(!this.currentView||"默认视图"===this.currentView.viewName)return!1;if(null===(e=this.currentView)||void 0===e||null===(e=e.searchModel)||void 0===e||!e.length)return!(null===(t=a[0])||void 0===t||!t.value);if(this.isFlagSaveStatus)return!0;var n=a.map((function(e){var t=e.fieldName,a=e.operator,n=e.value;return{fieldName:t,value:n,operator:a}})),i=!1;if(this.currentView.searchModel.length===a.length)for(var r=0;r<a.length;r++)JSON.stringify(this.currentView.searchModel[r])!==JSON.stringify(n[r])&&(i=!0);else i=!0;return i}}},V=a(78831),F=a(44391),D=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.list.setSearchConditions"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"search-condition-setting"},[t("div",{staticClass:"search-condition-setting-tips"},[t("i18n",{attrs:{path:"view.template.list.tip5"}},[t("span",{attrs:{place:"max"}},[e._v("5")])])],1),t("el-input",{attrs:{placeholder:e.$t("view.template.list.tip6"),"prefix-icon":"el-icon-search",clearable:""},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}}),t("el-checkbox-group",{model:{value:e.conditions,callback:function(t){e.conditions=t},expression:"conditions"}},e._l(e.conditionFields,(function(a){return t("el-checkbox",{key:a.fieldName,attrs:{label:a.fieldName,disabled:e.isDisabled&&!e.conditions.includes(a.fieldName)}},[e._v(" "+e._s(a.displayName)+" ")])})),1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v(e._s(e.$t("common.base.save")))])],1)])},$=[],B=a(16444),O=a(80906),E=["text","textarea","phone","number","user"],R={name:"search-condition-setting",props:{fields:{type:Array,default:function(){return[]}},value:{type:Array,default:function(){return[]}},templateId:{type:String,default:""}},data:function(){return{visible:!1,keyword:"",conditions:[]}},computed:{isDisabled:function(){return this.conditions.length>=B.s6},conditionFields:function(){var e=this,t=[{displayName:this.$t("view.template.list.systemFields.assignUser"),fieldName:"approveUserName"},{displayName:this.$t("view.createUser"),fieldName:"createUser"}],a=this.fields.filter((function(e){return E.includes(e.formType)})).concat(t);return a.filter((function(t){return(0,O.Pm)(t,!0)&&t.displayName.indexOf(e.keyword)>-1}))}},watch:{value:function(e){this.conditions=e||[]}},methods:{open:function(){this.conditions=this.value,this.visible=!0},submit:function(){this.$emit("update:value",this.conditions),this.visible=!1}}},M=R,z=a(49100),U=(0,z.A)(M,D,$,!1,null,"8552139a",null),W=U.exports,j=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{show:e.visible,title:e.$t("common.form.preview.materialOrder.materialDetail"),width:"740px"},on:{"update:show":function(t){e.visible=t},close:e.handleClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("el-button",{attrs:{type:"primary"},on:{click:e.handleClose}},[e._v(e._s(e.$t("common.base.close")))])]},proxy:!0}])},[t("div",{staticClass:"material-dialog-body"},[e.visible?t("form-material-order-V2",{attrs:{field:e.field,value:e.value,"table-row":e.tableRow,"currency-code-field-name":e.currencyCodeFieldName,"is-filter-attach":!1,"is-list-view":!0}}):e._e()],1)])},q=[],G={name:"material-table-dialog",data:function(){return{loading:!1,visible:!1,field:{},value:[],tableRow:{},currencyCodeFieldName:""}},methods:{handleClose:function(){this.visible=!1},open:function(e,t,a,n){this.field=e,this.value=t,this.tableRow=a,this.currencyCodeFieldName=n,this.visible=!0}}},H=G,J=(0,z.A)(H,j,q,!1,null,null,null),Q=J.exports,Y=function(){var e=this,t=e._self._c;return t("div",{staticClass:"operation—btn"},[t("div",{staticClass:"operation—btn-box"},[e._l(e.buttons,(function(a){return[a.isOpen?t("el-button",{key:a.code,attrs:{type:"text",disabled:"share"!==a.enName&&e.pending||e.checkedSomeButtonIsDisabled(a)},on:{click:function(t){return e.submit(a)}}},[e._v(" "+e._s(e.btnName(a))+" ")]):e._e()]}))],2)])},K=[],Z=a(64055),X=a(19055),ee={name:"operator-btn",props:{record:{type:Object,default:function(){}},pending:{type:Boolean,default:function(){return!1}}},computed:{buttons:function(){var e=this.record,t=e.contentVO,a=void 0===t?{}:t,n=e.wfButtonVO,i=(null===n||void 0===n?void 0:n.buttons)||[];return a.cnName=this.$t("common.base.share"),a.enName="share",i.map((function(e){return(0,l.A)((0,l.A)({},e),{},{isOpen:!0})})).concat([a])}},methods:{submit:function(e){console.log("handleBtnClickhandleBtnClick",this.record),this.$emit("handleBtnClick",this.record,e)},btnName:function(e){var t;return null!==e&&void 0!==e&&e.nameLanguage?null===e||void 0===e?void 0:e.nameLanguage[null!==(t=this.$i18n.locale)&&void 0!==t?t:Z.As]:e.cnName},checkedSomeButtonIsDisabled:function(e){var t;return(null===(t=this.record.wfButtonVO)||void 0===t?void 0:t.pauseFlag)&&e.enName!==X.A.PAUSE.value}}},te=ee,ae=(0,z.A)(te,Y,K,!1,null,"7498a9b8",null),ne=ae.exports,ie=function(){var e=this,t=e._self._c;return e.isRecordEmpty?e._e():t("div",[t("approval-dialog",{ref:"approvalDialog",attrs:{config:e.currentOpBtn,"task-id":e.flowTaskId,"build-form-params":e.buildFormValueParams},on:{success:e.successCallbackFn}}),t("share-link-dialog",{ref:"shareLinkDialog",attrs:{"share-data":e.record.contentVO,"form-content-id":e.record.bizId}}),t("tranfer-dialog",{ref:"transferDialog",attrs:{config:e.currentOpBtn,"task-id":e.flowTaskId,"build-form-params":e.buildFormValueParams},on:{success:e.successCallbackFn}}),t("pause-dialog",{ref:"pauseDialog",attrs:{config:e.currentOpBtn,"task-id":e.flowTaskId,"build-form-params":e.buildFormValueParams},on:{success:e.successCallbackFn}})],1)},re=[],oe=a(87),se=a(92367),le=a(90089),ce=a(52340),ue=a(42440),de=a(8265),me=a(50651),pe={name:"operator-dialog",components:{PauseDialog:ue.A,ApprovalDialog:se.A,ShareLinkDialog:le.A,TranferDialog:ce.A},data:function(){return{record:{},currentOpBtn:{},pending:!1}},computed:(0,l.A)((0,l.A)({},(0,me.aH)(["user"])),{},{isRecordEmpty:function(){return"{}"===JSON.stringify(this.record)},currentLoginUserId:function(){var e,t=(0,oe.zO)(window);return(null===t||void 0===t||null===(e=t.loginUser)||void 0===e?void 0:e.userId)||localStorage.getItem("loginUserId")},currenTaskItem:function(){var e=this;return"share"===this.currentOpBtn.enName?{}:this.record.esTaskList.filter((function(t){return 1===t.status&&t.approveUserId===e.currentLoginUserId}))[0]||{}},flowTaskId:function(){var e;return(null===(e=this.record)||void 0===e||null===(e=e.wfButtonVO)||void 0===e?void 0:e.currentaskId)||this.currenTaskItem.bizId},nodeInstanceIdForConnector:function(){var e;return(null===(e=this.record)||void 0===e||null===(e=e.wfButtonVO)||void 0===e?void 0:e.currentNodeInstanceId)||""},processId:function(){var e;return(null===(e=this.record)||void 0===e?void 0:e.processorInstanceId)||""},pauseFlag:function(){var e;return(null===(e=this.record)||void 0===e||null===(e=e.wfButtonVO)||void 0===e?void 0:e.pauseFlag)||!1}}),watch:{pending:function(e){var t=this.record;e?this.$emit("loading",t):this.$emit("closeLoading",t)}},methods:{successCallbackFn:function(){var e=this;setTimeout((function(){e.$emit("refreshData")}),500)},buildFormValueParams:function(){var e=this.record,t=e.templateBizId,a=e.bizId;return{formValueList:[],templateUUId:t,contentBizId:a,isStore:0}},checkIsCurrentItemFormIsRequired:function(e,t,a){var n=this;return(0,o.A)((0,r.A)().mark((function i(){var o,s,l,c,d,m,p,f,v,h,w,g,b,C,_,y,S,A;return(0,r.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(o=n.record,s=o.templateBizId,l=o.processorInstanceId,c=de.A.methods,d=c.finishedFlowContent,m=c.backToMeApi,p=c.formCountersign,f=e.enName,v=e.showAttached,h=e.showRemark,w=e.code,g=[4,7].includes(w)?a:"","custom"!==f){i.next=6;break}return i.abrupt("return");case 6:if(b=[X.A.REFUSE.value,X.A.ROLLBACK.value,X.A.CANCEL.value,X.A.BACKTOME.value,X.A.COUNTERSIGN.value],b.includes(f)){i.next=19;break}return i.next=10,(0,u.jI)({processorInstanceId:l,templateBizId:s,nodeInstanceId:n.currenTaskItem.nodeInstanceId});case 10:if(C=i.sent,_=C.data,!_){i.next=19;break}return i.next=15,n.$confirm(n.$t("view.template.list.tip4"),n.$t("common.base.toast"),{confirmButtonText:n.$t("common.base.makeSure"),cancelButtonText:n.$t("common.base.cancel"),type:"warning"})["catch"]((function(){}));case 15:if(y=i.sent,!y){i.next=18;break}return i.abrupt("return",n.$emit("handleJumpDetail",n.record));case 18:return i.abrupt("return");case 19:if(S=[X.A.AGREE.value,X.A.REFUSE.value,X.A.ROLLBACK.value,X.A.CANCEL.value,X.A.BACKTOME.value],!S.includes(f)||!v&&!h){i.next=23;break}if("function"!=typeof t){i.next=23;break}return i.abrupt("return",t(g));case 23:if(f!==X.A.BACKTOME.value||v||h){i.next=26;break}return m.call(n,{flowTaskId:n.flowTaskId,nodeButtonName:e.enName,approveResult:e.code}),i.abrupt("return");case 26:if(f!==X.A.COUNTERSIGN.value){i.next=29;break}return p.call(n),i.abrupt("return");case 29:A={flowTaskId:n.flowTaskId,nodeButtonName:e.enName,approveResult:e.code},g&&(A.backNodeId=g),d.call(n,A);case 32:case"end":return i.stop()}}),i)})))()},handleBtnClick:function(e,t,a){var n=this,i=de.A.methods.fetchRecoverFlowTask;this.record=e,this.currentOpBtn=t;var s=t.enName;this.$nextTick((0,o.A)((0,r.A)().mark((function e(){return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.t0=s,e.next=e.t0===X.A.REFUSE.value||e.t0===X.A.ROLLBACK.value||e.t0===X.A.CANCEL.value||e.t0===X.A.BACKTOME.value||e.t0===X.A.COUNTERSIGN.value?3:e.t0===X.A.AGREE.value?4:e.t0===X.A.FORWARD.value?5:"share"===e.t0?6:e.t0===X.A.PAUSE.value?7:8;break;case 3:return e.abrupt("return",n.checkIsCurrentItemFormIsRequired(t,n.$refs.approvalDialog.openDialog,a));case 4:return e.abrupt("return",n.checkIsCurrentItemFormIsRequired(t,n.$refs.approvalDialog.openDialog));case 5:return e.abrupt("return",n.$refs.transferDialog.openDialog());case 6:return e.abrupt("return",n.$refs.shareLinkDialog.openDialog());case 7:return e.abrupt("return",n.pauseFlag?i.call(n,{formContentId:n.record.bizId,flowTaskId:n.flowTaskId}):n.$refs.pauseDialog.openDialog());case 8:n.checkIsCurrentItemFormIsRequired(t);case 9:case"end":return e.stop()}}),e)}))))}}},fe=pe,ve=(0,z.A)(fe,ie,re,!1,null,"00665ef7",null),he=ve.exports,we=a(71523),ge=function(){var e=this,t=e._self._c;return t("div",{staticClass:"share-form-dialog-container"},[t("base-modal",{staticClass:"share-form-modal",attrs:{title:e.$t("view.template.list.formData"),show:e.visible,width:"800px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"form-data-container"},[t("div",{staticClass:"form-data-tip"},[t("i18n",{attrs:{path:"view.template.list.formDataTips"}},[t("el-button",{attrs:{place:"operate",type:"text",size:"middle"},on:{click:e.goSet}},[e._v(e._s(e.$t("common.base.toSet")))])],1)],1),e.init?t("form-builder",{key:e.formKey,ref:"form",attrs:{mode:"base",value:e.value,fields:e.fields,"is-edit-state":!0,"is-edit":e.isEdit,"template-id":e.templateId,"form-cell-count":e.formCellCount,"form-editing-mode":e.formEditingMode},on:{update:e.update}}):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.pending},on:{click:function(t){e.visible=!1}}},[e._v(e._s(e.$t("buttons.cancel")))]),t("el-button",{attrs:{type:"primary",disabled:e.pending},on:{click:e.submit}},[e._v(" "+e._s(e.$t("view.template.list.generateShare")))])],1)]),t("share-form-link-dialog",{ref:"shareFormLinkDialog",attrs:{"form-write-config":e.formWriteConfig}})],1)},be=[],Ce=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{title:e.$t("view.template.detail.shareLink"),show:e.visible,width:"500px"},on:{"update:show":function(t){e.visible=t}}},[t("div",{staticClass:"share-modal-content"},[t("div",{staticClass:"share-modal-qrcode-wrap"},[t("div",{ref:"qrcode",staticClass:"qrcode-img",attrs:{id:"qrcode"}}),t("el-button",{staticClass:"qrcode-btn",attrs:{type:"text",size:"middle"},on:{click:e.downloadQrcode}},[e._v(e._s(e.$t("common.base.downloadQrcode")))])],1),t("div",{staticClass:"share-modal-input"},[t("span",{staticClass:"mar-r-8"},[e._v(e._s(e.$t("view.template.detail.shareLink")))]),t("el-input",{attrs:{disabled:""},model:{value:e.outerUrl,callback:function(t){e.outerUrl=t},expression:"outerUrl"}},[t("el-button",{attrs:{slot:"append",type:"primary"},on:{click:e.copyText},slot:"append"},[e._v(e._s(e.$t("common.base.copy")))])],1)],1)])])},_e=[],ye=a(20548),Se=a.n(ye),Ae={name:"share-form-link-dialog",props:{formWriteConfig:{type:Object,default:function(){return{}}}},data:function(){return{visible:!1,outerUrl:""}},methods:{openDialog:function(e){this.outerUrl=e,this.createQrcode(),this.visible=!0},createQrcode:function(){this.$refs.qrcode.innerHTML="";new(Se())("qrcode",{width:132,height:132,text:this.outerUrl})},downloadQrcode:function(){var e=document.getElementById("qrcode"),t=e.getElementsByTagName("canvas"),a=document.createElement("a");a.href=t[0].toDataURL("image/png"),a.download=this.$t("view.template.detail.formQrCode"),a.click()},copyText:function(){var e=document.createElement("input");e.value=this.outerUrl,document.body.appendChild(e),e.select(),document.execCommand("Copy"),document.body.removeChild(e),this.$message.success(this.$t("view.template.detail.tip3"))}}},ke=Ae,Ie=(0,z.A)(ke,Ce,_e,!1,null,"26d47878",null),Ne=Ie.exports,xe=a(70072),Le=(0,xe.useFormTimezone)(),Te=(Le.disposeFormViewTime,Le.disposeFormSubmitTime),Pe={name:"share-form-dialog",provide:function(){return{isEdit:this.isEdit}},props:{originFields:{type:Array,default:function(){return[]}},templateName:{type:String,default:""},templateId:{type:String,default:""},appId:{type:String,default:""},formWriteConfig:{type:Object,default:function(){}}},data:function(){return{formKey:(0,xe.uuid)(),init:!1,value:{},fields:[],formCellCount:1,visible:!1,pending:!1,isEdit:!1}},watch:{visible:function(e,t){var a=this;return(0,o.A)((0,r.A)().mark((function t(){return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e){t.next=5;break}return t.next=3,a.getFields();case 3:a.value=O.n_(a.fields,{}),a.formKey=(0,xe.uuid)();case 5:case"end":return t.stop()}}),t)})))()}},computed:{formEditingMode:function(){return this.isEdit?"edit":"create"}},created:function(){this.formCellCount=this.$platform.getMainAllFormBuilderCell()},methods:{update:function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;this.$set(this.value,n,a)},openDialog:function(){this.visible=!0},getFields:function(){var e=this;if(!this.fields.length)return(0,A.b5)({bizId:this.formWriteConfig.bizId}).then((function(t){e.init=!0;var a=t.data||[];e.fields=e.originFields.map((function(e){var t,n=a.find((function(t){return t.fieldName==e.fieldName}));return e.revisable=n&&n.revisable?1:0,e.dependencies=null,null!==(t=e.setting)&&void 0!==t&&t.dependencies&&(e.setting.dependencies=null),e.isNull=1,e})).filter((function(e){return 1==e.revisable}))}))},submit:function(){var e=this;if(!this.fields.length)return this.$message.warning(this.$t("view.template.list.tip7"));var t=Te(this.fields,this.value),a=[];for(var n in t)a.push({fieldName:n,fieldValue:t[n]});this.pending=!0,(0,A.Sl)({templateId:this.templateId,valueSetting:a}).then((function(t){if(e.pending=!1,!t.success)return e.$message.warning(t.message);e.$message.success(e.$t("common.base.saveSuccess")),setTimeout((function(){e.$refs.shareFormLinkDialog.openDialog(t.data),e.value={},e.visible=!1}),300)}))["catch"]((function(t){e.pending=!1,console.error("shareFormDialog submit error:",t)}))},goSet:function(){var e="/paas/#/designer/rule?appId=".concat(this.appId,"&formId=").concat(this.templateId);this.$platform.openTab({id:"frame_tab_".concat(this.templateId),title:this.templateName,close:!0,reload:!0,url:e})}},components:(0,c.A)({},Ne.name,Ne)},Ve=Pe,Fe=(0,z.A)(Ve,ge,be,!1,null,"1184b069",null),De=Fe.exports,$e=a(56268),Be=a(52751),Oe=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"viewport-dropdown"},[t("popover",{attrs:{"popper-class":"viewport-dropdown__popper",trigger:"click"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[t("div",{staticClass:"viewport-dropdown__button",staticStyle:{"white-space":"nowrap"},attrs:{slot:"reference"},slot:"reference"},[t("span",[e._v(e._s(e.activeViewport&&e.activeViewport.viewName||e.$t("component.viewportDropdown.defaultName")))]),t("i",{staticClass:"iconfont icon-down-fill"})]),t("div",{staticClass:"viewport-dropdown__content"},[t("div",{staticClass:"viewport-dropdown__title"},[t("span",[e._v(e._s(e.$t("component.viewportDropdown.title")))]),t("tooltip",{attrs:{content:e.viewportTooltip,placement:"right"}},[t("i",{staticClass:"iconfont icon-jieshishuoming"})])],1),t("div",{staticClass:"viewport-dropdown__scroll-wrap"},[t("div",{staticClass:"viewport-dropdown__list"},e._l(e.list,(function(a,n){return t("div",{key:a.viewId,staticClass:"viewport-dropdown__list-item",class:{active:e.activeViewport.viewId===a.viewId},on:{click:function(t){return e.handleChoose(a)}}},[t("div",{staticClass:"viewport-dropdown__list-item-name"},[e._v(" "+e._s(a.viewName)+" ")]),t("div",{directives:[{name:"show",rawName:"v-show",value:a.viewId&&!a.authEdit&&!a.isPre,expression:"item.viewId && !item.authEdit && !item.isPre"}],staticClass:"viewport-dropdown__list-item-type"},[t("span",{staticClass:"tag"},[e._v(" "+e._s(e.$t("component.viewportDropdown.public"))+" ")])]),a.authEdit?t("dropdown",{attrs:{trigger:"click",placement:"right"},on:{command:function(t){return e.changeCommand(a,n,t)}}},[t("span",{staticClass:"operator-drop-down",on:{click:function(e){e.stopPropagation()}}},[t("i",{staticClass:"iconfont icon-MoreOutlined"})]),t("dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("dropdown-item",{attrs:{command:"edit"}},[e._v(e._s(e.$t("common.base.edit")))]),t("dropdown-item",{attrs:{command:"delete"}},[e._v(e._s(e.$t("common.base.delete")))])],1)],1):e._e()],1)})),0)]),t("div",{staticClass:"viewport-dropdown__create-btn",on:{click:function(t){return t.stopPropagation(),e.handleCreate.apply(null,arguments)}}},[t("i",{staticClass:"iconfont icon-add"}),t("span",[e._v(e._s(e.$t("component.advancedSearch.modal.createTitle")))])])])])],1)},Ee=[],Re=a(48649),Me=a(84859),ze={viewId:"",viewName:Me.Ay.t("component.viewportDropdown.defaultName"),searchModel:[],authEdit:!1,visibleType:1,viewNo:"",url:""};function Ue(e,t,a){var n=(0,Re.ref)(ze),i=(0,Re.ref)([ze]),l=function(){var n=(0,o.A)((0,r.A)().mark((function n(){var o;return(0,r.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,h(e);case 3:return o=n.sent,o=(o||[]).filter((function(e){var n,i=(null===(n=e.searchModel[0])||void 0===n?void 0:n.value)||{};return t==i.appId&&a==i.templateId})),n.abrupt("return",i.value=[ze].concat((0,s.A)(o)));case 8:return n.prev=8,n.t0=n["catch"](0),console.log(n.t0),n.abrupt("return",Promise.reject(n.t0));case 12:case"end":return n.stop()}}),n,null,[[0,8]])})));return function(){return n.apply(this,arguments)}}(),c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ze;n.value=e},u=function(e){if(0!==e&&i.value[e])return i.value[e].viewId===n.value.viewId?(c(),!0):void i.value.splice(e,1)};return{activeViewport:n,list:i,getList:l,chooseViewport:c,removeViewport:u}}var We=a(20462),je=a(69749),qe=Me.Ay.t("component.viewportDropdown.tooltip"),Ge=(0,Re.defineComponent)({name:"ViewportDropdown",components:{Popover:je.Popover,Dropdown:je.Dropdown,DropdownItem:je.DropdownItem,DropdownMenu:je.DropdownMenu,Tooltip:je.Tooltip},props:{module:{type:String,default:""},appId:{type:String,default:""},templateId:{type:String,default:""},currentView:{type:Object,default:function(){return null}},preViews:{type:Array,default:function(){return[]}}},setup:function(e,t){var a=t.emit,n=(Re["default"].prototype.$track,(0,Re.ref)(!1)),i=Ue(e.module,e.appId,e.templateId),s=i.list,l=i.getList,c=i.activeViewport,u=i.chooseViewport,d=i.removeViewport;function m(){n.value=!1}function p(e){u(e),a("choose",e),a("update:currentView",e),m()}function f(){m(),a("create")}function v(e){m(),a("edit",e)}function h(e,t){return w.apply(this,arguments)}function w(){return w=(0,o.A)((0,r.A)().mark((function t(a,n){var i,o;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return m(),t.next=3,(0,We.lJ)(Me.Ay.t("component.viewportDropdown.deleteTip",{viewName:a.viewName}),"",{type:"warning",customClass:"delete-view-confirm",showClose:!1});case 3:if(t.sent){t.next=5;break}return t.abrupt("return");case 5:return t.prev=5,i={viewId:a.viewId,module:e.module},t.next=9,y(i);case 9:m(),(0,We.oR)(Me.Ay.t("common.base.deleteSuccess")),o=d(n),o&&p(c.value),g(),t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](5),(0,We.oR)(Me.Ay.t("common.base.deleteFail"),"error");case 19:case"end":return t.stop()}}),t,null,[[5,16]])}))),w.apply(this,arguments)}function g(e){l().then((function(t){if(e){var a=(t||[]).find((function(t){return e===t.viewId}));a&&p(a)}}))}function b(e,t,a){var n={edit:v,delete:h};n[a](e,t)}return g(),(0,Re.watch)((function(){return e.currentView}),(function(e){e?e.viewId!==c.value.viewId&&u(e):u()})),{visible:n,list:s,activeViewport:c,viewportTooltip:qe,handleCreate:f,handleEdit:v,handleChoose:p,handleRemove:h,refresh:g,changeCommand:b}}}),He=Ge,Je=(0,z.A)(He,Oe,Ee,!1,null,null,null),Qe=Je.exports,Ye=function(){var e=this,t=e._self._c;e._self._setupProxy;return e.realVisible?t("base-modal",{staticClass:"advanced-search-modal",attrs:{show:e.realVisible,title:e.title,width:"650px"},on:{cancel:e.close}},[t("div",{staticClass:"advanced-search-modal__content"},[t("div",{staticClass:"advanced-search-modal__view-name"},[t("label",{attrs:{for:""}},[e._v(e._s(e.$t("component.advancedSearch.modal.name")))]),t("div",{staticClass:"advanced-search-modal__view-name-input"},[t("el-input",{class:{"is-error":e.validatorError.name},attrs:{maxlength:"10",placeholder:e.$t("component.advancedSearch.modal.namePlaceholder")},on:{input:e.handleInputViewName},model:{value:e.viewport.viewName,callback:function(t){e.$set(e.viewport,"viewName",t)},expression:"viewport.viewName"}}),e.validatorError.name?t("div",{staticClass:"error-tip"},[e._v(" "+e._s(e.validatorError.name)+" ")]):e._e()],1)]),t("div",{staticClass:"advanced-search-modal__form"},[e._t("prefix"),e._t("searchForm",(function(){return[t("advanced-search-form",{ref:"searchFormRef",attrs:{fields:e.fields,"search-model":e.viewport.searchModel,"show-url":e.showUrl,url:e.viewport.url,"remote-method":e.remoteMethod},on:{change:e.changeForm}})]}))],2)]),t("template",{slot:"footer"},[t("div",{staticClass:"advanced-search-modal__footer"},[t("div",{staticClass:"advanced-search-modal__footer-left"},[t("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.viewport.visibleType,callback:function(t){e.$set(e.viewport,"visibleType",t)},expression:"viewport.visibleType"}},[e._v(" "+e._s(e.$t("component.advancedSearch.modal.fullyVisible"))+" ")])],1),t("div",{staticClass:"advanced-search-modal__footer-right"},[t("el-button",{attrs:{type:"plain-third"},on:{click:e.close}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSave}},[e._v(" "+e._s(e.$t("common.base.save"))+" ")])],1)])])],2):e._e()},Ke=[],Ze=(a(33656),a(74499)),Xe={empty:Me.Ay.t("component.advancedSearch.modal.namePlaceholder")},et=(0,Re.defineComponent)({name:"AdvancedSearchModal",components:{AdvancedSearchForm:Ze.A},props:{visible:{type:Boolean,default:!1},fields:{type:Array,default:function(){return[]}},remoteMethod:{type:Object,default:function(){return{}}},module:{type:String,default:""},config:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return null}},beforeSave:{type:Function,default:null},allowEmpty:{type:Boolean,default:!1}},emits:["update:value"],setup:function(e,t){var a=t.emit,n=(0,Re.ref)(!1),i=(0,Re.ref)(),c=(0,Re.ref)(null),u=(0,Re.ref)(!1),d=(0,Re.ref)(!1),m=(0,Re.computed)((function(){return u.value?Me.Ay.t("component.advancedSearch.modal.editTitle"):Me.Ay.t("component.advancedSearch.modal.createTitle")})),p=(0,Re.ref)(!1),f=(0,Re.ref)({name:""});function v(){return h.apply(this,arguments)}function h(){return h=(0,o.A)((0,r.A)().mark((function t(){var a,n,i=arguments;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=i.length>0&&void 0!==i[0]?i[0]:{},c.value=(0,l.A)({viewName:"",visibleType:0,url:""},a),u.value=!!c.value.viewId,d.value=["paas"].includes(e.module),u.value||!d.value){t.next=9;break}return t.next=7,g({module:e.module});case 7:n=t.sent,c.value.url=n.url||c.value.url;case 9:case"end":return t.stop()}}),t)}))),h.apply(this,arguments)}function w(e){c.value.viewName=e.trim(),f.value.name=""===c.value.viewName?Xe.empty:""}function b(){a("change")}function _(){return y.apply(this,arguments)}function y(){return y=(0,o.A)((0,r.A)().mark((function t(){var n,o,d,m;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=i.value.getSearchList(),o=n.map((function(e){var t=e.fieldName,a=e.operator,n=e.value;return{fieldName:t,operator:a,value:n}})),c.value.viewName){t.next=4;break}return t.abrupt("return",f.value.name=Xe.empty);case 4:if(e.allowEmpty||o.length){t.next=6;break}return t.abrupt("return",(0,We.oR)(Me.Ay.t("component.advancedSearch.modal.saveTip.noCondition"),"warning"));case 6:return e.beforeSave&&(d=e.beforeSave(u.value?c.value:""),o.unshift.apply(o,(0,s.A)(d))),t.prev=7,p.value=!0,t.next=11,C((0,l.A)((0,l.A)({},c.value),{},{module:e.module,searchModel:o,config:JSON.stringify(e.config)}));case 11:m=t.sent,(0,We.oR)(Me.Ay.t("common.base.saveSuccess")),k(),a("save",m),t.next=21;break;case 17:t.prev=17,t.t0=t["catch"](7),(0,We.oR)(Me.Ay.t("common.base.saveFail"),"error"),console.error("save viewport error: ",t.t0);case 21:return t.prev=21,p.value=!1,t.finish(21);case 24:case"end":return t.stop()}}),t,null,[[7,17,21,24]])}))),y.apply(this,arguments)}function S(){var e;null===(e=i.value)||void 0===e||e.clear()}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};v(e),n.value=!0,a("update:visible",!0)}function k(){n.value=!1,a("update:visible",!1)}return(0,Re.watchEffect)((function(){return n.value=e.visible})),(0,Re.watch)((function(){return e.data}),(function(e){v(e)})),{realVisible:n,title:m,searchFormRef:i,isEdit:u,showUrl:d,viewport:c,loading:p,validatorError:f,handleInputViewName:w,open:A,close:k,changeForm:b,handleSave:_,clearField:S}}}),tt=et,at=(0,z.A)(tt,Ye,Ke,!1,null,null,null),nt=at.exports,it=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"paas-common-advanced-search-form"},[t("div",{staticClass:"search-row"},[t("div",{staticClass:"search-row__label"},[e._v(" "+e._s(e.$t("common.base.quickCondition"))+"： ")]),t("div",{staticClass:"search-row__content"},[t("el-tag",{staticClass:"__tag",attrs:{type:"info"}},[e._v(" "+e._s(e.$t("common.base.dataState"))+"："+e._s(e.statusLabel)+" ")]),t("el-tag",{staticClass:"__tag",attrs:{type:"info"}},[e._v(" "+e._s(e.$t("common.base.createAngle"))+"："+e._s(e.angleLabel)+" ")])],1)])])},rt=[],ot=(0,Re.defineComponent)({name:"paas-common-advanced-search-form",props:{statusLabel:{type:String,default:function(){return""}},angleLabel:{type:String,default:function(){return""}}}}),st=ot,lt=(0,z.A)(st,it,rt,!1,null,null,null),ct=lt.exports,ut=function(){var e=this,t=e._self._c;return e.isShowSave?t("section",{staticClass:"pass-save"},[t("section",{directives:[{name:"show",rawName:"v-show",value:e.isShowSaveMove,expression:"isShowSaveMove"}],staticClass:"pass-save-move",on:{click:function(t){e.isShowSaveMove=!1}}},[t("section",{staticClass:"pass-save-move-box"},[t("i",{staticClass:"iconfont icon-xiangzuo"}),t("i",{staticClass:"iconfont icon-save"}),t("section",[e._v(e._s(e.$t("forPaas.passSave.saveView")))])])]),t("section",{directives:[{name:"show",rawName:"v-show",value:!e.isShowSaveMove,expression:"!isShowSaveMove"}],staticClass:"pass-save-dialog"},[t("section",{staticClass:"pass-save-dialog-header"},[t("p",[e._v(e._s(e.$t("forPaas.passSave.notBeenSaved")))]),t("section",{on:{click:e.taskSaveClose}},[t("i",{staticClass:"iconfont icon-close"})])]),t("footer",[t("el-button",{on:{click:e.saveAs}},[e._v(e._s(e.$t("forPaas.passSave.saveAs")))]),e.hasSave?t("el-button",{on:{click:e.overWriteCurrent}},[e._v(e._s(e.$t("forPaas.passSave.overWriteCurrent")))]):e._e()],1)]),t("base-modal",{staticClass:"pass-save-modal base-modal-mask",attrs:{title:e.title,show:e.isShowView,width:"528px"},on:{"update:show":function(t){e.isShowView=t}}},["saveAs"===e.currentType?[t("section",{staticClass:"pass-save-modal-saveAs"},[t("section",[t("span",{staticClass:"pass-save-modal-saveAs-required"},[e._v("* ")]),t("span",[e._v(e._s(e.$t("forPaas.passSave.viewName")))])]),t("el-form",{ref:"formRef",attrs:{model:e.FormData}},[t("el-form-item",{attrs:{prop:"viewName",rules:e.baseRule.viewName}},[t("el-input",{attrs:{placeholder:e.$t("common.placeholder.input2")},model:{value:e.FormData.viewName,callback:function(t){e.$set(e.FormData,"viewName",t)},expression:"FormData.viewName"}})],1)],1)],1),t("section",{attrs:{slot:"footer"},slot:"footer"},[t("section",{staticClass:"pass-save-modal-footer"},[t("section",{staticClass:"pass-save-modal-footer-left"},[t("el-checkbox",{staticClass:"pass-save-modal-footer-left-view",attrs:{"true-label":1,"false-label":0},model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}},[e._v(e._s(e.$t("forPaas.passSave.settingView")))])],1),t("section",{staticClass:"pass-save-modal-footer-right"},[t("el-button",{on:{click:function(t){e.isShowView=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.createView}},[e._v(e._s(e.$t("common.base.save")))])],1)])])]:e._e(),"overWriteCurrent"===e.currentType?[t("section",{staticClass:"pass-save-modal-overWriteCurrent"},[t("p",[e._v(e._s(e.$t("forPaas.passSave.modifyView"))+"["+e._s(e.currentView.viewName)+"]")])]),t("section",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isShowView=!1}}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.overWriteView}},[e._v(e._s(e.$t("forPaas.passSave.overWrite")))])],1)]:e._e()],2)],1):e._e()},dt=[],mt=a(92935),pt=a.n(mt),ft={name:"pass-save",data:function(){return{isShowSaveMove:!1,isShowView:!1,isShowSave:!1,currentType:"",checked:0,FormData:{viewName:""},searchModel:[],baseRule:{viewName:[{required:!0,message:this.$t("forPaas.passSave.namePlaceholder")}]}}},props:{hasSave:{type:Boolean,default:!0},currentView:{type:Object,default:function(){}},beforeSave:{type:Function,default:null},module:{type:String,default:""},config:{type:Object,default:function(){}},filterParams:{type:Object,default:function(){}}},computed:{title:{get:function(){return"overWriteCurrent"===this.currentType?this.$t("forPaas.passSave.overWriteCurrentView"):this.$t("forPaas.passSave.saveView")}}},methods:{overWriteCurrent:function(){this.isShowView=!0,this.currentType="overWriteCurrent"},saveAs:function(){this.isShowView=!0,this.currentType="saveAs"},close:function(){this.isShowSave=!1,this.isShowView=!1},createView:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.$refs.formRef.validate(function(){var t=(0,o.A)((0,r.A)().mark((function t(a){var n,i,o,l,c,u,d,m;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=15;break}return o={},t.next=4,g({module:e.module});case 4:return l=t.sent,c=null===l||void 0===l?void 0:l.url,u=pt().cloneDeep(e.config),o={viewName:e.FormData.viewName,module:e.module,url:c,searchModel:""!==(null===(n=e.searchModel[0])||void 0===n?void 0:n.fieldName)?e.searchModel:[],visibleType:e.checked,config:JSON.stringify(u)},d=e.beforeSave(o),(i=o.searchModel).unshift.apply(i,(0,s.A)(d)),t.next=12,C(o);case 12:m=t.sent,e.$emit("save",m),e.close();case 15:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}catch(a){console.log("error",a)}case 1:case"end":return t.stop()}}),t)})))()},overWriteView:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n,i,o,c,u;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,i={},o=pt().cloneDeep(e.currentView),o.shortcutConditions.filterParams=e.filterParams,i=(0,l.A)({},o),""!==(null===(a=e.searchModel[0])||void 0===a?void 0:a.fieldName)?i.searchModel=e.searchModel:i.searchModel=[],c=e.beforeSave(i),(n=i.searchModel).unshift.apply(n,(0,s.A)(c)),i.module=e.module,t.next=11,C(i);case 11:u=t.sent,e.$emit("save",u),e.close(),t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](0),console.log("error",t.t0);case 19:case"end":return t.stop()}}),t,null,[[0,16]])})))()},taskSaveClose:function(){this.isShowSaveMove=!0},init:function(){!1===this.isShowSave&&(this.isShowSaveMove=!1),this.FormData.viewName="",this.checked=0},open:function(e,t){if(this.isShowSave=e,this.init(),t.length>0){var a=t.map((function(e){var t=e.fieldName,a=e.operator,n=e.value;return{fieldName:t,operator:a,value:n}}));this.searchModel=a}else this.searchModel=t}}},vt=ft,ht=(0,z.A)(vt,ut,dt,!1,null,"aedbb98e",null),wt=ht.exports,gt=a(16828),bt=a(94848),Ct=a(74660),_t=a(14389);function yt(){return _t.A}var St=a(50152),At=function(){var e=yt(),t=(0,Re.computed)((function(){return e.state.listView.showManageDrawer})),a=function(t){e.commit("".concat(bt.cc.ListView,"/").concat(St.EB),t)};return{showManageDrawer:t,setShowManageDrawer:a}},kt=function(){var e=yt(),t=(0,Re.computed)((function(){return e.getters["".concat(bt.cc.ListView,"/isCardViewShowFields")]})),a=(0,Re.computed)((function(){return e.getters["".concat(bt.cc.ListView,"/isCardViewGroupFields")]})),n=(0,Re.computed)((function(){return e.getters["".concat(bt.cc.ListView,"/isCardViewDefaultGroupFields")]}));return{isCardViewDefaultGroupFields:n,isCardViewShowFields:t,isCardViewGroupFields:a}},It=function(){var e=yt(),t=(0,Re.computed)((function(){return e.getters["".concat(bt.cc.ListView,"/cardViewList")]})),a=(0,Re.computed)((function(){return e.state["".concat(bt.cc.ListView)].currentView})),n=(0,Re.computed)((function(){return e.state["".concat(bt.cc.ListView)].currentViewDataFull})),i=(0,Re.computed)((function(){return e.state["".concat(bt.cc.ListView)].initializationLoading})),r=function(t){e.commit("".concat(bt.cc.ListView,"/").concat(St.Eh),t)},o=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e.dispatch("".concat(bt.cc.ListView,"/fetchListCardViewListData"),{setDefaultCardView:t})},s=function(t){e.commit("".concat(bt.cc.ListView,"/").concat(St.YB),t)};return{initializationLoading:i,cardViewList:t,currentView:a,currentViewDataFull:n,setCurrentView:r,fetchListCardViewListData:o,setInitializationLoading:s}},Nt=function(){var e=yt(),t=(0,Re.computed)((function(){return e.getters["".concat(bt.cc.ListView,"/currentViewAllGroupList")]})),a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.dispatch("".concat(bt.cc.ListView,"/fetchCurrentCardViewData"),t)},n=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.dispatch("".concat(bt.cc.ListView,"/fetchDataListForCard"),t)};return{currentViewAllGroupList:t,getCurrentCardViewData:a,getCurrentCardViewDataForList:n}},xt=a(22055);function Lt(e){var t=!1,a=120;return["date","datetime","number","formula"].indexOf(e.formType)>=0&&(t="custom",a=100),["address"].indexOf(e.formType)>=0&&(a=200),["level","updateTime","createUserName","executorName","state"].indexOf(e.fieldName)>=0&&(t="custom"),e.displayName.length>4&&(a=20*e.displayName.length),t&&e.displayName.length>=4&&(a=125),"serialNumber"!==e.formType&&"related_task"!==e.formType&&"datetime"!==e.formType&&"updateTime"!==e.fieldName&&"createTime"!==e.fieldName||(a=150),["taddress","templateName"].indexOf(e.fieldName)>=0&&(a=200),"customer"===e.fieldName&&(t="custom",a=125),(0,l.A)((0,l.A)({},e),{},{label:e.displayName,field:e.fieldName,formType:e.formType,minWidth:"number"==typeof a?a:"".concat(a,"px"),sortable:t,isSystem:e.isSystem,show:!0})}var Tt=function(e){return e.type===bt.aI.BOARD?"icon-kanban":"icon-fdn-cascader"},Pt=function(e){xt.A.$on("resetSearchListParams",e)},Vt=function(e){xt.A.$off("resetSearchListParams",e)},Ft=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];xt.A.$emit("resetSearchListParams",e)},Dt=(0,Re.defineComponent)({name:"ViewChangeDropdown",setup:function(e,t){var a=t.emit,n=At(),i=n.setShowManageDrawer,r=(0,Ct.C)(),o=r.setBoardForStorage,s=It(),l=s.cardViewList,c=s.currentView,u=s.initializationLoading,d=s.setCurrentView,m=(0,Re.computed)((function(){var e;return(null===(e=l.value.find((function(e){var t;return e.id===(null===(t=c.value)||void 0===t?void 0:t.id)})))||void 0===e?void 0:e.name)||""})),p=function(e){var t;if((null===(t=c.value)||void 0===t?void 0:t.id)!==e.id)return"manage"===e.value?i(!0):void(0,Re.nextTick)((function(){d(e),o(e),e.type==bt.aI.LIST&&a("refresh")}))};return function(){return(0,Re.h)("el-dropdown",{on:{command:p}},[(0,Re.h)("span",{class:"el-dropdown-btn page-list-mode_dropdown-text-main pointer"},[(0,Re.h)("p",{class:"text"},[m.value]),(0,Re.h)("i",{class:"iconfont icon-fdn-select"})]),(0,Re.h)("el-dropdown-menu",{class:"page-list-mode__dropdown-menu",slot:"dropdown"},[(0,Re.h)("el-scrollbar",{class:"page-list-mode__scrollbar",attrs:{tag:"div"}},[l.value.map((function(e){var t,a;return(0,Re.h)("el-dropdown-item",{class:[(null===(t=c.value)||void 0===t?void 0:t.id)===e.id?"active":null],attrs:{command:e,disabled:u.value}},[(0,Re.h)("div",{class:"list-mode-menu__item-left"},[(0,Re.h)("i",{class:["icon-block","iconfont",Tt(e)]}),(0,Re.h)("span",[e.name])]),(null===(a=c.value)||void 0===a?void 0:a.id)===e.id?(0,Re.h)("i",{class:"iconfont icon-check"}):null])}))]),(0,Re.h)("el-dropdown-item",{attrs:{command:{value:"manage"}},class:"el-dropdown-menu__item-last"},[(0,Re.h)("i",{class:"iconfont icon-setting"}),(0,Me.t)("view.board.manage")])])])}}}),$t=a(17319),Bt=a.n($t),Ot=a(13021),Et=a(89546),Rt=a(87512),Mt={"进行中":(0,Me.t)("common.base.processing"),"已完成":(0,Me.t)("common.base.usualStatus.finish"),"已取消":(0,Me.t)("common.task.type.offed"),"草稿":(0,Me.t)("common.base.draft"),"已拒绝":(0,Me.t)("common.task.type.refused")},zt=((0,Me.t)("common.base.systemProject"),(0,Me.t)("common.base.systemProject1"),(0,Me.t)("common.base.maintenanceService"),(0,Me.t)("common.base.extendedWarrantyService"),a(21975)),Ut=a(46274),Wt=a(92648),jt=(0,Re.defineComponent)({name:"CardViewListCardItem",props:{dataItem:{type:Object,default:function(){return{}}}},setup:function(e,t){t.slots;var a=t.emit,n=(0,Re.toRefs)(e),i=n.dataItem,s=(0,Re.ref)(!1),c=(0,Re.ref)(null),d=(0,Re.ref)(!1),m=(0,Re.ref)([]),p=(0,Re.ref)({}),f=It(),v=f.currentView,h=f.currentViewDataFull,w=yt(),g=kt(),b=g.isCardViewShowFields,C=g.isCardViewDefaultGroupFields,_=(0,xe.useFormTimezone)(),y=_.disposeFormListViewTime,S=(0,Re.computed)((function(){var e,t;return![zt.A.OFFED.value,zt.A.DRAFT.value].includes(null===(e=i.value)||void 0===e?void 0:e.status)&&(null===(t=w.state.user.authData)||void 0===t?void 0:t.editAuth)})),A=(0,Re.computed)((function(){var e;return[zt.A.DRAFT.value].includes(null===(e=i.value)||void 0===e?void 0:e.status)})),k=(0,Re.computed)((function(){var e,t=(0,Wt.bI)(Et.A,i.value),a={},n="";L.value.length&&L.value.forEach((function(e){var t;"currencyCode"===e.formType&&(n=e.fieldName),"formula"===e.formType&&null!==(t=e.setting)&&void 0!==t&&t.includeAmount&&(a[e.fieldName]="")}));var r=(0,l.A)((0,l.A)((0,l.A)({},i.value),i.value.formValue||{}),t);for(var o in a){var s=r[o],c=r[n]||"CNY";r[o]=s?"".concat(s," ").concat(c):s}return r?null===(e=y([r],L.value))||void 0===e?void 0:e[0]:{}})),I=(0,Re.computed)((function(){return b.value.find((function(e){var t;return e.fieldName===(null===(t=v.value)||void 0===t?void 0:t.titleField)}))})),N=(0,Re.computed)((function(){return v.value&&I.value&&v.value.titleField||Rt.E.SerialNumber})),x=(0,Re.computed)((function(){return b.value.find((function(e){return e.fieldName===N.value}))})),L=(0,Re.computed)((function(){var e=b.value.filter((function(e){var t;return null===(t=h.value)||void 0===t||null===(t=t.viewFieldList)||void 0===t?void 0:t.includes(e.fieldName)}));return(0,mt.isEmpty)(e)?C.value:e})),T=(0,Re.computed)((function(){var e;return((null===(e=m.value)||void 0===e?void 0:e.length)>0||S.value)&&(s.value||d.value)&&!A.value})),P=(0,Re.computed)((function(){return k.value.labelList||[]})),V=function(e){e.stopPropagation(),s.value=!0},F=function(e){e.stopPropagation(),s.value=!1},D=function(){var e=(0,o.A)((0,r.A)().mark((function e(t){return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.stopPropagation(),e.next=4,B();case 4:e.next=9;break;case 6:e.prev=6,e.t0=e["catch"](0),console.error("fetchCardButtonList error",e.t0);case 9:c.value.show();case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(t){return e.apply(this,arguments)}}(),$=function(e){d.value=e},B=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.value;return u.bc({formContentId:e.bizId}).then((function(e){var t=e.success,a=e.data,n=void 0===a?{}:a;if(t){var r=n.contentVO||{},o=n.buttonVO,s=(null===o||void 0===o?void 0:o.buttons)||[];null!==r&&void 0!==r&&r.isOpen&&!A.value&&(r.cnName=(0,Me.t)("common.base.share"),r.enName="share",s.push(r)),p.value={contentVO:n.contentVO,wfButtonVO:n.buttonVO},m.value=s.map((function(e){return![X.A.PAUSE.value,"share"].includes(e.enName)&&i.value.pauseFlag?e.disabled=!0:e.disabled=!1,e}))}}))},O=function(e){if(!e)return a("edit",i.value);a("btnClick",i.value,e,p.value)},E=function(e){e.stopPropagation(),e.preventDefault(),a("cardClick",i.value)},R=function(e,t){return t?(0,Me.t)("buttons.pause")+(0,Me.t)("formType.middle"):Mt[e]||e};return function(){var e;return(0,Re.h)("li",{class:["common-list-card__view-item-card",s.value||d.value?"active":null],on:{mouseenter:V,mouseleave:F,click:E}},[(0,Re.h)("div",{class:"common-list-card__view-item-box"},[(0,Re.h)("h2",{class:"card-view-item__row-title"},[(0,Ut.C)(k.value[x.value.fieldName],x.value)]),T.value?(0,Re.h)("div",{class:"card-view-item__row-more",on:{click:D}},[(0,Re.h)("i",{class:"iconfont icon-MoreOutlined"})]):null,(0,mt.isEmpty)(P.value)?null:(0,Re.h)(Ot.A,{attrs:{type:"detail","tags-list":P.value,config:{normalMaxLength:99999999},showMoreIcon:!1}}),(0,Re.h)("el-dropdown",{class:"card-view-item__row-more-dropdown",attrs:{placement:"bottom",trigger:"click"},ref:c,on:(0,l.A)({},{"visible-change":$,command:O})},[(0,Re.h)("div"),(0,Re.h)("el-dropdown-menu",{slot:"dropdown"},[m.value.map((function(e){return(0,Re.h)("el-dropdown-item",{attrs:{disabled:e.disabled,command:e}},[e.cnName])})),S.value?(0,Re.h)("el-dropdown-item",[(0,Me.t)("common.base.edit")," "]):null])]),(0,Re.h)("div",{class:"common-list-card__view-item-row"},[null===(e=L.value)||void 0===e?void 0:e.map((function(e){var t,a,n;return"WFLOW_STATUS"==e.fieldName?(0,Re.h)("p",{class:"text"},[(null===e||void 0===e||null===(a=e.displayNameLanguage)||void 0===a?void 0:a[Me.Ay.locale])||e.displayName,"：",k.value.customStatus.map((function(e){return R(e)}))]):"assignUser"==e.fieldName?(0,Re.h)("p",{class:"text"},[(null===e||void 0===e||null===(n=e.displayNameLanguage)||void 0===n?void 0:n[Me.Ay.locale])||e.displayName,"：",k.value.approveUserName]):(0,Re.h)("p",{class:"text"},[(null===e||void 0===e||null===(t=e.displayNameLanguage)||void 0===t?void 0:t[Me.Ay.locale])||e.displayName,"：",(0,Ut.C)(k.value[e.fieldName],e)])}))]),d.value?(0,Re.h)("div",{class:"card-view-item__row-more-mask",on:{click:F}}):null])])}}}),qt=a(57698),Gt=(a(62830),function(e){var t=(0,Re.ref)(),a=(0,Re.ref)(0),n=function(){var n=t.value,i=n.scrollWidth,r=n.clientWidth,o=n.scrollLeft;o>a.value&&i-(o+r)<50&&(0,mt.isObject)(e)&&Reflect.has(e,"fetchFun")&&(0,mt.isFunction)(e.fetchFun)&&e["fetchFun"](),a.value=o},i=function(){t.value&&t.value.addEventListener("scroll",(0,mt.throttle)(n,200))},r=function(){t.value&&t.value.removeEventListener("scroll",n)};return(0,Re.onMounted)((function(){i()})),(0,Re.onBeforeUnmount)((function(){r()})),{cardViewRef:t}}),Ht=function(e){var t=(0,Re.ref)(),a=(0,Re.ref)(0),n=function(t){var n=t.target,i=n.scrollHeight,r=n.clientHeight,o=n.scrollTop;o>a.value&&i-(o+r)<50&&(0,mt.isObject)(e)&&Reflect.has(e,"fetchFun")&&(0,mt.isFunction)(e.fetchFun)&&e["fetchFun"](),a.value=o},i=function(){t.value&&t.value.wrap.addEventListener("scroll",(0,mt.throttle)(n,200))},r=function(){t.value&&t.value.wrap.removeEventListener("scroll",n)};return(0,Re.onBeforeUnmount)((function(){r()})),{domRef:t,addEventListerScroll:i}},Jt=(0,Re.defineComponent)({name:"CardViewItem",emits:["loadMore","btnClick","cardClick","edit"],props:{customClass:{type:String,default:""},dataItem:{type:Object,default:function(){return{}}},buildSearchParams:{type:Function,default:function(){return function(){return{}}}}},setup:function(e,t){var a=t.emit,n=(0,Re.toRefs)(e),i=n.dataItem,c=n.customClass,u=n.buildSearchParams,d=(0,Re.ref)(!1),m=(0,Re.ref)(new qt.A),p=Nt(),f=p.getCurrentCardViewDataForList,v=Ht({fetchFun:function(){return A()}}),h=v.domRef,w=v.addEventListerScroll,g=(0,Re.computed)((function(){return i.value.parentCode?"".concat(i.value.parentCode,"-").concat(i.value.code):i.value.code})),b=(0,Re.computed)((function(){return i.value.listPageInfo.total})),C=function(){var e=(0,o.A)((0,r.A)().mark((function e(){var t,a,n,i,o,c,p,v,h=arguments;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=h.length>0&&void 0!==h[0]?h[0]:1,a=h.length>1&&void 0!==h[1]&&h[1],e.prev=2,d.value=!0,e.next=6,f({groupFieldCode:[g.value],query:(0,l.A)((0,l.A)({},u.value()),{},{pageNum:t,pageSize:20})});case 6:if(n=e.sent,i=n.success,o=n.data,!i){e.next=13;break}return p=null===(c=o.dataPageList)||void 0===c?void 0:c[0],a&&p&&(v=(0,l.A)({},m.value),v.list=[].concat((0,s.A)(v.list),(0,s.A)(p.list)),v.hasNextPage=p.hasNextPage,m.value=(0,l.A)({},v)),e.abrupt("return",p);case 13:return e.abrupt("return",o);case 16:e.prev=16,e.t0=e["catch"](2),console.error("[ fetchDataList error ]",e.t0);case 19:return e.prev=19,d.value=!1,e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[2,16,19,22]])})));return function(){return e.apply(this,arguments)}}(),_=function(e,t,n){a("btnClick",e,t,n)},y=function(e){a("cardClick",e)},S=function(e){a("edit",e)},A=function(){var e=(0,o.A)((0,r.A)().mark((function e(){return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(m.value.hasNextPage&&!d.value){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,C(m.value.pageNum+=1,!0);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,Re.watchEffect)((function(){m.value=i.value.listPageInfo})),(0,Re.onBeforeMount)((0,o.A)((0,r.A)().mark((function e(){return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:(0,Re.nextTick)((function(){w()}));case 1:case"end":return e.stop()}}),e)})))),function(){return(0,Re.h)($e.F,[(0,Re.h)("div",{class:["common-list-card__view-item",c.value?c.value:null]},[(0,Re.h)("div",{class:"common-list-card__view-item-title"},[(0,Re.h)("h1",{class:"card-view-item__title"},[i.value.displayName," (",b.value,")"])]),m.value.list.length>0?(0,Re.h)("el-scrollbar",{class:"common-list-card__view-item-wrapper",attrs:{tag:"div"},ref:h},[(0,Re.h)("ul",{class:"common-list-card__view-item-list"},[m.value.list.map((function(e){return(0,Re.h)(jt,{key:e.bizId,attrs:{dataItem:e},on:{btnClick:_,cardClick:y,edit:S}})}))])]):null])])}}}),Qt=(0,Re.defineComponent)({name:"CardView",props:{buildSearchParams:{type:Function,default:function(){return function(){return{}}}}},setup:function(e,t){var a=t.emit,n=t.expose,i=(0,Re.toRefs)(e),c=i.buildSearchParams,u=(0,Re.ref)([]),d=(0,Re.ref)(!1),m=(0,Re.ref)({pageNum:1,pageSize:0}),p=Gt({fetchFun:function(){return x()}}),f=p.cardViewRef,v=It(),h=v.currentView,w=v.initializationLoading,g=v.setInitializationLoading,b=Nt(),C=b.currentViewAllGroupList,_=b.getCurrentCardViewData,y=b.getCurrentCardViewDataForList,S=function(e,t,n){a("btnClick",e,t,n)},A=function(){var e=(0,o.A)((0,r.A)().mark((function e(){var t,a,n,i,o,p,f,v,h,w,g,b,_=arguments;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=_.length>0&&void 0!==_[0]&&_[0],e.prev=1,t&&(d.value=!0),a=m.value.pageSize,n=m.value.pageNum-1,i=t?n*a:n,o=t?a*n+a:a,p=C.value.slice(i,o),f=p.reduce((function(e,t){return t.parentCode&&t.code&&e.push("".concat(t.parentCode,"-").concat(t.code)),t.code&&!t.parentCode&&e.push(t.code),e}),[]),e.next=11,y({groupFieldCode:f,query:(0,l.A)((0,l.A)({},c.value()),{},{pageSize:20})});case 11:return v=e.sent,h=v.success,w=v.data,g=v.message,h?(b=p.map((function(e,t){return(0,l.A)((0,l.A)({},e),{},{listPageInfo:w.dataPageList[t]})})),u.value=t?[].concat((0,s.A)(u.value),(0,s.A)(b)):b):(u.value=[],je.Message.warning(g)),e.abrupt("return",w);case 19:e.prev=19,e.t0=e["catch"](1),console.error("[ handleFetchColumnsDataList error ]",e.t0);case 22:return e.prev=22,t&&(d.value=!1),e.finish(22);case 25:case"end":return e.stop()}}),e,null,[[1,19,22,25]])})));return function(){return e.apply(this,arguments)}}(),k=function(){var e,t=document.body.clientWidth||(null===(e=(0,oe.zO)(window))||void 0===e?void 0:e.document.querySelector(".frame-tab-content").clientWidth);m.value.pageSize=0===t&&0!==m.value.pageSize?m.value.pageSize:Math.ceil(t/286)},I=function(){var e=(0,o.A)((0,r.A)().mark((function e(){return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!h.value){e.next=18;break}return e.prev=1,u.value=[],g(!0),N(),e.next=7,_({id:h.value.id});case 7:return k(),e.next=10,A();case 10:e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](1),console.error("[ initializationData error ]",e.t0);case 15:return e.prev=15,g(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,12,15,18]])})));return function(){return e.apply(this,arguments)}}(),N=function(){m.value={pageNum:1,pageSize:0},f.value&&(f.value.scrollLeft=0)},x=function(){d.value||u.value.length===C.value.length||(m.value.pageNum+=1,A(!0))},L=function(e){a("cardClick",e)},T=function(e){a("edit",e)};return(0,Re.watch)((function(){return h.value}),(function(e,t){(0,mt.eq)(e,t)||I()}),{immediate:!0}),n({initializationData:I}),function(){return(0,Re.h)("div",Bt()([{class:"common-list-card__view",ref:f},{directives:[{name:"loading",value:w.value}]}]),[u.value.length?u.value.map((function(e){return(0,Re.h)(Jt,{attrs:{dataItem:e,buildSearchParams:c.value},on:{btnClick:S,cardClick:L,edit:T}})})):(0,Re.h)("div",{class:"common-list-card__view-empty",style:{display:w.value?"none":"block"}},[(0,Re.h)("no-data-view")])])}}}),Yt=(0,Re.defineComponent)({name:"CardViewManageDrawerContentItem",components:{Fragment:$e.F},props:{data:{type:Object,default:function(){return{}}}},setup:function(e,t){var a=t.emit,n=(0,Re.toRefs)(e),i=n.data,r=(0,Re.ref)(null),o=(0,Re.ref)(!1),s=(0,Re.ref)(!1),c=It(),u=(c.currentView,(0,Re.computed)((function(){return s.value||o.value}))),d=function(e){a("command",e)},m=function(e){e.stopPropagation(),r.value.show()},p=function(e){o.value=e},f=function(e){e.stopPropagation(),s.value=!0},v=function(e){e.stopPropagation(),s.value=!1},h=function(e){e.preventDefault(),a("click")};return function(){return(0,Re.h)("li",{class:["card-view-manage__list-item",u.value?"active":null],on:{mouseenter:f,mouseleave:v,click:h}},[(0,Re.h)("div",{class:"card-view-manage__list-item-l"},[(0,Re.h)("i",{class:["iconfont",Tt(i.value)]}),(0,Re.h)("span",[i.value.name])]),i.value.isSystem?null:(0,Re.h)("div",{class:"card-view-manage__list-item-r"},[(0,Re.h)("i",{class:"iconfont icon-MoreOutlined",on:{click:m},style:{visibility:s.value||o.value?"visible":"hidden"}}),(0,Re.h)("el-dropdown",{ref:r,class:"card-view-manage__list-item-placeholder",attrs:{trigger:"click"},on:(0,l.A)({},{"visible-change":p,command:d})},[(0,Re.h)("div"),(0,Re.h)("el-dropdown-menu",{slot:"dropdown"},[(0,Re.h)("el-dropdown-item",{attrs:{command:(0,l.A)((0,l.A)({},i.value),{},{type:"edit"})}},[(0,Me.t)("common.base.edit")]),(0,Re.h)("el-dropdown-item",{attrs:{command:(0,l.A)((0,l.A)({},i.value),{},{type:"delete"})}},[(0,Me.t)("common.base.delete")])])])]),o.value?(0,Re.h)("div",{class:"card-view-manage__list-item-mask",on:{click:v}}):null])}}}),Kt=(0,Re.defineComponent)({name:"CardViewManageDrawerContent",setup:function(e,t){var a=t.emit,n=t.expose,i=It(),r=i.cardViewList,o=i.initializationLoading,s=i.setCurrentView,l=At(),c=l.setShowManageDrawer,u=(0,Ct.C)(),d=u.setBoardForStorage,m=function(){a("add")},p=function(e){a("edit",e)},f=function(e){a("delete",e)},v=function(e){var t=(0,mt.omitBy)(e,(function(e,t){return"type"===t}));"edit"===e.type&&p(t),"delete"===e.type&&f(t)},h=function(e){o.value||(Ft(e.type!==bt.aI.LIST),(0,Re.nextTick)((function(){s(e),c(!1),d(e)})))};return n({handleSelectMode:h}),function(){return(0,Re.h)("div",{class:"card-view-manage__drawer-content-main"},[(0,Re.h)("ul",{class:"card-view-manage__list"},[r.value.map((function(e,t){return(0,Re.h)(Yt,{attrs:{data:e},on:{click:function(){return h(e)},command:v}})}))]),(0,Re.h)("div",{class:"card-view-manage__list-button",on:{click:m}},[(0,Re.h)("i",{class:"el-icon-plus"}),(0,Me.t)("view.board.create")])])}}}),Zt=a(92050),Xt=a(37801),ea=a(72964),ta=a(98424),aa=function(){var e=(0,ea.J0)(!1),t=(0,Xt.A)(e,2),a=t[0],n=t[1],i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(a,i){n(!0);var r=e?ta.Rj:ta.E6;r(t).then(a)["catch"](i)["finally"]((function(){return n(!1)}))}))};return{isLoading:a,fetchSubmitCardFun:i}},na=(0,Re.defineComponent)({name:"CardViewManageDrawerForm",props:{isEdit:{type:Boolean,default:!1},value:{type:Object,default:function(){return{}}}},setup:function(e,t){var a=t.emit,n=(0,Re.toRefs)(e),i=n.value,s=n.isEdit,c=(0,Re.ref)(),u=(0,Re.ref)({}),d=At(),m=d.setShowManageDrawer,p=(0,Ct.C)(),f=p.setBoardForStorage,v=(0,ea.rd)(),h=v.query,w=kt(),g=w.isCardViewShowFields,b=w.isCardViewGroupFields,C=w.isCardViewDefaultGroupFields,_=aa(),y=_.isLoading,S=_.fetchSubmitCardFun,A=It(),k=A.fetchListCardViewListData,I=A.currentView,N=A.setCurrentView,x=A.cardViewList,L=(0,Re.computed)((function(){return h.formId})),T=(0,Re.computed)((function(){return g.value.map((function(e){return{id:e.fieldName,name:e.displayName}}))})),P=(0,Re.computed)((function(){return b.value.map((function(e){return{id:e.fieldName,name:e.displayName}}))})),V=(0,Re.computed)((function(){var e;return[new Zt.A({displayName:(0,Me.t)("common.base.title"),fieldName:"name",formType:"text",maxlength:20,isNull:0,setting:{isLimitWord:!0,limitWordMax:20}}),new Zt.A({displayName:(0,Me.t)("view.board.groupField"),fieldName:"groupField",formType:"select",isNull:0,setting:{dataSource:P.value}}),new Zt.A({displayName:(0,Me.t)("view.board.titleField"),fieldName:"titleField",formType:"select",isNull:0,setting:{dataSource:T.value}}),new Zt.A({displayName:"".concat((0,Me.t)("view.template.view.cardShowFieldsText"),"（").concat((null===(e=u.value)||void 0===e||null===(e=e.viewFieldList)||void 0===e?void 0:e.length)||0,"/10）"),fieldName:"viewFieldList",formType:"select",isNull:0,setting:{dataSource:T.value,isMulti:!0}})]})),F=function(){a("cancel")},D=function(){var e=(0,o.A)((0,r.A)().mark((function e(){var t,a,n,i,o,d,p,v;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!c.value){e.next=21;break}return e.next=3,c.value.validate(!1);case 3:if(t=e.sent,!t){e.next=21;break}return e.next=7,S(s.value,(0,l.A)((0,l.A)({},u.value),{},{type:bt.aI.BOARD,formTemplateBizId:L.value,description:""}));case 7:if(a=e.sent,n=a.success,i=a.message,o=a.data,d=void 0===o?"":o,!n){e.next=20;break}return je.Message.success({message:s.value?(0,Me.t)("common.base.tip.updateSuccess"):(0,Me.t)("common.base.tip.createSuccess"),duration:500}),s.value&&u.value.id===(null===(p=I.value)||void 0===p?void 0:p.id)&&(v=(0,l.A)((0,l.A)({},I.value),{},{groupField:u.value.groupField,titleField:u.value.titleField}),N(v)),e.next=17,k(!1);case 17:setTimeout((function(){F();var e=d?x.value.find((function(e){return e.id===d})):null;e&&(Ft(e.type!==bt.aI.LIST),N(e),f(e),m(!1))}),550),e.next=21;break;case 20:je.Message.warning(i);case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$=function(e){var t=e.field,a=e.newValue,n=(e.oldValue,t.fieldName);t.displayName;if("viewFieldList"===n&&a.length>10)return je.Message.warning((0,Me.t)("view.template.view.cardViewMaxLength"));u.value[n]=a},B=function(){setTimeout((function(){c.value.$children.forEach((function(e){e.errMessage=""}))}),0)};return(0,Re.watchEffect)((function(){var e=(0,mt.isEmpty)(i.value)?{titleField:[Rt.E.SerialNumber]}:i.value;if(e.titleField){var t=g.value.find((function(t){return t.fieldName===e.titleField}));t||(e.titleField=[Rt.E.SerialNumber])}(0,mt.isEmpty)(null===e||void 0===e?void 0:e.viewFieldList)&&(e.viewFieldList=C.value.map((function(e){return e.fieldName}))),u.value=(0,O.n_)(V.value,e,null),B()})),function(){return(0,Re.h)("div",{class:"card-view-manage__drawer-content-form"},[(0,Re.h)("form-builder",{ref:c,attrs:{fields:V.value,value:u.value},on:{update:$}}),(0,Re.h)("div",{class:"card-view-manage__drawer-footer"},[(0,Re.h)("el-button",{attrs:{type:"plain-third",disabled:y.value},on:{click:F}},[(0,Me.t)("common.base.cancel")]),(0,Re.h)("el-button",{attrs:{type:"primary",disabled:y.value},on:{click:D}},[s.value?(0,Me.t)("common.base.submit"):(0,Me.t)("view.board.createComplete")])])])}}}),ia=a(72706),ra=(0,Re.defineComponent)({name:"CardViewManageDrawer",setup:function(e,t){var a=t.emit,n=(0,Re.ref)(!1),i=(0,Re.ref)({}),s=(0,Re.ref)(),l=At(),c=l.showManageDrawer,u=l.setShowManageDrawer,d=It(),m=d.fetchListCardViewListData,p=d.currentView,f=d.cardViewList,v=(0,Re.computed)((function(){return f.value.find((function(e){return e.type===bt.aI.LIST}))})),h=(0,Re.computed)((function(){var e;return Boolean(null===(e=i.value)||void 0===e?void 0:e.id)})),w=function(){u(!1)},g=function(e){n.value=e},b=function(e){i.value=e,g(!0)},C=function(){var e=(0,o.A)((0,r.A)().mark((function e(t){var a,n,i;return(0,r.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,je.MessageBox.confirm((0,Me.t)("common.base.tip.areYouSureYouWantDeletIt"),(0,Me.t)("common.base.toast"),{confirmButtonText:(0,Me.t)("common.base.confirm"),cancelButtonText:(0,Me.t)("common.base.cancel")});case 2:return e.next=4,(0,ta.zL)({id:t.id});case 4:a=e.sent,n=a.success,n&&(je.Message.success((0,Me.t)("common.base.deleteSuccess")),t.id===(null===(i=p.value)||void 0===i?void 0:i.id)&&v.value&&s.value.handleSelectMode(v.value),setTimeout((function(){m(!1)}),300));case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),_=function(){i.value={},g(!0)},y=function(){a("refresh")},S=function(e){e.preventDefault(),e.stopPropagation(),g(!1),u(!1)};return function(){return c.value?(0,Re.h)("transition",{attrs:{name:"el-drawer-fade"}},[(0,Re.h)("div",{class:"card-view-manage__drawer",style:{zIndex:ia["default"].nextZIndex()}},[(0,Re.h)("div",{class:"card-view-manage__drawer-mask",on:{click:S}}),(0,Re.h)("div",{class:"card-view-manage__drawer-body list"},[(0,Re.h)("div",{class:"card-view-manage__drawer-header"},[(0,Re.h)("div",{class:"title"},[(0,Me.t)("view.board.mode")]),(0,Re.h)("i",{class:"icon el-icon-close",on:{click:w}})]),(0,Re.h)("div",{class:"card-view-manage__drawer-content"},[(0,Re.h)(Kt,{ref:s,on:{add:_,delete:C,edit:b}})])]),(0,Re.h)("div",{class:["card-view-manage__drawer-body",n.value?"form":null]},[(0,Re.h)("div",{class:"card-view-manage__drawer-header"},[(0,Re.h)("div",{class:"title"},[" ",h.value?(0,Me.t)("view.board.edit"):(0,Me.t)("view.board.create")]),(0,Re.h)("i",{class:"icon el-icon-d-arrow-right",on:{click:function(){return g(!1)}}})]),(0,Re.h)("div",{class:"card-view-manage__drawer-content"},[(0,Re.h)(na,{attrs:{isEdit:h.value,value:i.value},on:{cancel:function(){return g(!1)},refresh:y}})])])])]):null}}}),oa=function(){var e=this,t=e._self._c;return t("base-modal",{attrs:{show:e.visible,title:e.$t("common.form.preview.materialOrder.materialDetail"),width:"740px"},on:{"update:show":function(t){e.visible=t},close:e.handleClose},scopedSlots:e._u([{key:"footer",fn:function(){return[t("el-button",{attrs:{type:"primary"},on:{click:e.handleClose}},[e._v(e._s(e.$t("common.base.close")))])]},proxy:!0}])},[t("div",{staticClass:"providerSettlement-dialog-body"},[e.visible?t("form-provider-settlement",{attrs:{field:e.field,value:e.value,"is-list-view":!0}}):e._e()],1)])},sa=[],la={name:"material-table-dialog",data:function(){return{loading:!1,visible:!1,value:[],field:{}}},methods:{handleClose:function(){this.visible=!1},open:function(e,t,a){this.field=e,this.value=t,console.log(t),this.visible=!0}}},ca=la,ua=(0,z.A)(ca,oa,sa,!1,null,null,null),da=ua.exports,ma=a(26183),pa=a(52275),fa=(0,ma.A)((function e(){(0,pa.A)(this,e),(0,c.A)(this,"property",""),(0,c.A)(this,"direction","")}));(0,c.A)(fa,"ASC","ASC"),(0,c.A)(fa,"DESC","DESC");var va,ha=fa,wa=a(44916);(function(e){e["WFLOW_STATUS"]="status",e["CREATE_USER"]="createUserIdList",e["assignUser"]="assignUserIdList",e["CREATE_TIME"]="createTime",e["UPDATE_TIME"]="updateTime"})(va||(va={}));var ga,ba=va;(function(e){e["ASC"]="ascending",e["DESC"]="descending"})(ga||(ga={}));var Ca,_a=ga,ya=a(95743),Sa=a(9138),Aa=a(23559),ka=a(58635),Ia={SysField:Me.Ay.t("common.form.fieldGroupName.system"),CustomField:Me.Ay.t("common.form.fieldGroupName.attribute"),subForm:Me.Ay.t("common.form.type.subForm"),subFormApi:Me.Ay.t("common.form.type.subFormApi"),sparepart:Me.Ay.t("common.base.sparePart"),product:Me.Ay.t("common.base.product")};(function(e){e["SysField"]="sysFieldChecked",e["CustomField"]="customFieldChecked",e["subForm"]="subFormChecked",e["subFormApi"]="subFormApiChecked",e["sparepart"]="sparepartChecked",e["serviceItem"]="serviceItemChecked",e["product"]="productChecked"})(Ca||(Ca={}));var Na=["attachment","separator","info","autograph","subForm","subFormApi","sparepart","serviceItem","serviceItemrelatedData","product","qualityInfo","materialOrder","outSparepart","inSparepart","inWarehouse","outWarehouse","sunmiOutWarehouse","sunmiInWarehouse","flowNode","providerSettlement","jsCodeBlock","connector","richText","memberPay","contract"],xa=["subForm","sparepart","serviceItem","product","inSparepart","outSparepart","outWarehouse","inWarehouse","sunmiOutWarehouse","sunmiInWarehouse","providerSettlement","materialOrder","flowNode","contract"];function La(e){var t=e.filter((function(e){return!Na.filter((function(e){return"connector"!==e})).includes(e.formType)})).map((function(e){return e["export"]=!0,e.label=e.displayName,e}));return[{label:Ia.SysField,value:Ca.SysField,columns:t.filter((function(e){return 1===e.isSystem}))},{label:Ia.CustomField,value:Ca.CustomField,columns:t.filter((function(e){return 1!==e.isSystem}))}].concat((0,s.A)(Ta(e)))}function Ta(e){var t=e.filter((function(e){return xa.includes(e.formType)})),a=t.map((function(e){return Pa(e)}));return a}function Pa(e,t){var a=e.subFormFieldList.map((function(e){return(0,l.A)({export:!0,label:e.displayName,exportAlias:e.fieldName},e)})).filter((function(e){return!Na.includes(e.formType)}));return{label:"".concat(null===e||void 0===e?void 0:e.displayName),value:"".concat(Ca[null===e||void 0===e?void 0:e.formType],"_").concat(null===e||void 0===e?void 0:e.fieldName),columns:a}}function Va(e){var t={},a={},n=[];return Object.keys(e).forEach((function(t){var i=t.split("_");i.length>1&&e[t].length>0?a[i[i.length-1]]=e[t]:n=n.concat(e[t])})),t.checkedMap=a,t.fieldNameList=n,Reflect.has(t.checkedMap,"flowNode")&&(t.fieldNameList=[].concat((0,s.A)(n),(0,s.A)(t.checkedMap.flowNode)),Reflect.deleteProperty(t.checkedMap,"flowNode")),t}var Fa,Da=a(23043),$a=a(79556),Ba=a(16029),Oa=a(63590),Ea=a(70362),Ra=a(56582),Ma=a(52527),za=(0,xe.useFormTimezone)(),Ua=za.disposeFormListViewTime,Wa={name:"template-list-view",components:(Fa={},(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)(Fa,W.name,W),F.A.name,F.A),ne.name,ne),he.name,he),we.A.name,we.A),De.name,De),$e.F.name,$e.F),"MaterialTableDialog",Q),"AdvancedSearch",Be.A),"ViewportDropdown",Qe),(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)(Fa,"AdvancedSearchModal",nt),ct.name,ct),wt.name,wt),gt.A.name,gt.A),"ProviderSettlementTableDialog",da),gt.A.name,gt.A),Dt.name,Dt),Qt.name,Qt),ra.name,ra)),mixins:[T,P,V.A],data:function(){return{defaultTableData:$a.o,isFlowForm:!1,collaspe:!1,packUp:!0,flowStateEnum:zt.A,advanceSearchForm:{},templateName:"",searchParams:{keyword:"",pageSize:30,pageNum:1,sorts:[]},searchCondition:[],filterParams:{status:"",customStatusList:[],statusList:[],createView:"",tagId:""},fields:[],exportColumns:[],data:new qt.A,multipleSelection:[],columnNum:1,columns:[],advancedVisible:!1,loading:!0,formStatus:[],tagsViewList:[],tagIdList:[],hasQuota:!1,logisticsDialogShow:!1,logisticsInfo:null,houseCarTenantGray:!0,fixRightColWidth:70,currentOperatorRow:{},searchParamsPeer:!1,tableCaleHeight:"calc(100% - 104px)",customStatusList:[],shortcutConditions:{},viewportSearchModel:[],viewConfig:{},currentView:null,inCommonUse:[],conditionParams:{conditions:[],systemConditions:[]},searchList:[],isFlagSaveStatus:!1,templateNameLanguage:{},pageButtonList:[],pageButtonLoading:!1,ButtonSetDetailForShowPositionEnum:Ea.cS,ButtonSetDetailForButtonConcatEventEnum:Ea.Sd,shareLinkOpen:!1,formWriteConfig:{},backButton:[],tablelist:[],moreBtnList:[],moreBtnLoading:!1,rowBtn:{},fullLoading:!1}},computed:(0,l.A)((0,l.A)((0,l.A)({},(0,me.aH)("user",["authData"])),(0,me.L8)("".concat(bt.cc.ListView),["listPageIsTableStyle","onlyRestAdvSearchParamsTag"])),{},{appId:function(){return this.$route.query.appId||this.viewConfig.appId},templateId:function(){return this.$route.query.formId||this.viewConfig.formTemplateId},isSettlementForm:function(){return"c41a2071-7624-4b2a-b30a-c18ba1273ba3"==this.templateId},isSettlementChangeForm:function(){return"2c6a85de-35d3-4681-9dbe-3c378cd12b83"==this.templateId},advanceSearchColumn:function(){var e=Et.A.map((function(e){return(0,l.A)((0,l.A)({},e),{},{fieldName:ba[e.fieldName]||e.fieldName})}));return this.isOpenConnector&&(e=e.concat(Et.D)),[].concat((0,s.A)(this.fields),(0,s.A)(e)).filter((function(e){return 1===e.isSearch}))},statusLabel:function(){var e,t,a=(null===(e=this.shortcutConditions)||void 0===e||null===(e=e.filterParams)||void 0===e?void 0:e.status)||"",n=[].concat((0,s.A)(this.formStatus),(0,s.A)(this.customStatusList));return(null===(t=n.find((function(e){return e.code==a})))||void 0===t?void 0:t.name)||this.$t("common.base.all")},angleLabel:function(){var e,t,a=(null===(e=this.shortcutConditions)||void 0===e||null===(e=e.filterParams)||void 0===e?void 0:e.createView)||"";return(null===(t=this.createView.find((function(e){return e.value==a})))||void 0===t?void 0:t.label)||this.$t("common.base.all")},selectedIds:function(){return this.multipleSelection.map((function(e){return null===e||void 0===e?void 0:e.bizId}))},createView:function(){return[{label:this.$t("common.base.all"),value:"",show:!0},{label:this.$t("view.template.list.angleList[0]"),value:"1",show:!0},{label:this.$t("view.template.list.angleList[1]"),value:"5",show:this.isFlowForm},{label:this.$t("view.template.list.angleList[2]"),value:"3",show:this.isFlowForm},{label:this.$t("view.template.list.angleList[3]"),value:"4",show:this.isFlowForm}].filter((function(e){return e.show}))},tableColumns:function(){return this.isProductAndSparepartGray?this.columns.filter((function(e){return e.show&&"productAndSparepart"!==e.formType})):this.columns.filter((function(e){return e.show}))},processingCode:function(){var e=zt.A.PROCESSING.value;return e.toString()},advancedColumnNumberKey:function(){return"".concat(this.templateId,"_").concat(wa.Wo)},currentOperatorRecordIsEmpty:function(){return"{}"===JSON.stringify(this.currentOperatorRow)},isOpenData:function(){return this.$platform.isOpenData},isLanguageIsZh:function(){return this.$i18n.locale===Z.As},isOpenConnector:function(){var e,t=(0,oe.zO)(window);return(null===t||void 0===t||null===(e=t.grayAuth)||void 0===e?void 0:e.linkCard)||!1},isProductAndSparepartGray:function(){return(0,Ba.Gr)()},isContainerGray:function(){return(0,Ba.pX)()||!1},viewModelGray:function(){return!0},listPageIsCardStyle:function(){return this.viewModelGray&&this.$store.getters["listView/listPageIsCardStyle"]}}),methods:(0,l.A)((0,l.A)((0,l.A)({},(0,me.i0)({fetchAuthData:"user/fetchAuthData",fetchListCardViewListData:"".concat(bt.cc.ListView,"/fetchListCardViewListData")})),(0,me.PY)({setListViewFields:"".concat(bt.cc.ListView,"/").concat(St.e5),setOnlyRestAdvSearchParamsTag:"".concat(bt.cc.ListView,"/").concat(St.sb)})),{},{handelPageButtonClick:function(e,t){var a=this;(0,Oa.Yi)(e,t,{fields:this.fields,multipleSelection:t,js_vm:this},(function(){a.pageButtonLoading=!0}),null,(function(){a.pageButtonLoading=!1}))},getPageButtonList:function(){var e=this;(0,Oa.iH)(Ea.cS.PcList,this.templateId,(function(t){e.pageButtonList=t.filter((function(e){var t;return null===(t=e.event)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.type}))}))},fetchNodes:function(e){var t=this;return(0,o.A)((0,r.A)().mark((function a(){var n,i,o,s;return(0,r.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,u.ML({nodeInstanceId:e});case 2:n=a.sent,i=n.success,o=n.data,i&&(s=o.map((function(e){return{name:e.name,bizId:e.bizId}})),t.backButton=s);case 5:case"end":return a.stop()}}),a)})))()},btnName:function(e){var t;return null!==e&&void 0!==e&&e.nameLanguage?null===e||void 0===e?void 0:e.nameLanguage[null!==(t=this.$i18n.locale)&&void 0!==t?t:Z.As]:e.cnName},getFlowStateLabel:function(e){return Mt[e]||e},openSourceBizNoTab:function(e){var t=this,a=e.sourceBizType,n=e.sourceBizId,i=e.sourceBizTypeId,r="",o="";switch(a){case"TASK":r="PageTaskView";break;case"EVENT":r="PageEventView";break;case"CUSTOMER":r="PageCustomerView";break;case"PRODUCT":r="PageProductView";break;case"CONTRACT":r="PageContractView",o="&contractTemplateId==".concat(n);break;case"SMART_PLAN":r="PageSmartPlanDetail",o="planId=".concat(i);break;case"INDENT_ADDITIONAL":r="PagePurchaaseOrderManageView",o="id=".concat(n);break;default:break}"PAAS"!=a?this.openTab(r,n,o):u.nL(n).then((function(e){var a,r,o="/template/detail?formId=".concat(i,"&formContentId=").concat(n);o+="&processId=".concat(null!==(a=e.data)&&void 0!==a&&a.processorInstanceId?null===(r=e.data)||void 0===r?void 0:r.processorInstanceId:""),t.jump(o,"detail_view_".concat(n),t.$t("view.template.detail.formDetail"))}))},openTab:function(e,t){var a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",i=null===(a=window)||void 0===a||null===(a=a.frameElement)||void 0===a?void 0:a.getAttribute("id");(0,We.iL)({type:ya.Z[e],key:t,params:n,fromId:i})},isShowEditBtn:function(e){return![zt.A.OFFED.value,zt.A.DRAFT.value].includes(null===e||void 0===e?void 0:e.status)},goToEdit:function(e){var t=this;return(0,o.A)((0,r.A)().mark((function a(){var n,i,o,s,l,c,d,m;return(0,r.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=e.bizId,o=e.formValue,a.next=3,u.NT({templateBizId:t.templateId,formContentId:i})["catch"]((function(e){return console.error("[fetch FieldApi.getCanEdit error]",e)}));case 3:if(s=a.sent,l=s.data,c="/template/edit?formId=".concat(t.templateId,"&appId=").concat(t.appId||"","&noHistory=1&formContentId=").concat(i,"&nodeInstanceId=").concat(l,"&onlyEdit=true"),d=o[Rt.E.SerialNumber]||"",m=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id"),m){a.next=10;break}return a.abrupt("return",t.$router.push(c));case 10:try{t.$platform.closeTab("edit_view_".concat(d||i))}catch(r){console.error("[closeTab error ]",r)}t.$platform.openTab({id:"edit_view_".concat(d||i),title:t.$t("common.otherPageTitle.editForm")+d,close:!0,reload:!0,fromId:m,url:"/paas/#".concat(c)});case 12:case"end":return a.stop()}}),a)})))()},handleHeaderDragend:function(e,t,a,n){var i=this,r=this.columns.map((function(e){return e.fieldName===a.property&&(e.width=a.width),e}));this.mergeLocalStorageColumnsToColumns(r),this.$nextTick((function(){i.$refs.table.doLayout()}))},formatApproveName:function(e){return e?"string"===typeof e?e.split(";"):e.toString():[]},isShowMoreOperatorBtns:function(){var e;return this.rowBtn.wfButtonVO&&Array.isArray(this.rowBtn.wfButtonVO.buttons)&&this.rowBtn.wfButtonVO.buttons.length>0&&!(null!==(e=this.rowBtn.wfButtonVO)&&void 0!==e&&e.isBackStart)},handleRowOperatorLoading:function(e,t){this.currentOperatorRow=t?e:{bizId:-1}},handleOperatorBtnClick:function(e,t,a){this.$refs.operatorDialog.handleBtnClick(e,t,a)},handleBtn:function(e,t,a){var n=this;return(0,o.A)((0,r.A)().mark((function i(){var o,s;return(0,r.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return a&&(n.rowBtn=a),Object.assign(e,{wfButtonVO:n.rowBtn.wfButtonVO,contentVO:n.rowBtn.contentVO}),i.next=4,n.fetchNodes(null===(o=e.wfButtonVO)||void 0===o?void 0:o.currentNodeInstanceId);case 4:30===t.code?"codeContent"===t.event[0].type?n.handelPageButtonClick(t,[e.bizId]):(0,Oa.Yi)(t,[e.bizId],{}):[4,7].includes(t.code)&&1===n.backButton.length?n.handleOperatorBtnClick(e,t,null===(s=n.backButton[0])||void 0===s?void 0:s.bizId):[4,7].includes(t.code)&&n.backButton.length>1?(n.$refs.backNodeDialogRef.openDialog(t),n.tablelist=e):n.handleOperatorBtnClick(e,t);case 5:case"end":return i.stop()}}),i)})))()},updateBtn:function(e,t){this.handleOperatorBtnClick(this.tablelist,e,t)},fetchRvTenantGray:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,m.w5)({grayFunctionName:"paas_RvOperate"});case 3:a=t.sent,e.houseCarTenantGray=null===a||void 0===a?void 0:a.data,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),e.houseCarTenantGray=!0;case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},isLogisticsNo:function(e){return(0,Aa.Tb)(e)},fetchAuthList:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.fetchAuthData({templateBizId:e.templateId,appId:e.appId});case 2:case"end":return t.stop()}}),t)})))()},importData:function(){this.$refs.templateUpload.open()},buildExportParams:function(e,t){var a=!t||!t.length,n=this.buildAdvancedSearchParams(),i=(0,l.A)((0,l.A)((0,l.A)((0,l.A)({searchCondition:this.searchCondition},this.searchParams),this.filterParams),n),this.builderIntelligentTagsSearchParams()),r=Va(e);return(0,l.A)((0,l.A)({fieldNameList:r.fieldNameList,subFieldNameMap:r.checkedMap,bizIdList:a?[]:t},i),{},{templateBizId:this.templateId})},exportData:function(e){var t="".concat((0,Da.Yq)(new Date,"YYYY-MM-DD")," ").concat(this.$t("view.template.list.sthData",{name:this.templateName}),".xlsx");if(!e&&!this.selectedIds.length)return this.$platform.alert(this.$t("common.base.tip.exportNoChoice"));var a=e?[]:this.selectedIds;this.$refs.exportPanel.open(a,t)},exportAlert:function(e){this.$platform.alert(e.message)},checkExportCount:function(e,t){var a,n=!e||0==e.length,i=Number(null===(a=this.data)||void 0===a?void 0:a.total);return n&&i>t?this.$t("common.base.tip.exportLimit",{max:t}):null},changeFilterParams:function(e,t){var a,n,i,r=this,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(o&&0!==o&&t===this.filterParams.status)return this.collaspe=!this.collaspe;(0!=o&&t!==this.filterParams.status&&(this.filterParams.customStatusList=[]),null===(a=this.$refs.popperAdvancedSearchRef)||void 0===a||null===(a=a.$refs)||void 0===a||null===(a=a.searchFormRef)||void 0===a||null===(a=a.searchList)||void 0===a||a.forEach((function(e){"status"==e.fieldName&&(e.value=[])})),null===(n=this.conditionParams.systemConditions)||void 0===n||n.forEach((function(e,t){"status"==e.property&&r.conditionParams.systemConditions.splice(t,1)})),o)&&(this.customStatusList=(null===(i=this.formStatus.find((function(e){return e.code==t})))||void 0===i?void 0:i.childList)||[]);o&&this.filterParams.status!=t&&(this.filterParams.status=t),this.filterParams=(0,l.A)((0,l.A)({},this.filterParams),{},(0,c.A)({},e,t)),this.toggleSelection(),this.listPageIsTableStyle&&this.handlePageNumChange(1,"save"),this.listPageIsCardStyle&&this.handlePageModeRefresh()},openSearchConditionSetting:function(){this.$refs.SearchConditionSetting.open()},mergeLocalStorageSearchCondition:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getListStorage(e.templateId,"searchCondition");case 2:if(a=t.sent,!pt().isEmpty(a)){t.next=5;break}return t.abrupt("return");case 5:e.searchCondition=a.filter((function(t){return e.fields.some((function(e){return e.fieldName===t}))||["approveUserName","createUser"].includes(t)}));case 6:case"end":return t.stop()}}),t)})))()},toggleAdvancedSearchPanel:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.advancedVisible=e},setAdvanceSearchColumn:function(e){this.columnNum=Number(e)},buildSearchPanelParams:function(){var e=this.$route.query||{},t=(0,ka.v0)([],e),a=pt().cloneDeep(this.conditionParams),n={};t.forEach((function(t){var a=e[t.fieldName];n[t.fieldName]=a||(0,ka.el)(t)}));var i=(0,ka.xm)(t,n);return(0,ka.Ot)(a,i),a},buildAdvancedSearchParams:function(){var e=this.buildSearchPanelParams(),t=(null===e||void 0===e?void 0:e.systemConditions)||[],a=[];return t.forEach((function(e,n){"status"==e.property&&(a=(0,ka.Dx)(e.inValue||[]),t.splice(n,1))})),e.customStatusSearchList=a,a.length>0&&(this.filterParams.status=""),this.filterParams.status&&(e.customStatusList=this.filterParams.customStatusList),null!==e&&void 0!==e&&e.USER_DEPARTMENT&&Array.isArray(e.USER_DEPARTMENT)&&e.USER_DEPARTMENT.length>0&&(e["createUserTagList"]=e.USER_DEPARTMENT.map((function(e){return e.id})),Reflect.deleteProperty(e,"USER_DEPARTMENT")),e},toggleSelection:function(){var e;this.listPageIsCardStyle||(this.multipleSelection=[],null===(e=this.$refs.table)||void 0===e||e.clearSelection(),this.handleDoLayoutTable())},matchSelected:function(){var e=this;if(this.multipleSelection.length){var t=this.data.list.filter((function(t){return e.multipleSelection.some((function(e){return e.bizId===t.bizId}))}))||[];this.$nextTick((function(){t.forEach((function(t){e.$refs.table.toggleRowSelection(t,!0)}))}))}},handleSelection:function(e){var t=this,a=this.selectionCompute(e),n=this.multipleSelection.filter((function(e){return t.data.list.some((function(t){return t.bizId===e.bizId}))})),i=this.data.list.filter((function(e){return n.every((function(t){return t.bizId!==e.bizId}))}))||[];if(a.length>B.Az)return i.forEach((function(e){t.$refs.table.toggleRowSelection(e,!1)})),this.$platform.alert(this.$t("common.base.tip.choiceLimit",{limit:B.Az}));this.multipleSelection=a,this.handleDoLayoutTable()},selectionCompute:function(e){var t=this,a=[];return a=this.multipleSelection.filter((function(e){return t.data.list.every((function(t){return t.bizId!==e.bizId}))})),a=pt().uniqWith([].concat((0,s.A)(a),(0,s.A)(e)),pt().isEqual),a},showAdvancedSetting:function(){this.$refs.advanced.open(this.columns,{})},saveColumnStatus:function(e){var t,a=this,n=e.data||[],i=n.filter((function(e){return e.show}));if(0===i.length)return this.$message.warning(this.$t("view.template.list.tip1"));this.columns=[],null===(t=this.$refs.advanced)||void 0===t||t.close(),this.$nextTick((function(){a.$set(a,"columns",n.slice()),setTimeout((function(){return a.$refs.table.doLayout()}),0)}))},mergeLocalStorageColumnsToColumns:function(e){var t=this;return(0,o.A)((0,r.A)().mark((function a(){var n,i,o,s,l,c,u;return(0,r.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.getListStorage(t.templateId,"columns");case 2:if(n=a.sent,!pt().isEmpty(n)){a.next=6;break}return t.columns=e,a.abrupt("return");case 6:for(i=e.map((function(e){var t;return e.show=n.some((function(t){return pt().isString(t)?t.includes(e.fieldName):t.fieldName.includes(e.fieldName)})),e.fixed=n.some((function(t){return pt().isString(t)?t.includes("".concat(e.fieldName,"_fixed")):t.fieldName.includes("".concat(e.fieldName,"_fixed"))})),e.width=e.width||(null===(t=n.find((function(t){var a;return null===t||void 0===t||null===(a=t.fieldName)||void 0===a?void 0:a.includes(e.fieldName)})))||void 0===t?void 0:t.width),e})),o=i.reduce((function(e,t){return e[t.fieldName]=t,e}),{}),s=[],l=0;l<n.length;l++)c=n[l],u=pt().isString(c)?c:c.fieldName,o[u]&&s.push(o[u]);i.forEach((function(e){s.includes(e)||s.push(e)})),t.columns=s;case 12:case"end":return a.stop()}}),a)})))()},sortChangeHandler:function(e){var t=e.prop,a=e.order;if(!a)return this.searchParams.sorts=[],this.search();this.searchParams.sorts=[{property:t,direction:a===_a.ASC?ha.ASC:ha.DESC}],this.search()},handlePageNumChange:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"pass";this.searchParams.pageNum=e,this.data.list=[],this.search(t)},handleSizeChange:function(e){this.setListStorage(this.templateId,e,"pageSize"),this.searchParams.pageSize=e,this.searchParams.pageNum=1,this.search()},buildParams:function(){var e=this.buildAdvancedSearchParams();return(0,l.A)((0,l.A)((0,l.A)((0,l.A)({templateBizId:this.templateId,searchCondition:this.searchCondition},this.filterParams),this.searchParams),e),{},{tableIndexListFlag:!0},this.builderIntelligentTagsSearchParams())},search:function(){var e=arguments,t=this;return(0,o.A)((0,r.A)().mark((function a(){var n,i;return(0,r.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:n=e.length>0&&void 0!==e[0]?e[0]:"pass",t.loading=!0,i=t.buildParams(),u.kU(i).then((function(e){var a=e.success,n=e.message,i=e.data;if(!a)return t.$message.warning(n);var r={},o="";t.columns.length&&t.columns.forEach((function(e){var t;"currencyCode"===e.formType&&(o=e.fieldName),"formula"===e.formType&&null!==(t=e.setting)&&void 0!==t&&t.includeAmount&&(r[e.fieldName]="")})),i.list=Ua(((null===i||void 0===i?void 0:i.list)||[]).map((function(e){var t=(0,Wt.bI)(Et.A,e),a=(0,l.A)((0,l.A)((0,l.A)({},e),e.formValue),t);for(var n in r){var i=a[n],s=a[o]||"CNY";a[n]=i?"".concat(i," ").concat(s):i}return a})),t.tableColumns),t.data=i||{},t.$nextTick((function(){var e=document.querySelectorAll(".operator-column"),a=Array.from(e).map((function(e){return e.scrollWidth}));t.fixRightColWidth=Math.max.apply(null,a)+25||70,t.handleDoLayoutTable()})),t.matchSelected(),t.loading=!1}))["catch"]((function(e){var a;console.error("FieldApi.searchFormList error",e),null!==e&&void 0!==e&&null!==(a=e.message)&&void 0!==a&&a.includes("Request cancelled")||(t.loading=!1)}))["finally"]((function(){"save"===n?t.currentView&&"默认视图"!==t.currentView.viewName&&(t.$refs.passSaveRef.open(!0,t.searchList),t.isFlagSaveStatus=!0):"hgihSave"===n&&t.$refs.passSaveRef.open(t.searchParamsPeer,t.searchList)}));case 4:case"end":return a.stop()}}),a)})))()},handleMoreButtonList:function(e,t){var a=this;return(0,o.A)((0,r.A)().mark((function n(){return(0,r.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:a.moreBtnList=[],a.rowBtn={},e&&(a.moreBtnLoading=!0,u.bc({formContentId:t.bizId}).then((function(e){var t=e.success,n=e.data;if(t){var i=n,r=i.contentVO||{},o=i.buttonVO;if(a.rowBtn={contentVO:i.contentVO,wfButtonVO:o},r){var s=(null===o||void 0===o?void 0:o.buttons)||[];r.cnName=a.$t("common.base.share"),r.enName="share",a.moreBtnList=s.map((function(e){return(0,l.A)((0,l.A)({},e),{},{isOpen:!0})})).concat([r])}else a.moreBtnList=[]}}))["finally"]((function(){a.moreBtnLoading=!1})));case 3:case"end":return n.stop()}}),n)})))()},refresh:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e&&(this.search(),this.getStatusNumReq()),this.fetchAuthList()},handleBaseSearch:function(){this.listPageIsTableStyle&&(this.handlePageNumChange(1),this.getStatusNumReq()),this.listPageIsCardStyle&&this.handlePageModeRefresh()},reset:function(){var e=this,t=this.$options.data(),a=t.searchParams,n=t.filterParams,i=t.conditionParams;this.searchParams=a,this.filterParams=n,this.conditionParams=i,this.resetIntelligentTagsSearchParams(),this.toggleSelection(),this.chooseView(),this.onlyRestAdvSearchParamsTag||this.listPageIsTableStyle&&this.$nextTick((function(){e.getStatusNumReq()}))},fetchFields:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n,i,o,l,c,d,m,p,f;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,u.QA({templateBizId:e.templateId,excludeConnectOption:!0});case 3:return a=t.sent,n=(null===a||void 0===a?void 0:a.data)||{},i=n.paasFormFieldVOList,o=void 0===i?[]:i,l=n.isContainWf,c=n.templateName,d=n.templateNameLanguage,a.data.paasFormFieldVOList.forEach((function(t){if(t.displayNameLanguage){var a,n=t.displayNameLanguage;t.displayName=(null===n||void 0===n?void 0:n[null===(a=e.$i18n)||void 0===a?void 0:a.locale])||n[Z.As]}})),e.templateName=c,e.templateNameLanguage=d||{},e.isFlowForm=1==l,e.isFlowForm&&e.listPageIsTableStyle&&(e.filterParams.status=e.processingCode),m=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=O.aZ(e?(0,Sa.lR)(o):o);return t.filter((function(e){return O.Pm(e)}))},e.fields=m(!0),e.mergeLocalStorageSearchCondition(),p=[].concat((0,s.A)(e.fields.filter((function(e){return!(0,Aa.o)(e)}))),(0,s.A)(Et.A)).map((function(e){return Lt(e)})),e.isOpenConnector&&(p=p.concat(Et.D)),f=[],t.prev=16,t.next=19,e.fetchFlowExportNode();case 19:f=t.sent,t.next=25;break;case 22:t.prev=22,t.t0=t["catch"](16),console.error("fetch fetchFlowExportNode error",t.t0);case 25:e.exportColumns=La([].concat((0,s.A)(m(!1)),(0,s.A)(Et.A),(0,s.A)(f),(0,s.A)(e.intelligentTagsExportFields),(0,s.A)(e.isOpenConnector?Et.D:[]))),e.mergeLocalStorageColumnsToColumns(p),e.setListViewFields(o),t.next=33;break;case 30:t.prev=30,t.t1=t["catch"](0),console.log("template-list-view fetchTemplateFields error:",t.t1);case 33:case"end":return t.stop()}}),t,null,[[0,30],[16,22]])})))()},getStatusNumReq:function(){var e=this,t={isNumShow:1},a=this.buildParams();u.z1((0,l.A)((0,l.A)({},t),a)).then((function(t){var a,n=t.code,i=(t.message,t.data),r=void 0===i?[]:i;0==n&&(r.forEach((function(t){t.name!=e.$t("common.base.all")&&"全部"!=t.name||(t.code="")})),e.formStatus=r||[],e.customStatusList=(null===(a=e.formStatus.find((function(t){return t.code===e.filterParams.status})))||void 0===a?void 0:a.childList)||[])}))["catch"]((function(e){return console.error("err",e)}))},checkQuota:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.fields.find((function(e){return"logistics"===e.formType})),!a){t.next=13;break}return t.prev=2,t.next=5,u.FV();case 5:return e.hasQuota=t.sent,t.abrupt("return",e.hasQuota);case 9:t.prev=9,t.t0=t["catch"](2),n=t.t0&&t.t0.message||e.$t("common.form.tip.logistics.tips5"),e.$alert(n);case 13:case"end":return t.stop()}}),t,null,[[2,9]])})))()},handleViewLogistics:function(e,t){var a=this;return(0,o.A)((0,r.A)().mark((function n(){var i,o,s,c,u,d,m;return(0,r.A)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=a.isLogisticsNo(t),o=e[t.fieldName.split("_")[0]],i&&o&&o.length){n.next=4;break}return n.abrupt("return");case 4:if(s=o[0],c=s.company,u=s.no,d=s.phone,u&&t.setting.canCheckForStaff){n.next=7;break}return n.abrupt("return");case 7:return n.next=9,a.checkQuota();case 9:if(m=n.sent,m){n.next=12;break}return n.abrupt("return");case 12:a.logisticsInfo=(0,l.A)((0,l.A)({},c),{},{no:u,phone:d}),a.logisticsDialogShow=!0;case 14:case"end":return n.stop()}}),n)})))()},getTagsView:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={appId:e.appId,formTemplateId:e.templateId},t.next=4,(0,p.Y2)(a);case 4:n=t.sent,e.tagsViewList=(null===n||void 0===n?void 0:n.data)||[],e.tagsViewList.unshift({tagName:e.$t("common.base.all"),tagId:""}),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),console.log("error",t.t0);case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},onTagHandler:function(){var e=this.selectedIds.length;if(!e)return this.$platform.alert(this.$t("view.designer.rule.tagSetting.selectTagTip"));var t=[];1==this.multipleSelection.length&&this.multipleSelection.map((function(e){e.paasTagFormContentVoList&&e.paasTagFormContentVoList.map((function(e){t.push(e.tagId)}))})),this.tagIdList=(0,s.A)(new Set(t)),this.$refs.onTagDialog.visible=!0},saveOnTag:function(){this.multipleSelection=[],this.refresh()},deleteHandler:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n,i;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.selectedIds.length,a){t.next=3;break}return t.abrupt("return",e.$platform.alert(e.$t("view.template.list.tip2")));case 3:return t.prev=3,t.next=6,e.$platform.confirm(e.$t("view.template.list.tip3",{num:a}));case 6:if(n=t.sent,n){t.next=9;break}return t.abrupt("return");case 9:return t.next=11,u.Ug(e.selectedIds);case 11:if(i=t.sent,null!==i&&void 0!==i&&i.success){t.next=14;break}return t.abrupt("return",e.$message.warning(null===i||void 0===i?void 0:i.message));case 14:e.$message.success(e.$t("common.base.deleteSuccess")),e.toggleSelection(),setTimeout((function(){e.handlePageNumChange(1),e.getStatusNumReq(),e.deleteTagFetch()}),600),t.next=22;break;case 19:t.prev=19,t.t0=t["catch"](3),console.error("template-list-view deleteHandler error:",t.t0);case 22:case"end":return t.stop()}}),t,null,[[3,19]])})))()},goToCreate:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="/template/edit?formId=".concat(this.templateId,"&appId=").concat(this.appId);e&&(n+="&formContentId=".concat(e,"&onlyEdit=").concat(a));var i=(null===(t=this.templateNameLanguage)||void 0===t?void 0:t[this.$i18n.locale])||this.templateName,r=e?this.$t("common.otherPageTitle.editForm"):this.$t("common.base.createModule",{data1:i});this.jump(n,"create_view",r)},goToDetail:function(e){var t=this,a=e.isStore,n=e.templateBizId,i=e.bizId,r=e.processorInstanceId,o=void 0===r?"":r;if(a)return this.goToCreate(i);u.nL(i).then((function(e){var a,r,s="/template/detail?formId=".concat(n,"&formContentId=").concat(i,"&appId=").concat(t.appId,"&JumpKey=").concat(i);o&&(s+="&processId=".concat(null!==(a=e.data)&&void 0!==a&&a.processorInstanceId?null===(r=e.data)||void 0===r?void 0:r.processorInstanceId:o)),t.jump(s,"detail_view_".concat(i),t.$t("view.template.detail.formDetail"));try{sessionStorage.setItem(i,JSON.stringify(t.data.list.filter((function(e){return!(null!==e&&void 0!==e&&e.isStore&&(null===e||void 0===e||!e.processorInstanceId)||4===(null===e||void 0===e?void 0:e.status))})).map((function(e){return{bizId:null===e||void 0===e?void 0:e.bizId,appId:null===e||void 0===e?void 0:e.appId,templateBizId:null===e||void 0===e?void 0:e.templateBizId}}))))}catch(l){console.error(l)}}))},jump:function(e,t,a){var n,i=null===(n=window.frameElement)||void 0===n?void 0:n.getAttribute("id");if(!i)return this.$router.push(e);this.$platform.openTab({id:"".concat(t,"_").concat(this.templateId),title:a,close:!0,reload:!0,fromId:i,url:"/paas/#".concat(e,"&noHistory=1")})},goToTaskDetail:function(e){var t,a=null===(t=window.frameElement)||void 0===t?void 0:t.getAttribute("id");if(a){var n=e||{},i=n.taskId,r=n.taskNo;this.$platform.openTab({id:"task_view_".concat(i),title:"".concat(this.$t("common.pageTitle.pageTaskView")).concat(r),close:!0,reload:!0,url:"/task/view/".concat(i,"?noHistory=1")})}},handleShowMaterial:function(e,t){var a=this.columns.find((function(e){return"currencyCode"===e.formType}))||{},n=(0,l.A)({},e);n.disabled=1;var i=t.formValue[n.fieldName]||[];this.$refs.materialDialog.open(n,i,t,a.fieldName)},handleDoLayoutTable:function(){var e=this;this.$nextTick((function(){var t;return null===(t=e.$refs.table)||void 0===t?void 0:t.doLayout()}))},handleShowproviderSettlementDialog:function(e,t){var a=(0,l.A)({},e);a.disabled=1;var n=t.formValue[a.fieldName]||[];this.$refs.providerSettlementDialog.open(a,n,t)},fetchFlowExportNode:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n,i,o,s;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.getFlowExportNode)({formTemplateId:e.templateId});case 2:if(a=t.sent,n=a.success,i=void 0!==n&&n,o=a.data,s=void 0===o?[]:o,!(i&&s.length>0)){t.next=6;break}return t.abrupt("return",[{fieldName:"flowNode",formType:"flowNode",displayName:e.$t("common.base.node"),subFormFieldList:s.map((function(e){return{fieldName:e.bizId,displayName:e.name,formType:"text"}}))}]);case 6:return t.abrupt("return",[]);case 7:case"end":return t.stop()}}),t)})))()},toCustomerPage:function(e){e&&(0,We.iL)({type:ya.Z.PageCustomerView,key:e,params:"noHistory=1"})},genShortcutConditions:function(){return{filterParams:this.filterParams,appId:this.appId,templateId:this.templateId}},beforeSaveView:function(e){var t=(null===e||void 0===e?void 0:e.shortcutConditions)||this.genShortcutConditions();return[{fieldName:"paasCommonSearch",operator:"",value:t}]},resetAdvancedConditions:function(){this.conditionParams={conditions:[],systemConditions:[]},this.$refs.popperAdvancedSearchRef.setButtonStatus(!1)},handleAdvancedSearch:function(e){var t=this,a=e.searchModel,n=void 0===a?[]:a,i=e.isShowSave,r=void 0!==i&&i,o=e.searchList,s=void 0===o?[]:o;if(this.conditionParams=n,this.searchParamsPeer=r,r||(this.isFlagSaveStatus=!1),this.searchList=s,this.searchParamsPeer=this.handleShowSave(this.searchList),this.onlyRestAdvSearchParamsTag)return this.setOnlyRestAdvSearchParamsTag(!1);this.listPageIsTableStyle&&(this.handlePageNumChange(1,"hgihSave"),this.$nextTick((function(){t.getStatusNumReq()}))),this.listPageIsCardStyle&&this.handlePageModeRefresh()},createViewBySearchModel:function(e){this.shortcutConditions=this.genShortcutConditions(),this.$refs.advancedSearchModalRef.open({searchModel:e})},updateViewBySearchModel:function(e){this.shortcutConditions=this.genShortcutConditions(),this.currentView.shortcutConditions=this.shortcutConditions;var t=(0,l.A)((0,l.A)({},this.currentView),{},{searchModel:e});this.$refs.advancedSearchModalRef.open(t)},genViewport:function(e){var t;if(!e||!e.searchModel)return null;var a=pt().cloneDeep(e);if("paasCommonSearch"===(null===a||void 0===a||null===(t=a.searchModel[0])||void 0===t?void 0:t.fieldName)){var n=a.searchModel.shift();a.shortcutConditions=n.value}return a},chooseView:function(){var e,t,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(this.resetParams(),this.currentView=this.genViewport(a),null!==(e=this.currentView)&&void 0!==e&&e.shortcutConditions){var n,i;this.filterParams=this.currentView.shortcutConditions.filterParams;var r=this.filterParams,o=r.status,s=r.customStatusList,l=r.createView;null!==(n=this.formStatus.find((function(e){return e.code==o})))&&void 0!==n&&n.code||(this.filterParams.status=""),null!==(i=this.customStatusList.find((function(e){return s.includes(e.code)})))&&void 0!==i&&i.code||(this.filterParams.customStatusList=[]),this.createView.find((function(e){return e.value==l}))||(this.filterParams.createView="")}this.viewportSearchModel=(null===(t=this.currentView)||void 0===t?void 0:t.searchModel)||[]},editViewByViewport:function(e){var t=this.genViewport(e);this.shortcutConditions=(null===t||void 0===t?void 0:t.shortcutConditions)||this.genShortcutConditions(),this.$refs.advancedSearchModalRef.open(t)},changeCommonUse:function(e){var t=e.fieldName,a=e.isChecked,n=new Set(this.inCommonUse);a?n.add(t):n["delete"](t),this.inCommonUse=Array.from(n);var i="PAAS_COMMON_USE_SEARCH";try{(0,k.EI)(i,this.inCommonUse,Ma.A.Advance,!0,this.appId,this.templateId)}catch(r){console.error("changeCommonUse ~ setIndexedDbData ~ error",r)}},recoverCommonUse:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a="PAAS_COMMON_USE_SEARCH",t.prev=1,t.next=4,(0,k["if"])(a,[],Ma.A.Advance,!0,e.appId,e.templateId);case 4:e.inCommonUse=t.sent,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](1),console.error("recoverCommonUse ~ getIndexedDbData ~ error",t.t0);case 10:case"end":return t.stop()}}),t,null,[[1,7]])})))()},handleViewportSave:function(e){e&&(this.chooseView(e),this.$refs.viewportListRef.refresh(e.viewId))},resetParams:function(){var e=this.$options.data(),t=e.searchParams,a=e.filterParams;this.searchParams=t,this.filterParams=a,this.searchParams.pageNum=1,this.data.list=[],this.toggleSelection()},checkedOperatorButtonIsDisabled:function(e,t){var a,n=(null===(a=this.rowBtn.wfButtonVO)||void 0===a?void 0:a.pauseFlag)&&t.enName!==X.A.PAUSE.value;return"share"!==t.enName&&(!this.currentOperatorRecordIsEmpty&&this.currentOperatorRow.bizId===e.bizId||n)},initFormApprovalPageParams:function(){var e=this.$route.query.customerStatus,t=this.$route.query.createView;e&&(this.filterParams.customStatusList=[e]),t&&(this.filterParams.createView=t)},getOuterFormSetting:function(){var e=this;(0,A.Vp)({paasFormTemplateId:this.templateId}).then((function(t){var a,n;e.formWriteConfig=(null===(a=t.data)||void 0===a?void 0:a.paasFormOuterWriteForm)||{},e.shareLinkOpen=e.formWriteConfig.switchOpen&&(null===(n=e.formWriteConfig.setting)||void 0===n?void 0:n.shareLinkOpen)}))},openShareFormDialog:function(){this.$refs.shareFormDialog.openDialog()},handleToProductView:function(e){(0,We.iL)({type:ya.Z.PageProductView,key:e,params:"noHistory=1"})},initUsageViewSelect:function(){var e=this.$route.query,t=(null===e||void 0===e?void 0:e.useView)||"";"all"==t&&(this.filterParams.status="")},handlePageModeRefresh:function(){var e;null===(e=this.$refs.cardViewRef)||void 0===e||e.initializationData()},handleOnlyResetSearchParams:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.setOnlyRestAdvSearchParamsTag(e),this.reset()},fetchBoardCardDataList:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.fullLoading=!0,t.next=4,e.fetchListCardViewListData();case 4:return t.abrupt("return",t.sent);case 7:t.prev=7,t.t0=t["catch"](0),console.error("[ fetchListCardViewListData error]",t.t0);case 10:return t.prev=10,e.fullLoading=!1,t.finish(10);case 13:case"end":return t.stop()}}),t,null,[[0,7,10,13]])})))()},successRefreshCallBack:function(){this.listPageIsTableStyle&&(this.search(),this.getStatusNumReq()),this.listPageIsCardStyle&&this.handlePageModeRefresh()},isColumnLink:function(e){var t;return(null===e||void 0===e||null===(t=e.setting)||void 0===t?void 0:t.isLink)||!1},handleTextLinkClick:function(e,t){var a,n,i=null===t||void 0===t||null===(a=t.setting)||void 0===a?void 0:a.linkFormat,r=null!==(n=null===e||void 0===e?void 0:e[null===t||void 0===t?void 0:t.fieldName])&&void 0!==n?n:"",o=i.replace("%s",r);try{Ra.Ay.openLink(o)}catch(s){console.error("Failed to open link:",s)}}}),mounted:function(){var e=this;return(0,o.A)((0,r.A)().mark((function t(){var a,n,i,s,l,c;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.initFormApprovalPageParams(),e.recoverCommonUse(),n=e.$route.query.viewNo||"",i={},!n){t.next=10;break}return t.next=7,h("paas");case 7:s=t.sent,i=(s||[]).find((function(e){return n===e.viewNo}))||{};try{e.viewConfig=JSON.parse(i.config||"{}")}catch(u){console.error("getViewConfig error:",u)}case 10:return null!==(a=e.viewConfig)&&void 0!==a&&a.appId||(e.viewConfig={appId:e.appId,formTemplateId:e.templateId}),t.next=13,e.getListStorage(e.templateId,"pageSize");case 13:if(l=t.sent,l&&(e.searchParams.pageSize=l),!e.viewModelGray){t.next=18;break}return t.next=18,e.fetchBoardCardDataList();case 18:return t.next=20,e.fetchFields();case 20:if(c=!0,!(n&&i&&i.searchModel)){t.next=25;break}return c=!1,t.next=25,e.chooseView(i);case 25:e.initUsageViewSelect(),e.refresh(c),e.checkQuota(),e.fetchRvTenantGray(),e.isContainerGray&&e.getPageButtonList(),e.getOuterFormSetting(),Pt(e.handleOnlyResetSearchParams),window.__exports__refresh=(0,o.A)((0,r.A)().mark((function t(){return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.listPageIsTableStyle&&e.refresh(),e.listPageIsCardStyle&&e.handlePageModeRefresh();case 2:case"end":return t.stop()}}),t)})));case 33:case"end":return t.stop()}}),t)})))()},beforeDestroy:function(){Vt(this.handleOnlyResetSearchParams)}},ja=Wa,qa=ja,Ga=(0,z.A)(qa,n,i,!1,null,"ae4995d0",null),Ha=Ga.exports}}]);