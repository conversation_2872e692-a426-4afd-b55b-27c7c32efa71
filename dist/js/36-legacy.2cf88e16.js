"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[36],{63036:function(e,t,n){n.r(t),n.d(t,{default:function(){return L}});n(33656);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"form-setting-frame-container",on:{click:e.handleClosePopover}},[t("div",{staticClass:"setting-flow-header"},[t("el-row",{staticClass:"setting-flow-header-left",attrs:{type:"flex"}},[t("div",{staticClass:"return-btn",on:{click:e.goBack}},[t("i",{staticClass:"iconfont icon-arrow-left"}),e._v(" "+e._s(e.$t("common.base.back")))]),t("div",{class:["form-name-panel",e.isOpenMultiLanguage?"multi-language__name":null]},[e.isFormDesignTab?[t("el-tooltip",{attrs:{effect:"dark",content:e.$t("view.designer.editFormName"),placement:"bottom"}},[t("el-input",{staticClass:"form-name",attrs:{placeholder:e.$t("common.placeholder.inputSomething",{data1:e.$t("view.designer.formName")}),maxlength:"20"},model:{value:e.customizeName,callback:function(t){e.customizeName="string"===typeof t?t.trim():t},expression:"customizeName"}},[e.isOpenMultiLanguage?t("i",{staticClass:"iconfont icon-earth",attrs:{slot:"suffix"},on:{click:function(t){return t.stopPropagation(),e.handleSettingFormNameLang.apply(null,arguments)}},slot:"suffix"}):e._e()])],1)]:[e._v(" "+e._s(e.customizeName)+" ")]],2)]),t("div",{staticClass:"setting-flow-header-right"},[t("el-button",{staticClass:"header-save-btn",on:{click:e.perviewClick}},[e._v(" "+e._s(e.$t("common.base.preview"))+" ")]),t("el-button",{staticClass:"header-save-btn",attrs:{plain:""},on:{click:e.submit}},[e._v(" "+e._s(e.$t("common.base.save"))+" ")])],1)],1),t("div",{staticClass:"customize-setting"},[t("div",{staticClass:"customize-setting-content"},[t("div",{staticClass:"content-left"},[t("div",{staticClass:"left-header"},[t("span",{staticClass:"customize-name"},[e._v(e._s(e.customizeName))])]),t("div",{ref:"systemViewNavRef",staticClass:"system-view-nav"},[t("div",{staticClass:"tips"},[t("i",{staticClass:"iconfont icon-fdn-info"}),t("span",[e._v(e._s(e.$t("view.designer.rule.printSetting.tip")))])]),t("div",{staticClass:"menus",style:{height:"".concat(e.menusHeight,"px")}},[t("el-tree",{attrs:{data:e.menuList,props:e.defaultProps,"node-key":"id","default-expanded-keys":e.defaultExpandedKeys,"render-content":e.renderContent,"icon-class":"icon-class"},on:{"node-click":e.menuSelect}})],1)])]),t("div",{staticClass:"content-right"},[t("div",{staticClass:"tinymce-box"},[t("tinymce",{ref:"createTinymce",staticClass:"rich-txt",attrs:{height:e.richHeight,init:e.getEditConfig(),plugins:e.showPlugins,toolbar:e.showToolbar},model:{value:e.content,callback:function(t){e.content=t},expression:"content"}})],1)])])]),t("setting-select-mode",{ref:"settingSelectModeRef"}),t("el-popover",{attrs:{placement:"bottom",width:"320",trigger:"manual","popper-class":"paper-setting-popover"},on:{click:function(e){e.stopPropagation()}},model:{value:e.paperPopoverVisible,callback:function(t){e.paperPopoverVisible=t},expression:"paperPopoverVisible"}},[t("div",{staticClass:"paper-setting-popover__content",on:{click:function(e){e.stopPropagation()}}},[t("div",{staticClass:"paper-setting-popover__content-main"},[e.paperPopoverVisible?t("form-builder",{staticClass:"paper-setting-popover__form",attrs:{value:e.paperForm,fields:e.paperPopoverFormFields},on:{update:e.handleUpdatePaperForm},scopedSlots:e._u([{key:"pageSize",fn:function(n){var a=n.field,i=n.value;return[t("form-item",{attrs:{label:a.displayName}},[t("form-select",{attrs:{value:i,field:a,clearable:!1},on:{update:e.handleUpdatePaperForm}}),"customize"===i?t("ul",{staticClass:"paper-setting-popover__form-input size"},[t("li",{staticClass:"paper-setting-popover__form-input-item"},[t("label",[e._v("宽：")]),t("el-input",{staticClass:"input",on:{input:function(t){return e.onInput(t,"width")}},model:{value:e.paperForm.width,callback:function(t){e.$set(e.paperForm,"width",t)},expression:"paperForm.width"}}),t("span",{staticClass:"text"},[e._v("mm")])],1),t("li",{staticClass:"paper-setting-popover__form-input-item"},[t("label",[e._v("高：")]),t("el-input",{staticClass:"input",on:{input:function(t){return e.onInput(t,"height")}},model:{value:e.paperForm.height,callback:function(t){e.$set(e.paperForm,"height",t)},expression:"paperForm.height"}}),t("span",{staticClass:"text"},[e._v("mm")])],1)]):e._e()],1)]}},{key:"isHorizontal",fn:function(n){var a=n.field,i=n.value;return[t("form-item",{attrs:{label:a.displayName}},[t("form-select",{attrs:{value:i,field:a,clearable:!1},on:{update:e.handleUpdatePaperForm}})],1)]}},{key:"pageDistance",fn:function(n){var a=n.field;n.value;return[t("form-item",{attrs:{label:a.displayName}},[t("ul",{staticClass:"paper-setting-popover__form-input"},e._l(e.paperForm.pageDistance,(function(n){return t("li",{key:n.value,staticClass:"paper-setting-popover__form-input-item"},[t("label",[e._v(e._s(n.name)+"：")]),t("el-input",{key:n.value,staticClass:"input",on:{input:function(t){return e.onInput(t,n.value)}},model:{value:e.paperForm[n.value],callback:function(t){e.$set(e.paperForm,n.value,t)},expression:"paperForm[item.value]"}}),t("span",{staticClass:"text"},[e._v("mm")])],1)})),0)])]}}],null,!1,2551191898)}):e._e()],1)])])],1)},i=[],o=n(35730),r=n(18885),s=n(42881),l=n(71357),c=n(62361),u=(n(67880),n(2286),n(44807),n(62838),n(3923),n(80793),n(35256),n(21484),n(27408),n(76119),n(16961),n(54615),n(7354),n(89370),n(32807),n(22665),n(75069),n(39789),n(42925),n(79526),n(13262),n(13506)),p=function(){var e=this,t=e._self._c;return t("div",[t("base-modal",{attrs:{show:e.visible,title:e.$t("view.designer.rule.printSetting.tip2"),width:"556px"},on:{close:function(t){e.visible=!1}}},[t("div",{staticClass:"base-modal-content"},[t("el-radio",{attrs:{label:"showSelected"},model:{value:e.data.dropDownDisplayMode,callback:function(t){e.$set(e.data,"dropDownDisplayMode",t)},expression:"data.dropDownDisplayMode"}},[e._v(e._s(e.$t("view.designer.rule.printSetting.showSelect")))]),t("el-radio",{attrs:{label:"showAll"},model:{value:e.data.dropDownDisplayMode,callback:function(t){e.$set(e.data,"dropDownDisplayMode",t)},expression:"data.dropDownDisplayMode"}},[e._v(e._s(e.$t("view.designer.rule.printSetting.showAll")))])],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleConfirmDialog}},[e._v(e._s(e.$t("common.base.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmitDialog}},[e._v(e._s(e.$t("common.base.makeSure")))])],1)])],1)},d=[],m={name:"setting-select-mode",components:{},data:function(){return{visible:!1,data:{}}},computed:{},created:function(){},methods:{handleConfirmDialog:function(){this.visible=!1},handleSubmitDialog:function(){this.visible=!1}}},f=m,g=n(49100),h=(0,g.A)(f,p,d,!1,null,"7ca6da7f",null),v=h.exports,b=n(74526),y=n(64055),F=n(25623),A=n(13465),N=n(98e3),S=n(92935),w=n.n(S),C=n(97022),E=n(92540),x=n(87512),k=n(80602),_=n(92050),T=(n(44771),[x.E.SerialNumber,x.E.Text,x.E.Textarea,x.E.Number,x.E.Select,x.E.Attachment,x.E.User,x.E.Date,x.E.DateTime,x.E.Phone,x.E.Email,x.E.Address,x.E.Autograph,x.E.Formula,x.E.SubForm,x.E.ServiceItem,x.E.MaterialOrder,x.E.Product,x.E.OutWarehouse,x.E.SunmiOutWarehouse,x.E.SunmiInWarehouse,x.E.Currency,x.E.CurrencyCode,x.E.QualityInfo,x.E.RelatedTask,x.E.IdCard,x.E.Cascader,x.pb.Customer,x.pb.CustomerAddress,x.pb.Linkman,x.E.Warehouse,x.E.SparePart,x.E.OutSparepart,x.E.InSparepart,x.E.InWarehouse,x.E.OutDataSource,x.E.Connector,x.E.Tag]),P=[x.E.SubForm,x.E.Product,x.E.ServiceItem,x.E.MaterialOrder,x.E.OutWarehouse,x.E.SparePart,x.E.OutSparepart,x.E.InSparepart,x.E.InWarehouse,x.E.SunmiOutWarehouse,x.E.SunmiInWarehouse,x.E.Connector],z={name:"customize-template",components:(0,c.A)((0,c.A)({},u.A.name,u.A),v.name,v),data:function(){return{defaultProps:{children:"subFormFieldList",label:"displayName"},menuList:[],content:"",menusHeight:0,richHeight:document.documentElement.clientHeight-50,isFormDesignTab:!0,customizeName:this.$route.query.customizeName||"",languageList:Object.freeze((0,y.Z0)()),defaultExpandedKeys:[],formNameLang:(0,l.A)({},(0,y.T2)()),approveNodeField:null,paperPopoverVisible:!1,paperForm:{pageSize:"A4",isHorizontal:"纵向",height:"",width:"",topMargin:"",leftMargin:"",rightMargin:"",bottomMargin:"",pageDistance:[{name:"上",value:"topMargin"},{name:"右",value:"rightMargin"},{name:"下",value:"bottomMargin"},{name:"左",value:"leftMargin"}]}}},computed:{templateBizId:function(){return this.$route.query.formId},editor:function(){return this.$refs.createTinymce.editor},customizeTemplateId:function(){return this.$route.query.customizeTemplateId},isOpenMultiLanguage:function(){return(0,y.g9)()||!1},showToolbar:function(){var e="undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | rowspacingtop rowspacingbottom | lineheight | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist | table | forecolor backcolor removeformat | pagebreak | insertfile image link  | ltr rtl | paper-setting";return e},showPlugins:function(){var e="print preview paste importcss searchreplace autolink autosave save directionality code visualblocks visualchars  image link media codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons lineheight rowspacing";return e},paperPopoverFormFields:function(){return[new _.A({fieldName:"pageSize",displayName:"纸张大小",formType:"select",isNull:1,setting:{dataSource:[{id:"A4",name:"A4"},{id:"A3",name:"A3"},{id:"A5",name:"A5"},{id:"B5",name:"B5"},{id:"customize",name:"自定义"}]}}),new _.A({fieldName:"isHorizontal",displayName:"纸张方向",formType:"select",isNull:1,setting:{dataSource:["纵向","横向"]}}),new _.A({fieldName:"pageDistance",displayName:"页边距",isNull:1,formType:"text"})]}},created:function(){this.getFormFieldListInfo(),this.getHtmlContent(),this.fetchAllNode()},mounted:function(){var e=this;this.calcHeight(),window.addEventListener("resize",(function(){return e.$nextTick((function(){return e.calcHeight()}))})),document.body.addEventListener("click",(function(){return e.paperPopoverVisible=!1}))},beforeDestroy:function(){window.removeEventListener("resize",(function(){}))},methods:{onInput:function(e,t){this.paperForm[t]=e.replace(/^(0+)|[^\d]+/g,"")},calcHeight:function(){var e;this.menusHeight=(null===(e=this.$refs)||void 0===e||null===(e=e.systemViewNavRef)||void 0===e?void 0:e.offsetHeight)-85},renderContent:function(e,t){var n=this,a=t.node,i=t.data,o=t.store;return i.formType!==x.E.Select||i.subFormState?P.includes(i.formType)?e("div",{style:"display:flex; align-item:'center'"},[i.subFormIconState?e("i",{class:"iconfont icon-down",on:{click:function(e){return n.handleCollapseData(i,o,e)}}}):e("i",{class:"iconfont icon-right",on:{click:function(e){return n.handleExpandData(i,e)}}}),e("span",{class:"custom-tree-node",style:"padding-left:0"},[e("span",[a.label])])]):e("span",{class:"custom-tree-node"},[e("span",[a.label])]):e("span",{class:"custom-tree-node"},[e("span",[a.label]),e("i",{class:"iconfont icon-setting custom-tree-node-icon",on:{click:function(e){return n.settingSelectVal(i,e)}}})])},handleExpandData:function(e,t){t.stopPropagation(),this.defaultExpandedKeys.push(e.id),e.subFormIconState=!0},handleCollapseData:function(e,t,n){n.stopPropagation(),this.changeTreeNodeStatus(t.root),this.defaultExpandedKeys=this.defaultExpandedKeys.filter((function(t){return t!==e.id})),e.subFormIconState=!1},changeTreeNodeStatus:function(e){for(var t=0;t<e.childNodes.length;t++)e.childNodes[t].expanded=!1,e.childNodes[t].childNodes.length>0&&this.changeTreeNodeStatus(e.childNodes[t])},settingSelectVal:function(e,t){t.stopPropagation(),this.$refs.settingSelectModeRef.visible=!0,this.$refs.settingSelectModeRef.data=e},getHtmlContent:function(){var e=this;return(0,s.A)((0,r.A)().mark((function t(){var n,a,i,o;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,C.om)({id:e.customizeTemplateId})["catch"]((function(e){return console.log(e)}));case 2:n=t.sent,n.success&&(e.content=n.result.content,o=(null===(a=n.result)||void 0===a?void 0:a.setting)||{},e.paperForm=(0,l.A)((0,l.A)((0,l.A)({},e.paperForm),(null===(i=n.result)||void 0===i?void 0:i.setting)||{}),{},{isHorizontal:null!==o&&void 0!==o&&o.isHorizontal?"横向":"纵向"}));case 4:case"end":return t.stop()}}),t)})))()},getPaperSetting:function(){var e=(0,l.A)((0,l.A)({},this.paperForm),{},{isHorizontal:"横向"===this.paperForm.isHorizontal});return Reflect.deleteProperty(e,"pageDistance"),e},submit:function(){var e=this;return(0,s.A)((0,r.A)().mark((function t(){var n,a,i;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={id:e.customizeTemplateId,content:(null===(n=e.$refs)||void 0===n||null===(n=n.createTinymce)||void 0===n?void 0:n.dataValue)||e.content,templateName:e.customizeName,bizId:e.templateBizId,module:"PAAS",setting:e.getPaperSetting()},t.next=3,(0,C.TT)(a)["catch"]((function(e){return console.log(e)}));case 3:i=t.sent,i.success&&e.$message.success(i.message);case 5:case"end":return t.stop()}}),t)})))()},handleSettingFormNameLang:function(){var e=this;this.$fast.languageSetting.show({title:this.$t("common.base.languageSetting"),type:"input",languageDefaultValueObj:(0,l.A)((0,l.A)({},this.formNameLang),{},(0,c.A)({},y.As,this.customizeName))}).then((function(t){e.formNameLang=t,e.customizeName=t[y.As]}))},goBack:function(){this.$router.go(-1)},getFormFieldListInfo:function(){var e=this;return(0,s.A)((0,r.A)().mark((function t(){var n,a,i,s,l,c,u;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,b.QA)({templateBizId:e.templateBizId});case 2:i=t.sent,s=(null===(n=i.data)||void 0===n?void 0:n.paasFormFieldVOList)||[],l=(null===(a=i.data)||void 0===a?void 0:a.systemFormFieldVOList)||[],c=s.filter((function(e){return T.includes(e.formType)})).map((function(t){var n;if(t.formType===x.pb.Customer)if(null!==(n=t.setting)&&void 0!==n&&null!==(n=n.relatedFields)&&void 0!==n&&n.length){var a=w().cloneDeep(t);a.displayName=e.$t("common.form.type.customerName"),a.formType=x.E.Text,a.parentFieldName=a.fieldName,t.setting.relatedFields=t.setting.relatedFields.map((function(e){return e.parentFieldName=t.fieldName,e})),t.subFormFieldList=[a].concat(t.setting.relatedFields),P.push(x.pb.Customer)}else{var i=P.findIndex((function(e){return e==x.pb.Customer}));P.splice(i,1)}else if(t.formType===x.E.Product)t.subFormFieldList=(0,E._)(t.subFormFieldList||[],t.fieldName).map((function(e){return e.parentFieldName=t.fieldName,e}));else if(t.formType===x.E.MaterialOrder||t.formType===x.E.SubForm)t.subFormFieldList.unshift({fieldName:"materialIndex",displayName:e.$t("common.base.SN"),formType:"text",isChildForm:0,isSystem:0,orderId:0,parentFieldName:t.fieldName});else if(t.formType===x.E.Connector){var o,r;t.subFormFieldList=(null===(o=t.setting)||void 0===o?void 0:o.subFormFieldList)||[];var s=null===(r=t.setting)||void 0===r?void 0:r.connector;if(s){var l,c=(null===(l=s.actionList)||void 0===l||null===(l=l.find((function(e){return"SELECT"===e.action})))||void 0===l?void 0:l.toResponseFieldList)||[],u=s.showFieldNameList||[];t.subFormFieldList=(0,N.bN)(c,u).concat(t.subFormFieldList).map((function(e){return e.displayName=e.displayName||e.cnName,e.fieldName=e.fieldName||e.enName,e.formType=e.formType||e.fieldType,e.parentFieldName=t.fieldName,e}))}}return t})).filter((function(e){var t;return(null===(t=e.subFormFieldList)||void 0===t?void 0:t.length)>0&&(e.subFormFieldList=e.subFormFieldList.filter((function(e){return T.includes(e.formType)}))),e})),u=[].concat((0,o.A)(c),(0,o.A)(l)),e.approveNodeField&&u.push(e.approveNodeField),e.menuList=e.changeFieldData(u);case 9:case"end":return t.stop()}}),t)})))()},changeFieldData:function(e){var t=this;return e&&e.length>0&&e.map((function(e){var n;e.formType===x.E.Select&&(e.dropDownDisplayMode="showSelected"),P.includes(e.formType)&&(e.subFormIconState=!1),(null===(n=e.subFormFieldList)||void 0===n?void 0:n.length)>0&&(e.subFormParentState=!0,e.subFormFieldList.forEach((function(e){e.subFormState=!0})),t.changeFieldData(e.subFormFieldList))})),e},getEditConfig:function(){var e=this,t=this;return{toolbar_mode:"wrap",browser_spellcheck:!0,elementpath:!1,statusbar:!1,paste_data_images:!0,fontsize_formats:"2px 4px 6px 8px 10px 12px 14px 16px 18px 20px 24px 26px 28px 30px 32px 36px 48px",lineheight_formats:"0.1 0.2 0.3 0.4 0.5 0.6 0.7 0.8 0.9 1 1.1 1.2 1.3 1.4 1.5 2, 3, 5",font_formats:"微软雅黑=Microsoft Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings,zapf dingbats;微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif",convert_urls:!1,content_style:"\n          body {\n            padding:72px 56px 0;\n          }\n          .mce-pagebreak{\n            margin: 15px 0 !important;\n          }\n          td{padding:0!important}\n        ",setup:function(n){n.on("focusin",(function(t){e.setSpanEditAttr(e.currentNode,!1)})),n.on("focusout",(function(t){e.setSpanEditAttr(e.currentNode,!0)})),n.on("click",(function(a){t.paperPopoverVisible&&(t.paperPopoverVisible=!1);var i=n.selection.getSel(),o=n.selection.getNode();if("Range"===i.type)e.currentNode=o;else{var r=e.getCurrentParentByTag("span[contenteditable]");if(r)return!!r.getAttribute("contenteditable")&&(r.setAttribute("contenteditable",!1),!1)}})),n.ui.registry.addButton("paper-setting",{text:'<div class="paper-setting"><i class="iconfont icon-a-zhizhangdaxiao" style="font-size: 20px;color:#000"></i> </div>',onAction:function(){if(!t.paperPopoverVisible){var e=document.querySelector(".paper-setting"),n=document.querySelector(".paper-setting-popover"),a=e.getBoundingClientRect(),i=a.x,o=a.y;i+320-document.body.clientWidth>0&&(i=document.body.clientWidth-165),n.style.cssText="left: ".concat(i-160,"px; top: ").concat(o+28,"px"),t.paperPopoverVisible=!0}}})}}},menuSelect:function(e){if(e.subFormParentState)return e.subFormIconState=!e.subFormIconState;if(e.parentFieldName==k.A.APPROVE_NODE)return this.editor.insertContent(this.getApproveNode(e));if(e.subFormState){var t=this.getCurrentParentByTag('table[data-shb-table-tag="table"]');if(t){var n,a=this.editor.dom.select('tr[data-shb-table-tr-tag="header"]',t),i=this.editor.dom.select('tr[data-shb-table-tr-tag="value"]',t);if(e.parentFieldName!==(null===(n=i[0])||void 0===n||null===(n=n.attributes["data-shb-parent-tag"])||void 0===n?void 0:n.value))return;a&&i&&(this.editor.dom.add(a,"td",{"data-shb-table-td-tag":"name"},e.displayName),this.editor.dom.add(i,"td",{"data-shb-table-td-tag":"value"},this.getSpanNode(e)),this.content=this.editor.getContent({format:"html"}))}else this.getCurrentParentByTag("table")||this.editor.insertContent(this.getCreateTable(e))}else{var o=this.getCurrentParentByTag('table[data-shb-table-tag="table"]');if(o)return this.content=this.editor.getContent({format:"html"});var r=this.getSpanNode(e);e.formType===x.E.Select&&e.dropDownDisplayMode&&"showSelected"!==e.dropDownDisplayMode&&(r=this.getSelectNode(e)),this.editor.insertContent(r)}},perviewClick:function(){var e=this;return(0,s.A)((0,r.A)().mark((function t(){var n,a,i,o,s;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={type:"pdf",content:(0,A.v4)((null===(n=e.$refs)||void 0===n||null===(n=n.createTinymce)||void 0===n?void 0:n.dataValue)||e.content),setting:e.getPaperSetting()},t.next=4,(0,C.Wf)(a)["catch"]((function(e){return console.log(e)}))["finally"]((function(){return e.printBtnState=!1}));case 4:if(i=t.sent,o=i.success,s=i.result,o&&s){t.next=9;break}return t.abrupt("return");case 9:F.A.openHelp(s),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](0),console.warn(t.t0,"error try catch perviewClick");case 15:case"end":return t.stop()}}),t,null,[[0,12]])})))()},getCurrentParentByTag:function(e){var t;return null!==(t=this.editor.selection)&&void 0!==t&&t.getNode()?this.editor.dom.getParent(this.editor.selection.getNode(),e):null},setSpanEditAttr:function(e,t){if(e&&e.hasAttribute("contenteditable")&&e.getAttribute("contenteditable")!=t&&e.setAttribute("contenteditable",t),e&&e.children)for(var n=0;n<e.children.length;n++){var a=e.children[n];a.children?this.setSpanEditAttr(a,t):a.hasAttribute("contenteditable")&&e.getAttribute("contenteditable")!=t&&a.setAttribute("contenteditable",t)}},getSpanNode:function(e){var t="";return e.subFormState||(t="".concat(e.displayName,"：")),"".concat(t,"<span data-shb-tag='").concat(e.fieldName,"' contenteditable='false'>{").concat(e.displayName,"}</span>")},getSelectNode:function(e){var t="";e.subFormState||(t="".concat(e.displayName,"："));var n=e.setting.dataSource,a="".concat(t);return n.forEach((function(t){a+="<span class='mceNonEditable' data-shb-select='".concat(e.fieldName,"'> <input type='checkbox'/>").concat(t,"</span>")})),a},getCreateTable:function(e){return"\n      <table data-shb-table-tag='table' style='border-collapse: collapse; width: 100%;' border='1' >\n        <tbody>\n          <tr data-shb-table-tr-tag='header'>\n            <td data-shb-table-td-tag='name'>".concat(e.displayName,"</td>\n          </tr>\n          <tr data-shb-table-tr-tag='value' data-shb-parent-tag='").concat(e.parentFieldName,"'>\n            <td data-shb-table-td-tag='value'>").concat(this.getSpanNode(e),"</td>\n          </tr>\n        </tbody>\n      </table>")},fetchAllNode:function(){var e=this;return(0,s.A)((0,r.A)().mark((function t(){var n,a;return(0,r.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,C.ao)({formTemplateBizId:e.$route.query.formId});case 3:n=t.sent,a=((null===n||void 0===n?void 0:n.data)||[]).filter((function(e){return e.typeString===k.A.APPROVE_NODE})).map((function(e){return e.bizId=e.bizId.replace("_",""),e})),e.approveNodeField=(0,A.no)(a),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.approveNodeField=null;case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},getApproveNode:function(e){var t="【".concat(e.displayName,"】").concat(this.$t("view.template.detail.approveSuggestion"),"：");return t+="<span data-shb-approve-node='".concat(e.fieldName,"' contenteditable='false'>{【").concat(this.$t("common.base.approveUser"),"】").concat(this.$t("view.designer.rule.printSetting.approveOpinionContent"),"}{【").concat(this.$t("common.base.approveUser"),"】").concat(this.$t("view.designer.rule.printSetting.approvalSignature"),"}</span>"),t},handleClosePopover:function(){this.paperPopoverVisible=!1},handleUpdatePaperForm:function(e){var t=e.field,n=e.newValue,a=(e.oldValue,t.fieldName);t.displayName;"pageSize"===a&&"customize"!==n&&(this.paperForm=(0,l.A)((0,l.A)({},this.paperForm),{},{width:"",height:""})),this.$set(this.paperForm,a,n)}}},D=z,I=(0,g.A)(D,a,i,!1,null,"1246fd6a",null),$=I.exports,L=$},97022:function(e,t,n){n.d(t,{$u:function(){return x},Aw:function(){return S},Dw:function(){return F},I2:function(){return w},IQ:function(){return p},Jg:function(){return o},Jm:function(){return A},Sl:function(){return u},TT:function(){return C},Vp:function(){return s},Wf:function(){return _},XX:function(){return r},XZ:function(){return d},aU:function(){return c},ao:function(){return b},b5:function(){return l},hZ:function(){return g},j4:function(){return h},jV:function(){return v},ny:function(){return y},om:function(){return E},r5:function(){return k},sj:function(){return N},uN:function(){return m},zH:function(){return f}});var a=n(22229),i="/api/paas",o=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/outer/setting/switch"),e)},r=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/outer/setting/save"),e)},s=function(e){return a.A.get("".concat(i,"/outside/pc/form/admin/outer/setting/get"),e)},l=function(e){return a.A.get("".concat(i,"/outside/pc/form/admin/outer/setting/field"),e)},c=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/outer/setting/field/save"),e)},u=function(e){return a.A.post("".concat(i,"/outside/pc/link/defaultValue/save"),e)},p=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/outer/setting/phone/save"),e)},d=function(e){return a.A.get("".concat(i,"/outside/pc/form/admin/outer/setting/phone/list"),e)},m=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/outer/setting/phone/delete"),e)},f=function(e){return a.A.post("".concat(i,"/outside/pc/form/auth/editFormAuth"),e)},g=function(e){return a.A.get("".concat(i,"/outside/pc/form/auth/getFormAuth"),e)},h=function(e){return a.A.get("".concat(i,"/outside/pc/form/admin/print/setting/all"),e)},v=function(e){return a.A.post("".concat(i,"/outside/pc/form/admin/print/setting/update"),e)},b=function(e){return a.A.get("".concat(i,"/outside/pc/form/admin/print/setting/node/all"),e)},y=function(e){return a.A.get("/api/customer/outside/satisfactionConfig/getConfigGroup",e)},F=function(e){return a.A.post("".concat(i,"/outside/pc/returnVisit/batchCreateSetting"),e)},A=function(e){return a.A.get("".concat(i,"/outside/pc/returnVisit/getListByFormBizId"),e)},N=function(e){return a.A.post("".concat(i,"/outside/pc/returnVisit/sendMsg"),e)},S=function(e){return a.A.post("/api/app/outside/print/template/list",e)},w=function(e){return a.A.post("/api/app/outside/print/template/create",e)},C=function(e){return a.A.post("/api/app/outside/print/template/update",e)},E=function(e){return a.A.get("/api/app/outside/print/template/getOne",e)},x=function(e){return a.A.post("/api/app/outside/print/template/delete",e)},k=function(e){return a.A.get("/api/paas/outside/pc/form/admin/print/setting/initPrintTemplate",e)},_=function(e){return a.A.post("/api/app/outside/print/template/preview",e)}}}]);