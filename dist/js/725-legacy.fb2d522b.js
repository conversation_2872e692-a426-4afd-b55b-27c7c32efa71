"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[725],{21975:function(e,t,i){var r=i(52275),n=i(26183),a=i(54232),o=i(58091),s=i(62361),l=(i(67880),i(86651)),u=i(84859),f=function(e){function t(){return(0,r.A)(this,t),(0,a.A)(this,t)}return(0,o.A)(t,e),(0,n.A)(t,null,[{key:"getBgColor",value:function(e,i){var r,n=t,a="";for(var o in n)if(r=n[o],r.value==e){a=r.bgColor||"";break}return a||console.warn("Caused: FlowStateEnum getBgColor got the value is empty"),a="number"===typeof i?"rgba(".concat(a,", ").concat(i,")"):"rgb(".concat(a,")"),a}},{key:"getColor",value:function(e){var i,r=t;for(var n in r)if(i=r[n],i.value==e)return i.color||"";return""}},{key:"getName",value:function(e){var i,r=t;for(var n in r)if(i=r[n],i.value==e)return i.name;return""}},{key:"getColorForFlow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{color:t.getColor(e),bgColor:t.getBgColor(e)}}}])}(l.A);(0,s.A)(f,"PROCESSING",{bgColor:"250, 174, 20",color:"#fff",name:u.Ay.t("common.base.processing"),value:1}),(0,s.A)(f,"FINISHED",{bgColor:"103, 194, 58",color:"#fff",name:u.Ay.t("common.base.usualStatus.finish"),value:2}),(0,s.A)(f,"OFFED",{bgColor:"140, 140, 140",color:"#fff",name:u.Ay.t("common.task.type.offed"),value:3}),(0,s.A)(f,"DRAFT",{bgColor:"140, 140, 140",color:"#fff",name:u.Ay.t("common.base.draft"),value:4}),(0,s.A)(f,"REFUSED",{bgColor:"245, 108, 108",color:"#fff",name:u.Ay.t("common.task.type.refused"),value:5}),t.A=f},81725:function(e,t,i){i.r(t),i.d(t,{default:function(){return N}});i(80793),i(36700),i(21633);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"print-box"},[t("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.loading,expression:"loading",modifiers:{fullscreen:!0,lock:!0}},{name:"print",rawName:"v-print",value:e.printOption,expression:"printOption"}],staticStyle:{"margin-bottom":"10px"},attrs:{id:"print-button",size:"small",icon:"iconfont icon-printer"}},[e._v(" "+e._s(e.$t("common.base.print"))+" ")]),t("div",{staticClass:"print-sheet",attrs:{id:"printElement"}},[t("el-card",{staticClass:"print-card"},[t("div",{staticClass:"print-time"},[e._v(e._s(e._f("fmt_datetime")(e.nowDate)))]),t("div",{staticClass:"company-name"},[e._v(e._s(e.title))]),t("div",{staticClass:"list materiel-box"},[e._l(e.tableListData,(function(i,r){return[t("div",{key:r,staticClass:"list-item"},[t("h1",[e._v(e._s(i.title))]),i.fields.length>0&&e.stockFormArr.includes(i.fields[0].formType)?[t("form-stock",{tag:"component",staticStyle:{"margin-top":"10px"},attrs:{field:i.fields[0],"print-fields":i.fields[0].subFormFieldList,"need-show-some-click-field":!1}})]:"表单字段"===i.title||i.title===e.$t("view.template.print.formFields")?t("el-row",{staticClass:"detail-part"},e._l(i.fields,(function(r){return t("el-col",{key:r.fieldName,staticClass:"detail-li drag-row",attrs:{span:6}},[t("div",{staticClass:"label"},[e._v(e._s(r.displayName))]),t("div",{staticClass:"detail-content"},["autograph"===r.formType?[t("img",{attrs:{src:e._f("fmt_form_field")(i.value[0][r.fieldName],r)}})]:"user"===r.formType?[e.isOpenData&&Array.isArray(i.value[0][r.fieldName])?t("div",e._l(i.value[0][r.fieldName],(function(n,a){return t("span",{key:n.userId},[t("open-data",{attrs:{type:"userName",openid:n.staffId}}),a+1!==i.value[0][r.fieldName].length?[e._v("，")]:e._e()],2)})),0):[e._v(" "+e._s(e._f("fmt_form_field")(i.value[0][r.fieldName],r))+" ")]]:"richText"===r.formType?[t("span",{staticClass:"rich-text",domProps:{innerHTML:e._s(i.value[0][r.fieldName])}})]:[e._v(" "+e._s(e._f("fmt_form_field")(i.value[0][r.fieldName],r))+" ")]],2)])})),1):t("el-table",{key:r,staticClass:"table",staticStyle:{width:"100%",color:"#262626"},attrs:{data:i.value,border:"","header-row-class-name":"tableHead","header-cell-style":e.tableHeaderColor}},e._l(i.fields,(function(i){return t("el-table-column",{key:i.fieldName,attrs:{label:i.displayName},scopedSlots:e._u([{key:"default",fn:function(r){var n;return["CURRENT_NODE"===i.fieldName?[[e._v(" "+e._s(e.flowPermiss.currentNodeName)+" ")]]:e._e(),"WFLOW_STATUS"===i.fieldName?[[e._v(" "+e._s(e.flowPermiss.customStatus||e.stateText)+" ")]]:"serialNumber"===i.fieldName?[t("span",{attrs:{href:"javascript:;"}},[e._v(e._s(r.row[i.fieldName]))])]:"related_task"===i.formType?[t("span",{attrs:{href:"javascript:;"}},[e._v(" "+e._s(e._f("fmt_form_field")(r.row[i.fieldName],i))+" ")])]:"CREATE_USER"==i.fieldName?[e.isOpenData&&r.row[i.fieldName].staffId?[t("open-data",{attrs:{type:"userName",openid:r.row[i.fieldName].staffId}})]:[e._v(" "+e._s((null===(n=r.row[i.fieldName])||void 0===n?void 0:n.displayName)||"")+" ")]]:"user"===i.formType?[e.isOpenData&&Array.isArray(r.row[i.fieldName])?t("div",e._l(r.row[i.fieldName],(function(n,a){return t("span",{key:n.userId},[t("open-data",{attrs:{type:"userName",openid:n.staffId}}),a+1===r.row[i.fieldName]?[e._v("，")]:e._e()],2)})),0):[e._v(" "+e._s(e._f("fmt_form_field")(r.row[i.fieldName],i))+" ")]]:"logistics"===i.formType?[t("div",{class:{link:e.hasQuota&&e.isLogisticsNo(i)}},[e._v(" "+e._s(e._f("fmt_form_field")(r.row[i.fieldName.split("_")[0]],i))+" ")])]:[e._v(" "+e._s(e._f("fmt_form_field")(r.row[i.fieldName],i))+" ")]]}}],null,!0)})})),1)],2)]}))],2)])],1)],1)},n=[],a=i(71357),o=i(35730),s=i(18885),l=i(42881),u=(i(67880),i(2286),i(44807),i(3923),i(35256),i(21484),i(13560),i(27408),i(76119),i(16961),i(54615),i(7354),i(89370),i(32807),i(75069),i(69594),i(13262),i(68735),i(74526)),f=i(87512),d=i(21975),c=i(92648),m=i(80906),p=i(92540),v=i(98216),h=["info","separator","attachment","relatedData","richText",f.E.JsCodeBlock],_=[f.E.LackStock,f.E.InStock,f.E.OutStock],F={name:"print",data:function(){return{templateName:this.$route.query.templateName,tableListData:[],value:[],flowPermiss:{},title:"",loading:!1,isFlowForm:0,canPrintFields:[]}},computed:{printOption:function(){return{id:"printElement",popTitle:"",extraCss:"",extraHead:""}},nowDate:function(){return(new Date).getTime()},templateId:function(){return this.$route.query.formId},nodeInstanceId:function(){return this.$route.query.nodeInstanceId},formContentId:function(){return this.$route.query.formContentId},processId:function(){return this.$route.query.processId},stateText:function(){return d.A.getName(this.flowPermiss.status)},stockFormArr:function(){return _||[]},stockSubFormListMap:function(){return{inStock:v.WK,outStock:v.ns,lackStock:v.c6}},isOpenData:function(){return this.$platform.isOpenData}},methods:{tableHeaderColor:function(e){var t=e.rowIndex;if(0===t)return"background-color: #fafafa;font-weight: 400; color: #262626;border-bottom: 1px solid #D9D9D9;"},fetchPrintTemplateSetting:function(){var e=this;return(0,l.A)((0,s.A)().mark((function t(){var i;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,u.wD)({formTemplateId:e.templateId});case 2:return i=t.sent,e.canPrintFields=i.data,t.abrupt("return",i.data);case 5:case"end":return t.stop()}}),t)})))()},fetchFlowBtnList:function(e){var t=this;u.y5({contentBizId:this.formContentId,nodeInstanceId:e},!0).then((function(e){e.success&&(t.flowPermiss=(null===e||void 0===e?void 0:e.data)||{})}))["catch"]((function(e){return console.error("err",e)}))},fetchFields:function(e){var t=this;return u.Tw({templateBizId:this.templateId,processorInstanceId:this.processId,nodeInstanceId:e}).then((function(i){var r=i.data,n=i.success,a=i.message;if(n){var s=r||{},l=s.isContainWf,u=s.paasFormFieldVOList,f=void 0===u?[]:u,d=s.systemFormFieldVOList,c=void 0===d?[]:d;return t.isFlowForm=1===l,t.isFlowForm&&t.fetchFlowBtnList(e),{fields:[].concat((0,o.A)(f),(0,o.A)(c)).map((function(e){return e.revisable=e.revisable||0,e.disabled=0===e.revisable,e})),systemFields:c,customFields:f}}t.$message.warning(a)}))["catch"]((function(e){return console.error("detail fetchFields error",e)}))},fetchFormData:function(){var e=this;return u.Ev({contentBizId:this.formContentId}).then((function(t){var i=t.data,r=void 0===i?{}:i;return e.templateName=r.templateName,e.isDelete=1==(null===r||void 0===r?void 0:r.isDelete),r}))["catch"]((function(e){return console.error("detail fetchFormData error",e)}))},initialize:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.nodeInstanceId,i=[this.fetchFields(t),this.fetchFormData(),this.fetchPrintTemplateSetting()];this.loading=!0,this.init=!1,Promise.all(i).then(function(){var t=(0,l.A)((0,s.A)().mark((function t(i){var r,n,l,u,d,v,h,F,y,b,g,N,w,k,C,I,A;return(0,s.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=i[0]||[],l=n.customFields,u=n.systemFields,n.fields,d=i[2]||[],v=i[1]||{},h=m.n_(l,(0,c.Sq)((null===v||void 0===v?void 0:v.paasFormValueList)||[])),F=(0,c.bI)(u,v),y=(0,a.A)((0,a.A)({},h),F),b=[],g=[],N=null,l.forEach((function(e,t){["outWarehouse","sunmiOutWarehouse"].includes(e.formType)&&(N=t,e.subFormFieldList.forEach((function(e){_.includes(e.formType)&&g.push(e)})))})),N&&(w=l).splice.apply(w,[N+1,0].concat(g)),l=l.map((function(t){return["backCheck","qualityResult"].includes(t.formType)&&(t.subFormFieldList=(0,p._)((null===t||void 0===t?void 0:t.subFormFieldList)||[],t.fieldName)),["outWarehouse","sunmiOutWarehouse"].includes(t.formType)&&(t.subFormFieldList=t.subFormFieldList.filter((function(e){return!_.includes(e.formType)}))),_.includes(t.formType)&&(t.subFormFieldList=e.stockSubFormListMap[t.formType]),t})),k=l.filter((function(t){return!(0,m.ZH)(t,y,l)&&e.checkCanPrintField(t,d)})),(0,o.A)(k).forEach((function(t){m.hL(t)?t.subFormFieldList.length>0&&("product"===(null===t||void 0===t?void 0:t.formType)&&(t.subFormFieldList=(0,p._)((null===t||void 0===t?void 0:t.subFormFieldList)||[],t.fieldName)),b.push({title:t.displayName,fields:t.subFormFieldList,value:y[t.fieldName]})):e.checkedPrintListIsExistField(t,b,y,e.$t("view.template.print.formFields"))})),C=u.filter((function(t){return e.checkCanPrintField(t,d)})),C.length>0&&b.unshift({title:e.$t("common.form.systemWidget"),fields:C,value:[y]}),console.log(b),e.tableListData=b,e.init=!0,I=h[f.E.SerialNumber]||"",A="".concat(e.templateName||"","-").concat(I),e.title=A,null!==(r=window)&&void 0!==r&&null!==(r=r.frameElement)&&void 0!==r&&r.id&&parent.setFrameTabTitle({id:window.frameElement.id,title:A||e.$t("common.base.print")}),e.loading=!1;case 24:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())["catch"]((function(t){e.loading=!1,console.log("template-detail-view initialize error: ",t)}))},checkedPrintListIsExistField:function(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",n=t.filter((function(e){return e.title===r})).pop(),a=function(){return t.push({title:r,fields:[e],value:[i]})};if(_.includes(e.formType))return e.subFormFieldList.length>0?t.push({title:e.displayName,fields:[e],value:[i]}):void 0;n?n.fields.push(e):a()},checkCanPrintField:function(e,t){if(m.hL(e)||_.includes(e.formType)){var i=t.find((function(t){return t.fieldName===e.fieldName}));return!(!i||e.isHidden)&&(e.subFormFieldList=e.subFormFieldList.filter((function(e){return i.subFormFieldList.find((function(t){return e.fieldName===t.fieldName}))&&"material"!==e.formType})),!0)}return!e.isHidden&&!(0,m.hL)(e)&&t.find((function(t){return t.fieldName===e.fieldName&&!h.includes(e.formType)}))},change2DimensionalArr:function(e){for(var t=e.length,i=4,r=t%4===0?t/4:Math.floor(t/4+1),n=[],a=0;a<r;a++){var o=e.slice(a*i,a*i+i);n.push(o)}return n}},created:function(){this.initialize()},mounted:function(){}},y=F,b=i(49100),g=(0,b.A)(y,r,n,!1,null,"2f654472",null),N=g.exports}}]);