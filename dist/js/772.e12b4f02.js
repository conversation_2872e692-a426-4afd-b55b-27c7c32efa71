"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[772],{93772:function(e,s,n){n.r(s),n.d(s,{default:function(){return i}});var r=function(){var e=this,s=e._self._c;return s("div",[s("div",[s("h1",[e._v(e._s(e.$t("common.base.pageIsNotExist"))+".")])]),s("div",[s("a",{attrs:{href:"#"},on:{click:function(s){return e.$router.go(-1)}}},[e._v(e._s(e.$t("common.base.backToThePrevPage")))])])])},t=[],u={name:"error_404"},a=u,o=n(49100),c=(0,o.A)(a,r,t,!1,null,null,null),i=c.exports}}]);