"use strict";(self["webpackChunkshb_pass"]=self["webpackChunkshb_pass"]||[]).push([[858],{90215:function(e,t,r){r.r(t),r.d(t,{default:function(){return F}});var n=function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticClass:"work-flow-page"},[e.isTaskMode?t("workflow-design",{ref:"workflowRef",attrs:{mode:e.mode}}):e._e(),e.isPreviewMode?t("workflow-preview"):e._e()],1)},o=[],a=(r(67880),r(3923),r(21484),r(7509),r(16961),r(89370),r(75069),r(13262),r(48649)),i=r(19708),s=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"workflow-wrap"},[e.newCanvas?[t("flow-process-chart",{attrs:{data:e.newFlowProcessList,"node-through-list":e.logList,readonly:"",vertical:""}})]:t("div",{attrs:{id:"container"}})],2)},c=[],d=r(18885),l=r(42881),u=r(35730),f=r(71357),w=(r(2286),r(80793),r(35256),r(54615),r(32807),r(55650),r(21633),r(69396)),p=(r(92935),r(51280)),m=(r(35564),r(44377)),h=r(81616),v=r(46687),g=r(74526),k=r(80602),A=r(19055),C=r(88445),I=r(50651),y=r(50152),b={name:"workflow-preview",props:{},data:function(){return{loading:!1,width:500,height:500,newCanvas:!1,newFlowProcessList:[],logList:[]}},computed:(0,f.A)((0,f.A)((0,f.A)({},(0,I.aH)(["user"])),(0,I.L8)(["flowDesignNodeCardStyle","flowCurrentVersion"])),{},{appId:function(){return this.$route.query.appId},formTemplateId:function(){return this.$route.query.formId},processId:function(){return this.$route.query.processId},formPaas:function(){return"paas"===this.$route.query.formType},nodeThroughIds:function(){return this.logList.map((function(e){return e.nodeTemplateId})).reverse()},formContentId:function(){return this.$route.query.formContentId},newCanvasGray:function(){var e;return null===(e=this.user.grayAuth)||void 0===e?void 0:e.includes(C.l.NewFlowDesign)},isNewNodeCardStyle:function(){return this.flowDesignNodeCardStyle===k.A.FULL_NODE_CARD}}),mounted:function(){this.width=document.documentElement.scrollWidth,this.height=document.documentElement.scrollHeight,this.initialize()},methods:(0,f.A)((0,f.A)((0,f.A)({},(0,I.i0)({fetchVersionList:"design/initFlowVersionList"})),(0,I.PY)({updateNodeCardStyle:"design/".concat(y.Wd)})),{},{fetchProcessLog:function(){var e=this;if(this.processId)return g.O0({contentBizId:this.formContentId},!0).then((function(t){if(t.success){var r=t.data||{},n=r.createUser,o=r.createTime,a=r.nodeLogList,i=void 0===a?[]:a;i.forEach((function(e){if(e.collapse=!1,e.nodeType==k.A.START_NODE){var t=A.A.SUBMIT,r=t.name,a=t.code;e.operate=r,e.operateCode=a,e.userIds=[n],e.taskLogVOList=[{approveResult:r,completeTime:o,user:n}]}}));var s=i.filter((function(e){return e.isCurrent})),c=i.filter((function(e){return!e.isCurrent}));s.length>1&&(i=[{isCurrent:!0,isMulti:!0}].concat((0,u.A)(c))),e.logList=i}}))["catch"]((function(e){return console.error(e)}))},initialize:function(){var e=this;return(0,l.A)((0,d.A)().mark((function t(){var r,n,o,a,i,s,c,l,f,A,C,I,y,b,L,P,N,S,T,x,_,E,O,D,F,V;return(0,d.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.appId,o=e.formTemplateId,e.loading=!0,!e.formPaas){t.next=28;break}return t.next=6,g.Tw({templateBizId:e.formTemplateId});case 6:if(a=t.sent,!a.success){t.next=27;break}return i=a.data||{},s=i.paasFormFieldVOList,c=void 0===s?[]:s,l=i.systemFormFieldVOList,f=void 0===l?[]:l,A=[].concat((0,u.A)(c),(0,u.A)(f)),t.next=12,w.getProcessByProcessId({processorInstanceId:e.processId});case 12:if(C=t.sent,C.success){t.next=15;break}return t.abrupt("return",e.$platform.toast(C.message,"error"));case 15:if(I=C.data||{},y=I.attribute,b=I.newCanvas,L=void 0!==b&&b,e.newCanvas=L,P=JSON.parse(y||"{}"),!L||!e.newCanvasGray){t.next=23;break}return t.next=21,e.fetchProcessLog();case 21:e.newFlowProcessList=(0,v.i5)(P.cells,A),e.$nextTick((function(){document.getElementsByClassName("workflow-wrap")[0].style.background="#fff"}));case 23:N=window.location.origin,S={action:"PaaS.frame.newFlowWork.loaded"},window.parent.postMessage(S,N),e.loading=!1;case 27:return t.abrupt("return");case 28:return t.next=30,e.fetchVersionList();case 30:return t.next=32,w.getProcess({appId:n,formTemplateId:o,workflowTemplateId:null===(r=e.currentVersion)||void 0===r?void 0:r.bizId});case 32:if(T=t.sent,T.success){t.next=35;break}return t.abrupt("return",e.$platform.toast(T.message,"error"));case 35:x=T.data||{},_=x.attribute,E=x.nodeStyle,O=void 0===E?k.A.NORMAL_NODE_CARD:E,D=JSON.parse(_||"{}"),e.updateNodeCardStyle(O),F=D.cells.map((function(t){return"edge"==t.shape?new m.Ay(t):new h.A(t,e.isNewNodeCardStyle)})),V=new p.ke({container:document.getElementById("container"),width:e.width,height:e.height,scroller:{enabled:!0,pannable:!0},interacting:function(){return{nodeMovable:!1}},mousewheel:{enabled:!0,modifiers:["ctrl","meta"]},connecting:{anchor:"center",connectionPoint:"anchor"}}),V.fromJSON(F||[]),V.centerContent(),e.loading=!1,t.next=49;break;case 45:t.prev=45,t.t0=t["catch"](0),e.loading=!1,console.error("workflow dialog initialize error: ",t.t0);case 49:case"end":return t.stop()}}),t,null,[[0,45]])})))()}})},L=b,P=r(49100),N=(0,P.A)(L,s,c,!1,null,"798b3bb8",null),S=N.exports,T=r(44309),x=r(84859),_=r(14389),E=(0,a.defineComponent)({name:"work-flow-page",setup:function(e){var t=(0,a.ref)(null);function r(){try{var e,r=[];return null===(e=t.value)||void 0===e||null===(e=e.graph)||void 0===e||null===(e=e.toJSON())||void 0===e||null===(e=e.cells)||void 0===e||e.forEach((function(e){var t=(null===e||void 0===e?void 0:e.data)||{},n=t.name,o=t.attribute;if(e.shape===k.A.APPROVE_NODE){var a=(null===o||void 0===o?void 0:o.candidate)||[];a&&a.length||r.push("".concat(n).concat(x.Ay.t("view.designer.workFlow.tip28")))}})),r}catch(n){console.error(n)}}window.addEventListener("message",(function(e){var o=e.data,a=o.action,i=o.compId;if("shb.frame.getPaaSWorkflow"===a){var s=r(),c={flowData:t.value.packToJson(),currentVersion:_.A.getters.flowCurrentVersion,nodeStyle:_.A.getters.flowDesignNodeCardStyle};n(c,i,s)}}));var n=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=window.location.origin,o={action:"PaaS.frame.returnPaaSWorkflow",data:e,compId:t,errMsg:r};window.parent.postMessage(o,n)};return{workflowRef:t}},computed:{mode:function(){return this.$route.query.mode||T.A.TASK},isTaskMode:function(){return this.mode===T.A.TASK},isPreviewMode:function(){return this.mode===T.A.PREVIEW}},components:{WorkflowDesign:i["default"],WorkflowPreview:S}}),O=E,D=(0,P.A)(O,n,o,!1,null,"3f5b0d08",null),F=D.exports}}]);