#

##  form-builder表单字段编译器
 form-builder编辑相关

<!-- ### 文字输入demo -->

<!-- 没有边框和背景色的按钮。 -->

::: demo Progress 组件设置percentage属性即可，表示进度条对应的百分比，必填，必须在 0-100。通过 format 属性来指定进度条文字内容。

``` html {2}
<template>
  <div>
      <publink-paas-form-builder></publink-paas-form-builder>
  </div>
</template>
<script>
export default {
  data() {
    return {
      fields: [{
            isSystem: 1,
            fieldName: 'CURRENT_NODE',
            displayName: '当前节点',
            formType: 'text',
            show: true,
            isSearch: 0
      }],
      value: {}
    }
  },
  methods: {
    update() {

    }
  }
}
</script>
```