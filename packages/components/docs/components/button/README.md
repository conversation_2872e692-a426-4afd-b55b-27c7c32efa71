# 
##  Button按钮
常用的操作按钮。

### 文字按钮

没有边框和背景色的按钮。

::: demo Progress 组件设置percentage属性即可，表示进度条对应的百分比，必填，必须在 0-100。通过 format 属性来指定进度条文字内容。

``` html {2}
<template>
  <div>
      <span>sdfsdf</span>
      <button @click="click">按钮</button>
      <!-- <publink-paas-button>sdfsdfds</publink-paas-button> -->
      <plink-paas-timeout-setting-modal 
        ref="timeoutSettingModal" 
        template-name="templateName" 
        @timeoutSettingSubmit="timeoutSettingSubmit" 
        :showUserNativeComp="false" 
        @handleChooseActionTo="handleChooseActionTo"/>
      <!-- <form-builder
            ref="form"
            mode="base"
            :value="value"
            :fields="fields"
            :is-edit-state="true"
            @update="update"
          >
    </form-builder> -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      fields: [{
            isSystem: 1,
            fieldName: 'CURRENT_NODE',
            displayName: '当前节点',
            formType: 'text',
            show: true,
            isSearch: 0
      }],
      value: {}
    }
  },
  provide() {
    return {
      flowData: {
        fields: []
      }
    };
  },
  methods: {
    handleChooseActionTo() {
      console.log('chhose')
    },
    update() {

    },
    timeoutSettingSubmit() {
      
    },
    click() {
      console.log(this.$refs.timeoutSettingModal.show({}))
    }
  },
  mounted() {

  }
}
</script>
```
