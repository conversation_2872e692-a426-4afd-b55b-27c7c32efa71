
# 超时设置弹窗组件
## 相关PaaS中流程设计中相关有超时设置的弹窗表单


### 代码案例

#

::: demo

``` html 
<template>
  <div class="box">
      <el-button @click="click">点击打开弹窗</el-button>
      <publink-paas-timeout-setting-modal 
        ref="timeoutSettingModal" 
        @timeoutSettingSubmit="timeoutSettingSubmit" 
        @handleChooseActionTo="handleChooseActionTo"/>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  provide() {
    return {
      flowData: {
        fields: []
      }
    };
  },
  methods: {
    handleChooseActionTo() {
      console.log('chhose')
    },
    timeoutSettingSubmit() {
      
    },
    click() {
      this.$refs.timeoutSettingModal.show({})
    }
  }
}
</script>
```
:::




### Modal props

<div class="table">

|  参数   | 说明  | 类型 | 可选值 | 默认值 |
|  ------------  | ------------   |  ------------   | ----  | -- |
| `title`  | 弹窗的顶部名称 | `string`  | -  | 超时规则设置 |
| `fetchGrayFun`  | 相关灰度控制的接口方法 | `Function <Promise>`  | -  | - |
| `chooseUserComponent`  | 选人控件(因为scss中渲染控件与paas中不一样所以自定义传入,此props仅供paas项目使用) | `Function`  | -  | -- |
| `chooseNodeShow`  | 是否显示可选择节点的select | `Boolean ` | -  | false |

</div>

### Modal Event

|  参数   | 说明  | 参数 |
|  ---    | ---  | ----| 
| `timeoutSettingSubmit`  | 当点击底部的保存按钮触发的事件 | formValue  |
| `handleChooseActionTo`  | 当点击人员选择框触发的事件| -  |

### Modal Methods

|  参数   | 说明  | 参数 |
|  ------------  | ------------   |  ------------   | ----  |
| `show`  | 显示弹窗的方法 | 传入一个对象赋值表单数据,默认值为 {}  数据类型:formValue  | 





### formValue

| key | 类型 | 说明 |
|- |- | - |
| ruleName | string | 规则名称
| nodeId | string | 节点的id
| startTimeType | number | 超时起算时间 0 从进入该节点就开始起算 1 从表单中某个日期开始起算
| overTimeLengthArr | array<{overTimeLength: '', overTimeUnit:'HOUR'}> | 超时时长  overTimeLength: string 时长 overTimeUnit: string 时长单位
| startTimeFieldName | string | 起算时间的fieldName 为表单设计器中的field
| triggerAction | string | 超时后处理方式, 自动转交: autoForward、自动提醒: autoRemind、自动完成： autoComplete
| cycleCount | number | 提醒次数 0 仅提醒1次 1 重复提醒 2 依次提醒
| cycleStep | string | 提醒的步长（当选择重复提醒时候）
| cycleStepUnit | string | 提醒的单位（当选择重复提醒时候）默认值 HOUR
| actionTo | array | 转交人员 人员控件的选值

### Time Unit

| key | 说明 |
|- |- |
| HOUR | 时 | 
| MINUTE | 分 | 
| DAY | 天 | 
| WEEK | 周 | 