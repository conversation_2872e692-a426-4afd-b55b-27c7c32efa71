
# 流程图组件
## 相关PaaS中新版流程图组件


### 代码案例

#
::: demo

``` html 
<template>
  <div class="box">
    123123123
    <flow-chart
      v-model="data"
      :readonly="false"
      :beforeDeleteNode="beforeDeleteNode"
      :nodeTypeList="nodeTypeList"
      :vertical="true"
      @node-content-click="onNodeContentClick"
    />
  </div>
</template>
<script>
const data = [
    { id: 'startEvent', type: 'start', title: '开始' },
    {
        id: '随机id1',
        type: 'normal',
        title: '申请人',
        content: '员工',
        configData: {},
        nodeList: []
    },
    {
        id: '随机id2',
        type: 'normal',
        title: '审批人',
        content: '主管',
        configData: {},
        nodeList: []
    },
    {
        id: '随机id3',
        type: 'condition',
        title: '条件分支',
        children: [
            {
                id: '随机id3-1',
                title: '通过',
                content: '理由充分',
                type: 'normal',
                configData: {},
                nodeList: [
                    {
                        id: '随机id3-1-1',
                        type: 'normal',
                        title: '审批人',
                        content: '上级主管',
                        configData: {},
                        nodeList: [
                            {
                                id: '随机id3-1-1-1',
                                type: 'condition',
                                title: '条件分支',
                                children: [
                                    {
                                        id: '随机id3-1-1-1-1',
                                        title: '通过',
                                        content: '理由充分',
                                        type: 'normal',
                                        configData: {},
                                        nodeList: []
                                    },
                                    {
                                        id: '随机id3-1-1-1-2',
                                        type: 'normal',
                                        title: '驳回',
                                        content: '理由不充分',
                                        configData: {},
                                        nodeList: []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: '随机id3-2',
                type: 'normal',
                title: '指派',
                content: 'HR',
                configData: {},
                nodeList: [
                    {
                        id: '随机id3-2-1',
                        type: 'condition',
                        title: '条件分支',
                        children: [
                            {
                                id: '随机id3-2-1-1',
                                title: '通过',
                                content: '理由充分',
                                type: 'normal',
                                configData: {},
                                nodeList: []
                            },
                            {
                                id: '随机id3-2-1-2',
                                type: 'normal',
                                title: '驳回',
                                content: '理由不充分',
                                configData: {},
                                nodeList: []
                            }
                        ]
                    }
                ]
            },
            {
                id: '随机id3-3',
                type: 'normal',
                title: '驳回',
                content: '理由不充分',
                configData: {},
                nodeList: []
            }
        ]
    },
    { id: 'endEvent', type: 'end', title: '结束' }
]
export default {
  data() {
    return {
      data: data,
      showNodeEdit: false,
      editNodeData: null,
      isEditingTitle: false,
      nodeTypeList: [
        {
          name: '普通节点',
          list: [
            {
              type: 'normal',
              name: '普通节点'
            }
          ]
        },
        {
          name: '分支节点',
          list: [
            {
              type: 'condition',
              name: '条件分支'
            }
          ]
        },
        {
          name: '并行分支',
          list: [
            {
              type: 'condition',
              name: '条件分支'
            }
          ]
        }
      ]
    }
  },
  methods: {
    onNodeContentClick() {
    }
  }
}
</script>
```
:::




### props
