
# 人员选择组件
## 相关PaaS中人员选择的组件


### 代码案例

#
::: demo

``` html 
<template>
  <div class="box">
     <el-button @click="click">点击打开弹窗</el-button>
     <base-modal
      class="paas-choose-user-modal"
      title="转交指定人员选择"
      :show.sync="visible"
      width="640px"
    >
      <publink-paas-config-contact
        v-if="visible"
        ref="paasConfigContact"
        title="转交指定人员"
        :value="paasCandidates"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handlePaasContactChooseUser">确 定</el-button>
      </div>
    </base-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      paasCandidates: []
    }
  },
  provide() {
    return {
      flowData: {
        fields: []
      }
    };
  },
  methods: {
    handlePaasContactChooseUser() {
      // 这里通过获取组件实例中测checker获取选中后的值
      console.log('submit')
    },
    click() {
      this.visible = true
    }
  }
}
</script>
```
:::




### props

<div class="table">

|  参数   | 说明  | 类型 | 可选值 | 默认值 |
|  ------------  | ------------   |  ------------   | ----  | -- |
| `flowApi`  | 当前弹窗中需要请求的api对象可参考paas中的`flowApi.ts` 必传 | `Object`  | -  | `{}` |
| `value`  | 默认值 | `Array`  | -  | `[]` |
| `title`  | 标题暂时无用 | `string`  | -  | -- |
|`showDynamic`| 是否显示动态获取Tab | `Boolean ` | - | `false` |
|`fields`| 表单设计器的的fields列表 | `Array ` | - | `[]` |
|`showTagClosable`| 顶部的选中的列表tag是否支持close关闭取消选择 | `Boolean ` | - | `true` |
|`disableValues`| 树的列表checkbox不可勾选的列表 | `Array ` | - | `[]` |
|`showCustomer`| 动态获取是否显示客户栏（依赖于showDynamic） | `Boolean ` | - | `false` |
|`onlyShowCustomer`| 动态获取栏是否单单显示客户（依赖于showDynamic） | `Boolean ` | - | `false` |
|`isDynamicFlowDesignChoose`| 动态获取栏动态获取节点负责人是否支持节负责人选择（依赖于showDynamic） | `Boolean ` | - | `false` |
|`isNeedTagSlot`| 顶部的tag是支持slot（为了兼容fe中slot问题 vue版本问题） | `Boolean ` | - | `true` |
| `showTabSetting`  | 构建的tab的是否显示setting | `Object`  | -  | `{}:TabSetting` |

</div>

### TabSetting

| key | 类型 | 说明 |
|- |- | - |
| department | object<{ show: true // 是否显示, isMulti: true // 是否多选}> | 部门
| member | object<{ show: true // 是否显示, isMulti: true // 是否多选}>  | 成员
| role | object<{ show: true // 是否显示, isMulti: true // 是否多选}>  | 角色
| dynamic | object<{ show: true // 是否显示, isMulti: true // 是否多选}>  | 动态获取
