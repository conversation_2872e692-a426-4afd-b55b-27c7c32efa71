
const path = require('path');


function genSassResourceLoader() {
  return {
    loader: 'sass-resources-loader',
    options: {
      resources: [
        path.resolve('../../src/assets/scss/_variables.scss'),
        path.resolve('../../src/assets/scss/_mixins.scss')
      ]
    }
  };
}

module.exports = {
    themeConfig: {
        search: false,
        lastUpdated: false,
        smoothScroll: false,
        nav: [
          { text: '主页', link: '/' },
          { text: '组件', link: '/components/' },
          { text: 'External', link: 'https://google.com' },
        ],
        sidebar: [
          {
            title: '',   // 必要的
            collapsable: false, // 可选的, 默认值是 true,
            children: [
            {
              title: '主体组件',   // 必要的
              collapsable: false, // 可选的, 默认值是 true,
              sidebarDepth: 0,    // 可选的, 默认值是 1
              children: [
                {
                  title: '超时设置弹窗组件',
                  path: '/components/timeout-setting/'
                },
                {
                  title: '选人控件',
                  path: '/components/config-contact/'
                },
                {
                  title: '流程图组件',
                  path: '/components/flow-process-chart/'
                },
                // {
                //   title: '按钮',
                //   path: '/components/button/'
                // }
                // {
                //   title: '表单编译器',
                //   path: '/components/form-builder/'
                // }
              ]
            }
            ]
          },
          {
            title: '分组2',
            children: [ /* ... */ ],
            collapsable: false,
            initialOpenGroupIndex: -1 // 可选的, 默认值是 0
          },

        ]
      },
      configureWebpack: {
        resolve: {
            alias: {
              '@styles': path.resolve(__dirname, './styles'),
              '@src':  path.resolve('../../src'),
              '@hooks': path.resolve('../../hooks'),
              '@': path.resolve('../../src'),
              '@service': path.resolve('../../service'),
              '@packages': path.resolve('../../packages'),
              '@model': path.resolve('../../src/model'),
              '@lang': path.resolve('../../node_modules/pub-bbx-global/lang/dist'),
              '@pageType': path.resolve('../../node_modules/pub-bbx-global/pageType/dist'),
              'pub-bbx-global': path.resolve('../../node_modules/pub-bbx-global'),
              'pub-bbx-utils': path.resolve('../../node_modules/pub-bbx-utils/lib/v2.cjs'),
            },
            extensions: ['.js', '.ts', '.tsx', '.vue', '.json']
        },
        module: {
          rules: [
            {
              test: /\.scss$/,
              use: [genSassResourceLoader()]
            },
            {
              test: /\.tsx?$/,
              exclude: /node_modules/,
              use: [
                  'cache-loader',
                  {
                      loader: 'babel-loader',
                      options: {
                          babelrc: false,
                          configFile: false,
                          presets: [
                              '@babel/preset-env', // 可以识别es6语法
                              '@vue/babel-preset-jsx' // 解析jsx语法
                          ]
                      }
                  },
                  {
                      loader: 'ts-loader',
                      options: {
                          appendTsxSuffixTo: [/\.vue$/, /\.md$/]
                      }
                  }
              ]
            }
          ]
        }
      },
      plugins: [
        'vue-demo',
        'vuepress-plugin-typescript',
        {
          tsLoaderOptions: {
            // ts-loader 的所有配置项
          },
        }]
}
