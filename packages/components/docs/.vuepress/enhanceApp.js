import PaasComponets from '../../src/index'
// import formComponents from '@src/component/index'

// import BaseComponents from '@src/component/common'

// import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '@src/assets/scss/index.scss';
import '@src/component/element-ui/element-variables.scss';


// import '@src/component/element-ui/_variables.scss';
// import '@src/component/element-ui/index.scss';

export default ({
    Vue, // VuePress 正在使用的 Vue 构造函数
    options, // 附加到根实例的一些选项
    router, // 当前应用的路由实例
    siteData, // 站点元数据
    isServer // 当前应用配置是处于 服务端渲染 或 客户端
  }) => {
    if(!isServer) {
      // const libArr =  ['../../src/index', 'element-ui', '@src/component/index']
      import ('../../src/index').then(rs => Vue.use(rs.default)).catch(err=> console.error(err))
      import ('element-ui').then(rs => Vue.use(rs.default)).catch(err=> console.error(err))
      import ('@src/component/index').then(rs => Vue.use(rs.default)).catch(err=> console.error(err))


      // Vue.use(PaasComponets)
      // Vue.use(ElementUI)
      // // BaseComponents.forEach(component => Vue.use(component))
      // Vue.use(formComponents)


      // 无法使用
      // for(let i = 0; i < libArr.length; i++) {
      //   import (libArr[i]).then(rs => console.log(rs)).catch(err=> console.error(err))
      // }
   
    }
}