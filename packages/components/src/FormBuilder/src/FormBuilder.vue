<template>
  <div class="p-form-builder">
    <form-builder
      ref="form"
      mode="base"
      :value="value"
      :fields="fields"
      :is-edit-state="true"
      @update="update"
    >
    </form-builder>
  </div>
</template>

<script>
export default {
  name: 'publink-paas-form-builder',
  data() {
    return {
      fields: [{
        isSystem: 1,
        fieldName: 'CURRENT_NODE',
        displayName: '当前节点',
        formType: 'text',
        show: true,
        isSearch: 0
      }],
      value: {}
    };
  },
  methods: {
    update() {

    }
  }
};
</script>

<style lang="scss" scoped>

</style>