<template>
    <config-contact
      ref="configContact"
      v-bind="$attrs"
      v-on="$listeners"
    ></config-contact>
</template>

<script>
import ConfigContact from '@src/view/designer/workflow/components/ConfigPanel/ConfigContact/index.vue';
export default {
  name: 'publink-paas-config-contact',
  components: {
    ConfigContact
  },
  data() {
    return {}
  },
  methods: {
    update() {

    }
  }
};
</script>

<style lang="scss" scoped>

</style>
