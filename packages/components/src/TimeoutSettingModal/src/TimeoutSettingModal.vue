<template>
  <timeout-setting-modal
    ref="timeoutSettingModal"
    v-bind="$attrs"
    v-on="$listeners"/>
</template>

<script>
import TimeoutSetting from '@src/view/designer/workflow/components/ConfigPanel/ConfigNode/AdvancedSetting/components/TimeoutSetting/index';
export default {
  name: 'publink-paas-timeout-setting-modal',
  components: {
    [TimeoutSetting.name]: TimeoutSetting
  },
  methods: {
    show(value) {
      this.$refs.timeoutSettingModal.handleShowModal(value);
    },
    hide() {
      this.$refs.timeoutSettingModal.handleHideModal();
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
