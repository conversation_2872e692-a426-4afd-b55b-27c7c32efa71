import { v4 as uuidv4 } from 'uuid'
export const store = {
  vertical: true,
  nodeThroughIds: [],
  currentNodeIds: []
}

export const defaultNodeList = [
  { id: uuidv4(), type: 'start-node', title: '发起节点' },
  { id: uuidv4(), type: 'end-node', title: '结束' }
]

export const defaultNodeTypeList = [
  {
    name: '普通节点',
    list: [
      {
        type: 'normal',
        name: '普通节点'
      }
    ]
  },
  {
    name: '分支节点',
    list: [
      {
        type: 'condition',
        name: '条件分支'
      }
    ]
  }
]
