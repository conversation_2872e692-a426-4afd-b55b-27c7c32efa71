import { v4 as uuidv4 } from 'uuid'
import { store } from './constant';

export const generateNode = (
  type,
  title = '普通节点',
  content = '节点内容'
) => {
  const createUid = store.customCreateNodeId || uuidv4
  switch (type) {
    case 'normal':
      return {
        id: createUid(),
        type: 'normal',
        title,
        content,
        configData: {},
        nodeList: []
      }
    case 'condition':
      return {
        id: createUid(),
        type: 'condition',
        title: '条件分支',
        children: [
          {
            id: createUid(),
            title: '条件1',
            content: '条件1的内容',
            type: 'normal',
            configData: {},
            nodeList: []
          },
          {
            id: createUid(),
            type: 'normal',
            title: '条件2',
            content: '条件2的内容',
            configData: {},
            nodeList: []
          }
        ]
      }
    default:
      break
  }
}


export const textIsBeyond = (e, direction = 'height') => {
  const ev = window.event || e	// 浏览器兼容，最好写一下
  const textRange = (el) => {
    const textContent = el
    const targetW = textContent.getBoundingClientRect()[direction]
    const range = document.createRange()
    range.setStart(textContent, 0)
    range.setEnd(textContent, textContent.childNodes.length)
    const rangeWidth = range.getBoundingClientRect()[direction]
    return rangeWidth > targetW
  }
  return !textRange(ev.target)	// target可以保证当前划过的dom节点一直保持是:el-tooltip__trigger
}


