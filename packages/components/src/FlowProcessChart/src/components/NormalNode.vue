<template>
  <div class="flow-process-node__normal-container" :class="{ vertical: vertical }">
    <div class="flow-process-node__normal-wrap">
      <div
        class="flow-process-node__normal-content-wrap"
        @mouseenter.stop="onContentMouseenter"
        @mouseleave.stop="onContentMouseleave"
        @click.stop="onContentClick"
      >
        <flow-process-node-content v-if="isConditionOrParallelNode"  :data="data" :nextData="nextData"></flow-process-node-content>
        <flow-process-normal-node-content v-else :data="data" :nextData="nextData"></flow-process-normal-node-content>
        <flow-process-delete-node
          v-if="showDeleteBtn && !readonly"
          :class="[data.type.includes('condition') ? 'condition-del': null]"
          @click="onDeleteNode"
        ></flow-process-delete-node>
      </div>
      <flow-process-arrow-line :data="data"></flow-process-arrow-line>
      <flow-process-add-node
        v-if="!isConditionOrParallelNode"
        :nodeList="nodeList"
        :nodeData="data"
        :nextData="nextData"
        :btnType="isMouseEnter ? 'dot' : ''"
      ></flow-process-add-node>
    </div>
    <flow-process-node
      v-for="(node, index) in data.nodeList || []"
      :key="node.id"
      :nodeList="data.nodeList"
      :data="node"
      :nextData="node[index + 1]"
      :isMouseEnter="isMouseEnter"
      :belong-condition-node-data="belongConditionNodeData"
      :children-list="childrenList"
    ></flow-process-node>
  </div>
</template>

<script>
import ArrowLine from './ArrowLine.vue'
import AddNode from './AddNode.vue'
import emitter from '../emit'
import { store } from '../constant'
import FlowProcessNormalNodeContent from './NormalNodeContent.vue';

export default {
  name: 'flow-process-normal-node',
  components: {
    [FlowProcessNormalNodeContent.name]: FlowProcessNormalNodeContent,
    [ArrowLine.name]: ArrowLine,
    [AddNode.name]: AddNode
  },
  props: {
    nodeList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    childrenList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    data: {
      type: Object,
      default: null
    },
    nextData: {
      type: Object,
      default: null
    },
    belongConditionNodeData: {
      type: Object,
      default: null
    },
    isMouseEnter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      readonly: store.readonly,
      vertical: store.vertical,
      showDeleteBtn: false
    }
  },
  computed: {
    isConditionOrParallelNode() {
      return ['condition-custom', 'parallel-custom'].includes(this.data.type)
    }
  },
  methods: {
    onContentMouseenter() {
      this.showDeleteBtn = true
    },

    onContentMouseleave() {
      this.showDeleteBtn = false
    },

    onContentClick() {
      emitter.emit('node-content-click', this.data, this.nodeList)
    },

    onDeleteNode() {
      emitter.emit(
        'delete-node-click',
        this.nodeList,
        this.childrenList,
        this.belongConditionNodeData,
        this.data
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__normal-container {
  position: relative;
  display: flex;
  flex-shrink: 0;
  align-items: center;

  &.vertical {
    flex-direction: column;

    .flow-process-node__normal-wrap {
      flex-direction: column;
    }
  }

  .flow-process-node__normal-wrap {
    position: relative;
    display: flex;
    align-items: center;
  }
}
</style>
