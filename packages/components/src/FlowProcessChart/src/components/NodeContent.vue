<template>
  <div :class="['flow-process-node__normal-content', readonly ? 'cursor-default' : null]" >
    <div class="flow-process-node__normal-content-title">
      {{ data.title || '' }}
    </div>
    <div class="flow-process-node__normal-content-data">
      <template v-if="data.content">
<!--        <div class="flow-process-node__normal-content-data__text">{{ data.content }}</div>-->
        <el-tooltip :content="data.content" :disabled="disabled" popper-class="flow-process-tooltip">
         <span class="flow-process-node__normal-content-data__text" @mouseenter="handleMouse($event)">
            {{ data.content}}
         </span>
        </el-tooltip>
      </template>
      <template v-else>
        <div class="flow-process-node__normal-content-data__text placeholder">{{ data.placeholder ||  '' }}</div>
      </template>
      <img v-if="!isParallelNode && !readonly" class="flow-process-node__normal-content-data__icon" src="../assets/svg/arrow.svg" alt="" />
    </div>
  </div>
</template>

<script>

import { store } from '../constant'
import { textIsBeyond } from "../utils";
export default {
  name: 'flow-process-node-content',
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      readonly: store.readonly,
      disabled: false
    }
  },
  computed: {
    isParallelNode() {
      return ['parallel-custom'].includes(this.data.type)
    }
  },
  methods: {
    handleMouse(e)  {
      this.disabled = textIsBeyond(e);
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__normal-content {
  width: 220px;
  min-height: 70px;
  padding: 5px 10px 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  box-shadow: 0 1px 4px 0 rgba(10, 30, 65, 0.16);
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;

  &-title {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    font-size: 14px;
    text-align: left;
    //font-weight: 600;
    word-break: break-all;
    min-height: 20px;
    width: 220px;
    @include text-ellipsis();
    color: $color-primary;
  }

  &-data {
    display: flex;
    min-height: 32px;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    margin-top: 10px;
    //background: rgba(0, 0, 0, 0.03);
    border-radius: 4px;

    &__text {
      color: #262626;
      font-size: 14px;
      //line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      &.placeholder{
        color: #8C8C8C;
      }
    }

    &__icon {
      width: 16px;
    }
  }
}
</style>
