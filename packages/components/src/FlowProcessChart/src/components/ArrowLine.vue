<template>
  <div
    class="flow-process-arrow__line"
    :class="{ 'show-arrow': showArrow, vertical: vertical }"
  ></div>
</template>

<script>
import { store } from '../constant'

export default {
  name: 'flow-process-arrow-line',
  props: {
    showArrow: {
      type: Boolean,
      default: true
    },
    data: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      vertical: store.vertical,
      nodeThroughIds: store.nodeThroughIds
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-arrow__line {
  position: relative;
  width: 65px;
  user-select: none;

  &.vertical {
    width: auto;
    height: 75px;

    &::before {
      width: 2px;
      height: 100%;
      transform: translateX(-50%);
    }

    &.show-arrow {
      &::after {
        right: auto;
        bottom: 0;
        top: auto;
        border-top: 10px solid #dedede;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: none;
        transform: translateX(-50%);
      }
    }
    &.show-arrow-active{
      &::after {
        border-top: 10px solid $color-main;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: none;
      }
    }
  }

  &::before {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    transform: translateY(-50%);
    height: 2px;
    width: 100%;
    background-color: #dedede;
    content: '';
  }

  &.show-arrow {
    &::after {
      position: absolute;
      width: 0;
      height: 0;
      border-left: 10px solid #dedede;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      content: '';
      right: 0;
      top: 0;
      transform: translateY(-50%);
    }
  }
  &.show-arrow-active{
    &::after {
      border-left: 10px solid $color-main;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }
  &.active-line{
    &:before{
      background-color: $color-main;
    }
  }
}
</style>
