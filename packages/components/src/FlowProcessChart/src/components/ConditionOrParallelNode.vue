<template>
  <div class="flow-process-condition__node-container" :class="{ vertical: vertical }">
    <div
      class="flow-process-condition__add-btn"
      v-if="!readonly"
      @click="onAddConditionBranchClick"
    >
      <template v-if="data.type === 'parallel'">
      添加分支
      </template>
      <template v-else>
        添加条件
      </template>
    </div>
    <div class="flow-process-condition__node-list">
      <div
        class="flow-process-condition__node-item"
        v-for="node in data.children"
        :key="node.id"
      >
        <!-- 左侧的竖线 -->
        <div
          class="flow-process-condition__node-item-line first-line"
        ></div>
        <!-- 右侧的竖线 -->
        <div
          class="flow-process-condition__node-item-line last-line"
        ></div>
        <!-- 连接竖线和节点的水平线 -->
        <div class="flow-process-condition__node-link-line"></div>
        <div class="flow-process-condition__node-item-wrap">
          <flow-process-node
            :nodeList="null"
            :childrenList="data.children"
            :data="node"
            :nextData="nextData"
            :belongConditionNodeData="data"
            :isMouseEnter="isMouseEnter"
          ></flow-process-node>
          <!-- 连接较短分支和分支整体右侧的水平线 -->
          <div class="flow-process-condition__link-cross-line"></div>
        </div>
      </div>
    </div>
    <flow-process-arrow-line :data="data"></flow-process-arrow-line>
    <flow-process-add-node
      :nodeList="nodeList"
      :nodeData="data"
      :nextData="nextData"
      :btnType="isMouseEnter ? 'dot' : ''"
    ></flow-process-add-node>
  </div>
</template>

<script>
import ArrowLine from './ArrowLine.vue'
import AddNode from './AddNode.vue'
import emitter from '../emit'
import { store } from '../constant'

export default {
  name: 'flow-process-condition-or-parallel-node',
  components: {
    [ArrowLine.name]: ArrowLine,
    [AddNode.name]: AddNode
  },
  props: {
    nodeList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    data: {
      type: Object,
      default: null
    },
    nextData: {
      type: Object,
      default: null
    },
    isMouseEnter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      readonly: store.readonly,
      vertical: store.vertical
    }
  },
  methods: {
    onAddConditionBranchClick() {
      emitter.emit('add-condition-branch-click', this.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-condition__node-container {
  position: relative;
  display: flex;
  align-items: center;

  &.vertical {
    flex-direction: column;

    .flow-process-condition__add-btn {
      left: 50%;
      top: -14px;
      transform: translateX(-50%);
      width: auto;
      height: 28px;
      box-sizing: border-box;
      padding: 0 12px;
      background: #F5F8FA;
      color: #595959;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .flow-process-condition__node-list {
      display: flex;

      .flow-process-condition__node-item {
        padding-right: 30px;
        padding-bottom: 0;

        // 中间的竖线高度100%
      .flow-process-condition__node-item-line{
          height: 2px;
          width: 100%;
          top: 0px;
          left: 0;

          &.last-line {
            left: 0;
            top: 100%;
          }
        }

        // 头尾的竖线高度50%
        &:first-of-type {
          // 头部的竖线距顶部50%
          > .flow-process-condition__node-item-line {
            left: 50%;
            width: 50%;
            height: 2px;
            top: 0;

            &.last-line {
              top: 100%;
            }
          }
        }

        &:last-of-type {
          // 尾部的竖线距顶部0
          > .flow-process-condition__node-item-line {
            left: 0;
            width: 50%;
            height: 2px;
            top: 0;

            &.last-line {
              top: 100%;
            }
          }
        }

        // 连接竖线和节点的水平线
        .flow-process-condition__node-link-line {
          height: 30px;
          width: 2px;
          top: 0px;
          left: 50%;
          transform: translateX(-50%);
        }

        .flow-process-condition__node-item-wrap {
          flex-direction: column;
          height: 100%;

          // 连接较短分支和分支整体右侧的水平线
          .flow-process-condition__link-cross-line {
            width: 2px;
          }
        }
      }
    }
  }

  .flow-process-condition__add-btn {
    position: absolute;
    left: -18px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    width: 36px;
    display: flex;
    flex-wrap: wrap;
    border: 2px solid #dedede;
    background: #fff;
    border-radius: 18px;
    color: #222;
    cursor: pointer;
    font-size: 12px;
    padding: 10px;
    text-align: center;
  }

  .flow-process-condition__node-list {
    .flow-process-condition__node-item {
      padding: 30px;
      padding-right: 0;
      position: relative;

      // 中间的竖线高度100%
      .flow-process-condition__node-item-line {
        position: absolute;
        height: 100%;
        width: 2px;
        left: 0px;
        top: 0;
        background-color: #dedede;

        &.last-line {
          left: 100%;
        }
      }

      // 头尾的竖线高度50%
      &:first-of-type {
        // 头部的竖线距顶部50%
        > .flow-process-condition__node-item-line {
          top: 50%;
          height: 50%;
        }
      }

      &:last-of-type {
        // 尾部的竖线距顶部0
        > .flow-process-condition__node-item-line {
          top: 0;
          height: 50%;
        }
      }

      // 连接竖线和节点的水平线
     .flow-process-condition__node-link-line {
        position: absolute;
        width: 30px;
        height: 2px;
        left: 0px;
        top: 50%;
        transform: translateY(-50%);
        background-color: #dedede;
      }

      .flow-process-condition__node-item-wrap {
        display: flex;
        align-items: center;

        // 连接较短分支和分支整体右侧的水平线
        .flow-process-condition__link-cross-line {
          height: 2px;
          flex-grow: 1;
          background-color: #dedede;
        }
      }
    }
  }
}
</style>
