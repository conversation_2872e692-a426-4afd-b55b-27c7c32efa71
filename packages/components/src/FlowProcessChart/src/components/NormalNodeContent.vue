<template>
  <div :class="['flow-process-node__normal-content', readonly ? 'cursor-default' : null ]">
    <div :class="{
      'flow-process-node__normal-content-title':  true,
      'start-node': isStartNode,
      'process-node': isProcessNode,
      'approve-node': isApproveNode,
      'end-node': isEndNode,
      'carbon-copy-node': isCarbonCopyNode,
    }">
      {{ data.title || '' }}
    </div>
    <div class="flow-process-node__normal-content-data">
      <template v-if="data.content">
<!--        <div class="flow-process-node__normal-content-data__text">{{ data.content }}</div>-->
        <el-tooltip :content="data.content" :disabled="disabled" popper-class="flow-process-tooltip">
         <span class="flow-process-node__normal-content-data__text" @mouseenter="handleMouse($event)">
            {{ data.content}}
         </span>
        </el-tooltip>
      </template>
      <template v-else>
        <div class="flow-process-node__normal-content-data__text placeholder">{{ data.placeholder ||  '' }}</div>
      </template>
      <img v-if="!readonly" class="flow-process-node__normal-content-data__icon" src="../assets/svg/arrow.svg" alt="" />
    </div>
    <i v-if="currentNodeExistInNodeThroughId" :class="['iconfont through-icon', nodeCurrentNodesIds.includes(data?.id) ? 'icon-shenpizhong': 'icon-check-circle']"></i>
  </div>
</template>

<script>

import { store } from '../constant'
import { textIsBeyond } from "../utils";
export default {
  name: 'flow-process-normal-node-content',
  data() {
    return {
      readonly: store.readonly,
      nodeThroughIds: store.nodeThroughIds,
      nodeCurrentNodesIds: store.currentNodeIds,
      disabled: false
    }
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  computed: {
    currentNodeExistInNodeThroughId() {
      return this.nodeThroughIds.concat(this.nodeCurrentNodesIds).includes(this.data?.id)
    },
    isStartNode() {
      return this.data.type === 'start-node'
    },
    isProcessNode() {
      return this.data.type === 'process-node'
    },
    isApproveNode() {
      return this.data.type === 'approve-node'
    },
    isEndNode() {
      return this.data.type === 'approve-node'
    },
    isCarbonCopyNode() {
      return this.data.type === 'carbon-copy-node'
    }
  },
  methods: {
    handleMouse(e)  {
      this.disabled = textIsBeyond(e);
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__normal-content {
  width: 220px;
  min-height: 76px;
  padding: 0 0 8px;
  //border: 2px solid transparent;
  border-radius: 8px;
  box-shadow: 0 1px 4px 0 rgba(10, 30, 65, 0.16);
  transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  box-sizing: border-box;
  position: relative;

  &-title {

    position: relative;
    //display: flex;
    //align-items: center;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    font-size: 14px;
    text-align: left;
    color: #fff;
    //font-weight: 600;
    word-break: break-all;
    box-sizing: border-box;
    padding: 0 12px;
    height: 28px;
    line-height: 28px;
    width: 220px;
    @include text-ellipsis();

    &.start-node {
      background: #7B93A8;
    }

    &.end-node{

    }

    &.process-node{
        background: $color-primary;
    }
    &.approve-node{
        background: #FF8800;
    }
    &.carbon-copy-node{
      background: #7173DF;
    }
  }

  &-data {
    width: 100%;
    display: flex;
    min-height: 32px;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    margin-top: 10px;
    //background: rgba(0, 0, 0, 0.03);
    border-radius: 4px;

    &__text {
      //min-height: 60px;
      max-width: calc(220px - 32px);
      color: #262626;
      font-size: 14px;
      //line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;

      &.placeholder {
        color: #8C8C8C;
      }
    }

    &__icon {
      width: 16px;
    }
  }

  .through-icon{
    position: absolute;
    right: -24px;
    top: 2px;
  }
  .icon-check-circle{
    color: #52c41a;
  }
  .icon-shenpizhong{
    color: rgb(250, 174, 20);
  }
}
</style>
