<template>
  <div class="flow-process-node__delete" @click.stop="$emit('click')">
    <i class="el-icon-close"></i>
<!--    <svg-->
<!--      viewBox="0 0 1024 1024"-->
<!--      version="1.1"-->
<!--      xmlns="http://www.w3.org/2000/svg"-->
<!--      xmlns:xlink="http://www.w3.org/1999/xlink"-->
<!--    >-->
<!--      <path d="M106.666667 213.333333h810.666666v42.666667H106.666667z"></path>-->
<!--      <path-->
<!--        d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"-->
<!--      ></path>-->
<!--      <path-->
<!--        d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"-->
<!--      ></path>-->
<!--      <path-->
<!--        d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"-->
<!--      ></path>-->
<!--    </svg>-->
  </div>
</template>

<script>
export default {
  name: 'flow-process-delete-node'
}
</script>

<style lang="scss" scoped>
.flow-process-node__delete {
  position: absolute;
  top: 2px;
  right: 0;
  //background: $color-primary;
  width: 24px;
  height: 24px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  animation: show 0.25s;
  z-index: 20;
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 4px;
    left: 0;
    bottom: -4px;
  }

  svg {
    fill: #fff;
    width: 16px;
    height: 16px;
  }
  .el-icon-close{
    font-size: 16px;
    color: #fff;
  }
  &.condition-del{
  .el-icon-close{
      color: #888888;
    }
  }
}

@keyframes show {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
