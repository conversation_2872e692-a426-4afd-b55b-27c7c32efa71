<template>
  <div class="flow-process-node__type-content">
    <div
      class="flow-process-node__type-group"
      v-for="(group, groupIndex) in nodeTypeList"
      :key="groupIndex"
    >
      <div class="flow-process-node__type-group-name">{{ group.name }}</div>

      <div class="flow-process-node__type-list">
        <div
          :class="['flow-process-node__type-list-item', readonly ? 'cursor-default' : null ]"
          v-for="(type, typeIndex) in group.list"
          :key="typeIndex"
          @click="onNodeTypeClick(type, group)"
        >
          <i :class="type.icon" :style="{ color: type.color }"></i>
          <span>{{type.name}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defaultNodeTypeList, store } from '../constant'

export default {
  name: 'flow-process-node-type-content',

  props: {
    showNodeTypePopover: {
      type: <PERSON><PERSON><PERSON>,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      nodeTypeList: store.nodeTypeList || defaultNodeTypeList,
      readonly: store.readonly,
      }
  },
  methods: {
    onNodeTypeClick(...args) {
      this.$emit('click', ...args)
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__type-content {
  padding: 12px 8px;
  width: 300px;
  background-color: #fff;
  border-radius: 4px;
  display: flex;

  .flow-process-node__type-group {
    width: 100%;
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }

    &-name {
      color: #262626;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .flow-process-node__type-list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: -12px;

      &-item {
        display: flex;
        width: 136px;
        padding: 8px 12px;
        //border: 1px solid #2e2e2e;
        background: #F5F5F5;
        color: #262626;
        font-size: 14px;
        cursor: pointer;
        margin-bottom: 12px;
        align-items: center;
        border-radius: 8px;
        i{
          font-size: 18px;
          padding: 5px 8px;
          border: 1px solid #dedfdf;
          background: #fff;
          border-radius: 8px;
        }
        span {
          display: block;
          margin-left: 8px;
        }
        &:hover {
          //border-color: #595959;
          //background: #3b3b3b;
        }
      }
    }
  }
}



//.node-select{
//  div{
//    display: inline-block;
//    margin: 5px 5px;
//    cursor: pointer;
//    padding: 10px 15px;
//    border: 1px solid #F8F9F9;
//    background-color: #F8F9F9;
//    border-radius: 10px;
//    width: 130px;
//    position: relative;
//    span{
//      position: absolute;
//      left: 65px;
//      top: 18px;
//    }
//    &:hover{
//      background-color: #fff;
//      box-shadow: 0 0 8px 2px #d6d6d6;
//    }
//    i{
//      font-size: 25px;
//      padding: 5px;
//      border: 1px solid #dedfdf;
//      border-radius: 14px;
//    }
//  }
//}
</style>
