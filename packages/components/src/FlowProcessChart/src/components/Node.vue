<template>
  <div
    class="flow-process-node__container"
    @mouseenter.stop="onMouseenter"
    @mouseleave.stop="onMouseleave"
  >
    <!-- 开始节点 -->
    <flow-process-start-node
      v-if="data.type === 'start-node'"
      :nodeList="nodeList"
      :data="data"
      :nextData="nextData"
      :isMouseEnter="isMouseEnter || isCurrentMouseEnter"
    ></flow-process-start-node>
    <!-- 结束节点 -->
    <flow-process-end-node v-else-if="data.type === 'end-node'" :data="data" :nodeList="nodeList"></flow-process-end-node>
    <!-- 分支节点 -->
    <!--    TODO 相关分支并行分支处理  -->
    <flow-process-condition-or-parallel-node
      v-else-if="data.type === 'condition' || data.type === 'parallel'"
      :nodeList="nodeList"
      :data="data"
      :nextData="nextData"
      :isMouseEnter="isMouseEnter || isCurrentMouseEnter"
    ></flow-process-condition-or-parallel-node>
    <!-- 普通节点 -->
    <flow-process-normal-node
      v-else
      :nodeList="nodeList"
      :childrenList="childrenList"
      :data="data"
      :nextData="nextData"
      :belongConditionNodeData="belongConditionNodeData"
      :isMouseEnter="isMouseEnter || isCurrentMouseEnter"
    ></flow-process-normal-node>
  </div>
</template>

<script>

export default {
  name: 'flow-process-node',
  props: {
    nodeList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    childrenList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    data: {
      type: Object,
      default: null
    },
    nextData: {
      type: Object,
      default: null
    },
    belongConditionNodeData: {
      type: Object,
      default: null
    },
    isMouseEnter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isCurrentMouseEnter: false
    }
  },
  methods: {
    onMouseenter() {
      this.isCurrentMouseEnter = true
    },

    onMouseleave() {
      this.isCurrentMouseEnter = false
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__container {
  // flex-shrink: 0;
  position: relative;
}
</style>
