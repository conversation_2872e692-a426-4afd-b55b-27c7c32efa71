<template>
  <div class="flow-process-node__start-container" :class="{ vertical: vertical }">
    <flow-process-normal-node-content :data="data" :nextData="nextData" @click.native="onContentClick"></flow-process-normal-node-content>
    <flow-process-arrow-line :data="data"></flow-process-arrow-line>
    <flow-process-add-node
      :nodeList="nodeList"
      :nodeData="data"
      :nextData="nextData"
      :btnType="isMouseEnter ? 'dot' : ''"
    ></flow-process-add-node>
  </div>
</template>

<script>
import ArrowLine from './ArrowLine.vue'
import AddNode from './AddNode.vue'
import FlowProcessNormalNodeContent from './NormalNodeContent.vue';
import { store } from '../constant'
import emitter from "@packages/components/src/FlowProcessChart/src/emit";

export default {
  name: 'flow-process-start-node',
  components: {
    [ArrowLine.name]: <PERSON>L<PERSON>,
    [AddNode.name]: AddNode,
    [FlowProcessNormalNodeContent.name]: FlowProcessNormalNodeContent
  },
  props: {
    nodeList: {
      type: [Array, null],
      default() {
        return null
      }
    },
    data: {
      type: Object,
      default: null
    },
    nextData: {
      type: Object,
      default: null
    },
    isMouseEnter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      vertical: store.vertical
    }
  },
  methods: {
    onContentClick() {
      emitter.emit('node-content-click', this.data, this.nodeList)
    },
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__start-container {
  display: flex;
  align-items: center;

  &.vertical {
    flex-direction: column;
  }

  &-content {
    width: 90px;
    height: 40px;
    border-radius: 30px;
    box-shadow: 0 1px 5px 0 rgba(10, 30, 65, 0.08);
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #2c2c2c;
  }
}
</style>
