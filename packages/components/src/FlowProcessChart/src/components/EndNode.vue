<template>
  <div :class="['flow-process-node__end-container', readonly ? 'cursor-default' : null]" @click="onContentClick">{{ data.title }}</div>
</template>

<script>

import emitter from '../emit';
import { store } from '../constant'
export default {
  name: 'flow-process-end-node',
  props: {
    data: {
      type: Object,
      default: null
    },
    nodeList: {
      type: [Array, null],
      default() {
        return null
      }
    },
  },
  data() {
    return  {
      readonly: store.readonly
    }
  },
  methods: {
    onContentClick() {
      emitter.emit('node-content-click', this.data, this.nodeList)
    },
  }
}
</script>

<style lang="scss" scoped>
.flow-process-node__end-container {
  width: 150px;
  height: 40px;
  border-radius: 60px;
  box-shadow: 0 1px 5px 0 rgba(10, 30, 65, 0.08);
  color: #595959;
  line-height: 40px;
  text-align: center;
  background: #F5F8FA;
  font-size: 14px;
  border: 1px solid #CBD6E2;
  cursor: pointer;
  box-sizing: border-box;
  padding: 0 10px;
  @include text-ellipsis;
}
</style>
