/* init */
import './init'
/* components */
// import Button from './Button';
// import FormBuilder from './FormBuilder';
import TimeoutSettingModal from './TimeoutSettingModal';
import ConfigContact from "./ConfigContact";
import FlowProcessChart from "./FlowProcessChart";
// import formComponents from '@src/component';

// import Modal from '@src/component/common/BaseModal';

const components = [
  // Button,
  // FormBuilder,
  TimeoutSettingModal,
  ConfigContact,
  FlowProcessChart
];

const install = function(Vue) {
  if (install.installed) return;
  install.installed = true;
  components.map(component => Vue.use(component));
  // Vue.use(formComponents);
};

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
  ...components
};
