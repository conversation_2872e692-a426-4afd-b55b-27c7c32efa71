
# shb-pass 业务组件供其他项目使用


##  简介
```
售后包PAAS平台业务组件 pc端
```



### 项目结构
```




这个子项目中其实有很多依赖没用,暂时没有移除 

```


## 打包需要修改的文件

- Edge.ts文件中的`@antv/x6`引用直接注释 不影响打包 然后将有关的`@antv/x6`引用使用的地方直接置为空字符串即可
- 对比Dynamic.vue修改Dynamic.vue中代码 --src/view/designer/workflow/components/ConfigPanel/ConfigContact/components/Dynamic.vue
    ```
    /* model */
    import { buildDynamicMenus, convertDeptManagerId } from '@model/Graph/Dynamic';
    // import FlowEnum from '@model/enum/FlowEnum';
    // import Cell from '@model/Graph/Cell';
    // const { PROCEDD_NODE, APPROVE_NODE } = FlowEnum;
    
    const PROCEDD_NODE = 'process-nod'
    const APPROVE_NODE = 'approve-node'
    
    
    const packToCell = (cell) => {
      const option = {};
    
      // 节点id
      option.id = cell.id;
      // 节点类型
      option.shape = cell.shape;
      // 节点层级
      option.zIndex = cell.zIndex || 0;
    
      // 自定义数据
      option.data = cell.data;
    
    
      // 处理边
      if (cell.shape == 'edge') {
        option.source = cell.source;
        option.target = cell.target;
        option.vertices = cell.vertices; //位置信息
      } else {
        let { x, y } = cell.position || {};
        option.x = x;
        option.y = y;
      }
    
      return option;
    }
    
    ```
- 在src/locales/ 新建一个文件与index.ts文件内容一样然后修改packages/components/src/init.js中的import { t } from '@src/locales'为你新建的文件









