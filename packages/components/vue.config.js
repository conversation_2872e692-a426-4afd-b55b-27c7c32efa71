const path = require('path');
const resolve = dir => path.join(__dirname, dir);
const webpack = require("webpack");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const CompressionPlugin = require("compression-webpack-plugin");
const LodashModuleReplacementPlugin = require("lodash-webpack-plugin");

function genSassResourceLoader() {
  return {
    loader: 'sass-resources-loader',
    options: {
      resources: [
        path.resolve('../../src/assets/scss/_variables.scss'),
        path.resolve('../../src/assets/scss/_mixins.scss')
      ]
    }
  };
}

module.exports = {
  productionSourceMap: false,
  css: { extract: false }, //组件暂时不对 将css打入相关js文件中
  configureWebpack: {
    resolve: {
      alias: {
        '@src': path.resolve('../../src'),
        '@': resolve('../../src'),
        '@service': path.resolve('../../service'),
        '@packages': path.resolve('../../packages'),
        '@model': path.resolve('../../src/model'),
        '@business': resolve('../../src/business'),
        '@hooks': resolve('../../hooks'),
        '@lang': resolve('./../../node_modules/pub-bbx-global/lang/dist'),
        '@pageType': resolve('./../../node_modules/pub-bbx-global/pageType/dist'),
        'pub-bbx-global': resolve('./../../node_modules/pub-bbx-global'),
      },
      extensions: ['.js', '.ts', '.tsx', '.vue', '.json']
    },
    externals: {
      'vue': 'vue',
      'element-ui':'element-ui',
      'codemirror': 'codemirror',
      'vue-codemirror': 'vue-codemirror',
      // 'lodash': 'lodash',
      'quill': 'quill',
      'jquery': 'jquery',
      'async-validator': 'async-validator',
      'core-js': 'core-js',
    },
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                babelrc: false,
                configFile: false,
                presets: [
                  '@babel/preset-env', // 可以识别es6语法
                  '@vue/babel-preset-jsx' // 解析jsx语法
                ]
              }
            },
            {
              loader: 'ts-loader',
              options: {
                appendTsxSuffixTo: [/\.vue$/]
              }
            }
          ]
        },
        {
          test: /\.scss$/,
          use: [genSassResourceLoader()]
        }
      ],
    },
    plugins: [
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 1
      }),
      new LodashModuleReplacementPlugin(),
      new CompressionPlugin(),
      // new BundleAnalyzerPlugin()
    ]
  },
  chainWebpack: config => {
    config.optimization.delete('splitChunks');
    config.plugins.delete('copy');
    config.plugins.delete('html');
    config.plugins.delete('preload');
    config.plugins.delete('prefetch');
    config.plugins.delete('hmr');
    config.entryPoints.delete('app');
    config.optimization.minimize(true);
  }
};
